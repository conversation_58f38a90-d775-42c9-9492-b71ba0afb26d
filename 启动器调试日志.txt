=== 启动器调试日志 ===
启动时间: 2025/6/12 19:02:53

步骤1: 初始化对象...
成功: 对象初始化完成

步骤2: 获取当前目录...
成功: 当前目录 = E:\自动发邮件

步骤3: 检查主脚本文件...
查找文件: E:\自动发邮件\gui_main.py
成功: gui_main.py文件存在

步骤4: 检测Python环境...
测试python命令...
python --version 返回码: 0
成功: python命令可用
最终选择的Python命令: python

步骤5: 构建启动命令...
启动命令: cmd.exe /c "cd /d "E:\自动发邮件" && python gui_main.py"

步骤6: 启动程序...
启动命令返回码: 0
成功: 程序启动命令已执行
注意: 这不代表GUI一定启动成功，只是命令执行了

步骤7: 启动器执行完成
完成时间: 2025/6/12 19:02:54
=== 日志结束 ===
