#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试相似度计算问题
"""

import os
import sqlite3
import datetime
from email_history_manager import EmailHistoryManager
from rag_search_engine import RAGSearchEngine

def debug_database_content():
    """调试数据库内容"""
    print("🔍 调试数据库内容...")
    
    # 删除现有数据库
    if os.path.exists("debug_email_history.db"):
        os.remove("debug_email_history.db")
    
    # 创建测试历史管理器
    history_manager = EmailHistoryManager("debug_email_history.db")
    
    # 添加测试邮件记录
    test_emails = [
        {
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "subject": "重要通知",
            "body": "这是一封重要的通知邮件，请查收相关文件。",
            "success": True
        },
        {
            "sender": "<EMAIL>", 
            "recipient": "<EMAIL>",
            "subject": "重要通知",
            "body": "这是一封重要的通知邮件，请查收相关文件。",
            "success": True
        }
    ]
    
    for email in test_emails:
        record_id = history_manager.add_email_record(
            email["sender"],
            email["recipient"], 
            email["subject"],
            email["body"],
            email["success"]
        )
        print(f"✅ 添加测试邮件: {email['recipient']} - {record_id}")
    
    # 直接查询数据库
    conn = sqlite3.connect("debug_email_history.db")
    cursor = conn.cursor()
    
    cursor.execute("SELECT * FROM email_records")
    records = cursor.fetchall()
    
    print(f"\n📊 数据库中的记录 ({len(records)} 条):")
    for record in records:
        print(f"ID: {record[0]}")
        print(f"发件人: {record[1]}")
        print(f"收件人: {record[2]}")
        print(f"主题: {record[3]}")
        print(f"正文: {record[4]}")
        print(f"内容哈希: {record[5]}")
        print(f"发送时间: {record[6]}")
        print(f"成功: {record[7]}")
        print("-" * 40)
    
    conn.close()
    return history_manager

def debug_search_process():
    """调试搜索过程"""
    print("\n🔍 调试搜索过程...")
    
    # 创建RAG搜索引擎
    rag_search = RAGSearchEngine("debug_email_history.db")
    
    # 测试查询
    test_subject = "重要通知"
    test_body = "这是一封重要的通知邮件，请查收相关文件。"
    test_sender = "<EMAIL>"
    
    print(f"📧 查询内容:")
    print(f"   主题: {test_subject}")
    print(f"   正文: {test_body}")
    print(f"   发件人: {test_sender}")
    
    # 1. 测试预处理
    query = f"{test_subject} {test_body}"
    query_terms = rag_search.preprocess_text(query)
    print(f"\n🔤 预处理结果:")
    print(f"   原始查询: {query}")
    print(f"   查询词汇: {query_terms}")
    
    # 2. 测试数据库查询
    conn = sqlite3.connect("debug_email_history.db")
    cursor = conn.cursor()
    
    # 构建查询条件
    where_conditions = []
    params = []
    
    if test_sender:
        where_conditions.append("sender_email = ?")
        params.append(test_sender)
    
    # LIKE查询
    like_conditions = []
    for term in query_terms[:5]:
        like_conditions.append("(subject LIKE ? OR body LIKE ?)")
        params.extend([f"%{term}%", f"%{term}%"])
    
    if like_conditions:
        where_conditions.append(f"({' OR '.join(like_conditions)})")
    
    where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
    
    print(f"\n🔍 SQL查询:")
    print(f"   WHERE子句: {where_clause}")
    print(f"   参数: {params}")
    
    cursor.execute(f'''
        SELECT id, sender_email, recipient_email, subject, body, 
               send_time, success, batch_id, content_hash
        FROM email_records 
        WHERE {where_clause}
        ORDER BY send_time DESC
        LIMIT ?
    ''', params + [20])
    
    records = cursor.fetchall()
    print(f"\n📊 LIKE查询结果 ({len(records)} 条):")
    for record in records:
        print(f"   - {record[2]}: {record[3]} | {record[4][:50]}...")
    
    # 如果LIKE查询没有结果，尝试获取所有邮件
    if not records and test_sender:
        print(f"\n🔄 LIKE查询无结果，获取该发件人的所有邮件...")
        cursor.execute('''
            SELECT id, sender_email, recipient_email, subject, body, 
                   send_time, success, batch_id, content_hash
            FROM email_records 
            WHERE sender_email = ?
            ORDER BY send_time DESC
            LIMIT ?
        ''', [test_sender, 20])
        records = cursor.fetchall()
        print(f"📊 发件人查询结果 ({len(records)} 条):")
        for record in records:
            print(f"   - {record[2]}: {record[3]} | {record[4][:50]}...")
    
    conn.close()
    
    # 3. 测试TF-IDF计算
    if records:
        print(f"\n🧮 测试TF-IDF计算...")
        
        # 转换为文档格式
        documents = []
        for record in records:
            # 确保时间字段不为None
            send_time = record[5] if record[5] else datetime.datetime.now().isoformat()

            documents.append({
                'id': record[0],
                'sender_email': record[1],
                'recipient_email': record[2],
                'subject': record[3],
                'body': record[4],
                'send_time': send_time,
                'success': bool(record[6]),
                'batch_id': record[7] if len(record) > 7 else None,
                'content_hash': record[8] if len(record) > 8 else None
            })
        
        print(f"   文档数量: {len(documents)}")
        
        # 详细分析每个文档的词汇
        print(f"   查询词汇: {query_terms}")
        for i, doc in enumerate(documents):
            content = f"{doc['subject']} {doc['body']}"
            doc_terms = rag_search.preprocess_text(content)
            common_terms = set(query_terms) & set(doc_terms)
            print(f"   文档{i+1} ({doc['recipient_email']}):")
            print(f"     内容: {content}")
            print(f"     文档词汇: {doc_terms}")
            print(f"     共同词汇: {list(common_terms)}")

        # 计算TF-IDF
        scored_docs = rag_search.calculate_tf_idf(query_terms, documents)
        print(f"   评分文档数量: {len(scored_docs)}")

        if scored_docs:
            for doc, score in scored_docs:
                print(f"   - {doc['recipient_email']}: 分数 {score:.6f}")
        else:
            print("   ⚠️ 没有评分文档，可能TF-IDF计算有问题")
    
    return rag_search

def debug_exact_match():
    """调试完全匹配的情况"""
    print("\n🎯 调试完全匹配...")
    
    rag_search = RAGSearchEngine("debug_email_history.db")
    
    # 使用完全相同的内容进行查询
    test_subject = "重要通知"
    test_body = "这是一封重要的通知邮件，请查收相关文件。"
    test_sender = "<EMAIL>"
    
    # 直接调用semantic_search
    results = rag_search.semantic_search(f"{test_subject} {test_body}", test_sender, 10)
    
    print(f"📊 语义搜索结果 ({len(results)} 条):")
    for result in results:
        print(f"   - {result['recipient_email']}: 相似度 {result['relevance_score']:.6f}")
        print(f"     主题: {result['subject']}")
        print(f"     正文: {result['body']}")
        print(f"     匹配: {result['subject'] == test_subject and result['body'] == test_body}")

def cleanup():
    """清理"""
    if os.path.exists("debug_email_history.db"):
        os.remove("debug_email_history.db")
        print("✅ 清理完成")

def main():
    """主函数"""
    print("🚀 开始调试相似度计算问题")
    print("="*60)
    
    try:
        # 1. 调试数据库内容
        history_manager = debug_database_content()
        
        # 2. 调试搜索过程
        rag_search = debug_search_process()
        
        # 3. 调试完全匹配
        debug_exact_match()
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        cleanup()

if __name__ == "__main__":
    main()
