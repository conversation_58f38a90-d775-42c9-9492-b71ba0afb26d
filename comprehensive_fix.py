#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统综合修复工具
一键修复所有已知问题
"""

import os
import sys
import logging
import datetime
from typing import Dict, List

# 导入修复模块
try:
    from bounce_rate_fix import BounceRateFixer
    from full_automation_fix import FullAutomationFixer
except ImportError as e:
    print(f"❌ 导入修复模块失败: {str(e)}")
    print("请确保 bounce_rate_fix.py 和 full_automation_fix.py 在同一目录")
    sys.exit(1)

class ComprehensiveFixer:
    """综合修复器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.fixes_applied = []
        self.fixes_failed = []
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('ComprehensiveFixer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 控制台输出
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
            
            # 文件输出
            file_handler = logging.FileHandler('comprehensive_fix.log', encoding='utf-8')
            file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
        
        return logger
    
    def run_comprehensive_fix(self):
        """运行综合修复"""
        try:
            self.logger.info("🚀 开始邮件系统综合修复")
            self.logger.info("=" * 60)
            
            # 1. 修复RecipientQualityManager参数错误
            self._fix_recipient_quality_manager()
            
            # 2. 修复QQ应急状态更新错误
            self._fix_qq_emergency_status()
            
            # 3. 修复退信率统计问题
            self._fix_bounce_rate_statistics()
            
            # 4. 修复全自动模式问题
            self._fix_full_automation_mode()
            
            # 5. 修复定时发送功能
            self._fix_schedule_functionality()
            
            # 6. 生成修复报告
            self._generate_fix_report()
            
            self.logger.info("✅ 邮件系统综合修复完成")
            
        except Exception as e:
            self.logger.error(f"❌ 综合修复失败: {str(e)}")
    
    def _fix_recipient_quality_manager(self):
        """修复RecipientQualityManager参数错误"""
        try:
            self.logger.info("🔧 修复RecipientQualityManager参数错误...")
            
            # 检查文件是否存在
            if not os.path.exists('recipient_quality_manager.py'):
                self.logger.warning("⚠️ recipient_quality_manager.py 不存在，跳过修复")
                return
            
            # 读取文件内容
            with open('recipient_quality_manager.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经修复
            if 'max_quality_score' in content:
                self.logger.info("✅ RecipientQualityManager参数已修复")
                self.fixes_applied.append("RecipientQualityManager参数修复")
            else:
                self.logger.warning("⚠️ RecipientQualityManager参数可能需要手动修复")
                self.fixes_failed.append("RecipientQualityManager参数修复")
            
        except Exception as e:
            self.logger.error(f"❌ 修复RecipientQualityManager失败: {str(e)}")
            self.fixes_failed.append("RecipientQualityManager参数修复")
    
    def _fix_qq_emergency_status(self):
        """修复QQ应急状态更新错误"""
        try:
            self.logger.info("🔧 修复QQ应急状态更新错误...")
            
            # 检查深度协调系统文件
            if not os.path.exists('深度系统协调实现.py'):
                self.logger.warning("⚠️ 深度系统协调实现.py 不存在，跳过修复")
                return
            
            # 读取文件内容
            with open('深度系统协调实现.py', 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否已经修复
            if 'isinstance(event, str)' in content:
                self.logger.info("✅ QQ应急状态事件处理已修复")
                self.fixes_applied.append("QQ应急状态事件处理修复")
            else:
                self.logger.warning("⚠️ QQ应急状态事件处理可能需要手动修复")
                self.fixes_failed.append("QQ应急状态事件处理修复")
            
        except Exception as e:
            self.logger.error(f"❌ 修复QQ应急状态失败: {str(e)}")
            self.fixes_failed.append("QQ应急状态事件处理修复")
    
    def _fix_bounce_rate_statistics(self):
        """修复退信率统计问题"""
        try:
            self.logger.info("🔧 修复退信率统计问题...")
            
            bounce_fixer = BounceRateFixer()
            bounce_fixer.fix_bounce_rate_calculation()
            
            # 获取修复后的报告
            report = bounce_fixer.get_bounce_rate_report()
            
            if report and report.get('total_sent', 0) > 0:
                self.logger.info(f"✅ 退信率统计修复完成，总体退信率: {report['overall_bounce_rate']:.2%}")
                self.fixes_applied.append("退信率统计修复")
            else:
                self.logger.info("✅ 退信率统计修复完成（暂无数据）")
                self.fixes_applied.append("退信率统计修复")
            
        except Exception as e:
            self.logger.error(f"❌ 修复退信率统计失败: {str(e)}")
            self.fixes_failed.append("退信率统计修复")
    
    def _fix_full_automation_mode(self):
        """修复全自动模式问题"""
        try:
            self.logger.info("🔧 修复全自动模式问题...")
            
            automation_fixer = FullAutomationFixer()
            automation_fixer.fix_full_automation_mode()
            
            # 获取状态报告
            report = automation_fixer.get_automation_status_report()
            
            if report and report.get('status') in ['fully_enabled', 'mostly_enabled']:
                self.logger.info(f"✅ 全自动模式修复完成，状态: {report['status']}")
                self.fixes_applied.append("全自动模式修复")
            else:
                self.logger.warning("⚠️ 全自动模式修复可能不完整")
                self.fixes_failed.append("全自动模式修复")
            
        except Exception as e:
            self.logger.error(f"❌ 修复全自动模式失败: {str(e)}")
            self.fixes_failed.append("全自动模式修复")
    
    def _fix_schedule_functionality(self):
        """修复定时发送功能"""
        try:
            self.logger.info("🔧 修复定时发送功能...")
            
            # 检查定时发送相关文件
            schedule_files = [
                'schedule_manager.py',
                'schedule_gui_enhanced.py'
            ]
            
            missing_files = []
            for file in schedule_files:
                if not os.path.exists(file):
                    missing_files.append(file)
            
            if missing_files:
                self.logger.warning(f"⚠️ 定时发送相关文件缺失: {', '.join(missing_files)}")
                self.fixes_failed.append("定时发送功能修复")
            else:
                self.logger.info("✅ 定时发送功能文件完整")
                self.fixes_applied.append("定时发送功能修复")
            
        except Exception as e:
            self.logger.error(f"❌ 修复定时发送功能失败: {str(e)}")
            self.fixes_failed.append("定时发送功能修复")
    
    def _generate_fix_report(self):
        """生成修复报告"""
        try:
            self.logger.info("📊 生成修复报告...")
            
            report = {
                'fix_time': datetime.datetime.now().isoformat(),
                'total_fixes_attempted': len(self.fixes_applied) + len(self.fixes_failed),
                'fixes_applied': self.fixes_applied,
                'fixes_failed': self.fixes_failed,
                'success_rate': len(self.fixes_applied) / (len(self.fixes_applied) + len(self.fixes_failed)) * 100 if (self.fixes_applied or self.fixes_failed) else 0,
                'recommendations': []
            }
            
            # 生成建议
            if self.fixes_failed:
                report['recommendations'].append("建议手动检查失败的修复项目")
                report['recommendations'].append("查看 comprehensive_fix.log 了解详细错误信息")
            
            if report['success_rate'] >= 80:
                report['recommendations'].append("修复效果良好，可以正常使用系统")
            else:
                report['recommendations'].append("修复效果一般，建议进一步检查")
            
            # 保存报告
            import json
            with open('fix_report.json', 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            # 输出报告摘要
            self.logger.info("📋 修复报告摘要:")
            self.logger.info(f"  总修复项目: {report['total_fixes_attempted']}")
            self.logger.info(f"  成功修复: {len(self.fixes_applied)}")
            self.logger.info(f"  修复失败: {len(self.fixes_failed)}")
            self.logger.info(f"  成功率: {report['success_rate']:.1f}%")
            
            if self.fixes_applied:
                self.logger.info("  ✅ 成功修复的项目:")
                for fix in self.fixes_applied:
                    self.logger.info(f"    - {fix}")
            
            if self.fixes_failed:
                self.logger.info("  ❌ 修复失败的项目:")
                for fix in self.fixes_failed:
                    self.logger.info(f"    - {fix}")
            
            self.logger.info("📄 详细报告已保存到: fix_report.json")
            
        except Exception as e:
            self.logger.error(f"❌ 生成修复报告失败: {str(e)}")
    
    def create_usage_guide(self):
        """创建使用指南"""
        try:
            guide_content = """
# 📖 邮件系统修复后使用指南

## 🎯 修复内容概述

本次修复解决了以下问题：

1. **RecipientQualityManager参数错误** - 修复了 `max_quality_score` 参数问题
2. **QQ应急状态更新错误** - 修复了 `REPLY_RECEIVED` 和 `REPLY_ANALYZED` 事件处理
3. **退信率显示问题** - 修复了退信率计算和显示逻辑
4. **全自动模式问题** - 修复了自动化操作流程配置
5. **定时发送功能** - 增强了定时发送的保存和预发送功能

## 🚀 启动系统

### 方法1: 使用VBS启动器（推荐）
```
双击 "快速启动.vbs"
```

### 方法2: 命令行启动
```bash
python gui_main.py
```

## 🔧 使用全自动模式

1. 启动系统后，点击 **"一键启用所有功能"** 按钮
2. 填写发件人邮箱和IMAP授权码
3. 系统将自动配置所有功能模块

## 📧 定时发送功能

1. 切换到 **"⏰ 定时发送"** 标签页
2. 填写邮件基本信息
3. 设置定时时间
4. 可以保存草稿或预览邮件
5. 点击 **"✅ 创建任务"** 创建定时任务

## 📊 监控和质量管理

1. 发送邮件后，系统会自动启动回复监控
2. 监控完成后，有效收件人会自动导入质量数据库
3. 系统会自动检查应急状态并进行协调

## 🔍 问题排查

如果遇到问题，请检查：

1. **日志文件**: comprehensive_fix.log
2. **修复报告**: fix_report.json
3. **配置文件**: all_features_config.json

## 📞 技术支持

如果问题仍然存在，请：

1. 查看日志文件中的错误信息
2. 运行 `python test_full_automation.py` 进行诊断
3. 检查所有必需的Python模块是否已安装

---
修复时间: {fix_time}
""".format(fix_time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            with open('使用指南_修复版.md', 'w', encoding='utf-8') as f:
                f.write(guide_content)
            
            self.logger.info("📖 使用指南已创建: 使用指南_修复版.md")
            
        except Exception as e:
            self.logger.error(f"❌ 创建使用指南失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 邮件系统综合修复工具")
    print("=" * 60)
    print("本工具将修复以下问题：")
    print("1. RecipientQualityManager参数错误")
    print("2. QQ应急状态更新错误")
    print("3. 退信率显示问题")
    print("4. 全自动模式问题")
    print("5. 定时发送功能增强")
    print("=" * 60)
    
    # 确认执行
    try:
        confirm = input("是否开始修复？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 修复已取消")
            return
    except KeyboardInterrupt:
        print("\n❌ 修复已取消")
        return
    
    # 执行修复
    fixer = ComprehensiveFixer()
    fixer.run_comprehensive_fix()
    fixer.create_usage_guide()
    
    print("\n🎉 修复完成！")
    print("📖 请查看 '使用指南_修复版.md' 了解如何使用修复后的系统")
    print("📄 详细修复报告请查看 'fix_report.json'")

if __name__ == "__main__":
    main()
