#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ ScheduleManager安全导入包装器
确保在GUI中正确导入和使用ScheduleManager
"""

import importlib
import sys
import os

def get_schedule_manager():
    """安全获取ScheduleManager实例"""
    try:
        # 确保模块路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 重新加载模块
        if 'schedule_manager' in sys.modules:
            import schedule_manager
            importlib.reload(schedule_manager)
        
        from schedule_manager import ScheduleManager, ScheduledTask
        
        # 创建实例
        manager = ScheduleManager()
        
        # 验证关键方法
        required_methods = [
            'add_scheduled_task',
            'get_all_tasks',
            'get_task_statistics',
            'get_optimal_send_time',
            'cancel_task',
            'delete_task'
        ]
        
        for method in required_methods:
            if not hasattr(manager, method):
                raise AttributeError(f"ScheduleManager缺少方法: {method}")
        
        return manager, ScheduledTask
        
    except Exception as e:
        print(f"❌ 获取ScheduleManager失败: {str(e)}")
        return None, None

def safe_call_method(manager, method_name, *args, **kwargs):
    """安全调用ScheduleManager方法"""
    try:
        if not hasattr(manager, method_name):
            raise AttributeError(f"方法不存在: {method_name}")
        
        method = getattr(manager, method_name)
        return method(*args, **kwargs)
        
    except Exception as e:
        print(f"❌ 调用方法 {method_name} 失败: {str(e)}")
        return None

# 使用示例:
# manager, ScheduledTask = get_schedule_manager()
# if manager:
#     tasks = safe_call_method(manager, 'get_all_tasks')
#     stats = safe_call_method(manager, 'get_task_statistics')
