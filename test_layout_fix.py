#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI布局管理器修复
"""

import tkinter as tk
from tkinter import ttk, messagebox

def test_layout_managers():
    """测试布局管理器冲突修复"""
    print("🔧 测试GUI布局管理器修复")
    print("=" * 50)
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.title("布局管理器测试")
        root.geometry("600x400")
        root.withdraw()  # 隐藏窗口，只做测试
        
        # 测试1：模拟反垃圾邮件管理器的策略配置标签页
        print("\n🧪 测试1: 策略配置标签页布局")
        print("-" * 30)
        
        # 创建测试框架
        test_frame = ttk.Frame(root)
        test_frame.pack(fill=tk.BOTH, expand=True)
        
        # 模拟策略配置框架
        pattern_frame = ttk.LabelFrame(test_frame, text="📋 发送模式配置", padding="10")
        pattern_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 使用修复后的布局方式
        pattern_config_frame = ttk.Frame(pattern_frame)
        pattern_config_frame.pack(fill=tk.X)
        
        # 测试grid布局
        ttk.Label(pattern_config_frame, text="发送模式:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        sending_pattern_var = tk.StringVar(value="moderate")
        pattern_combo = ttk.Combobox(pattern_config_frame, textvariable=sending_pattern_var, width=15, state="readonly")
        pattern_combo['values'] = ["conservative", "moderate", "aggressive"]
        pattern_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        ttk.Button(pattern_config_frame, text="💾 保存配置").grid(row=0, column=2, sticky=tk.E, padx=(20, 0))
        
        print("✅ 策略配置标签页布局测试成功")
        
        # 测试2：模拟QQ应急管理器的配置标签页
        print("\n🧪 测试2: QQ配置标签页布局")
        print("-" * 30)
        
        # 账户类型配置
        account_frame = ttk.LabelFrame(test_frame, text="👤 账户配置", padding="10")
        account_frame.pack(fill=tk.X, pady=(0, 10))
        
        account_row = ttk.Frame(account_frame)
        account_row.pack(fill=tk.X)
        
        # 测试pack布局
        ttk.Label(account_row, text="账户类型:").pack(side=tk.LEFT, padx=(0, 5))
        
        qq_account_type_var = tk.StringVar(value="normal")
        account_combo = ttk.Combobox(account_row, textvariable=qq_account_type_var, width=15, state="readonly")
        account_combo['values'] = ["normal", "vip"]
        account_combo.pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Label(account_row, text="每日限额:").pack(side=tk.LEFT, padx=(0, 5))
        
        qq_daily_limit_config_var = tk.StringVar(value="100")
        ttk.Entry(account_row, textvariable=qq_daily_limit_config_var, width=8).pack(side=tk.LEFT, padx=(0, 20))
        
        ttk.Button(account_row, text="💾 保存配置").pack(side=tk.LEFT, padx=10)
        
        print("✅ QQ配置标签页布局测试成功")
        
        # 测试3：混合布局测试
        print("\n🧪 测试3: 混合布局测试")
        print("-" * 30)
        
        # 创建混合布局测试框架
        mixed_frame = ttk.LabelFrame(test_frame, text="🔧 混合布局测试", padding="10")
        mixed_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 外层使用pack
        outer_frame = ttk.Frame(mixed_frame)
        outer_frame.pack(fill=tk.X)
        
        # 内层使用grid
        ttk.Label(outer_frame, text="测试标签1:").grid(row=0, column=0, sticky=tk.W)
        ttk.Entry(outer_frame, width=20).grid(row=0, column=1, padx=(5, 0))
        
        # 另一个内层框架使用pack
        inner_frame = ttk.Frame(mixed_frame)
        inner_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(inner_frame, text="测试标签2:").pack(side=tk.LEFT)
        ttk.Entry(inner_frame, width=20).pack(side=tk.LEFT, padx=(5, 0))
        
        print("✅ 混合布局测试成功")
        
        # 销毁测试窗口
        root.destroy()
        
        print("\n🎉 所有布局管理器测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 布局管理器测试失败: {str(e)}")
        return False

def test_gui_import():
    """测试GUI模块导入"""
    print("\n🧪 测试GUI模块导入")
    print("-" * 30)
    
    try:
        # 测试导入GUI主模块
        import gui_main
        print("✅ gui_main模块导入成功")
        
        # 测试创建GUI实例（不显示窗口）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = gui_main.EmailGUI(root)
        print("✅ EmailGUI实例创建成功")
        
        # 测试datetime导入
        import datetime
        current_time = datetime.datetime.now().isoformat()
        print(f"✅ datetime模块正常工作: {current_time[:19]}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI模块导入测试失败: {str(e)}")
        return False

def test_anti_spam_manager_creation():
    """测试反垃圾邮件管理器创建"""
    print("\n🧪 测试反垃圾邮件管理器创建")
    print("-" * 30)
    
    try:
        # 创建测试窗口
        root = tk.Tk()
        root.withdraw()
        
        # 模拟创建反垃圾邮件管理器窗口的关键部分
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 策略配置标签页
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="⚙️ 策略配置")
        
        # 发送模式选择（使用修复后的布局）
        pattern_frame = ttk.LabelFrame(config_frame, text="📋 发送模式配置", padding="10")
        pattern_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 创建内部框架来分离布局
        pattern_config_frame = ttk.Frame(pattern_frame)
        pattern_config_frame.pack(fill=tk.X)
        
        ttk.Label(pattern_config_frame, text="发送模式:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        sending_pattern_var = tk.StringVar(value="moderate")
        pattern_combo = ttk.Combobox(pattern_config_frame, textvariable=sending_pattern_var, width=15, state="readonly")
        pattern_combo['values'] = ["conservative", "moderate", "aggressive"]
        pattern_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        ttk.Button(pattern_config_frame, text="💾 保存配置").grid(row=0, column=2, sticky=tk.E, padx=(20, 0))
        
        print("✅ 反垃圾邮件管理器窗口创建成功")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 反垃圾邮件管理器创建测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 GUI布局管理器修复验证测试")
    print("=" * 60)
    
    tests = [
        ("布局管理器测试", test_layout_managers),
        ("GUI模块导入测试", test_gui_import),
        ("反垃圾邮件管理器创建测试", test_anti_spam_manager_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        print("=" * 40)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} - 通过")
        else:
            print(f"❌ {test_name} - 失败")
    
    print(f"\n📊 测试结果总结")
    print("=" * 40)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！布局管理器冲突已修复！")
        print("✅ 现在可以正常打开反垃圾邮件管理器了")
        print("✅ 策略配置标签页布局正常")
        print("✅ 不会再出现geometry manager错误")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    main()
