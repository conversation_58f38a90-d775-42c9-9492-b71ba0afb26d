#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发送模式测试程序
用于验证发送模式的延迟时间是否正确应用
"""

import tkinter as tk
from tkinter import ttk, messagebox
import random
import time
import threading

class SendModeTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("发送模式测试程序")
        self.root.geometry("600x400")
        
        # 发送模式变量
        self.send_mode = tk.StringVar(value="standard")
        
        self.create_widgets()
        
    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        ttk.Label(main_frame, text="发送模式延迟时间测试", 
                 font=('Arial', 16, 'bold')).grid(row=0, column=0, columnspan=3, pady=20)
        
        # 发送模式选择
        ttk.Label(main_frame, text="选择发送模式:", font=('Arial', 12)).grid(row=1, column=0, sticky=tk.W, pady=10)
        
        mode_frame = ttk.Frame(main_frame)
        mode_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        ttk.Radiobutton(mode_frame, text="快速发送（30-60秒间隔）",
                       variable=self.send_mode, value="fast").pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(mode_frame, text="标准发送（1-2分钟间隔）",
                       variable=self.send_mode, value="standard").pack(side=tk.LEFT, padx=10)
        ttk.Radiobutton(mode_frame, text="安全发送（3-5分钟间隔）",
                       variable=self.send_mode, value="safe").pack(side=tk.LEFT, padx=10)
        
        # 测试按钮
        ttk.Button(main_frame, text="🧪 测试延迟时间计算", 
                  command=self.test_delay_calculation).grid(row=3, column=0, columnspan=3, pady=20)
        
        # 模拟发送按钮
        ttk.Button(main_frame, text="📧 模拟发送3封邮件", 
                  command=self.simulate_sending).grid(row=4, column=0, columnspan=3, pady=10)
        
        # 结果显示区域
        self.result_text = tk.Text(main_frame, width=70, height=15, font=('Consolas', 10))
        self.result_text.grid(row=5, column=0, columnspan=3, pady=20)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        scrollbar.grid(row=5, column=3, sticky=(tk.N, tk.S))
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
    def log(self, message):
        """添加日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.result_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.result_text.see(tk.END)
        self.root.update()
        
    def test_delay_calculation(self):
        """测试延迟时间计算"""
        self.result_text.delete(1.0, tk.END)
        
        # 延迟时间配置（与主程序相同）
        delay_ranges = {
            "fast": (30, 60),      # 快速发送：30-60秒随机间隔
            "standard": (60, 120), # 标准发送：1-2分钟随机间隔
            "safe": (180, 300)     # 安全发送：3-5分钟随机间隔
        }
        
        selected_mode = self.send_mode.get()
        delay_range = delay_ranges.get(selected_mode, (60, 120))
        
        self.log("=" * 50)
        self.log("🧪 发送模式延迟时间测试")
        self.log("=" * 50)
        self.log(f"选择的发送模式: {selected_mode}")
        self.log(f"延迟时间范围: {delay_range[0]}-{delay_range[1]} 秒")
        self.log("")
        
        # 生成10个随机延迟时间进行测试
        self.log("生成10个随机延迟时间:")
        total_delay = 0
        for i in range(1, 11):
            delay = random.uniform(delay_range[0], delay_range[1])
            total_delay += delay
            minutes = delay // 60
            seconds = delay % 60
            self.log(f"第{i:2d}次: {delay:6.1f}秒 ({minutes:2.0f}分{seconds:4.1f}秒)")
        
        avg_delay = total_delay / 10
        avg_minutes = avg_delay // 60
        avg_seconds = avg_delay % 60
        
        self.log("")
        self.log(f"平均延迟: {avg_delay:.1f}秒 ({avg_minutes:.0f}分{avg_seconds:.1f}秒)")
        
        # 计算发送10封邮件的预计时间
        total_time = total_delay
        total_minutes = total_time // 60
        total_hours = total_minutes // 60
        remaining_minutes = total_minutes % 60
        
        self.log(f"发送10封邮件预计耗时: {total_time:.1f}秒")
        if total_hours > 0:
            self.log(f"                    = {total_hours:.0f}小时{remaining_minutes:.0f}分钟")
        else:
            self.log(f"                    = {total_minutes:.0f}分钟")
        
        self.log("=" * 50)
        
    def simulate_sending(self):
        """模拟发送邮件"""
        self.result_text.delete(1.0, tk.END)
        
        # 延迟时间配置
        delay_ranges = {
            "fast": (30, 60),
            "standard": (60, 120),
            "safe": (180, 300)
        }
        
        selected_mode = self.send_mode.get()
        delay_range = delay_ranges.get(selected_mode, (60, 120))
        
        mode_names = {
            "fast": "快速发送",
            "standard": "标准发送",
            "safe": "安全发送"
        }
        mode_name = mode_names.get(selected_mode, "标准发送")
        
        self.log("=" * 50)
        self.log("📧 模拟邮件发送测试")
        self.log("=" * 50)
        self.log(f"发送模式: {mode_name}")
        self.log(f"延迟范围: {delay_range[0]}-{delay_range[1]} 秒")
        self.log("")
        
        def send_thread():
            emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
            
            for i, email in enumerate(emails, 1):
                self.root.after(0, lambda i=i, email=email: 
                               self.log(f"📤 正在发送第 {i}/3 封邮件给: {email}"))
                
                # 模拟发送时间（1秒）
                time.sleep(1)
                
                self.root.after(0, lambda email=email: 
                               self.log(f"✅ 发送成功: {email}"))
                
                # 添加延迟（除了最后一封邮件）
                if i < len(emails):
                    delay = random.uniform(delay_range[0], delay_range[1])
                    minutes = delay // 60
                    seconds = delay % 60
                    
                    self.root.after(0, lambda d=delay, m=minutes, s=seconds: 
                                   self.log(f"⏱️ 等待 {d:.1f}秒 ({m:.0f}分{s:.1f}秒) 后发送下一封..."))
                    
                    # 模拟延迟（实际测试中缩短到1/10）
                    test_delay = delay / 10  # 缩短延迟用于测试
                    time.sleep(test_delay)
            
            self.root.after(0, lambda: self.log(""))
            self.root.after(0, lambda: self.log("🎉 模拟发送完成！"))
            self.root.after(0, lambda: self.log("注意：实际延迟已缩短到1/10用于快速测试"))
            self.root.after(0, lambda: self.log("=" * 50))
        
        # 在新线程中执行模拟发送
        threading.Thread(target=send_thread, daemon=True).start()
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    print("🧪 发送模式测试程序")
    print("=" * 40)
    print("此程序用于测试邮件发送系统的延迟时间配置")
    print("可以验证不同发送模式的延迟时间是否正确应用")
    print("=" * 40)
    
    app = SendModeTest()
    app.run()
