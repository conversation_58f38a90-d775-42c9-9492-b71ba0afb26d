#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库迁移脚本 - 修复系统集成问题
为现有数据库添加缺失的列和表结构
"""

import sqlite3
import os
import datetime
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def migrate_recipient_quality_database():
    """迁移收件人质量数据库"""
    db_path = "recipient_quality.db"
    
    if not os.path.exists(db_path):
        print(f"✅ 数据库 {db_path} 不存在，无需迁移")
        return True
    
    try:
        print(f"🔄 开始迁移数据库: {db_path}")
        
        # 备份原数据库
        backup_path = f"{db_path}.backup_{int(datetime.datetime.now().timestamp())}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"📁 已创建备份: {backup_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否需要添加 sender_email 列
        cursor.execute("PRAGMA table_info(recipient_quality)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'sender_email' not in columns:
            print("🔧 添加 sender_email 列到 recipient_quality 表")
            
            # 由于SQLite不支持直接修改主键约束，需要重建表
            # 1. 创建新表
            cursor.execute('''
                CREATE TABLE recipient_quality_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT NOT NULL,
                    sender_email TEXT NOT NULL DEFAULT '<EMAIL>',
                    quality_score REAL DEFAULT 50.0,
                    status TEXT DEFAULT 'unknown',
                    total_sent INTEGER DEFAULT 0,
                    total_replies INTEGER DEFAULT 0,
                    total_bounces INTEGER DEFAULT 0,
                    total_opens INTEGER DEFAULT 0,
                    total_clicks INTEGER DEFAULT 0,
                    first_contact_date TEXT,
                    last_reply_time TEXT,
                    last_sent_time TEXT,
                    last_bounce_time TEXT,
                    response_rate REAL DEFAULT 0.0,
                    bounce_rate REAL DEFAULT 0.0,
                    engagement_score REAL DEFAULT 0.0,
                    domain TEXT,
                    tags TEXT,
                    notes TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(email, sender_email)
                )
            ''')
            
            # 2. 复制数据
            cursor.execute('''
                INSERT INTO recipient_quality_new 
                (id, email, sender_email, quality_score, status, total_sent, total_replies,
                 total_bounces, total_opens, total_clicks, first_contact_date, last_reply_time,
                 last_sent_time, last_bounce_time, response_rate, bounce_rate, engagement_score,
                 domain, tags, notes, created_at, updated_at)
                SELECT 
                    id, email, '<EMAIL>', quality_score, status, total_sent, total_replies,
                    total_bounces, total_opens, total_clicks, first_contact_date, last_reply_time,
                    last_sent_time, last_bounce_time, response_rate, bounce_rate, engagement_score,
                    domain, tags, notes, created_at, updated_at
                FROM recipient_quality
            ''')
            
            # 3. 删除旧表
            cursor.execute('DROP TABLE recipient_quality')
            
            # 4. 重命名新表
            cursor.execute('ALTER TABLE recipient_quality_new RENAME TO recipient_quality')
            
            print("✅ 成功添加 sender_email 列")
        else:
            print("✅ sender_email 列已存在，无需迁移")
        
        # 重建索引
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_recipient_email ON recipient_quality(email)',
            'CREATE INDEX IF NOT EXISTS idx_sender_email ON recipient_quality(sender_email)',
            'CREATE INDEX IF NOT EXISTS idx_quality_score ON recipient_quality(quality_score)',
            'CREATE INDEX IF NOT EXISTS idx_status ON recipient_quality(status)',
            'CREATE INDEX IF NOT EXISTS idx_email_sender ON recipient_quality(email, sender_email)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        conn.close()
        
        print(f"✅ 数据库 {db_path} 迁移完成")
        return True
        
    except Exception as e:
        print(f"❌ 迁移数据库失败: {str(e)}")
        return False

def migrate_qq_anti_spam_database():
    """迁移QQ反垃圾邮件数据库"""
    db_path = "qq_anti_spam.db"
    
    if not os.path.exists(db_path):
        print(f"✅ 数据库 {db_path} 不存在，无需迁移")
        return True
    
    try:
        print(f"🔄 开始迁移数据库: {db_path}")
        
        # 备份原数据库
        backup_path = f"{db_path}.backup_{int(datetime.datetime.now().timestamp())}"
        import shutil
        shutil.copy2(db_path, backup_path)
        print(f"📁 已创建备份: {backup_path}")
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查是否存在 qq_sending_records 表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='qq_sending_records'")
        if not cursor.fetchone():
            print("🔧 创建 qq_sending_records 表")
            cursor.execute('''
                CREATE TABLE qq_sending_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_email TEXT NOT NULL,
                    recipient_email TEXT NOT NULL,
                    subject TEXT,
                    send_time TEXT NOT NULL,
                    has_auto_reply INTEGER DEFAULT 0,
                    reply_time TEXT,
                    reply_content TEXT,
                    batch_id TEXT,
                    sequence_number INTEGER,
                    is_emergency_send INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            print("✅ 成功创建 qq_sending_records 表")
        else:
            print("✅ qq_sending_records 表已存在")
        
        conn.commit()
        conn.close()
        
        print(f"✅ 数据库 {db_path} 迁移完成")
        return True
        
    except Exception as e:
        print(f"❌ 迁移数据库失败: {str(e)}")
        return False

def test_migration():
    """测试迁移结果"""
    print("\n🧪 测试迁移结果...")
    
    try:
        # 测试收件人质量数据库
        if os.path.exists("recipient_quality.db"):
            conn = sqlite3.connect("recipient_quality.db")
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("PRAGMA table_info(recipient_quality)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'sender_email' in columns:
                print("✅ recipient_quality 表包含 sender_email 列")
            else:
                print("❌ recipient_quality 表缺少 sender_email 列")
            
            conn.close()
        
        # 测试QQ反垃圾邮件数据库
        if os.path.exists("qq_anti_spam.db"):
            conn = sqlite3.connect("qq_anti_spam.db")
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='qq_sending_records'")
            if cursor.fetchone():
                print("✅ qq_anti_spam 数据库包含 qq_sending_records 表")
            else:
                print("❌ qq_anti_spam 数据库缺少 qq_sending_records 表")
            
            conn.close()
        
        print("✅ 迁移测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 迁移测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始数据库迁移...")
    print("="*60)
    
    success = True
    
    # 迁移收件人质量数据库
    if not migrate_recipient_quality_database():
        success = False
    
    # 迁移QQ反垃圾邮件数据库
    if not migrate_qq_anti_spam_database():
        success = False
    
    # 测试迁移结果
    if not test_migration():
        success = False
    
    print("\n" + "="*60)
    if success:
        print("🎉 数据库迁移完成！")
        print("\n💡 迁移内容：")
        print("• 为 recipient_quality 表添加了 sender_email 列")
        print("• 为 qq_anti_spam 数据库添加了 qq_sending_records 表")
        print("• 重建了相关索引以提高查询性能")
        print("• 创建了数据库备份文件")
        print("\n🔧 现在可以重新运行系统集成测试验证修复效果")
    else:
        print("❌ 数据库迁移失败！")
        print("请检查错误信息并手动修复")

if __name__ == "__main__":
    main()
