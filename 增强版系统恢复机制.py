#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版系统恢复机制 v2.0
确保2.0系统在任何情况下都能稳定运行
包括：系统重启、电脑重启、环境变化、依赖缺失等场景的自动处理
"""

import os
import sys
import json
import time
import shutil
import logging
import subprocess
import threading
from datetime import datetime, timedelta
from pathlib import Path

class EnhancedRecoveryManager:
    """增强版恢复管理器"""
    
    def __init__(self):
        self.setup_logging()
        self.state_file = 'backups/enhanced_system_state.json'
        self.recovery_file = 'backups/enhanced_recovery_info.json'
        self.lock_file = 'temp/enhanced_recovery.lock'
        self.monitoring_active = False
        
    def setup_logging(self):
        """设置日志"""
        os.makedirs('logs', exist_ok=True)
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/enhanced_recovery.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def comprehensive_state_save(self):
        """全面保存系统状态"""
        try:
            print("💾 全面保存系统状态...")
            self.logger.info("开始全面保存系统状态")
            
            # 收集全面的系统状态
            state = {
                'metadata': {
                    'version': '2.0',
                    'timestamp': datetime.now().isoformat(),
                    'save_reason': 'comprehensive_backup'
                },
                'system': {
                    'python_path': sys.executable,
                    'python_version': sys.version,
                    'platform': sys.platform,
                    'working_directory': os.getcwd(),
                    'script_directory': os.path.dirname(os.path.abspath(__file__)),
                    'environment_variables': dict(os.environ),
                    'system_time': datetime.now().isoformat()
                },
                'application': {
                    'installed_packages': self.get_installed_packages(),
                    'running_processes': self.get_python_processes(),
                    'open_files': self.scan_open_files(),
                    'active_connections': self.check_network_connections(),
                    'memory_usage': self.get_memory_info()
                },
                'data': {
                    'user_settings': self.backup_all_user_data(),
                    'database_status': self.comprehensive_db_check(),
                    'config_files': self.backup_config_files(),
                    'log_files': self.backup_recent_logs(),
                    'temp_files': self.catalog_temp_files()
                },
                'security': {
                    'file_permissions': self.check_file_permissions(),
                    'directory_structure': self.verify_directory_structure(),
                    'backup_integrity': self.verify_backup_integrity()
                },
                'performance': {
                    'disk_usage': self.get_disk_usage(),
                    'cpu_usage': self.get_cpu_usage(),
                    'startup_time': self.measure_startup_time(),
                    'response_time': self.measure_response_time()
                }
            }
            
            # 保存状态文件
            os.makedirs('backups', exist_ok=True)
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)
            
            # 创建增量备份
            self.create_incremental_backup()
            
            # 更新恢复信息
            recovery_info = {
                'last_save': datetime.now().isoformat(),
                'recovery_needed': True,
                'auto_recovery_enabled': True,
                'priority_level': 'critical',
                'backup_count': self.count_backups(),
                'next_scheduled_save': (datetime.now() + timedelta(hours=1)).isoformat()
            }
            
            with open(self.recovery_file, 'w', encoding='utf-8') as f:
                json.dump(recovery_info, f, ensure_ascii=False, indent=2)
            
            self.logger.info("全面系统状态保存完成")
            print("✅ 全面系统状态保存完成")
            return True
            
        except Exception as e:
            self.logger.error(f"保存系统状态失败: {str(e)}")
            print(f"❌ 保存系统状态失败: {str(e)}")
            return False
    
    def intelligent_recovery(self):
        """智能恢复系统"""
        try:
            print("🧠 开始智能恢复系统...")
            self.logger.info("开始智能恢复系统")
            
            # 检查恢复条件
            if not self.should_perform_recovery():
                print("ℹ️ 系统状态良好，无需恢复")
                return True
            
            # 防止并发恢复
            if self.is_recovery_locked():
                print("⚠️ 恢复正在进行中，等待完成...")
                self.wait_for_recovery_completion()
                return True
            
            # 创建恢复锁
            self.create_recovery_lock()
            
            try:
                # 加载保存的状态
                state = self.load_enhanced_state()
                if not state:
                    print("❌ 无法加载系统状态，执行基础恢复")
                    return self.basic_recovery()
                
                # 分析恢复需求
                recovery_plan = self.analyze_recovery_needs(state)
                
                # 执行智能恢复
                success = self.execute_recovery_plan(recovery_plan, state)
                
                if success:
                    # 验证恢复结果
                    if self.verify_recovery_success():
                        self.mark_recovery_complete()
                        print("✅ 智能恢复成功完成")
                        return True
                    else:
                        print("⚠️ 恢复验证失败，尝试基础恢复")
                        return self.basic_recovery()
                else:
                    print("❌ 智能恢复失败，尝试基础恢复")
                    return self.basic_recovery()
                
            finally:
                self.remove_recovery_lock()
                
        except Exception as e:
            self.logger.error(f"智能恢复失败: {str(e)}")
            print(f"❌ 智能恢复失败: {str(e)}")
            return self.basic_recovery()
    
    def should_perform_recovery(self):
        """判断是否应该执行恢复"""
        try:
            # 检查恢复文件
            if not os.path.exists(self.recovery_file):
                return False
            
            with open(self.recovery_file, 'r', encoding='utf-8') as f:
                recovery_info = json.load(f)
            
            if not recovery_info.get('recovery_needed', False):
                return False
            
            # 检查系统变化
            if self.detect_system_changes():
                return True
            
            # 检查关键文件
            if self.check_critical_files_missing():
                return True
            
            # 检查依赖包
            if self.check_dependencies_missing():
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检查恢复条件失败: {str(e)}")
            return True  # 出错时倾向于执行恢复
    
    def detect_system_changes(self):
        """检测系统变化"""
        try:
            if not os.path.exists(self.state_file):
                return True
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                saved_state = json.load(f)
            
            # 检查Python路径变化
            if saved_state['system']['python_path'] != sys.executable:
                return True
            
            # 检查工作目录变化
            if saved_state['system']['working_directory'] != os.getcwd():
                return True
            
            # 检查关键环境变量变化
            saved_env = saved_state['system']['environment_variables']
            current_env = dict(os.environ)
            
            key_vars = ['PATH', 'PYTHONPATH', 'USER', 'USERNAME']
            for var in key_vars:
                if saved_env.get(var) != current_env.get(var):
                    return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"检测系统变化失败: {str(e)}")
            return True
    
    def check_critical_files_missing(self):
        """检查关键文件是否缺失"""
        critical_files = [
            'gui_main.py', 'email_sender.py', 'email_history_manager.py',
            'rag_search_engine.py', 'recipient_quality_manager.py'
        ]
        
        for file_path in critical_files:
            if not os.path.exists(file_path):
                return True
        
        return False
    
    def check_dependencies_missing(self):
        """检查依赖包是否缺失"""
        required_packages = ['jieba', 'psutil']
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                return True
        
        return False
    
    def analyze_recovery_needs(self, state):
        """分析恢复需求"""
        recovery_plan = {
            'environment_recovery': False,
            'directory_recovery': False,
            'dependency_recovery': False,
            'database_recovery': False,
            'config_recovery': False,
            'permission_recovery': False,
            'priority': 'normal'
        }
        
        # 分析环境变化
        if self.detect_system_changes():
            recovery_plan['environment_recovery'] = True
            recovery_plan['priority'] = 'high'
        
        # 分析文件缺失
        if self.check_critical_files_missing():
            recovery_plan['directory_recovery'] = True
            recovery_plan['priority'] = 'critical'
        
        # 分析依赖缺失
        if self.check_dependencies_missing():
            recovery_plan['dependency_recovery'] = True
            recovery_plan['priority'] = 'high'
        
        # 分析数据库状态
        if self.need_database_recovery(state):
            recovery_plan['database_recovery'] = True
        
        # 分析配置文件
        if self.need_config_recovery(state):
            recovery_plan['config_recovery'] = True
        
        return recovery_plan
    
    def execute_recovery_plan(self, plan, state):
        """执行恢复计划"""
        try:
            print(f"🔧 执行恢复计划 (优先级: {plan['priority']})...")
            
            success_count = 0
            total_steps = sum(1 for v in plan.values() if isinstance(v, bool) and v)
            
            if plan['environment_recovery']:
                if self.recover_environment(state):
                    success_count += 1
                    print("  ✅ 环境恢复成功")
                else:
                    print("  ❌ 环境恢复失败")
            
            if plan['dependency_recovery']:
                if self.recover_dependencies(state):
                    success_count += 1
                    print("  ✅ 依赖恢复成功")
                else:
                    print("  ❌ 依赖恢复失败")
            
            if plan['database_recovery']:
                if self.recover_databases(state):
                    success_count += 1
                    print("  ✅ 数据库恢复成功")
                else:
                    print("  ❌ 数据库恢复失败")
            
            if plan['config_recovery']:
                if self.recover_configs(state):
                    success_count += 1
                    print("  ✅ 配置恢复成功")
                else:
                    print("  ❌ 配置恢复失败")
            
            if plan['directory_recovery']:
                if self.recover_directories(state):
                    success_count += 1
                    print("  ✅ 目录恢复成功")
                else:
                    print("  ❌ 目录恢复失败")
            
            # 计算成功率
            success_rate = success_count / total_steps if total_steps > 0 else 1.0
            
            return success_rate >= 0.7  # 70%成功率认为恢复成功
            
        except Exception as e:
            self.logger.error(f"执行恢复计划失败: {str(e)}")
            return False
