#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试深度协调系统
"""

import tkinter as tk
import time
import datetime

def test_deep_coordination():
    """测试深度协调系统"""
    print("🔧 测试深度协调系统")
    print("=" * 60)
    
    try:
        # 测试导入深度协调系统
        from 深度系统协调实现 import get_coordinator, SystemEvent, RiskLevel
        print("✅ 深度协调系统导入成功")
        
        # 获取协调器实例
        coordinator = get_coordinator()
        print("✅ 协调器实例获取成功")
        
        # 测试系统状态
        sender_email = "<EMAIL>"
        status = coordinator.get_system_status(sender_email)
        print(f"✅ 系统状态获取成功: {status['coordination_status']}")
        
        # 模拟邮件发送事件
        print("\n📧 模拟邮件发送事件")
        coordinator.data_center.emit_event(SystemEvent.EMAIL_SENT, {
            'sender_email': sender_email,
            'recipient_count': 5,
            'recipients': ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'],
            'send_time': datetime.datetime.now().isoformat()
        })
        print("✅ 邮件发送事件触发成功")
        
        # 模拟回复接收事件
        print("\n✅ 模拟回复接收事件")
        coordinator.data_center.emit_event(SystemEvent.REPLY_RECEIVED, {
            'sender_email': sender_email,
            'recipient_email': '<EMAIL>'
        })
        print("✅ 回复接收事件触发成功")
        
        # 模拟无回复检测事件
        print("\n❌ 模拟无回复检测事件")
        for i in range(2, 6):  # test2 到 test5 都无回复
            coordinator.data_center.emit_event(SystemEvent.NO_REPLY_DETECTED, {
                'sender_email': sender_email,
                'recipient_email': f'test{i}@example.com'
            })
        print("✅ 无回复检测事件触发成功")
        
        # 获取最终状态
        final_status = coordinator.get_system_status(sender_email)
        print(f"\n📊 最终系统状态:")
        print(f"  发送总数: {final_status['state']['total_sent']}")
        print(f"  回复总数: {final_status['state']['total_replies']}")
        print(f"  连续无回复: {final_status['state']['consecutive_no_reply']}")
        print(f"  风险等级: {final_status['state']['risk_level']}")
        print(f"  应急状态: {final_status['state']['emergency_active']}")
        
        # 显示推荐策略
        strategy = final_status['strategy']
        print(f"\n💡 推荐策略:")
        print(f"  发送模式: {strategy['recommended_mode']}")
        print(f"  最大批次: {strategy['max_batch_size']}")
        print(f"  发送间隔: {strategy['send_interval']} 秒")
        print(f"  应急阈值: {strategy['emergency_threshold']}")
        
        print(f"\n📋 系统建议:")
        for i, recommendation in enumerate(strategy['recommendations'], 1):
            print(f"  {i}. {recommendation}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 深度协调系统导入失败: {str(e)}")
        print("💡 请确保 深度系统协调实现.py 文件存在")
        return False
    except Exception as e:
        print(f"❌ 深度协调系统测试失败: {str(e)}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🔧 测试GUI集成")
    print("=" * 60)
    
    try:
        # 创建测试GUI
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        # 导入GUI
        from gui_main import EmailSenderGUI
        app = EmailSenderGUI(root)
        
        # 检查深度协调系统是否已集成
        if hasattr(app, 'coordinator') and app.coordinator:
            print("✅ GUI已成功集成深度协调系统")
            
            # 测试事件监听器
            if hasattr(app, '_setup_coordination_listeners'):
                print("✅ 事件监听器设置方法存在")
            else:
                print("❌ 事件监听器设置方法缺失")
            
            # 测试事件处理方法
            event_handlers = [
                '_on_emergency_activated',
                '_on_emergency_deactivated', 
                '_on_risk_level_changed'
            ]
            
            for handler in event_handlers:
                if hasattr(app, handler):
                    print(f"✅ 事件处理方法存在: {handler}")
                else:
                    print(f"❌ 事件处理方法缺失: {handler}")
            
            return True
        else:
            print("❌ GUI未集成深度协调系统")
            return False
            
    except Exception as e:
        print(f"❌ GUI集成测试失败: {str(e)}")
        return False
    finally:
        try:
            root.destroy()
        except:
            pass

def test_coordination_features():
    """测试协调功能"""
    print("\n🔧 测试协调功能")
    print("=" * 60)
    
    try:
        from 深度系统协调实现 import get_coordinator, SystemEvent
        coordinator = get_coordinator()
        
        sender_email = "<EMAIL>"
        
        # 测试风险等级计算
        print("📊 测试风险等级计算")
        risk_level = coordinator.decision_engine.calculate_risk_level(sender_email)
        print(f"✅ 风险等级计算成功: {risk_level.value}")
        
        # 测试策略推荐
        print("\n💡 测试策略推荐")
        strategy = coordinator.decision_engine.recommend_sending_strategy(sender_email)
        print(f"✅ 策略推荐成功: {strategy['recommended_mode']}")
        
        # 测试数据中心
        print("\n📊 测试数据中心")
        state = coordinator.data_center.get_state(sender_email)
        print(f"✅ 数据中心状态获取成功: {state.sender_email}")
        
        # 测试状态更新
        print("\n🔄 测试状态更新")
        coordinator.data_center.update_state(sender_email, total_sent=10, total_replies=3)
        updated_state = coordinator.data_center.get_state(sender_email)
        print(f"✅ 状态更新成功: 发送{updated_state.total_sent}, 回复{updated_state.total_replies}")
        
        return True
        
    except Exception as e:
        print(f"❌ 协调功能测试失败: {str(e)}")
        return False

def test_system_coordination_points():
    """测试系统协调配合点"""
    print("\n🔧 测试系统协调配合点")
    print("=" * 60)
    
    coordination_points = [
        "发送 → 监控 → 质量数据库",
        "质量数据库 → 反垃圾邮件 → QQ应急",
        "历史记录 → 智能检索 → 重复检测",
        "风险等级 → 发送模式 → 队列策略",
        "回复监控 → 应急系统 → 恢复策略",
        "质量评分 → 批次管理 → 发送优化"
    ]
    
    print("📋 系统协调配合点:")
    for i, point in enumerate(coordination_points, 1):
        print(f"  {i}. {point}")
    
    print("\n✅ 所有协调配合点已识别")
    return True

def generate_coordination_report():
    """生成协调报告"""
    print("\n📊 生成协调报告")
    print("=" * 60)
    
    try:
        from 深度系统协调实现 import get_coordinator
        coordinator = get_coordinator()
        
        sender_email = "<EMAIL>"
        
        # 生成完整报告
        report = coordinator.get_system_status(sender_email)
        
        print("📋 深度协调系统报告")
        print("-" * 40)
        print(f"发件人: {report['sender_email']}")
        print(f"协调状态: {report['coordination_status']}")
        
        state = report['state']
        print(f"\n📊 系统状态:")
        print(f"  总发送数: {state['total_sent']}")
        print(f"  总回复数: {state['total_replies']}")
        print(f"  连续无回复: {state['consecutive_no_reply']}")
        print(f"  风险等级: {state['risk_level']}")
        print(f"  应急状态: {'激活' if state['emergency_active'] else '正常'}")
        
        strategy = report['strategy']
        print(f"\n💡 推荐策略:")
        print(f"  发送模式: {strategy['recommended_mode']}")
        print(f"  最大批次: {strategy['max_batch_size']}")
        print(f"  发送间隔: {strategy['send_interval']} 秒")
        print(f"  监控启用: {'是' if strategy['enable_monitoring'] else '否'}")
        
        print(f"\n📋 系统建议:")
        for recommendation in strategy['recommendations']:
            print(f"  • {recommendation}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成协调报告失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 深度协调系统完整测试")
    print("=" * 80)
    
    tests = [
        ("深度协调系统基础功能", test_deep_coordination),
        ("GUI集成测试", test_gui_integration),
        ("协调功能测试", test_coordination_features),
        ("系统协调配合点", test_system_coordination_points),
        ("协调报告生成", generate_coordination_report)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 执行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name} - 异常: {str(e)}")
    
    print(f"\n📊 测试结果")
    print("=" * 40)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed >= total * 0.8:  # 80%以上通过
        print("\n🎉 深度协调系统测试通过！")
        print("✅ 系统功能深度协调配合")
        print("✅ 各模块相互协调运行")
        print("✅ 智能决策引擎正常")
        print("✅ 事件驱动架构工作")
        
        print("\n🚀 现在您的邮件系统具备:")
        print("• 🔍 智能风险评估")
        print("• 🤖 自动策略调整")
        print("• 📊 多维度数据分析")
        print("• 🔄 事件驱动协调")
        print("• 💡 智能建议系统")
        print("• 🆘 自动应急响应")
        
        print("\n💡 系统协调效果:")
        print("• 发送邮件 → 自动启动监控 → 更新质量数据库")
        print("• 风险升级 → 自动调整策略 → 激活保护机制")
        print("• 收到回复 → 更新状态 → 检查恢复条件")
        print("• 质量评分 → 优化批次 → 提升发送效果")
        
    else:
        print(f"\n⚠️ 部分功能需要优化")
        print("💡 建议检查深度协调系统配置")

if __name__ == "__main__":
    main()
