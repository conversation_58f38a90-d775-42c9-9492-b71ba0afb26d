#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查邮件程序中保存的密码配置
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import json

def check_saved_passwords():
    """检查程序中保存的密码"""
    print("🔍 检查邮件程序中保存的密码配置")
    print("=" * 60)
    
    # 检查可能的密码存储位置
    password_files = [
        "auth_codes.json",
        "email_config.json", 
        "config.json",
        "passwords.txt",
        "auth_codes.txt"
    ]
    
    found_configs = []
    
    for file_name in password_files:
        if os.path.exists(file_name):
            print(f"📁 找到配置文件: {file_name}")
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                    print(f"📄 文件内容预览:")
                    print("-" * 30)
                    print(content[:200] + "..." if len(content) > 200 else content)
                    print("-" * 30)
                    found_configs.append((file_name, content))
            except Exception as e:
                print(f"❌ 读取文件失败: {str(e)}")
            print()
    
    if not found_configs:
        print("📭 未找到保存的密码配置文件")
        print("💡 这意味着您需要在程序中重新输入密码")
    
    return found_configs

def create_password_update_gui():
    """创建密码更新界面"""
    root = tk.Tk()
    root.title("🔑 更新邮箱密码配置")
    root.geometry("600x400")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="🔑 更新邮箱密码配置", 
                           font=('Microsoft YaHei UI', 16, 'bold'))
    title_label.pack(pady=(0, 20))
    
    # 当前配置显示
    current_frame = ttk.LabelFrame(main_frame, text="📋 当前配置", padding="10")
    current_frame.pack(fill=tk.X, pady=(0, 20))
    
    configs = check_saved_passwords()
    
    if configs:
        for file_name, content in configs:
            ttk.Label(current_frame, text=f"文件: {file_name}").pack(anchor=tk.W)
            
            # 显示内容的前100个字符
            preview = content[:100] + "..." if len(content) > 100 else content
            ttk.Label(current_frame, text=f"内容: {preview}", 
                     font=('Consolas', 9)).pack(anchor=tk.W, pady=(0, 10))
    else:
        ttk.Label(current_frame, text="未找到保存的密码配置").pack(anchor=tk.W)
    
    # 正确配置信息
    correct_frame = ttk.LabelFrame(main_frame, text="✅ 正确配置", padding="10")
    correct_frame.pack(fill=tk.X, pady=(0, 20))
    
    correct_info = """📧 邮箱地址: <EMAIL>
🔑 授权码: cwnzcpaczwngdgfa
📤 SMTP服务器: smtp.qq.com:587
📬 IMAP服务器: imap.qq.com:993

⚠️ 重要提醒:
• 使用授权码 cwnzcpaczwngdgfa，不是QQ密码
• 确保在邮件程序中更新这个授权码
• 授权码已通过测试，完全正常工作"""
    
    ttk.Label(correct_frame, text=correct_info, font=('Microsoft YaHei UI', 10)).pack(anchor=tk.W)
    
    # 操作按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(20, 0))
    
    def open_main_program():
        """打开主程序"""
        try:
            import subprocess
            subprocess.Popen(['python', 'gui_main.py'])
            messagebox.showinfo("提示", "主程序已启动，请在其中更新密码配置")
        except Exception as e:
            messagebox.showerror("错误", f"启动主程序失败: {str(e)}")
    
    def copy_auth_code():
        """复制授权码到剪贴板"""
        try:
            root.clipboard_clear()
            root.clipboard_append("cwnzcpaczwngdgfa")
            messagebox.showinfo("复制成功", "授权码已复制到剪贴板")
        except Exception as e:
            messagebox.showerror("复制失败", f"复制失败: {str(e)}")
    
    ttk.Button(button_frame, text="📧 打开主程序", command=open_main_program).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="📋 复制授权码", command=copy_auth_code).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side=tk.RIGHT, padx=5)
    
    # 使用说明
    help_frame = ttk.LabelFrame(main_frame, text="💡 使用说明", padding="10")
    help_frame.pack(fill=tk.X, pady=(20, 0))
    
    help_text = """1. 点击'打开主程序'启动邮件发送程序
2. 在发件人邮箱处填写: <EMAIL>  
3. 在密码处填写授权码: cwnzcpaczwngdgfa
4. 确保勾选'保存密码'选项
5. 重新尝试发送邮件"""
    
    ttk.Label(help_frame, text=help_text, font=('Microsoft YaHei UI', 9)).pack(anchor=tk.W)
    
    root.mainloop()

def main():
    """主函数"""
    print("🔑 邮箱密码配置检查工具")
    print("=" * 60)
    
    # 检查保存的密码
    configs = check_saved_passwords()
    
    print("\n💡 解决方案:")
    print("-" * 30)
    print("1. 您的授权码已验证正常工作")
    print("2. 问题可能是程序中使用了错误的密码")
    print("3. 请在邮件程序中更新为正确的授权码")
    print()
    print("✅ 正确配置:")
    print(f"   邮箱: <EMAIL>")
    print(f"   授权码: cwnzcpaczwngdgfa")
    print()
    
    # 启动GUI
    try:
        create_password_update_gui()
    except Exception as e:
        print(f"❌ 启动GUI失败: {str(e)}")
        print("💡 请手动在邮件程序中更新密码配置")

if __name__ == "__main__":
    main()
