' Email Sender Assistant - VBS Launcher
Option Explicit

Dim objShell, objFSO, currentDir
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' Main menu loop
Do
    Dim choice
    choice = ShowMenu()
    
    Select Case choice
        Case "1"
            RunPythonScript "main.py", "Send Single Email"
        Case "2"
            RunPythonScript "main.py --batch", "Send Batch Emails"
        Case "3"
            RunPythonScript "gui_main.py", "GUI Version"
        Case "4"
            RunPythonScript "test_email.py", "Test Email Function"
        Case "5"
            RunPythonScript "setup.py", "Environment Check"
        Case "6"
            OpenFile "README.md"
        Case "7"
            MsgBox "Thank you for using Email Sender Assistant!", vbInformation, "Goodbye"
            Exit Do
        Case Else
            If choice <> "" Then
                MsgBox "Invalid option, please try again!", vbExclamation, "Error"
            Else
                Exit Do ' User clicked Cancel
            End If
    End Select
Loop

' Show main menu
Function ShowMenu()
    Dim menu
    menu = "Email Sender Assistant" & vbCrLf & vbCrLf & _
           "Please select function:" & vbCrLf & vbCrLf & _
           "1. Send single email (CLI)" & vbCrLf & _
           "2. Send batch emails (CLI)" & vbCrLf & _
           "3. GUI version (Recommended)" & vbCrLf & _
           "4. Test email function" & vbCrLf & _
           "5. Environment check" & vbCrLf & _
           "6. View documentation" & vbCrLf & _
           "7. Exit program" & vbCrLf & vbCrLf & _
           "Please enter option (1-7):"
    
    ShowMenu = InputBox(menu, "Email Sender Assistant", "3")
End Function

' Run Python script
Sub RunPythonScript(scriptName, description)
    On Error Resume Next
    
    ' Check if Python is available
    Dim pythonCheck
    pythonCheck = objShell.Run("python --version", 0, True)
    
    If pythonCheck <> 0 Then
        MsgBox "Error: Python environment not found!" & vbCrLf & vbCrLf & _
               "Please ensure Python 3.6+ is installed.", vbCritical, "Python Environment Error"
        Exit Sub
    End If
    
    ' Check if script file exists
    Dim scriptPath
    scriptPath = currentDir & "\" & Split(scriptName, " ")(0)
    
    If Not objFSO.FileExists(scriptPath) Then
        MsgBox "Error: Script file not found!" & vbCrLf & vbCrLf & _
               "File path: " & scriptPath, vbCritical, "File Not Found"
        Exit Sub
    End If
    
    ' Show startup information
    MsgBox "Starting: " & description & vbCrLf & vbCrLf & _
           "Command: python " & scriptName & vbCrLf & vbCrLf & _
           "Please operate in the opened command window.", vbInformation, "Starting"
    
    ' Run Python script
    Dim command
    command = "cmd /k ""cd /d """ & currentDir & """ && python " & scriptName & """"
    objShell.Run command, 1, False
    
    If Err.Number <> 0 Then
        MsgBox "Startup failed!" & vbCrLf & vbCrLf & _
               "Error message: " & Err.Description & vbCrLf & _
               "Error code: " & Err.Number, vbCritical, "Startup Error"
        Err.Clear
    End If
End Sub

' Open file
Sub OpenFile(fileName)
    On Error Resume Next
    
    Dim filePath
    filePath = currentDir & "\" & fileName
    
    If objFSO.FileExists(filePath) Then
        objShell.Run """" & filePath & """", 1, False
        If Err.Number <> 0 Then
            MsgBox "Cannot open file: " & fileName & vbCrLf & vbCrLf & _
                   "Please open the file manually.", vbExclamation, "Open File Failed"
            Err.Clear
        End If
    Else
        MsgBox "File not found: " & fileName & vbCrLf & vbCrLf & _
               "Full path: " & filePath, vbExclamation, "File Not Found"
    End If
End Sub
