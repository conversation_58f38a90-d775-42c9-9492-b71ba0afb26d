#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
发送模式调试器 - 检查发送模式是否真的被正确获取和应用
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import random
import threading
import time

class SendModeDebugger:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔍 发送模式调试器")
        self.root.geometry("800x600")
        
        # 发送模式变量 - 完全模拟主程序
        self.send_mode = tk.StringVar(value="standard")
        
        self.create_widgets()
        
    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        ttk.Label(main_frame, text="🔍 发送模式调试器", 
                 font=('Arial', 16, 'bold')).grid(row=0, column=0, pady=20)
        
        # 发送模式选择 - 完全模拟主程序的界面
        mode_frame = ttk.LabelFrame(main_frame, text="发送模式选择", padding="10")
        mode_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Label(mode_frame, text="发送模式:", font=('Arial', 10)).pack(anchor=tk.W, pady=5)
        
        radio_frame = ttk.Frame(mode_frame)
        radio_frame.pack(fill=tk.X, pady=5)
        
        # 完全模拟主程序的单选按钮
        ttk.Radiobutton(radio_frame, text="标准发送（1-2分钟间隔）",
                       variable=self.send_mode, value="standard").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(radio_frame, text="快速发送（30-60秒间隔）",
                       variable=self.send_mode, value="fast").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(radio_frame, text="安全发送（3-5分钟间隔）",
                       variable=self.send_mode, value="safe").pack(side=tk.LEFT, padx=5)
        
        # 测试按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=3, column=0, pady=20)
        
        ttk.Button(btn_frame, text="🧪 测试发送模式获取", 
                  command=self.test_mode_get).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="📧 模拟主系统发送", 
                  command=self.simulate_main_send).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="📬 模拟队列发送", 
                  command=self.simulate_queue_send).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="🔄 清空日志", 
                  command=self.clear_log).pack(side=tk.LEFT, padx=10)
        
        # 结果显示区域
        self.result_text = scrolledtext.ScrolledText(main_frame, width=90, height=25, 
                                                    font=('Consolas', 10))
        self.result_text.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        # 初始化日志
        self.log("🔍 发送模式调试器启动")
        self.log("=" * 60)
        self.log("此工具用于检查发送模式是否被正确获取和应用")
        self.log("请选择不同的发送模式并点击测试按钮")
        self.log("=" * 60)
        
    def log(self, message):
        """添加日志"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.result_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.result_text.see(tk.END)
        self.root.update()
        
    def clear_log(self):
        """清空日志"""
        self.result_text.delete(1.0, tk.END)
        self.log("🔄 日志已清空")
        
    def test_mode_get(self):
        """测试发送模式获取"""
        self.log("")
        self.log("🧪 测试发送模式获取")
        self.log("-" * 40)
        
        # 获取当前选择的发送模式
        current_mode = self.send_mode.get()
        self.log(f"📋 当前选择的发送模式: '{current_mode}'")
        
        # 测试延迟配置获取
        delay_ranges = {
            "fast": (30, 60),      # 快速发送：30-60秒
            "standard": (60, 120), # 标准发送：1-2分钟
            "safe": (180, 300)     # 安全发送：3-5分钟
        }
        
        # 模拟主程序的获取逻辑
        delay_range = delay_ranges.get(current_mode, (60, 120))
        self.log(f"📊 获取到的延迟范围: {delay_range}")
        
        # 生成随机延迟
        delay = random.uniform(delay_range[0], delay_range[1])
        minutes = delay // 60
        seconds = delay % 60
        self.log(f"⏱️ 生成的随机延迟: {delay:.1f}秒 ({minutes:.0f}分{seconds:.1f}秒)")
        
        # 模式名称获取
        mode_names = {
            "fast": "快速发送",
            "standard": "标准发送", 
            "safe": "安全发送"
        }
        mode_name = mode_names.get(current_mode, "标准发送")
        self.log(f"📝 模式名称: {mode_name}")
        
        # 检查是否正确
        if current_mode in delay_ranges:
            self.log("✅ 发送模式获取正常")
        else:
            self.log("❌ 发送模式获取异常！使用了默认值")
            
        self.log("")
        
    def simulate_main_send(self):
        """模拟主系统发送"""
        self.log("")
        self.log("📧 模拟主系统发送流程")
        self.log("-" * 40)
        
        def send_thread():
            try:
                # 完全模拟主程序的发送逻辑
                current_mode = self.send_mode.get()
                self.root.after(0, lambda: self.log(f"🔍 获取发送模式: '{current_mode}'"))
                
                # 延迟配置（与主程序完全一致）
                delay_ranges = {
                    "fast": (30, 60),      # 快速发送：30-60秒随机间隔
                    "standard": (60, 120), # 标准发送：1-2分钟随机间隔
                    "safe": (180, 300)     # 安全发送：3-5分钟随机间隔
                }
                
                # 获取延迟范围（与主程序完全一致）
                delay_range = delay_ranges.get(current_mode, (60, 120))
                self.root.after(0, lambda: self.log(f"📊 延迟范围: {delay_range}"))
                
                # 生成初始延迟
                delay = random.uniform(delay_range[0], delay_range[1])
                
                # 模式名称
                mode_names = {
                    "fast": "快速发送",
                    "standard": "标准发送",
                    "safe": "安全发送"
                }
                mode_name = mode_names.get(current_mode, "标准发送")
                
                self.root.after(0, lambda: self.log(f"🚀 开始{mode_name}模式（{delay:.1f}秒间隔）"))
                
                # 模拟发送3封邮件
                emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
                
                for i, email in enumerate(emails, 1):
                    self.root.after(0, lambda i=i, email=email: 
                                   self.log(f"📤 正在发送第 {i}/3 封邮件给: {email}"))
                    
                    # 模拟发送时间
                    time.sleep(0.5)
                    
                    self.root.after(0, lambda email=email: 
                                   self.log(f"✅ 发送成功: {email}"))
                    
                    # 添加延迟（除了最后一封）
                    if i < len(emails):
                        # 重新生成随机延迟（与主程序一致）
                        next_delay = random.uniform(delay_range[0], delay_range[1])
                        minutes = next_delay // 60
                        seconds = next_delay % 60
                        
                        self.root.after(0, lambda d=next_delay, m=minutes, s=seconds: 
                                       self.log(f"⏱️ 等待 {d:.1f}秒 ({m:.0f}分{s:.1f}秒) 后发送下一封..."))
                        
                        # 缩短延迟用于测试（实际是完整延迟）
                        test_delay = min(next_delay / 30, 3)  # 最多等3秒用于测试
                        time.sleep(test_delay)
                
                self.root.after(0, lambda: self.log("🎉 主系统发送模拟完成"))
                self.root.after(0, lambda: self.log("注意：实际延迟已大幅缩短用于快速测试"))
                
            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 模拟发送出错: {str(e)}"))
        
        # 在新线程中执行
        threading.Thread(target=send_thread, daemon=True).start()
        
    def simulate_queue_send(self):
        """模拟队列发送"""
        self.log("")
        self.log("📬 模拟队列发送流程")
        self.log("-" * 40)
        
        def queue_thread():
            try:
                # 模拟队列任务
                current_mode = self.send_mode.get()
                task = {
                    'id': 1,
                    'subject': '测试邮件',
                    'send_mode': current_mode,  # 队列任务保存的发送模式
                    'recipient_emails': '<EMAIL>\<EMAIL>'
                }
                
                self.root.after(0, lambda: self.log(f"📋 队列任务发送模式: '{task['send_mode']}'"))
                
                # 队列系统的延迟配置（与主程序一致）
                delay_ranges = {
                    "fast": (30, 60),
                    "standard": (60, 120),
                    "safe": (180, 300)
                }
                
                # 获取延迟范围（与队列系统一致）
                delay_range = delay_ranges.get(task['send_mode'], (60, 120))
                self.root.after(0, lambda: self.log(f"📊 队列延迟范围: {delay_range}"))
                
                # 模拟发送邮件
                emails = task['recipient_emails'].split('\n')
                
                for i, email in enumerate(emails, 1):
                    self.root.after(0, lambda i=i, email=email, task_id=task['id']: 
                                   self.log(f"📤 任务 #{task_id} 发送第 {i}/{len(emails)} 封邮件给: {email}"))
                    
                    # 模拟发送
                    time.sleep(0.3)
                    
                    self.root.after(0, lambda email=email: 
                                   self.log(f"✅ 发送成功: {email}"))
                    
                    # 添加延迟
                    if i < len(emails):
                        next_delay = random.uniform(delay_range[0], delay_range[1])
                        minutes = next_delay // 60
                        seconds = next_delay % 60
                        
                        self.root.after(0, lambda d=next_delay, m=minutes, s=seconds: 
                                       self.log(f"⏱️ 等待 {d:.1f}秒 ({m:.0f}分{s:.1f}秒) 后发送下一封..."))
                        
                        # 缩短延迟用于测试
                        test_delay = min(next_delay / 30, 2)
                        time.sleep(test_delay)
                
                self.root.after(0, lambda: self.log("🎉 队列发送模拟完成"))
                
            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 队列模拟出错: {str(e)}"))
        
        # 在新线程中执行
        threading.Thread(target=queue_thread, daemon=True).start()
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    print("🔍 发送模式调试器")
    print("=" * 50)
    print("正在启动调试工具...")
    print("请在界面中选择不同的发送模式并测试")
    print("=" * 50)
    
    app = SendModeDebugger()
    app.run()
