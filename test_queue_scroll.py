#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试队列滚动功能
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import datetime

class QueueScrollTest:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("队列滚动功能测试")
        self.root.geometry("600x500")
        
        # 模拟队列数据
        self.email_queue = []
        
        self.create_widgets()
        self.add_test_data()
        
    def create_widgets(self):
        """创建测试界面"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(main_frame, text="队列滚动功能测试", 
                 font=('Microsoft YaHei UI', 14, 'bold')).pack(pady=(0, 10))
        
        # 队列统计信息
        stats_frame = ttk.Frame(main_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 5))
        
        self.queue_total_label = ttk.Label(stats_frame, text="总数: 0", font=('Microsoft YaHei UI', 8))
        self.queue_total_label.pack(side=tk.LEFT, padx=(0, 5))
        
        self.queue_sent_label = ttk.Label(stats_frame, text="已发: 0", font=('Microsoft YaHei UI', 8))
        self.queue_sent_label.pack(side=tk.LEFT, padx=(0, 5))
        
        self.queue_remaining_label = ttk.Label(stats_frame, text="剩余: 0", font=('Microsoft YaHei UI', 8))
        self.queue_remaining_label.pack(side=tk.LEFT, padx=(0, 5))
        
        # 队列列表显示区域
        queue_list_frame = ttk.LabelFrame(main_frame, text="📋 队列任务列表", padding="3")
        queue_list_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 5))
        
        # 创建滚动文本框显示队列任务
        self.queue_list_text = scrolledtext.ScrolledText(
            queue_list_frame, 
            width=50, 
            height=15,
            font=('Consolas', 9), 
            wrap=tk.WORD,
            relief='solid', 
            borderwidth=1,
            bg='#1e293b', 
            fg='#e2e8f0',
            insertbackground='#3b82f6',
            selectbackground='#374151',
            state=tk.DISABLED
        )
        self.queue_list_text.pack(fill=tk.BOTH, expand=True)
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="添加测试任务", command=self.add_test_task).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="更新状态", command=self.update_task_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="清空队列", command=self.clear_queue).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="刷新显示", command=self.update_queue_list_display).pack(side=tk.LEFT, padx=5)
        
    def add_test_data(self):
        """添加测试数据"""
        test_tasks = [
            {
                'id': 1,
                'subject': '测试邮件1 - 重要通知',
                'recipient_emails': '<EMAIL>;<EMAIL>',
                'send_mode': 'standard',
                'status': 'pending',
                'created_time': datetime.datetime.now()
            },
            {
                'id': 2,
                'subject': '测试邮件2 - 系统更新',
                'recipient_emails': '<EMAIL>,<EMAIL>,<EMAIL>',
                'send_mode': 'fast',
                'status': 'completed',
                'created_time': datetime.datetime.now()
            },
            {
                'id': 3,
                'subject': '测试邮件3 - 紧急维护通知',
                'recipient_emails': '<EMAIL>',
                'send_mode': 'safe',
                'status': 'failed',
                'created_time': datetime.datetime.now()
            }
        ]
        
        self.email_queue.extend(test_tasks)
        self.update_queue_list_display()
        
    def add_test_task(self):
        """添加新的测试任务"""
        import random
        
        task_id = len(self.email_queue) + 1
        subjects = ['新邮件任务', '重要通知', '系统消息', '用户反馈', '数据报告']
        modes = ['fast', 'standard', 'safe']
        statuses = ['pending', 'sending', 'completed', 'failed']
        
        new_task = {
            'id': task_id,
            'subject': f'{random.choice(subjects)} #{task_id}',
            'recipient_emails': f'test{task_id}@example.com;user{task_id}@test.com',
            'send_mode': random.choice(modes),
            'status': random.choice(statuses),
            'created_time': datetime.datetime.now()
        }
        
        self.email_queue.append(new_task)
        self.update_queue_list_display()
        
    def update_task_status(self):
        """随机更新任务状态"""
        if not self.email_queue:
            return
            
        import random
        statuses = ['pending', 'sending', 'completed', 'failed']
        
        for task in self.email_queue:
            if random.random() < 0.3:  # 30%概率更新状态
                task['status'] = random.choice(statuses)
                
        self.update_queue_list_display()
        
    def clear_queue(self):
        """清空队列"""
        self.email_queue.clear()
        self.update_queue_list_display()
        
    def update_queue_list_display(self):
        """更新队列列表显示 - 像操作日志一样的滚动显示"""
        # 清空现有内容
        self.queue_list_text.config(state=tk.NORMAL)
        self.queue_list_text.delete(1.0, tk.END)
        
        if not self.email_queue:
            self.queue_list_text.insert(tk.END, "📭 队列为空，暂无任务\n")
            self.queue_list_text.insert(tk.END, "💡 提示：点击'添加测试任务'按钮添加邮件发送任务\n")
        else:
            # 添加队列标题
            self.queue_list_text.insert(tk.END, f"📋 队列任务列表 (共 {len(self.email_queue)} 个任务)\n")
            self.queue_list_text.insert(tk.END, "=" * 60 + "\n")
            
            # 显示每个任务
            for i, task in enumerate(self.email_queue, 1):
                # 状态图标
                status_icons = {
                    'pending': '⏳',
                    'sending': '📤', 
                    'completed': '✅',
                    'failed': '❌'
                }
                status_icon = status_icons.get(task['status'], '❓')
                
                # 计算收件人数量
                recipient_count = len([email.strip() for email in
                                      task['recipient_emails'].replace(',', '\n').replace(';', '\n').split('\n')
                                      if email.strip()])
                
                # 格式化主题（限制长度）
                subject = task['subject'][:30] + '...' if len(task['subject']) > 30 else task['subject']
                
                # 发送模式
                mode_text = {
                    'fast': '快速',
                    'standard': '标准', 
                    'safe': '安全'
                }.get(task['send_mode'], task['send_mode'])
                
                # 创建时间
                created_time = task['created_time'].strftime("%H:%M:%S") if hasattr(task['created_time'], 'strftime') else "未知"
                
                # 插入任务信息
                task_line = f"{status_icon} #{i:02d} | {subject:<32} | {recipient_count:2d}人 | {mode_text:4s} | {created_time}\n"
                self.queue_list_text.insert(tk.END, task_line)
            
            # 添加统计信息
            pending_count = len([task for task in self.email_queue if task['status'] == 'pending'])
            completed_count = len([task for task in self.email_queue if task['status'] == 'completed'])
            failed_count = len([task for task in self.email_queue if task['status'] == 'failed'])
            sending_count = len([task for task in self.email_queue if task['status'] == 'sending'])
            
            self.queue_list_text.insert(tk.END, "=" * 60 + "\n")
            self.queue_list_text.insert(tk.END, f"📊 统计: 待发送 {pending_count} | 发送中 {sending_count} | 已完成 {completed_count} | 失败 {failed_count}\n")
        
        # 更新统计标签
        queue_count = len(self.email_queue)
        pending_count = len([task for task in self.email_queue if task['status'] == 'pending'])
        completed_count = len([task for task in self.email_queue if task['status'] == 'completed'])
        
        self.queue_total_label.config(text=f"总数: {queue_count}")
        self.queue_sent_label.config(text=f"已发: {completed_count}")
        self.queue_remaining_label.config(text=f"剩余: {pending_count}")
        
        # 自动滚动到底部
        self.queue_list_text.see(tk.END)
        self.queue_list_text.config(state=tk.DISABLED)
        
    def run(self):
        """运行测试"""
        self.root.mainloop()

if __name__ == "__main__":
    test = QueueScrollTest()
    test.run()
