#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
退信率统计修复工具
修复退信率显示为0.0%的问题
"""

import sqlite3
import datetime
import logging
from typing import Dict, List, Tuple

class BounceRateFixer:
    """退信率修复器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('BounceRateFixer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def fix_bounce_rate_calculation(self):
        """修复退信率计算"""
        try:
            self.logger.info("🔧 开始修复退信率计算...")
            
            # 1. 修复反垃圾邮件数据库的退信统计
            self._fix_anti_spam_bounce_stats()
            
            # 2. 修复收件人质量数据库的退信统计
            self._fix_quality_db_bounce_stats()
            
            # 3. 修复邮件接收器的退信检测
            self._fix_email_receiver_bounce_detection()
            
            # 4. 重新计算所有退信率
            self._recalculate_all_bounce_rates()
            
            self.logger.info("✅ 退信率修复完成")
            
        except Exception as e:
            self.logger.error(f"❌ 退信率修复失败: {str(e)}")
    
    def _fix_anti_spam_bounce_stats(self):
        """修复反垃圾邮件数据库的退信统计"""
        try:
            self.logger.info("🔧 修复反垃圾邮件数据库退信统计...")
            
            # 检查数据库是否存在
            try:
                conn = sqlite3.connect('anti_spam.db')
                cursor = conn.cursor()
                
                # 检查sending_stats表是否存在
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS sending_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sender_email TEXT NOT NULL,
                        send_date TEXT NOT NULL,
                        hour INTEGER NOT NULL,
                        emails_sent INTEGER DEFAULT 0,
                        emails_delivered INTEGER DEFAULT 0,
                        emails_bounced INTEGER DEFAULT 0,
                        emails_replied INTEGER DEFAULT 0,
                        spam_rate REAL DEFAULT 0.0,
                        bounce_rate REAL DEFAULT 0.0,
                        reply_rate REAL DEFAULT 0.0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(sender_email, send_date, hour)
                    )
                ''')
                
                # 添加bounce_rate列（如果不存在）
                try:
                    cursor.execute('ALTER TABLE sending_stats ADD COLUMN bounce_rate REAL DEFAULT 0.0')
                except sqlite3.OperationalError:
                    pass  # 列已存在
                
                # 重新计算退信率
                cursor.execute('''
                    UPDATE sending_stats 
                    SET bounce_rate = CASE 
                        WHEN emails_sent > 0 THEN CAST(emails_bounced AS REAL) / emails_sent 
                        ELSE 0.0 
                    END
                ''')
                
                conn.commit()
                conn.close()
                
                self.logger.info("✅ 反垃圾邮件数据库退信统计修复完成")
                
            except Exception as e:
                self.logger.warning(f"⚠️ 反垃圾邮件数据库不存在或修复失败: {str(e)}")
                
        except Exception as e:
            self.logger.error(f"❌ 修复反垃圾邮件数据库失败: {str(e)}")
    
    def _fix_quality_db_bounce_stats(self):
        """修复收件人质量数据库的退信统计"""
        try:
            self.logger.info("🔧 修复收件人质量数据库退信统计...")
            
            try:
                conn = sqlite3.connect('recipient_quality.db')
                cursor = conn.cursor()
                
                # 检查recipient_quality表是否存在
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS recipient_quality (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email TEXT NOT NULL,
                        sender_email TEXT NOT NULL,
                        domain TEXT,
                        quality_score REAL DEFAULT 50.0,
                        status TEXT DEFAULT 'unknown',
                        total_sent INTEGER DEFAULT 0,
                        total_replies INTEGER DEFAULT 0,
                        total_bounces INTEGER DEFAULT 0,
                        response_rate REAL DEFAULT 0.0,
                        bounce_rate REAL DEFAULT 0.0,
                        engagement_score REAL DEFAULT 0.0,
                        first_contact_date TEXT,
                        last_reply_time TEXT,
                        last_sent_time TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(email, sender_email)
                    )
                ''')
                
                # 添加bounce_rate列（如果不存在）
                try:
                    cursor.execute('ALTER TABLE recipient_quality ADD COLUMN bounce_rate REAL DEFAULT 0.0')
                except sqlite3.OperationalError:
                    pass  # 列已存在
                
                # 重新计算退信率
                cursor.execute('''
                    UPDATE recipient_quality 
                    SET bounce_rate = CASE 
                        WHEN total_sent > 0 THEN CAST(total_bounces AS REAL) / total_sent 
                        ELSE 0.0 
                    END
                ''')
                
                # 重新计算质量评分（考虑退信率）
                cursor.execute('''
                    UPDATE recipient_quality 
                    SET quality_score = CASE 
                        WHEN total_sent > 0 THEN 
                            GREATEST(0, 100 - (bounce_rate * 100) - ((1 - response_rate) * 50))
                        ELSE 50.0 
                    END
                ''')
                
                conn.commit()
                conn.close()
                
                self.logger.info("✅ 收件人质量数据库退信统计修复完成")
                
            except Exception as e:
                self.logger.warning(f"⚠️ 收件人质量数据库不存在或修复失败: {str(e)}")
                
        except Exception as e:
            self.logger.error(f"❌ 修复收件人质量数据库失败: {str(e)}")
    
    def _fix_email_receiver_bounce_detection(self):
        """修复邮件接收器的退信检测"""
        try:
            self.logger.info("🔧 修复邮件接收器退信检测...")
            
            try:
                conn = sqlite3.connect('email_receiver.db')
                cursor = conn.cursor()
                
                # 检查recipient_status表是否存在
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS recipient_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sender_email TEXT NOT NULL,
                        recipient_email TEXT NOT NULL,
                        status TEXT DEFAULT 'unknown',
                        reply_count INTEGER DEFAULT 0,
                        bounce_count INTEGER DEFAULT 0,
                        last_reply_time TEXT,
                        last_bounce_time TEXT,
                        notes TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(sender_email, recipient_email)
                    )
                ''')
                
                # 检查auto_replies表中的退信记录
                cursor.execute('''
                    SELECT sender_email, recipient_email, reply_type, COUNT(*) as bounce_count
                    FROM auto_replies 
                    WHERE reply_type = 'bounce' OR reply_type LIKE '%退信%' OR reply_type LIKE '%bounce%'
                    GROUP BY sender_email, recipient_email
                ''')
                
                bounce_records = cursor.fetchall()
                
                # 更新recipient_status表的退信统计
                for sender, recipient, reply_type, count in bounce_records:
                    cursor.execute('''
                        INSERT OR REPLACE INTO recipient_status 
                        (sender_email, recipient_email, status, bounce_count, last_bounce_time, updated_at)
                        VALUES (?, ?, 'invalid', ?, ?, ?)
                    ''', (sender, recipient, count, datetime.datetime.now().isoformat(), datetime.datetime.now().isoformat()))
                
                conn.commit()
                conn.close()
                
                self.logger.info(f"✅ 邮件接收器退信检测修复完成，处理了 {len(bounce_records)} 条退信记录")
                
            except Exception as e:
                self.logger.warning(f"⚠️ 邮件接收器数据库不存在或修复失败: {str(e)}")
                
        except Exception as e:
            self.logger.error(f"❌ 修复邮件接收器退信检测失败: {str(e)}")
    
    def _recalculate_all_bounce_rates(self):
        """重新计算所有退信率"""
        try:
            self.logger.info("🔧 重新计算所有退信率...")
            
            # 获取所有发件人
            senders = self._get_all_senders()
            
            for sender_email in senders:
                self._recalculate_sender_bounce_rate(sender_email)
            
            self.logger.info(f"✅ 重新计算了 {len(senders)} 个发件人的退信率")
            
        except Exception as e:
            self.logger.error(f"❌ 重新计算退信率失败: {str(e)}")
    
    def _get_all_senders(self) -> List[str]:
        """获取所有发件人列表"""
        senders = set()
        
        # 从各个数据库收集发件人
        databases = [
            ('anti_spam.db', 'sending_stats', 'sender_email'),
            ('recipient_quality.db', 'recipient_quality', 'sender_email'),
            ('email_receiver.db', 'recipient_status', 'sender_email'),
            ('email_history.db', 'email_records', 'sender_email')
        ]
        
        for db_path, table_name, column_name in databases:
            try:
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                cursor.execute(f'SELECT DISTINCT {column_name} FROM {table_name}')
                results = cursor.fetchall()
                
                for result in results:
                    if result[0]:
                        senders.add(result[0])
                
                conn.close()
                
            except Exception as e:
                self.logger.debug(f"跳过数据库 {db_path}: {str(e)}")
        
        return list(senders)
    
    def _recalculate_sender_bounce_rate(self, sender_email: str):
        """重新计算特定发件人的退信率"""
        try:
            # 从邮件接收器获取退信统计
            bounce_count = 0
            total_sent = 0
            
            try:
                conn = sqlite3.connect('email_receiver.db')
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT SUM(bounce_count) FROM recipient_status 
                    WHERE sender_email = ?
                ''', (sender_email,))
                
                result = cursor.fetchone()
                bounce_count = result[0] or 0
                
                conn.close()
                
            except Exception:
                pass
            
            # 从邮件历史获取总发送数
            try:
                conn = sqlite3.connect('email_history.db')
                cursor = conn.cursor()
                
                cursor.execute('''
                    SELECT COUNT(*) FROM email_records 
                    WHERE sender_email = ?
                ''', (sender_email,))
                
                result = cursor.fetchone()
                total_sent = result[0] or 0
                
                conn.close()
                
            except Exception:
                pass
            
            # 计算退信率
            bounce_rate = (bounce_count / total_sent) if total_sent > 0 else 0.0
            
            # 更新反垃圾邮件数据库
            try:
                conn = sqlite3.connect('anti_spam.db')
                cursor = conn.cursor()
                
                today = datetime.datetime.now().strftime('%Y-%m-%d')
                hour = datetime.datetime.now().hour
                
                cursor.execute('''
                    INSERT OR REPLACE INTO sending_stats 
                    (sender_email, send_date, hour, emails_sent, emails_bounced, bounce_rate, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (sender_email, today, hour, total_sent, bounce_count, bounce_rate, datetime.datetime.now().isoformat()))
                
                conn.commit()
                conn.close()
                
            except Exception:
                pass
            
            self.logger.debug(f"发件人 {sender_email}: 退信率 {bounce_rate:.2%} ({bounce_count}/{total_sent})")
            
        except Exception as e:
            self.logger.error(f"❌ 重新计算发件人 {sender_email} 退信率失败: {str(e)}")
    
    def get_bounce_rate_report(self) -> Dict:
        """获取退信率报告"""
        try:
            self.logger.info("📊 生成退信率报告...")
            
            report = {
                'senders': [],
                'total_sent': 0,
                'total_bounced': 0,
                'overall_bounce_rate': 0.0,
                'generated_time': datetime.datetime.now().isoformat()
            }
            
            senders = self._get_all_senders()
            
            for sender_email in senders:
                sender_stats = self._get_sender_bounce_stats(sender_email)
                if sender_stats['total_sent'] > 0:
                    report['senders'].append(sender_stats)
                    report['total_sent'] += sender_stats['total_sent']
                    report['total_bounced'] += sender_stats['total_bounced']
            
            # 计算总体退信率
            if report['total_sent'] > 0:
                report['overall_bounce_rate'] = report['total_bounced'] / report['total_sent']
            
            self.logger.info(f"✅ 退信率报告生成完成，总体退信率: {report['overall_bounce_rate']:.2%}")
            
            return report
            
        except Exception as e:
            self.logger.error(f"❌ 生成退信率报告失败: {str(e)}")
            return {}
    
    def _get_sender_bounce_stats(self, sender_email: str) -> Dict:
        """获取发件人退信统计"""
        stats = {
            'sender_email': sender_email,
            'total_sent': 0,
            'total_bounced': 0,
            'bounce_rate': 0.0
        }
        
        try:
            # 从反垃圾邮件数据库获取统计
            conn = sqlite3.connect('anti_spam.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT SUM(emails_sent), SUM(emails_bounced) 
                FROM sending_stats 
                WHERE sender_email = ?
            ''', (sender_email,))
            
            result = cursor.fetchone()
            if result and result[0]:
                stats['total_sent'] = result[0] or 0
                stats['total_bounced'] = result[1] or 0
                
                if stats['total_sent'] > 0:
                    stats['bounce_rate'] = stats['total_bounced'] / stats['total_sent']
            
            conn.close()
            
        except Exception:
            pass
        
        return stats

def main():
    """主函数"""
    print("🔧 退信率统计修复工具")
    print("=" * 50)
    
    fixer = BounceRateFixer()
    
    # 修复退信率计算
    fixer.fix_bounce_rate_calculation()
    
    # 生成报告
    report = fixer.get_bounce_rate_report()
    
    if report:
        print("\n📊 退信率报告:")
        print(f"总发送数: {report['total_sent']}")
        print(f"总退信数: {report['total_bounced']}")
        print(f"总体退信率: {report['overall_bounce_rate']:.2%}")
        
        if report['senders']:
            print("\n发件人详情:")
            for sender_stats in report['senders']:
                print(f"  {sender_stats['sender_email']}: {sender_stats['bounce_rate']:.2%} ({sender_stats['total_bounced']}/{sender_stats['total_sent']})")

if __name__ == "__main__":
    main()
