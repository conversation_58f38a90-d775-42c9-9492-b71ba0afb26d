#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复数据库锁定问题
"""

import sqlite3
import os
import time
import threading
from contextlib import contextmanager

def find_database_files():
    """查找所有数据库文件"""
    print("🔍 查找数据库文件")
    print("-" * 30)
    
    db_files = []
    
    # 常见的数据库文件
    possible_dbs = [
        'auto_reply_monitor.db',
        'email_history.db', 
        'quality_manager.db',
        'qq_emergency.db',
        'recipient_quality.db'
    ]
    
    for db_file in possible_dbs:
        if os.path.exists(db_file):
            db_files.append(db_file)
            print(f"✅ 找到数据库: {db_file}")
    
    # 查找其他.db文件
    for file in os.listdir('.'):
        if file.endswith('.db') and file not in db_files:
            db_files.append(file)
            print(f"✅ 找到数据库: {file}")
    
    if not db_files:
        print("❌ 未找到数据库文件")
    
    return db_files

def check_database_locks(db_files):
    """检查数据库锁定状态"""
    print("\n🔍 检查数据库锁定状态")
    print("-" * 30)
    
    locked_dbs = []
    
    for db_file in db_files:
        try:
            # 尝试连接数据库
            conn = sqlite3.connect(db_file, timeout=1.0)
            
            # 尝试执行一个简单的查询
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")
            cursor.fetchone()
            
            conn.close()
            print(f"✅ {db_file} - 正常")
            
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                locked_dbs.append(db_file)
                print(f"❌ {db_file} - 锁定")
            else:
                print(f"⚠️ {db_file} - 其他错误: {str(e)}")
        except Exception as e:
            print(f"⚠️ {db_file} - 检查失败: {str(e)}")
    
    return locked_dbs

def fix_database_locks(db_files):
    """修复数据库锁定"""
    print("\n🔧 修复数据库锁定")
    print("-" * 30)
    
    fixed_count = 0
    
    for db_file in db_files:
        try:
            print(f"🔧 修复 {db_file}...")
            
            # 方法1: 强制关闭所有连接
            try:
                # 创建新连接并立即关闭
                conn = sqlite3.connect(db_file, timeout=30.0)
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA cache_size=10000")
                conn.execute("PRAGMA temp_store=memory")
                conn.execute("PRAGMA busy_timeout=30000")
                conn.close()
                
                print(f"✅ {db_file} - 连接优化完成")
                
            except Exception as e:
                print(f"⚠️ {db_file} - 优化失败: {str(e)}")
            
            # 方法2: 检查WAL文件
            wal_file = db_file + '-wal'
            shm_file = db_file + '-shm'
            
            if os.path.exists(wal_file):
                try:
                    # 尝试删除WAL文件（如果没有活动连接）
                    time.sleep(0.1)
                    if os.path.getsize(wal_file) == 0:
                        os.remove(wal_file)
                        print(f"✅ {db_file} - 清理WAL文件")
                except:
                    pass
            
            if os.path.exists(shm_file):
                try:
                    time.sleep(0.1)
                    if os.path.getsize(shm_file) == 0:
                        os.remove(shm_file)
                        print(f"✅ {db_file} - 清理SHM文件")
                except:
                    pass
            
            # 方法3: 测试连接
            try:
                conn = sqlite3.connect(db_file, timeout=5.0)
                cursor = conn.cursor()
                cursor.execute("SELECT 1")
                cursor.fetchone()
                conn.close()
                
                print(f"✅ {db_file} - 修复成功")
                fixed_count += 1
                
            except Exception as e:
                print(f"❌ {db_file} - 仍然锁定: {str(e)}")
                
        except Exception as e:
            print(f"❌ {db_file} - 修复失败: {str(e)}")
    
    return fixed_count

def optimize_databases(db_files):
    """优化数据库性能"""
    print("\n🔧 优化数据库性能")
    print("-" * 30)
    
    optimized_count = 0
    
    for db_file in db_files:
        try:
            print(f"🔧 优化 {db_file}...")
            
            conn = sqlite3.connect(db_file, timeout=30.0)
            
            # 设置优化参数
            optimizations = [
                "PRAGMA journal_mode=WAL",
                "PRAGMA synchronous=NORMAL", 
                "PRAGMA cache_size=10000",
                "PRAGMA temp_store=memory",
                "PRAGMA mmap_size=268435456",
                "PRAGMA busy_timeout=30000"
            ]
            
            for pragma in optimizations:
                try:
                    conn.execute(pragma)
                    print(f"  ✅ {pragma}")
                except Exception as e:
                    print(f"  ⚠️ {pragma} - 失败: {str(e)}")
            
            # 执行VACUUM清理
            try:
                conn.execute("VACUUM")
                print(f"  ✅ VACUUM完成")
            except Exception as e:
                print(f"  ⚠️ VACUUM失败: {str(e)}")
            
            conn.close()
            print(f"✅ {db_file} - 优化完成")
            optimized_count += 1
            
        except Exception as e:
            print(f"❌ {db_file} - 优化失败: {str(e)}")
    
    return optimized_count

def create_database_wrapper():
    """创建数据库包装器"""
    print("\n🔧 创建数据库包装器")
    print("-" * 30)
    
    wrapper_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的数据库操作包装器
"""

import sqlite3
import threading
import time
from contextlib import contextmanager

class SafeDatabase:
    """安全的数据库操作类"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.lock = threading.RLock()
    
    @contextmanager
    def get_connection(self, retries=3):
        """获取数据库连接"""
        for attempt in range(retries):
            try:
                with self.lock:
                    conn = sqlite3.connect(
                        self.db_path,
                        timeout=30.0,
                        check_same_thread=False
                    )
                    
                    # 设置优化参数
                    conn.execute("PRAGMA journal_mode=WAL")
                    conn.execute("PRAGMA synchronous=NORMAL")
                    conn.execute("PRAGMA busy_timeout=30000")
                    
                    try:
                        yield conn
                        conn.commit()
                        break
                    except Exception as e:
                        conn.rollback()
                        if attempt == retries - 1:
                            raise
                        time.sleep(0.1 * (attempt + 1))
                    finally:
                        conn.close()
                        
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < retries - 1:
                    time.sleep(0.5 * (attempt + 1))
                    continue
                raise
    
    def execute_query(self, query, params=None):
        """执行查询"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
    
    def execute_update(self, query, params=None):
        """执行更新"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.rowcount

# 使用示例:
# db = SafeDatabase("your_database.db")
# results = db.execute_query("SELECT * FROM table")
# db.execute_update("UPDATE table SET column=? WHERE id=?", (value, id))
'''
    
    try:
        with open('safe_database.py', 'w', encoding='utf-8') as f:
            f.write(wrapper_code)
        
        print("✅ 数据库包装器已创建: safe_database.py")
        return True
        
    except Exception as e:
        print(f"❌ 创建包装器失败: {str(e)}")
        return False

def main():
    """主修复函数"""
    print("🔧 修复数据库锁定问题")
    print("=" * 60)
    
    # 查找数据库文件
    db_files = find_database_files()
    
    if not db_files:
        print("❌ 未找到数据库文件，无需修复")
        return
    
    # 检查锁定状态
    locked_dbs = check_database_locks(db_files)
    
    # 修复锁定
    if locked_dbs:
        print(f"\n发现 {len(locked_dbs)} 个锁定的数据库")
        fixed_count = fix_database_locks(locked_dbs)
        print(f"修复了 {fixed_count} 个数据库")
    else:
        print("\n✅ 没有发现锁定的数据库")
    
    # 优化所有数据库
    optimized_count = optimize_databases(db_files)
    
    # 创建包装器
    wrapper_created = create_database_wrapper()
    
    # 总结
    print(f"\n📊 修复结果")
    print("=" * 30)
    print(f"数据库文件数: {len(db_files)}")
    print(f"锁定数据库数: {len(locked_dbs)}")
    print(f"修复数据库数: {fixed_count if locked_dbs else 0}")
    print(f"优化数据库数: {optimized_count}")
    print(f"包装器创建: {'成功' if wrapper_created else '失败'}")
    
    if optimized_count >= len(db_files) * 0.8:  # 80%以上成功
        print("\n🎉 数据库锁定问题修复完成！")
        print("✅ 数据库已优化")
        print("✅ 锁定问题已解决")
        print("✅ 性能已提升")
        
        print("\n💡 建议:")
        print("1. 重启邮件程序")
        print("2. 测试自动回复监控")
        print("3. 观察是否还有锁定错误")
        
    else:
        print(f"\n⚠️ 部分数据库修复失败")
        print("💡 可能需要:")
        print("1. 关闭所有相关程序")
        print("2. 重启计算机")
        print("3. 重新运行修复工具")

if __name__ == "__main__":
    main()
