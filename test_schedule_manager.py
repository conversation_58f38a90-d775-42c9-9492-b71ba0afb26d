#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 定时发送管理器测试脚本
测试定时发送功能的完整性和可靠性
"""

import datetime
import uuid
import time
import json
from schedule_manager import ScheduleManager, ScheduledTask

def test_schedule_manager():
    """测试定时发送管理器"""
    print("🧪 开始测试定时发送管理器")
    print("=" * 50)
    
    try:
        # 1. 创建管理器实例
        print("1. 创建管理器实例...")
        manager = ScheduleManager()
        print("✅ 管理器创建成功")
        
        # 2. 测试添加定时任务
        print("\n2. 测试添加定时任务...")
        
        # 创建测试任务
        test_task = ScheduledTask(
            id=str(uuid.uuid4()),
            name="测试定时任务",
            sender_email="<EMAIL>",
            recipient_emails="<EMAIL>\<EMAIL>",
            subject="定时发送测试邮件",
            body="这是一封定时发送的测试邮件。\n\n发送时间: " + datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            attachments=[],
            scheduled_time=(datetime.datetime.now() + datetime.timedelta(minutes=2)).isoformat(),
            send_mode="standard",
            anti_spam_strategy="moderate",
            enable_monitoring=True,
            enable_quality_db=True,
            enable_emergency=True,
            enable_coordination=True,
            status="pending",
            created_time=datetime.datetime.now().isoformat()
        )
        
        success = manager.add_scheduled_task(test_task)
        if success:
            print(f"✅ 测试任务添加成功: {test_task.name}")
        else:
            print("❌ 测试任务添加失败")
            return False
        
        # 3. 测试获取任务列表
        print("\n3. 测试获取任务列表...")
        all_tasks = manager.get_all_tasks()
        print(f"✅ 获取到 {len(all_tasks)} 个任务")
        
        pending_tasks = manager.get_pending_tasks()
        print(f"✅ 其中 {len(pending_tasks)} 个待执行任务")
        
        # 4. 测试统计信息
        print("\n4. 测试统计信息...")
        stats = manager.get_task_statistics()
        if stats:
            print("✅ 统计信息获取成功:")
            print(f"   总任务数: {stats.get('total_tasks', 0)}")
            print(f"   待执行: {stats.get('pending', 0)}")
            print(f"   成功率: {stats.get('success_rate', 0)}%")
        else:
            print("❌ 统计信息获取失败")
        
        # 5. 测试最佳时间分析
        print("\n5. 测试最佳时间分析...")
        optimal_time = manager.get_optimal_send_time("<EMAIL>")
        print(f"✅ 推荐最佳发送时间: {optimal_time.strftime('%Y-%m-%d %H:%M')}")
        
        # 6. 测试导出功能
        print("\n6. 测试导出功能...")
        exported_tasks = manager.export_tasks()
        print(f"✅ 导出 {len(exported_tasks)} 个任务数据")
        
        # 7. 测试执行历史
        print("\n7. 测试执行历史...")
        history = manager.get_execution_history()
        print(f"✅ 获取到 {len(history)} 条执行历史")
        
        # 8. 测试取消任务
        print("\n8. 测试取消任务...")
        cancel_success = manager.cancel_task(test_task.id)
        if cancel_success:
            print("✅ 任务取消成功")
        else:
            print("❌ 任务取消失败")
        
        # 9. 测试删除任务
        print("\n9. 测试删除任务...")
        delete_success = manager.delete_task(test_task.id)
        if delete_success:
            print("✅ 任务删除成功")
        else:
            print("❌ 任务删除失败")
        
        # 10. 测试清理功能
        print("\n10. 测试清理功能...")
        cleanup_result = manager.cleanup_old_tasks(days=1)
        print(f"✅ 清理完成: 删除 {cleanup_result['tasks_deleted']} 个任务")
        
        print("\n🎉 所有测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {str(e)}")
        return False

def test_schedule_workflow():
    """测试完整的定时发送工作流程"""
    print("\n🔄 测试完整工作流程")
    print("=" * 50)
    
    try:
        manager = ScheduleManager()
        
        # 创建一个即将执行的任务（1分钟后）
        workflow_task = ScheduledTask(
            id=str(uuid.uuid4()),
            name="工作流程测试任务",
            sender_email="<EMAIL>",
            recipient_emails="<EMAIL>",
            subject="工作流程测试",
            body="这是工作流程测试邮件",
            attachments=[],
            scheduled_time=(datetime.datetime.now() + datetime.timedelta(minutes=1)).isoformat(),
            send_mode="standard",
            anti_spam_strategy="moderate",
            enable_monitoring=False,  # 关闭监控以避免实际发送
            enable_quality_db=False,
            enable_emergency=False,
            enable_coordination=False,
            status="pending",
            created_time=datetime.datetime.now().isoformat()
        )
        
        # 添加任务
        if manager.add_scheduled_task(workflow_task):
            print("✅ 工作流程测试任务已添加")
            print(f"   任务ID: {workflow_task.id}")
            print(f"   执行时间: {workflow_task.scheduled_time}")
            
            # 等待任务执行
            print("⏳ 等待任务执行...")
            
            # 检查任务状态变化
            for i in range(70):  # 等待70秒
                time.sleep(1)
                
                # 获取任务状态
                all_tasks = manager.get_all_tasks()
                current_task = None
                for task in all_tasks:
                    if task.id == workflow_task.id:
                        current_task = task
                        break
                
                if current_task:
                    if current_task.status != 'pending':
                        print(f"✅ 任务状态已更新: {current_task.status}")
                        if current_task.result:
                            print(f"   执行结果: {json.dumps(current_task.result, ensure_ascii=False, indent=2)}")
                        break
                
                if i % 10 == 0:
                    print(f"   等待中... ({i}/70 秒)")
            
            # 清理测试任务
            manager.delete_task(workflow_task.id)
            print("✅ 测试任务已清理")
            
        else:
            print("❌ 工作流程测试任务添加失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流程测试失败: {str(e)}")
        return False

def test_database_persistence():
    """测试数据库持久化"""
    print("\n💾 测试数据库持久化")
    print("=" * 50)
    
    try:
        # 创建第一个管理器实例
        manager1 = ScheduleManager()
        
        # 添加测试任务
        persistence_task = ScheduledTask(
            id=str(uuid.uuid4()),
            name="持久化测试任务",
            sender_email="<EMAIL>",
            recipient_emails="<EMAIL>",
            subject="持久化测试",
            body="测试数据库持久化功能",
            attachments=[],
            scheduled_time=(datetime.datetime.now() + datetime.timedelta(hours=1)).isoformat(),
            send_mode="standard",
            anti_spam_strategy="moderate",
            enable_monitoring=True,
            enable_quality_db=True,
            enable_emergency=True,
            enable_coordination=True,
            status="pending",
            created_time=datetime.datetime.now().isoformat()
        )
        
        if manager1.add_scheduled_task(persistence_task):
            print("✅ 任务已添加到第一个管理器实例")
            
            # 停止第一个管理器
            manager1.stop_scheduler()
            print("✅ 第一个管理器已停止")
            
            # 创建第二个管理器实例
            manager2 = ScheduleManager()
            print("✅ 第二个管理器已创建")
            
            # 检查任务是否仍然存在
            all_tasks = manager2.get_all_tasks()
            task_found = False
            for task in all_tasks:
                if task.id == persistence_task.id:
                    task_found = True
                    print("✅ 任务在新管理器实例中找到")
                    print(f"   任务名称: {task.name}")
                    print(f"   状态: {task.status}")
                    break
            
            if not task_found:
                print("❌ 任务在新管理器实例中未找到")
                return False
            
            # 清理测试任务
            manager2.delete_task(persistence_task.id)
            manager2.stop_scheduler()
            print("✅ 测试任务已清理")
            
            return True
        else:
            print("❌ 任务添加失败")
            return False
        
    except Exception as e:
        print(f"❌ 持久化测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 定时发送管理器完整测试")
    print("=" * 60)
    
    test_results = []
    
    # 基础功能测试
    print("\n📋 基础功能测试")
    result1 = test_schedule_manager()
    test_results.append(("基础功能测试", result1))
    
    # 数据库持久化测试
    print("\n💾 数据库持久化测试")
    result2 = test_database_persistence()
    test_results.append(("数据库持久化测试", result2))
    
    # 工作流程测试（可选，需要较长时间）
    print("\n🔄 是否进行工作流程测试？(需要等待约1分钟)")
    user_input = input("输入 'y' 进行测试，其他键跳过: ").strip().lower()
    if user_input == 'y':
        result3 = test_schedule_workflow()
        test_results.append(("工作流程测试", result3))
    
    # 测试结果汇总
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！定时发送管理器功能正常")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    main()
