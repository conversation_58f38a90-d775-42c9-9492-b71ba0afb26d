自动化邮件发送助手 - 邮箱输入说明
================================

📧 支持的邮箱输入格式
--------------------

### 1. 分号分隔（推荐，最常用）
```
<EMAIL>;<EMAIL>;<EMAIL>
<EMAIL>; <EMAIL>; <EMAIL>  (带空格也支持)
```

### 2. 中文分号分隔
```
<EMAIL>；<EMAIL>；<EMAIL>
```

### 3. 逗号分隔
```
<EMAIL>, <EMAIL>, <EMAIL>
```

### 4. 换行分隔
```
<EMAIL>
<EMAIL>
<EMAIL>
```

### 5. 混合分隔（智能识别）
```
<EMAIL>, <EMAIL>
<EMAIL>; <EMAIL>
<EMAIL>
```

### 6. 中文标点分隔
```
<EMAIL>，<EMAIL>；<EMAIL>
```

🔧 使用技巧
-----------

### 批量输入120+邮箱
1. **从Excel复制**
   - 选中Excel中的邮箱列
   - 复制（Ctrl+C）
   - 粘贴到邮箱输入框（Ctrl+V）
   - 系统自动识别换行分隔

2. **从文本文件导入**
   - 准备txt文件，每行一个邮箱
   - 全选复制文件内容
   - 粘贴到输入框

3. **手动输入**
   - 每行输入一个邮箱（推荐）
   - 或用逗号分隔多个邮箱

### 验证邮箱功能
- 输入邮箱后点击"验证邮箱"按钮
- 系统会显示有效和无效的邮箱
- 自动过滤掉格式错误的邮箱

✅ 有效邮箱格式示例
------------------
```
✓ <EMAIL>
✓ <EMAIL>
✓ <EMAIL>
✓ <EMAIL>
✓ <EMAIL>
```

❌ 无效邮箱格式示例
------------------
```
✗ user@          (缺少域名)
✗ @qq.com         (缺少用户名)
✗ user.qq.com     (缺少@符号)
✗ user@.com       (域名格式错误)
✗ user@qq.        (域名格式错误)
✗ user@@qq.com    (多个@符号)
```

🚀 实际使用示例
---------------

### 示例1：小规模发送（<20个）
```
张三@qq.com
李四@163.com, 王五@gmail.com
赵六@hotmail.com; 钱七@outlook.com
```
**建议模式：** 快速发送（1秒间隔）

### 示例2：中等规模发送（20-100个）
```
<EMAIL>, <EMAIL>, <EMAIL>
<EMAIL>, <EMAIL>
<EMAIL>
<EMAIL>; <EMAIL>
... (更多邮箱)
```
**建议模式：** 标准发送（2秒间隔）

### 示例3：大规模发送（100+个）
```
从Excel表格复制的大量邮箱地址
每行一个邮箱，共120+个
```
**建议模式：** 安全发送（3秒间隔）

⚠️ 注意事项
-----------

### 邮箱地址要求
- 确保所有邮箱地址真实有效
- 避免输入重复的邮箱地址
- 建议先用少量邮箱测试

### 隐私保护
- 所有邮件都是分别发送
- 每个收件人只能看到自己的邮箱
- 完全保护收件人隐私

### 发送建议
- 大量邮箱建议分批发送
- 避免短时间内重复发送相同内容
- 选择合适的发送时间（工作日上午）

🔍 故障排除
-----------

### 问题1：邮箱解析错误
**现象：** 多个邮箱被当作一个地址
**解决：** 
- 检查分隔符是否正确
- 使用"验证邮箱"功能检查
- 确保没有多余的空格或特殊字符

### 问题2：部分邮箱无效
**现象：** 验证时显示无效邮箱
**解决：**
- 检查邮箱格式是否正确
- 确认@符号和域名部分
- 删除或修正无效邮箱

### 问题3：输入框显示不全
**现象：** 120+邮箱显示不完整
**解决：**
- 使用滚动条查看全部内容
- 点击"验证邮箱"查看解析结果
- 邮箱数量会在日志中显示

📞 技术支持
-----------

如遇问题：
1. 使用"验证邮箱"功能检查输入
2. 查看实时日志了解详细信息
3. 参考日志文件排查问题
4. 先用少量邮箱测试功能

================================
正确输入邮箱地址，享受高效的邮件发送体验！
