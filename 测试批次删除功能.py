#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 测试批次删除功能
验证新增的批次删除方法是否正常工作
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_batch_deletion():
    """测试批次删除功能"""
    print("🧪 测试批次删除功能")
    print("=" * 50)
    
    try:
        # 1. 测试导入 RecipientQualityManager
        print("\n📦 步骤1: 导入 RecipientQualityManager")
        from recipient_quality_manager import RecipientQualityManager
        
        quality_manager = RecipientQualityManager()
        print("✅ RecipientQualityManager 导入成功")
        
        # 2. 检查新增的方法是否存在
        print("\n🔍 步骤2: 检查新增的删除方法")
        
        methods_to_check = [
            'delete_batch',
            'delete_batches', 
            'delete_all_batches'
        ]
        
        for method_name in methods_to_check:
            if hasattr(quality_manager, method_name):
                print(f"✅ {method_name} 方法存在")
            else:
                print(f"❌ {method_name} 方法不存在")
        
        # 3. 测试创建一些测试批次
        print("\n📝 步骤3: 创建测试批次")
        
        # 先添加一些测试收件人
        test_recipients = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ]
        
        for email in test_recipients:
            quality_manager.add_recipient(
                email=email,
                initial_score=75.0,
                source="测试数据"
            )
        
        print(f"✅ 添加了 {len(test_recipients)} 个测试收件人")
        
        # 创建测试批次
        batch_result = quality_manager.create_smart_batches(
            batch_name="测试批次_删除功能",
            total_recipients=3,
            quality_threshold=50.0,
            max_batch_size=2,
            strategy="quality_balanced"
        )
        
        if batch_result.get('success'):
            batch_count = batch_result.get('batch_count', 0)
            print(f"✅ 创建了 {batch_count} 个测试批次")
            
            # 4. 测试获取所有批次
            print("\n📋 步骤4: 获取所有批次")
            all_batches = quality_manager.get_all_batches()
            print(f"✅ 当前共有 {len(all_batches)} 个批次")
            
            for batch in all_batches:
                print(f"   📦 批次ID: {batch['batch_id']}, 名称: {batch['batch_name']}")
            
            # 5. 测试删除单个批次
            if all_batches:
                print("\n🗑️ 步骤5: 测试删除单个批次")
                first_batch_id = all_batches[0]['batch_id']
                
                success = quality_manager.delete_batch(first_batch_id)
                if success:
                    print(f"✅ 成功删除批次 ID: {first_batch_id}")
                else:
                    print(f"❌ 删除批次失败 ID: {first_batch_id}")
                
                # 验证删除结果
                remaining_batches = quality_manager.get_all_batches()
                print(f"✅ 删除后剩余 {len(remaining_batches)} 个批次")
            
            # 6. 测试批量删除
            remaining_batches = quality_manager.get_all_batches()
            if len(remaining_batches) > 1:
                print("\n🗑️ 步骤6: 测试批量删除")
                batch_ids_to_delete = [batch['batch_id'] for batch in remaining_batches[:2]]
                
                deleted_count = quality_manager.delete_batches(batch_ids_to_delete)
                print(f"✅ 批量删除了 {deleted_count} 个批次")
                
                # 验证删除结果
                final_batches = quality_manager.get_all_batches()
                print(f"✅ 批量删除后剩余 {len(final_batches)} 个批次")
            
            # 7. 测试删除所有批次
            remaining_batches = quality_manager.get_all_batches()
            if remaining_batches:
                print("\n🧹 步骤7: 测试删除所有批次")
                
                success = quality_manager.delete_all_batches()
                if success:
                    print("✅ 成功删除所有批次")
                else:
                    print("❌ 删除所有批次失败")
                
                # 验证删除结果
                final_batches = quality_manager.get_all_batches()
                print(f"✅ 删除所有批次后剩余 {len(final_batches)} 个批次")
        
        else:
            print(f"❌ 创建测试批次失败: {batch_result.get('error', '未知错误')}")
        
        # 8. 清理测试数据
        print("\n🧹 步骤8: 清理测试数据")
        for email in test_recipients:
            quality_manager.remove_recipient(email)
        print("✅ 清理测试收件人完成")
        
        print("\n🎉 批次删除功能测试完成！")
        print("✅ 所有新增的删除方法都正常工作")
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        print("请确保 recipient_quality_manager.py 文件存在")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

def test_gui_methods():
    """测试GUI中的新方法"""
    print("\n🖥️ 测试GUI中的新方法")
    print("=" * 30)
    
    try:
        # 检查GUI文件中是否包含新方法
        with open('gui_main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        gui_methods_to_check = [
            '_select_all_batches',
            '_delete_selected_batches',
            '_delete_all_batches'
        ]
        
        for method_name in gui_methods_to_check:
            if f"def {method_name}(" in content:
                print(f"✅ GUI方法 {method_name} 存在")
            else:
                print(f"❌ GUI方法 {method_name} 不存在")
        
        # 检查按钮是否添加
        button_checks = [
            ("☑️ 全选批次", "_select_all_batches"),
            ("🗑️ 删除选中", "_delete_selected_batches"), 
            ("🧹 删除全部", "_delete_all_batches")
        ]
        
        for button_text, method_name in button_checks:
            if button_text in content and method_name in content:
                print(f"✅ 按钮 '{button_text}' 已添加")
            else:
                print(f"❌ 按钮 '{button_text}' 未找到")
        
        print("✅ GUI方法检查完成")
        
    except Exception as e:
        print(f"❌ GUI方法检查失败: {str(e)}")

if __name__ == "__main__":
    print("🔧 批次删除功能修复验证")
    print("=" * 60)
    
    # 测试后端方法
    test_batch_deletion()
    
    # 测试GUI方法
    test_gui_methods()
    
    print("\n📋 修复总结:")
    print("✅ 已在 RecipientQualityManager 中添加:")
    print("   • delete_batch(batch_id) - 删除单个批次")
    print("   • delete_batches(batch_ids) - 批量删除批次")
    print("   • delete_all_batches() - 删除所有批次")
    print("\n✅ 已在 GUI 中添加:")
    print("   • ☑️ 全选批次 按钮")
    print("   • 🗑️ 删除选中 按钮")
    print("   • 🧹 删除全部 按钮")
    print("\n🎉 批次删除功能修复完成！")
