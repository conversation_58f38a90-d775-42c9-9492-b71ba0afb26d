#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底修复配置文件问题
"""

import json
import os
import datetime

def fix_auth_codes_completely():
    """彻底修复授权码配置"""
    print("🔧 彻底修复授权码配置")
    print("=" * 50)
    
    # 正确的配置
    correct_config = {
        "<EMAIL>": {
            "auth_code": "cwnzcpaczwngdgfa",
            "add_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    }
    
    try:
        # 1. 备份现有配置
        if os.path.exists('auth_codes.json'):
            backup_name = f"auth_codes_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.rename('auth_codes.json', backup_name)
            print(f"✅ 已备份原配置为: {backup_name}")
        
        # 2. 创建新的正确配置
        with open('auth_codes.json', 'w', encoding='utf-8') as f:
            json.dump(correct_config, f, ensure_ascii=False, indent=2)
        
        print("✅ 新配置文件已创建")
        
        # 3. 验证配置文件
        with open('auth_codes.json', 'r', encoding='utf-8') as f:
            loaded_config = json.load(f)
        
        if loaded_config.get("<EMAIL>", {}).get("auth_code") == "cwnzcpaczwngdgfa":
            print("✅ 配置文件验证成功")
            print(f"✅ 邮箱: <EMAIL>")
            print(f"✅ 授权码: cwnzcpaczwngdgfa")
            return True
        else:
            print("❌ 配置文件验证失败")
            return False
            
    except Exception as e:
        print(f"❌ 修复配置失败: {str(e)}")
        return False

def test_imap_connection():
    """测试IMAP连接"""
    print("\n🔧 测试IMAP连接")
    print("=" * 50)
    
    try:
        import imaplib
        
        sender_email = "<EMAIL>"
        auth_code = "cwnzcpaczwngdgfa"
        
        print(f"📧 测试邮箱: {sender_email}")
        print(f"🔑 使用授权码: {auth_code}")
        print("🔗 连接服务器: imap.qq.com:993")
        
        # 连接IMAP服务器
        mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
        print("✅ IMAP服务器连接成功")
        
        # 登录验证
        mail.login(sender_email, auth_code)
        print("✅ IMAP登录验证成功")
        
        # 选择收件箱
        mail.select('INBOX')
        print("✅ 收件箱访问成功")
        
        # 检查邮件数量
        status, messages = mail.search(None, 'ALL')
        if status == 'OK':
            email_count = len(messages[0].split())
            print(f"✅ 收件箱邮件数量: {email_count}")
        
        mail.logout()
        print("🎉 IMAP连接测试完全成功！")
        return True
        
    except Exception as e:
        print(f"❌ IMAP连接测试失败: {str(e)}")
        return False

def clear_program_cache():
    """清理程序缓存"""
    print("\n🔧 清理程序缓存")
    print("=" * 50)
    
    try:
        # 清理可能的缓存文件
        cache_files = [
            '__pycache__',
            '*.pyc',
            'temp_*',
            '.cache'
        ]
        
        import glob
        import shutil
        
        for pattern in cache_files:
            for file_path in glob.glob(pattern):
                try:
                    if os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                        print(f"✅ 已删除缓存目录: {file_path}")
                    else:
                        os.remove(file_path)
                        print(f"✅ 已删除缓存文件: {file_path}")
                except:
                    pass
        
        print("✅ 程序缓存清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 清理缓存失败: {str(e)}")
        return False

def create_test_script():
    """创建测试脚本"""
    print("\n🔧 创建测试脚本")
    print("=" * 50)
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复效果
"""

import json
import imaplib

def test_config():
    """测试配置文件"""
    try:
        with open('auth_codes.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        auth_code = config.get("<EMAIL>", {}).get("auth_code")
        if auth_code == "cwnzcpaczwngdgfa":
            print("✅ 配置文件正确")
            return True
        else:
            print(f"❌ 配置文件错误，授权码: {auth_code}")
            return False
    except Exception as e:
        print(f"❌ 读取配置失败: {str(e)}")
        return False

def test_imap():
    """测试IMAP连接"""
    try:
        mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
        mail.login("<EMAIL>", "cwnzcpaczwngdgfa")
        mail.select('INBOX')
        mail.logout()
        print("✅ IMAP连接正常")
        return True
    except Exception as e:
        print(f"❌ IMAP连接失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🔧 快速测试修复效果")
    print("=" * 30)
    
    config_ok = test_config()
    imap_ok = test_imap()
    
    if config_ok and imap_ok:
        print("\\n🎉 所有测试通过！修复成功！")
    else:
        print("\\n⚠️ 仍有问题需要解决")
'''
    
    try:
        with open('快速测试.py', 'w', encoding='utf-8') as f:
            f.write(test_script)
        
        print("✅ 测试脚本已创建: 快速测试.py")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试脚本失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 彻底修复配置问题")
    print("=" * 60)
    
    steps = [
        ("修复授权码配置", fix_auth_codes_completely),
        ("测试IMAP连接", test_imap_connection),
        ("清理程序缓存", clear_program_cache),
        ("创建测试脚本", create_test_script)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔧 执行: {step_name}")
        if step_func():
            success_count += 1
            print(f"✅ {step_name} - 成功")
        else:
            print(f"❌ {step_name} - 失败")
    
    print(f"\n📊 修复结果")
    print("=" * 30)
    print(f"成功步骤: {success_count}/{len(steps)}")
    
    if success_count == len(steps):
        print("\n🎉 所有问题已彻底修复！")
        print("✅ 授权码配置正确")
        print("✅ IMAP连接正常")
        print("✅ 程序缓存已清理")
        print("✅ 测试脚本已创建")
        
        print("\n💡 下一步操作:")
        print("1. 重启邮件程序")
        print("2. 运行 python 快速测试.py 验证")
        print("3. 尝试发送邮件和自动回复监控")
        
    else:
        print(f"\n⚠️ 仍有 {len(steps) - success_count} 个问题")

if __name__ == "__main__":
    main()
