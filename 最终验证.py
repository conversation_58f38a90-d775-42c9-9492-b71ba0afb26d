#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证修复效果
"""

import json
import imaplib
import smtplib

def test_config_file():
    """测试配置文件"""
    print("🔧 测试配置文件")
    print("-" * 30)
    
    try:
        with open('auth_codes.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件读取成功")
        
        # 检查邮箱配置
        email_config = config.get("<EMAIL>")
        if email_config and isinstance(email_config, dict):
            auth_code = email_config.get("auth_code")
            if auth_code == "cwnzcpaczwngdgfa":
                print("✅ 授权码配置正确")
                print(f"   邮箱: <EMAIL>")
                print(f"   授权码: {auth_code}")
                return True
            else:
                print(f"❌ 授权码错误: {auth_code}")
                return False
        else:
            print("❌ 邮箱配置格式错误")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件测试失败: {str(e)}")
        return False

def test_imap_connection():
    """测试IMAP连接"""
    print("\n🔧 测试IMAP连接")
    print("-" * 30)
    
    try:
        sender_email = "<EMAIL>"
        auth_code = "cwnzcpaczwngdgfa"
        
        print(f"📧 邮箱: {sender_email}")
        print(f"🔑 授权码: {auth_code}")
        print("🔗 服务器: imap.qq.com:993")
        
        # 连接IMAP
        mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
        print("✅ IMAP服务器连接成功")
        
        # 登录
        mail.login(sender_email, auth_code)
        print("✅ IMAP登录成功")
        
        # 选择收件箱
        mail.select('INBOX')
        print("✅ 收件箱访问成功")
        
        # 检查邮件
        status, messages = mail.search(None, 'ALL')
        if status == 'OK':
            email_count = len(messages[0].split()) if messages[0] else 0
            print(f"✅ 收件箱邮件数: {email_count}")
        
        mail.logout()
        print("🎉 IMAP连接测试完全成功！")
        return True
        
    except Exception as e:
        print(f"❌ IMAP连接失败: {str(e)}")
        return False

def test_smtp_connection():
    """测试SMTP连接"""
    print("\n🔧 测试SMTP连接")
    print("-" * 30)
    
    try:
        sender_email = "<EMAIL>"
        auth_code = "cwnzcpaczwngdgfa"
        
        print(f"📧 邮箱: {sender_email}")
        print(f"🔑 授权码: {auth_code}")
        print("🔗 服务器: smtp.qq.com:587")
        
        # 连接SMTP
        server = smtplib.SMTP('smtp.qq.com', 587)
        print("✅ SMTP服务器连接成功")
        
        # 启动TLS
        server.starttls()
        print("✅ TLS加密启动成功")
        
        # 登录
        server.login(sender_email, auth_code)
        print("✅ SMTP登录成功")
        
        server.quit()
        print("🎉 SMTP连接测试完全成功！")
        return True
        
    except Exception as e:
        print(f"❌ SMTP连接失败: {str(e)}")
        return False

def test_json_import():
    """测试json模块导入"""
    print("\n🔧 测试json模块导入")
    print("-" * 30)
    
    try:
        import json
        import datetime
        
        # 测试json操作
        test_data = {
            "test": "success",
            "time": datetime.datetime.now().isoformat()
        }
        
        json_str = json.dumps(test_data, ensure_ascii=False)
        parsed_data = json.loads(json_str)
        
        print("✅ json模块导入成功")
        print("✅ json序列化/反序列化正常")
        return True
        
    except Exception as e:
        print(f"❌ json模块测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 最终验证修复效果")
    print("=" * 60)
    
    tests = [
        ("配置文件测试", test_config_file),
        ("IMAP连接测试", test_imap_connection),
        ("SMTP连接测试", test_smtp_connection),
        ("json模块测试", test_json_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
            print(f"✅ {test_name} - 通过")
        else:
            print(f"❌ {test_name} - 失败")
    
    print(f"\n📊 测试结果")
    print("=" * 30)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！修复完全成功！")
        print("✅ 配置文件格式正确")
        print("✅ 授权码配置正确")
        print("✅ IMAP连接正常")
        print("✅ SMTP连接正常")
        print("✅ json模块正常")
        
        print("\n💡 现在可以:")
        print("• 重启邮件程序")
        print("• 正常发送邮件")
        print("• 使用自动回复监控")
        print("• 保存监控设置")
        print("• QQ应急系统自动触发")
        
        print("\n🚀 所有功能都应该正常工作了！")
        
    else:
        print(f"\n⚠️ 仍有 {total - passed} 个问题需要解决")
        
        if passed >= 2:
            print("💡 主要问题已解决，可以尝试重启程序")

if __name__ == "__main__":
    main()
