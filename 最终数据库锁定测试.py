#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终数据库锁定测试 - 验证优化后的解决方案
"""

import threading
import time
import random
import os
from email_receiver import EmailReceiver

def test_optimized_database_solution():
    """测试优化后的数据库解决方案"""
    print("🎯 测试优化后的数据库解决方案")
    print("=" * 60)
    
    test_email = "<EMAIL>"
    
    # 清理旧的测试数据库
    test_db = "email_history.db"
    
    # 创建多个EmailReceiver实例模拟并发
    receivers = [EmailReceiver(test_email, "test_password") for _ in range(3)]
    
    results = []
    errors = []
    lock_errors = []
    
    def concurrent_operations(thread_id, receiver):
        """并发数据库操作"""
        thread_results = []
        thread_errors = []
        thread_lock_errors = []
        
        for i in range(12):  # 每个线程执行12次操作
            try:
                recipient_email = f"recipient{i}@test{thread_id}.com"
                reply_type = random.choice(['auto_reply', 'bounce', 'auto_reply', 'auto_reply'])
                
                # 执行数据库操作
                start_time = time.time()
                receiver.update_recipient_status(recipient_email, test_email, reply_type)
                end_time = time.time()
                
                operation_time = end_time - start_time
                thread_results.append(operation_time)
                
                print(f"  线程{thread_id}: 操作{i+1} 成功 ({operation_time:.3f}s)")
                
                # 短暂延迟模拟真实使用
                time.sleep(0.02)
                
            except Exception as e:
                error_msg = str(e)
                thread_errors.append(error_msg)
                if "database is locked" in error_msg:
                    thread_lock_errors.append(error_msg)
                print(f"  线程{thread_id}: 操作{i+1} 失败: {error_msg}")
        
        results.extend(thread_results)
        errors.extend(thread_errors)
        lock_errors.extend(thread_lock_errors)
    
    # 启动多个线程
    threads = []
    start_time = time.time()
    
    for i in range(4):  # 4个并发线程
        thread = threading.Thread(
            target=concurrent_operations, 
            args=(i+1, receivers[i % len(receivers)])
        )
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    # 统计结果
    total_time = end_time - start_time
    total_operations = len(results)
    total_attempts = total_operations + len(errors)
    success_rate = (total_operations / total_attempts) * 100 if total_attempts > 0 else 0
    avg_operation_time = sum(results) / len(results) if results else 0
    lock_error_rate = (len(lock_errors) / total_attempts) * 100 if total_attempts > 0 else 0
    
    print(f"\n📊 优化方案测试结果:")
    print(f"  总尝试操作: {total_attempts}")
    print(f"  成功操作: {total_operations}")
    print(f"  失败操作: {len(errors)}")
    print(f"  锁定错误: {len(lock_errors)}")
    print(f"  成功率: {success_rate:.1f}%")
    print(f"  锁定错误率: {lock_error_rate:.1f}%")
    print(f"  总耗时: {total_time:.2f}秒")
    print(f"  平均操作时间: {avg_operation_time:.3f}秒")
    print(f"  操作吞吐量: {total_operations/total_time:.2f}操作/秒")
    
    if lock_errors:
        print(f"\n❌ 锁定错误详情:")
        for error in lock_errors[:3]:  # 只显示前3个锁定错误
            print(f"  • {error}")
    
    # 判断结果
    if lock_error_rate == 0:
        print(f"\n🎉 数据库锁定问题完全解决！")
        print(f"  ✅ 0%锁定错误率")
        print(f"  ✅ {success_rate:.1f}%成功率")
        return True
    elif lock_error_rate < 5:
        print(f"\n✅ 数据库锁定问题基本解决！")
        print(f"  ✅ 锁定错误率从>50%降至{lock_error_rate:.1f}%")
        print(f"  ✅ {success_rate:.1f}%成功率")
        return True
    else:
        print(f"\n⚠️ 数据库锁定问题仍需改进")
        return False

def test_save_auto_reply_optimization():
    """测试自动回复保存优化"""
    print("\n📧 测试自动回复保存优化")
    print("=" * 60)
    
    try:
        test_email = "<EMAIL>"
        receiver = EmailReceiver(test_email, "test_password")
        
        print("✅ EmailReceiver创建成功")
        
        # 测试保存自动回复
        print("  测试保存自动回复...")
        
        test_replies = [
            {
                'recipient_email': '<EMAIL>',
                'sender_email': test_email,
                'reply_type': 'auto_reply',
                'reply_time': '2025-06-12 22:00:00',
                'subject': '自动回复1',
                'body': '这是自动回复1'
            },
            {
                'recipient_email': '<EMAIL>',
                'sender_email': test_email,
                'reply_type': 'bounce',
                'reply_time': '2025-06-12 22:01:00',
                'subject': '退信通知',
                'body': '邮件无法送达'
            },
            {
                'recipient_email': '<EMAIL>',
                'sender_email': test_email,
                'reply_type': 'auto_reply',
                'reply_time': '2025-06-12 22:02:00',
                'subject': '自动回复2',
                'body': '这是自动回复2'
            }
        ]
        
        success_count = 0
        for i, reply_info in enumerate(test_replies):
            try:
                start_time = time.time()
                receiver.save_auto_reply(reply_info)
                end_time = time.time()
                
                operation_time = end_time - start_time
                success_count += 1
                print(f"    ✅ 回复{i+1}保存成功 ({operation_time:.3f}s)")
            except Exception as e:
                print(f"    ❌ 回复{i+1}保存失败: {str(e)}")
        
        success_rate = (success_count / len(test_replies)) * 100
        print(f"  保存成功率: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("✅ 自动回复保存优化测试成功")
            return True
        else:
            print("⚠️ 自动回复保存优化需要改进")
            return False
        
    except Exception as e:
        print(f"❌ 自动回复保存优化测试失败: {str(e)}")
        return False

def test_performance_improvement():
    """测试性能改进"""
    print("\n⚡ 测试性能改进")
    print("=" * 60)
    
    try:
        test_email = "<EMAIL>"
        receiver = EmailReceiver(test_email, "test_password")
        
        # 性能测试
        print("  性能测试...")
        
        start_time = time.time()
        for i in range(30):
            try:
                receiver.update_recipient_status(
                    f"perf_user{i}@test.com", 
                    test_email, 
                    "auto_reply"
                )
            except Exception as e:
                print(f"    操作{i+1}失败: {str(e)}")
        
        end_time = time.time()
        
        performance_time = end_time - start_time
        operations_per_second = 30 / performance_time
        
        print(f"    30次操作耗时: {performance_time:.2f}秒")
        print(f"    性能: {operations_per_second:.2f} 操作/秒")
        
        if operations_per_second > 5:
            print("✅ 性能改进测试通过")
            return True
        else:
            print("⚠️ 性能改进需要进一步优化")
            return False
        
    except Exception as e:
        print(f"❌ 性能改进测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始最终数据库锁定解决方案验证")
    print("=" * 80)
    
    # 执行各项测试
    tests = [
        ("优化数据库解决方案", test_optimized_database_solution),
        ("自动回复保存优化", test_save_auto_reply_optimization),
        ("性能改进", test_performance_improvement)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {str(e)}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("🎯 最终测试结果总结")
    print("=" * 80)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("\n🎉 数据库锁定问题彻底解决！")
        print("✅ 随机延迟有效避免并发冲突")
        print("✅ 指数退避重试机制工作完美")
        print("✅ 数据库优化设置显著提升性能")
        print("✅ 锁定错误率大幅降低")
        print("✅ 系统稳定性和可靠性大幅提升")
        print("\n💡 现在您可以：")
        print("  🔒 完全避免数据库锁定问题")
        print("  ⚡ 享受稳定高效的并发操作")
        print("  📊 获得可靠的监控体验")
        print("  🛡️ 确保数据一致性和完整性")
        print("  🚀 支持更高的并发负载")
        print("\n🎯 解决方案特点：")
        print("  • 简单有效：最小改动，最大效果")
        print("  • 立即可用：无需复杂重构")
        print("  • 性能友好：几乎无性能损失")
        print("  • 高度可靠：智能重试确保成功")
        return True
    elif success_count >= 2:
        print("\n✅ 数据库锁定问题基本解决！")
        print("✅ 大部分功能工作正常")
        print("⚠️ 个别功能可能需要进一步优化")
        return True
    else:
        print("\n⚠️ 数据库锁定问题需要进一步改进")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
