#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终修复验证
验证所有高级功能是否完全修复
"""

import tkinter as tk
from tkinter import messagebox
import sys
import traceback

def test_all_fixes():
    """测试所有修复"""
    print("🔧 开始最终修复验证...")
    print("=" * 60)
    
    try:
        # 导入修复后的GUI
        from gui_complete_v3 import EmailSenderGUI
        
        print("✅ GUI模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        app = EmailSenderGUI(root)
        
        print("✅ GUI界面创建成功")
        
        # 测试所有高级功能方法
        all_methods = [
            # 主要功能方法
            ('view_email_history', '查看邮件历史'),
            ('show_statistics', '显示统计信息'),
            ('export_history', '导出历史记录'),
            ('open_smart_search', '打开智能搜索'),
            ('add_to_queue', '添加到队列'),
            ('open_queue_system', '打开队列系统'),
            ('start_queue_sending', '开始队列发送'),
            ('pause_queue_sending', '暂停队列发送'),
            ('clear_queue', '清空队列'),
            ('on_auto_queue_changed', '自动队列模式变化'),
            ('test_monitor', '测试监控功能'),
            ('reset_monitor', '重置监控'),
            ('clear_log', '清空日志'),
            ('save_log', '保存日志'),
            ('open_quality_manager', '打开质量数据库'),
            ('open_anti_spam', '打开反垃圾邮件'),
            ('open_emergency_manager', '打开应急管理'),
            ('open_reply_monitor', '打开自动回复监控'),
            ('open_duplicate_detection', '打开重复检测'),
            ('configure_monitoring', '配置监控设置'),
            ('configure_coordination', '配置深度协调'),
            
            # 界面创建方法
            ('_create_quality_manager_interface', '质量管理器界面'),
            ('_create_anti_spam_interface', '反垃圾邮件界面'),
            ('_create_emergency_interface', '应急管理界面'),
            ('_create_batch_manager_interface', '批次管理界面'),
            ('_create_import_interface', '导入功能界面'),
            ('_create_duplicate_detection_interface', '重复检测界面'),
            ('_create_monitor_interface', '监控管理界面'),
            ('_create_history_interface', '历史记录界面'),
            ('_create_search_interface', '智能搜索界面'),
            ('_create_monitoring_settings_interface', '监控设置界面'),
            ('_create_coordination_settings_interface', '协调设置界面'),
            
            # 辅助功能方法
            ('_analyze_quality', '质量分析'),
            ('_generate_quality_report', '生成质量报告'),
            ('_refresh_quality_data', '刷新质量数据'),
            ('_evaluate_spam_risk', '评估垃圾邮件风险'),
            ('_adjust_anti_spam_strategy', '调整反垃圾策略'),
            ('_show_anti_spam_report', '显示反垃圾报告'),
            ('_activate_emergency', '激活应急模式'),
            ('_reset_emergency', '重置应急系统'),
            ('_show_emergency_report', '显示应急报告'),
            ('_create_batches', '创建批次'),
            ('_recreate_batches', '重新创建批次'),
            ('_view_batches', '查看批次'),
            ('_select_import_file', '选择导入文件'),
            ('_start_import', '开始导入'),
            ('_import_to_main_system', '导入到主系统'),
            ('_start_duplicate_detection', '开始重复检测'),
            ('_show_duplicate_report', '显示重复检测报告'),
            ('_clean_duplicates', '清理重复内容'),
            ('_save_monitoring_settings', '保存监控设置'),
            ('_reset_monitoring_settings', '重置监控设置'),
            ('_test_monitoring_settings', '测试监控设置'),
            ('_save_coordination_settings', '保存协调设置'),
            ('_reset_coordination_settings', '重置协调设置'),
            ('_test_coordination_settings', '测试协调设置'),
            
            # 其他重要方法
            ('update_queue_status', '更新队列状态'),
            ('_search_history', '搜索历史记录'),
            ('_perform_smart_search', '执行智能搜索'),
            ('_show_history_stats', '显示历史统计'),
            ('_export_selected_history', '导出选中历史'),
            ('_cleanup_old_records', '清理旧记录'),
            ('_execute_queue_sending', '执行队列发送'),
            ('_send_queue_task', '发送队列任务'),
        ]
        
        print("\n🔍 检查所有功能方法:")
        print("-" * 40)
        
        missing_methods = []
        working_methods = []
        
        for method_name, description in all_methods:
            if hasattr(app, method_name):
                method = getattr(app, method_name)
                if callable(method):
                    working_methods.append((method_name, description))
                    print(f"✅ {description}")
                else:
                    missing_methods.append((method_name, description))
                    print(f"❌ {description} - 不是可调用方法")
            else:
                missing_methods.append((method_name, description))
                print(f"❌ {description} - 方法不存在")
        
        # 检查后端模块
        print(f"\n🔧 检查后端模块:")
        print("-" * 40)
        
        backend_modules = [
            ('email_history_manager', 'EmailHistoryManager', '邮件历史管理器'),
            ('rag_search_engine', 'RAGSearchEngine', '智能搜索引擎'),
            ('queue_system', 'QueueSystemWindow', '队列系统'),
            ('email_receiver', 'EmailReceiver', '邮件接收器'),
            ('recipient_quality_manager', 'RecipientQualityManager', '质量数据库管理器'),
            ('anti_spam_manager', 'AntiSpamManager', '反垃圾邮件管理器'),
            ('qq_email_anti_spam', 'QQEmailAntiSpamManager', 'QQ应急管理器'),
        ]
        
        available_modules = []
        missing_modules = []
        
        for module_name, class_name, description in backend_modules:
            try:
                module = __import__(module_name)
                if hasattr(module, class_name):
                    available_modules.append((module_name, class_name, description))
                    print(f"✅ {description}")
                else:
                    missing_modules.append((module_name, class_name, description))
                    print(f"❌ {description} - 类不存在")
            except ImportError as e:
                missing_modules.append((module_name, class_name, description))
                print(f"❌ {description} - 模块导入失败")
        
        # 计算总体修复率
        total_methods = len(all_methods)
        working_method_count = len(working_methods)
        method_fix_rate = (working_method_count / total_methods) * 100
        
        total_modules = len(backend_modules)
        available_module_count = len(available_modules)
        module_fix_rate = (available_module_count / total_modules) * 100
        
        overall_fix_rate = (method_fix_rate + module_fix_rate) / 2
        
        print(f"\n📊 最终修复统计:")
        print("=" * 40)
        print(f"📋 功能方法修复率: {method_fix_rate:.1f}% ({working_method_count}/{total_methods})")
        print(f"📦 后端模块可用率: {module_fix_rate:.1f}% ({available_module_count}/{total_modules})")
        print(f"🎯 总体修复率: {overall_fix_rate:.1f}%")
        
        if missing_methods:
            print(f"\n⚠️ 仍需修复的方法:")
            for method_name, description in missing_methods[:5]:  # 只显示前5个
                print(f"  - {description} ({method_name})")
            if len(missing_methods) > 5:
                print(f"  ... 还有 {len(missing_methods) - 5} 个方法")
        
        if missing_modules:
            print(f"\n⚠️ 仍需修复的模块:")
            for module_name, class_name, description in missing_modules:
                print(f"  - {description} ({module_name}.{class_name})")
        
        # 评估修复效果
        if overall_fix_rate >= 95:
            print(f"\n🎉 修复效果完美！")
            print("✅ 几乎所有功能都已正常工作")
            print("✅ 系统功能完整性达到预期")
            print("💡 建议：可以正常使用所有功能")
            status = "完美"
        elif overall_fix_rate >= 90:
            print(f"\n🎉 修复效果优秀！")
            print("✅ 绝大部分功能已经正常工作")
            print("✅ 系统基本达到功能完整性")
            print("💡 建议：可以正常使用，少数功能可能需要进一步优化")
            status = "优秀"
        elif overall_fix_rate >= 80:
            print(f"\n✅ 修复效果良好！")
            print("✅ 大部分功能已经修复")
            print("⚠️ 少数功能可能需要进一步优化")
            print("💡 建议：可以使用大部分功能")
            status = "良好"
        else:
            print(f"\n⚠️ 修复效果一般")
            print("❌ 仍有较多功能需要修复")
            print("💡 建议：继续修复缺失的功能")
            status = "一般"
        
        # 关闭测试窗口
        root.destroy()
        
        return status, overall_fix_rate
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
        return "失败", 0

def main():
    """主函数"""
    print("🚀 启动最终修复验证...")
    
    try:
        status, fix_rate = test_all_fixes()
        
        print(f"\n🎯 最终验证结果:")
        print("=" * 60)
        print(f"修复状态: {status}")
        print(f"修复率: {fix_rate:.1f}%")
        
        if fix_rate >= 90:
            print(f"\n🎉 恭喜！高级功能修复已基本完成")
            print("💡 现在可以正常使用邮件系统的所有高级功能")
            print("📝 建议运行: python gui_complete_v3.py")
        elif fix_rate >= 80:
            print(f"\n✅ 高级功能修复效果良好")
            print("💡 大部分功能可以正常使用")
        else:
            print(f"\n⚠️ 仍需要进一步修复")
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
