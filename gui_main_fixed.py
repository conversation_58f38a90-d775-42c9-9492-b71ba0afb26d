#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统GUI主文件 - 修复拼写警告版本
解决所有IDE拼写检查警告问题
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import datetime
import json
import time
import math
import sys
import traceback
from typing import List, Dict, Optional

# 尝试导入深度协调系统
try:
    from deep_coordination import get_coordinator, SystemEvent
    DEEP_COORDINATION_AVAILABLE = True
except ImportError:
    DEEP_COORDINATION_AVAILABLE = False

class EmailSenderGUI:
    """邮件发送系统GUI - 修复拼写警告版本"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.setup_theme()
        self.create_widgets()
        self.setup_deep_coordination()
        
        # 应用字体颜色修复
        self.apply_font_color_fixes()
        
        # 延迟初始化日志
        self.root.after(1000, self.initialize_log)
        
    def setup_window(self):
        """设置窗口基本属性"""
        self.root.title("📧 智能邮件系统 v3.0 - 深度协调版")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#f9fafb')
        
        # 窗口居中
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 1600) // 2
        y = (screen_height - 1000) // 2
        self.root.geometry(f"1600x1000+{x}+{y}")
        
        # 设置最小尺寸
        self.root.minsize(1200, 800)
        
        # 设置窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
    def setup_variables(self):
        """初始化变量"""
        # 基础变量
        self.send_mode = tk.StringVar(value="standard")
        self.add_personalization = tk.BooleanVar(value=True)
        self.auto_start_reply_monitoring = tk.BooleanVar(value=False)
        self.auto_queue_mode = tk.BooleanVar(value=True)
        
        # 状态变量
        self.is_sending = False
        self.should_stop = False
        self.should_pause = False
        self.sent_emails = []
        self.email_queue = []
        self.attachments = []
        
        # 深度协调相关
        self.coordinator = None
        
    def setup_theme(self):
        """设置主题样式 - 修复拼写警告"""
        style = ttk.Style()
        
        # 使用现代主题
        try:
            style.theme_use('clam')
        except:
            style.theme_use('default')
        
        # 颜色方案 - 柔和优雅的配色
        colors = {
            'primary': '#3b82f6',      # 柔和蓝色
            'secondary': '#64748b',    # 柔和灰蓝
            'success': '#10b981',      # 柔和绿色
            'warning': '#f59e0b',      # 柔和橙色
            'danger': '#ef4444',       # 柔和红色
            'light': '#f8fafc',        # 极浅灰
            'dark': '#374151',         # 柔和深灰
            'hover': '#60a5fa',        # 悬停蓝色
            'button_text': '#ffffff'   # 按钮文字颜色
        }
        
        # 配置按钮样式 - 移除拼写警告的参数
        style.configure('Primary.TButton',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       foreground=colors['button_text'],
                       background=colors['primary'])
        
        style.configure('Success.TButton',
                       font=('Microsoft YaHei UI', 9, 'bold'),
                       foreground=colors['button_text'],
                       background=colors['success'])
        
        style.configure('Warning.TButton',
                       font=('Microsoft YaHei UI', 9, 'bold'),
                       foreground=colors['button_text'],
                       background=colors['warning'])
        
        style.configure('Danger.TButton',
                       font=('Microsoft YaHei UI', 9, 'bold'),
                       foreground=colors['button_text'],
                       background=colors['danger'])
        
        # 配置标签框样式
        style.configure('Modern.TLabelframe',
                       background=colors['light'])
        
        style.configure('Modern.TLabelframe.Label',
                       font=('Microsoft YaHei UI', 11, 'bold'),
                       foreground=colors['primary'],
                       background=colors['light'])
        
        # 配置输入框样式
        style.configure('Modern.TEntry',
                       font=('Microsoft YaHei UI', 10),
                       foreground=colors['dark'],
                       fieldbackground='white')
        
        # 配置复选框样式
        style.configure('Modern.TCheckbutton',
                       font=('Microsoft YaHei UI', 9),
                       background=colors['light'],
                       foreground=colors['dark'])
        
        # 配置单选按钮样式
        style.configure('Modern.TRadiobutton',
                       font=('Microsoft YaHei UI', 9),
                       background=colors['light'],
                       foreground=colors['dark'])
        
    def apply_font_color_fixes(self):
        """应用字体颜色修复 - 确保所有文字都清晰可见"""
        # 延迟执行修复，确保所有组件都已创建
        self.root.after(500, self._apply_delayed_color_fixes)
        
    def _apply_delayed_color_fixes(self):
        """延迟应用颜色修复"""
        try:
            # 修复根窗口背景
            self.root.configure(bg='#f9fafb')
            
            # 递归修复所有组件
            self._fix_widget_colors(self.root)
            
            # 如果日志组件存在，记录修复信息
            if hasattr(self, 'log_text'):
                self.log_message("🎨 字体颜色已修复 - 所有按钮文字现在清晰可见")
                
        except Exception as e:
            print(f"字体颜色修复失败: {e}")
            
    def _fix_widget_colors(self, widget):
        """递归修复组件颜色"""
        try:
            widget_class = widget.winfo_class()
            
            # 修复不同类型的组件
            if widget_class == 'Frame':
                widget.configure(bg='#f9fafb')
            elif widget_class == 'Label':
                widget.configure(bg='#f9fafb', fg='#374151')
            elif widget_class == 'Button':
                # 根据按钮文本设置柔和颜色
                try:
                    text = widget.cget('text')
                    if any(word in text for word in ['发送', '开始', '启动', '添加', '🚀', '➕']):
                        widget.configure(bg='#3b82f6', fg='white',
                                       font=('Microsoft YaHei UI', 9, 'bold'))
                    elif any(word in text for word in ['停止', '删除', '清空', '🗑️', '⏹️']):
                        widget.configure(bg='#ef4444', fg='white',
                                       font=('Microsoft YaHei UI', 9, 'bold'))
                    elif any(word in text for word in ['暂停', '警告', '⏸️', '⚠️']):
                        widget.configure(bg='#f59e0b', fg='white',
                                       font=('Microsoft YaHei UI', 9, 'bold'))
                    elif any(word in text for word in ['恢复', '成功', '▶️', '✅']):
                        widget.configure(bg='#10b981', fg='white',
                                       font=('Microsoft YaHei UI', 9, 'bold'))
                    else:
                        widget.configure(bg='#6b7280', fg='white',
                                       font=('Microsoft YaHei UI', 9))
                except:
                    # 如果获取文本失败，使用默认样式
                    widget.configure(bg='#6b7280', fg='white',
                                   font=('Microsoft YaHei UI', 9))
            elif widget_class == 'Entry':
                widget.configure(bg='white', fg='#111827')
            elif widget_class == 'Text':
                widget.configure(bg='white', fg='#111827')
            elif widget_class == 'Listbox':
                widget.configure(bg='white', fg='#111827')
            elif widget_class in ['Checkbutton', 'Radiobutton']:
                widget.configure(bg='#f9fafb', fg='#374151')
                
        except Exception as e:
            # 忽略配置错误，继续处理其他组件
            pass
        
        # 递归处理子组件
        try:
            for child in widget.winfo_children():
                self._fix_widget_colors(child)
        except:
            pass
