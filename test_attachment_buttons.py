#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试附件管理按钮是否正确显示
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_attachment_buttons():
    """测试附件管理按钮"""
    print("🔧 测试附件管理按钮显示")
    print("=" * 50)
    
    try:
        # 导入GUI模块
        import gui_main
        print("✅ gui_main模块导入成功")
        
        # 创建测试GUI实例
        root = tk.Tk()
        root.title("附件按钮测试")
        root.geometry("400x300")
        
        app = gui_main.EmailSenderGUI(root)
        print("✅ EmailSenderGUI实例创建成功")
        
        # 检查附件列表框是否存在
        if hasattr(app, 'attachment_listbox'):
            print("✅ 附件列表框存在")
            
            # 检查列表框的父容器
            parent = app.attachment_listbox.master
            print(f"✅ 附件列表框父容器: {parent}")
            
            # 检查父容器中的所有子组件
            children = parent.winfo_children()
            print(f"📋 父容器中的子组件数量: {len(children)}")
            
            button_count = 0
            for i, child in enumerate(children):
                widget_type = child.winfo_class()
                print(f"  {i+1}. {widget_type}: {child}")
                
                if widget_type == 'TButton':
                    button_count += 1
                    try:
                        button_text = child.cget('text')
                        print(f"     按钮文本: '{button_text}'")
                    except:
                        print(f"     按钮文本: 无法获取")
            
            print(f"🔘 找到按钮数量: {button_count}")
            
            if button_count >= 3:
                print("✅ 附件管理按钮显示正常！")
                return True
            else:
                print("❌ 附件管理按钮数量不足！")
                return False
        else:
            print("❌ 附件列表框不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def create_simple_attachment_test():
    """创建简单的附件管理测试界面"""
    print("\n🧪 创建简单测试界面")
    print("-" * 50)
    
    try:
        # 创建测试窗口
        test_window = tk.Tk()
        test_window.title("附件管理测试")
        test_window.geometry("400x300")
        
        # 创建附件管理区域
        attachment_frame = ttk.LabelFrame(test_window, text="📎 附件管理", padding="8")
        attachment_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # 附件列表
        attachment_listbox = tk.Listbox(attachment_frame, height=3, font=('Microsoft YaHei UI', 9))
        attachment_listbox.pack(fill=tk.X, pady=(0, 8))
        
        # 测试按钮
        def test_add():
            print("🔘 添加附件按钮被点击")
            attachment_listbox.insert(tk.END, "测试附件.txt")
            
        def test_remove():
            print("🔘 删除附件按钮被点击")
            selection = attachment_listbox.curselection()
            if selection:
                attachment_listbox.delete(selection[0])
                
        def test_clear():
            print("🔘 清空附件按钮被点击")
            attachment_listbox.delete(0, tk.END)
        
        # 按钮
        ttk.Button(attachment_frame, text="📁 添加附件", command=test_add).pack(fill=tk.X, pady=2)
        ttk.Button(attachment_frame, text="🗑️ 删除附件", command=test_remove).pack(fill=tk.X, pady=2)
        ttk.Button(attachment_frame, text="🧹 清空附件", command=test_clear).pack(fill=tk.X, pady=2)
        
        # 说明文本
        info_label = ttk.Label(test_window, text="这是附件管理的测试界面\n按钮应该都能正常显示和点击", 
                              font=('Microsoft YaHei UI', 10), foreground='blue')
        info_label.pack(pady=20)
        
        print("✅ 测试界面创建成功")
        print("💡 如果您能看到三个按钮，说明代码是正确的")
        
        # 运行测试界面
        test_window.mainloop()
        
    except Exception as e:
        print(f"❌ 创建测试界面失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试附件管理按钮...")
    
    # 测试按钮是否存在
    success = test_attachment_buttons()
    
    if not success:
        print("\n⚠️ 主程序中的按钮可能有问题，创建独立测试界面...")
        create_simple_attachment_test()
    else:
        print("\n🎉 附件管理按钮测试通过！")
        
    print("\n" + "=" * 50)
    print("测试完成")
