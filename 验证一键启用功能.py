#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证一键启用全功能模式
"""

import tkinter as tk
import time
import threading
from gui_main import EmailSenderGUI

def test_one_click_enable():
    """测试一键启用功能"""
    print("🚀 验证一键启用全功能模式")
    print("=" * 60)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建GUI实例
        app = EmailSenderGUI(root)
        
        print("✅ GUI创建成功，无错误")
        
        # 模拟填写邮箱
        test_email = "<EMAIL>"
        app.sender_email.delete(0, tk.END)
        app.sender_email.insert(0, test_email)
        
        print(f"✅ 测试邮箱已填写: {test_email}")
        
        # 检查一键启用方法的各个步骤
        print("\n🔧 测试各个启用步骤:")
        
        steps = [
            ("初始化系统组件", app._init_all_components),
            ("启用自动回复监控", app._enable_auto_reply_monitoring),
            ("初始化质量数据库", app._enable_quality_database),
            ("配置反垃圾邮件", app._enable_anti_spam),
            ("激活QQ应急管理", app._enable_qq_emergency),
            ("启动智能队列", app._enable_smart_queue),
            ("激活深度协调", app._enable_deep_coordination),
            ("完成全功能配置", app._finalize_all_features)
        ]
        
        all_success = True
        
        for step_name, step_func in steps:
            try:
                result = step_func(test_email)
                if result['success']:
                    print(f"  ✅ {step_name} - 成功")
                else:
                    print(f"  ❌ {step_name} - 失败: {result['error']}")
                    all_success = False
            except Exception as e:
                print(f"  ❌ {step_name} - 异常: {str(e)}")
                all_success = False
        
        # 检查功能状态
        print("\n📊 检查功能状态:")
        if hasattr(app, 'feature_status'):
            for feature, status in app.feature_status.items():
                status_icon = "✅" if status else "❌"
                print(f"  {status_icon} {feature}: {status}")
        else:
            print("  ❌ feature_status 属性不存在")
            all_success = False
        
        # 检查全功能状态
        print("\n🎯 检查全功能状态:")
        if hasattr(app, 'all_features_enabled'):
            if app.all_features_enabled:
                print("  ✅ 全功能模式已启用")
            else:
                print("  ❌ 全功能模式未启用")
        else:
            print("  ❌ all_features_enabled 属性不存在")
        
        # 检查状态显示方法
        print("\n🎨 测试状态显示方法:")
        try:
            app._update_all_features_status_display(True)
            print("  ✅ 状态显示更新成功")
        except Exception as e:
            print(f"  ❌ 状态显示更新失败: {str(e)}")
            all_success = False
        
        # 检查配置文件
        print("\n📁 检查配置文件:")
        import os
        config_files = [
            'auto_reply_config.json',
            'all_features_config.json'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"  ✅ {config_file} 已创建")
            else:
                print(f"  ⚠️ {config_file} 未创建（可能正常）")
        
        # 最终结果
        print("\n" + "=" * 60)
        if all_success:
            print("🎉 一键启用功能验证成功！")
            print("✅ 所有功能模块都能正常启用")
            print("✅ 没有发现错误或异常")
            print("✅ 功能状态正确跟踪")
            print("✅ 配置保存正常工作")
        else:
            print("⚠️ 一键启用功能存在问题")
            print("请检查上述错误信息")
        
        print("\n💡 使用建议:")
        print("1. 启动程序后填写有效的QQ邮箱地址")
        print("2. 点击'🚀 一键启用全功能'按钮")
        print("3. 等待启用过程完成")
        print("4. 查看状态栏确认功能已启用")
        print("5. 开始享受完整的邮件发送体验")
        
        # 关闭窗口
        root.destroy()
        
        return all_success
        
    except Exception as e:
        print(f"❌ 验证过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_one_click_enable()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 验证完成：一键启用功能完全正常！")
        exit(0)
    else:
        print("❌ 验证失败：一键启用功能存在问题")
        exit(1)
