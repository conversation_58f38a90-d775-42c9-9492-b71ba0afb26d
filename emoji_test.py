# -*- coding: utf-8 -*-
"""
测试Emoji和特殊字符支持
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

def test_emoji_support():
    """测试Emoji和特殊字符支持"""
    root = tk.Tk()
    root.title("Emoji和特殊字符测试")
    root.geometry("600x400")
    
    # 主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)
    main_frame.columnconfigure(0, weight=1)
    main_frame.rowconfigure(1, weight=1)
    
    # 标题
    title_label = ttk.Label(main_frame, text="Emoji和特殊字符测试", 
                           font=('Arial', 14, 'bold'))
    title_label.grid(row=0, column=0, pady=(0, 10))
    
    # 测试文本框
    text_widget = scrolledtext.ScrolledText(main_frame, width=60, height=15,
                                           font=('Microsoft YaHei UI', 10),
                                           wrap=tk.WORD)
    text_widget.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
    
    # 预设测试内容
    test_content = """测试各种字符支持：

1. 基本Emoji：
😀 😃 😄 😁 😆 😅 😂 🤣 😊 😇
🙂 🙃 😉 😌 😍 🥰 😘 😗 😙 😚
😋 😛 😝 😜 🤪 🤨 🧐 🤓 😎 🤩

2. 表情符号：
❤️ 💛 💚 💙 💜 🖤 🤍 🤎 💔 ❣️
💕 💞 💓 💗 💖 💘 💝 💟 ☮️ ✝️

3. 手势Emoji：
👍 👎 👌 🤏 ✌️ 🤞 🤟 🤘 🤙 👈
👉 👆 🖕 👇 ☝️ 👋 🤚 🖐️ ✋ 🖖

4. 动物Emoji：
🐶 🐱 🐭 🐹 🐰 🦊 🐻 🐼 🐨 🐯
🦁 🐮 🐷 🐽 🐸 🐵 🙈 🙉 🙊 🐒

5. 食物Emoji：
🍎 🍊 🍋 🍌 🍉 🍇 🍓 🫐 🍈 🍒
🍑 🥭 🍍 🥥 🥝 🍅 🍆 🥑 🥦 🥬

6. 颜文字：
(╯°□°）╯︵ ┻━┻
¯\\_(ツ)_/¯
(ಠ_ಠ)
(◕‿◕)
ಠ╭╮ಠ
(｡◕‿◕｡)
(⌐■_■)
( ͡° ͜ʖ ͡°)

7. 特殊符号：
★ ☆ ✦ ✧ ✩ ✪ ✫ ✬ ✭ ✮ ✯ ✰ ✱ ✲ ✳ ✴ ✵ ✶ ✷ ✸
♠ ♣ ♥ ♦ ♤ ♧ ♡ ♢
♩ ♪ ♫ ♬ ♭ ♮ ♯
℃ ℉ ° ′ ″ ‰ ‱
© ® ™ ℗ ℠

8. 箭头符号：
← ↑ → ↓ ↔ ↕ ↖ ↗ ↘ ↙
⇐ ⇑ ⇒ ⇓ ⇔ ⇕ ⇖ ⇗ ⇘ ⇙
⟵ ⟶ ⟷ ⟸ ⟹ ⟺

9. 数学符号：
± × ÷ ∞ ∝ ∴ ∵ ∶ ∷ ∸ ∹ ∺ ∻ ∼ ∽ ∾ ∿
≈ ≠ ≡ ≢ ≤ ≥ ≦ ≧ ≨ ≩ ≪ ≫ ≬ ≭ ≮ ≯
∈ ∉ ∊ ∋ ∌ ∍ ∎ ∏ ∐ ∑ − ∓ ∔ ∕ ∖ ∗

10. 中文特殊字符：
【】〖〗《》「」『』〔〕（）
、。，；：？！…—～·
""''‚„‹›«»
①②③④⑤⑥⑦⑧⑨⑩
㈠㈡㈢㈣㈤㈥㈦㈧㈨㈩
"""
    
    text_widget.insert(tk.END, test_content)
    
    # 按钮框架
    btn_frame = ttk.Frame(main_frame)
    btn_frame.grid(row=2, column=0, pady=10)
    
    def get_content():
        content = text_widget.get(1.0, tk.END)
        messagebox.showinfo("获取的内容", f"内容长度: {len(content)} 字符\n\n前100个字符:\n{content[:100]}...")
    
    def clear_content():
        text_widget.delete(1.0, tk.END)
    
    def reset_content():
        text_widget.delete(1.0, tk.END)
        text_widget.insert(tk.END, test_content)
    
    ttk.Button(btn_frame, text="获取内容", command=get_content).pack(side=tk.LEFT, padx=5)
    ttk.Button(btn_frame, text="清空内容", command=clear_content).pack(side=tk.LEFT, padx=5)
    ttk.Button(btn_frame, text="重置内容", command=reset_content).pack(side=tk.LEFT, padx=5)
    ttk.Button(btn_frame, text="关闭", command=root.destroy).pack(side=tk.LEFT, padx=5)
    
    # 说明标签
    info_label = ttk.Label(main_frame, 
                          text="请测试在文本框中输入各种Emoji、颜文字和特殊字符，确认显示和输入正常",
                          font=('Arial', 9), foreground='gray')
    info_label.grid(row=3, column=0, pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    test_emoji_support()
