=== 深度调试日志 ===
启动时间: 2025/6/12 11:30:35

当前目录: E:\自动发邮件

使用Python命令: python

=== 方法1: 测试Python程序语法 ===
语法检查命令: cmd.exe /c "cd /d "E:\自动发邮件" && python -m py_compile gui_main.py 2>&1"
语法检查返回码: 0
成功: Python程序语法正确

=== 方法2: 使用Exec方法启动 ===
启动命令: cmd.exe /c "cd /d "E:\自动发邮件" && python gui_main.py 2>&1"
进程已启动，等待输出...
等待第 1 秒...
标准输出: 2025-06-12 11:30:38,390 - INFO - ==================================================
2025-06-12 11:30:38,390 - INFO - 邮件系统启动 - 详细日志记录已启用
2025-06-12 11:30:38,390 - INFO - ==================================================
2025-06-12 11:30:38,395 - INFO - 初始化邮件历史记录管理器，数据库路径: email_history.db
2025-06-12 11:30:38,398 - INFO - 邮件历史记录数据库初始化成功
2025-06-12 11:30:38,398 - INFO - 初始化RAG搜索引擎，数据库路径: email_history.db
Building prefix dict from the default dictionary ...
2025-06-12 11:30:38,398 - DEBUG - Building prefix dict from the default dictionary ...
Loading model from cache E:\九猫临时文件\jieba.cache
2025-06-12 11:30:38,400 - DEBUG - Loading model from cache E:\九猫临时文件\jieba.cache
Loading model cost 0.550 seconds.
2025-06-12 11:30:38,948 - DEBUG - Loading model cost 0.550 seconds.
Prefix dict has been built successfully.
2025-06-12 11:30:38,948 - DEBUG - Prefix dict has been built successfully.
2025-06-12 11:30:38,948 - INFO - 中文分词初始化成功
2025-06-12 11:30:39,870 - CRITICAL - 未捕获的异常:
Traceback (most recent call last):
  File "E:\自动发邮件\gui_main.py", line 7564, in <module>
    main()
    ~~~~^^
  File "E:\自动发邮件\gui_main.py", line 7553, in main
    EmailSenderGUI(root)
    ~~~~~~~~~~~~~~^^^^^^
  File "E:\自动发邮件\gui_main.py", line 84, in __init__
    self.create_widgets()
    ~~~~~~~~~~~~~~~~~~~^^
  File "E:\自动发邮件\gui_main.py", line 489, in create_widgets
    command=self.open_system_coordinator).pack(side=tk.LEFT, padx=5)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'EmailSenderGUI' object has no attribute 'open_system_coordinator'
2025-06-12 11:30:39,921 - ERROR - ❌ 系统错误: AttributeError: 'EmailSenderGUI' object has no attribute 'open_system_coordinator'

进程已结束，退出码: 1

=== 方法3: 检查Python进程 ===
没有发现Python进程在运行

=== 调试完成 ===
完成时间: 2025/6/12 11:30:45
