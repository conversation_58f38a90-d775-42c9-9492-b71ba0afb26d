#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试监控设置保存和加载功能
"""

import tkinter as tk
import json
import os
import time
from gui_main import EmailSenderGUI

def test_monitor_settings_save_load():
    """测试监控设置的保存和加载"""
    print("🔧 测试监控设置保存和加载功能")
    print("=" * 60)
    
    try:
        # 清理旧的设置文件
        if os.path.exists('monitor_settings.json'):
            os.remove('monitor_settings.json')
            print("✅ 清理旧设置文件")
        
        # 创建主窗口
        root = tk.Tk()
        
        # 创建GUI实例
        app = EmailSenderGUI(root)
        
        print("✅ GUI创建成功")
        
        # 测试默认设置加载
        print(f"\n📋 测试默认设置加载:")
        default_settings = app._load_monitor_settings()
        print(f"  默认设置: {default_settings}")
        
        expected_defaults = {
            'check_interval': '10',
            'monitor_duration': '2',
            'auto_start': True
        }
        
        defaults_correct = True
        for key, expected_value in expected_defaults.items():
            if default_settings.get(key) != expected_value:
                print(f"  ❌ {key}: 期望 {expected_value}, 实际 {default_settings.get(key)}")
                defaults_correct = False
            else:
                print(f"  ✅ {key}: {default_settings.get(key)}")
        
        if defaults_correct:
            print("  ✅ 默认设置加载正确")
        else:
            print("  ❌ 默认设置加载有误")
            return False
        
        # 测试设置保存
        print(f"\n💾 测试设置保存:")
        test_settings = {
            'check_interval': '5',
            'monitor_duration': '4',
            'auto_start': False
        }
        
        app._save_monitor_settings(
            test_settings['check_interval'],
            test_settings['monitor_duration'],
            test_settings['auto_start']
        )
        
        print(f"  保存测试设置: {test_settings}")
        
        # 验证文件是否创建
        if os.path.exists('monitor_settings.json'):
            print("  ✅ 设置文件已创建")
            
            # 读取文件内容
            with open('monitor_settings.json', 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            print(f"  文件内容: {saved_data}")
            
            # 验证保存的内容
            save_correct = True
            for key, expected_value in test_settings.items():
                if saved_data.get(key) != expected_value:
                    print(f"  ❌ {key}: 期望 {expected_value}, 实际 {saved_data.get(key)}")
                    save_correct = False
                else:
                    print(f"  ✅ {key}: {saved_data.get(key)}")
            
            if save_correct:
                print("  ✅ 设置保存正确")
            else:
                print("  ❌ 设置保存有误")
                return False
        else:
            print("  ❌ 设置文件未创建")
            return False
        
        # 测试设置加载
        print(f"\n📂 测试设置加载:")
        loaded_settings = app._load_monitor_settings()
        print(f"  加载的设置: {loaded_settings}")
        
        # 验证加载的内容
        load_correct = True
        for key, expected_value in test_settings.items():
            if loaded_settings.get(key) != expected_value:
                print(f"  ❌ {key}: 期望 {expected_value}, 实际 {loaded_settings.get(key)}")
                load_correct = False
            else:
                print(f"  ✅ {key}: {loaded_settings.get(key)}")
        
        if load_correct:
            print("  ✅ 设置加载正确")
        else:
            print("  ❌ 设置加载有误")
            return False
        
        # 测试设置在GUI中的应用
        print(f"\n🖥️ 测试GUI中的设置应用:")
        
        # 模拟打开监控窗口（不实际显示）
        test_email = "<EMAIL>"
        app.sender_email.delete(0, tk.END)
        app.sender_email.insert(0, test_email)
        
        # 设置收件人
        app.recipient_emails.delete(1.0, tk.END)
        app.recipient_emails.insert(1.0, test_email)
        
        print("  ✅ 设置测试邮箱和收件人")
        
        # 关闭窗口
        root.destroy()
        
        # 测试设置文件的持久性
        print(f"\n🔄 测试设置持久性:")
        
        # 重新创建GUI实例
        root2 = tk.Tk()
        app2 = EmailSenderGUI(root2)
        
        # 再次加载设置
        persistent_settings = app2._load_monitor_settings()
        print(f"  重新加载的设置: {persistent_settings}")
        
        # 验证持久性
        persistent_correct = True
        for key, expected_value in test_settings.items():
            if persistent_settings.get(key) != expected_value:
                print(f"  ❌ {key}: 期望 {expected_value}, 实际 {persistent_settings.get(key)}")
                persistent_correct = False
            else:
                print(f"  ✅ {key}: {persistent_settings.get(key)}")
        
        if persistent_correct:
            print("  ✅ 设置持久性正确")
        else:
            print("  ❌ 设置持久性有误")
            return False
        
        # 关闭第二个窗口
        root2.destroy()
        
        # 测试设置合并功能
        print(f"\n🔗 测试设置合并功能:")
        
        # 创建不完整的设置文件
        incomplete_settings = {
            'check_interval': '15'
            # 缺少 monitor_duration 和 auto_start
        }
        
        with open('monitor_settings.json', 'w', encoding='utf-8') as f:
            json.dump(incomplete_settings, f, ensure_ascii=False, indent=2)
        
        print(f"  创建不完整设置: {incomplete_settings}")
        
        # 创建新的GUI实例测试合并
        root3 = tk.Tk()
        app3 = EmailSenderGUI(root3)
        
        merged_settings = app3._load_monitor_settings()
        print(f"  合并后设置: {merged_settings}")
        
        # 验证合并结果
        expected_merged = {
            'check_interval': '15',  # 来自文件
            'monitor_duration': '2',  # 默认值
            'auto_start': True  # 默认值
        }
        
        merge_correct = True
        for key, expected_value in expected_merged.items():
            if merged_settings.get(key) != expected_value:
                print(f"  ❌ {key}: 期望 {expected_value}, 实际 {merged_settings.get(key)}")
                merge_correct = False
            else:
                print(f"  ✅ {key}: {merged_settings.get(key)}")
        
        if merge_correct:
            print("  ✅ 设置合并正确")
        else:
            print("  ❌ 设置合并有误")
            return False
        
        # 关闭第三个窗口
        root3.destroy()
        
        print(f"\n🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists('monitor_settings.json'):
            os.remove('monitor_settings.json')
            print("🧹 清理测试文件")

def main():
    """主测试函数"""
    print("🚀 开始监控设置保存测试")
    print("=" * 80)
    
    # 测试设置保存和加载
    success = test_monitor_settings_save_load()
    
    # 总结
    print("\n" + "=" * 80)
    if success:
        print("🎉 监控设置保存和加载功能测试成功！")
        print("✅ 默认设置加载正确")
        print("✅ 设置保存功能正常")
        print("✅ 设置加载功能正常")
        print("✅ 设置持久性正确")
        print("✅ 设置合并功能正常")
        print("\n💡 现在监控设置会正确保存和恢复：")
        print("  📋 检查间隔设置会被保存")
        print("  ⏰ 监控时长设置会被保存")
        print("  🚀 自动启动选项会被保存")
        print("  🔄 重新打开窗口时会恢复您的设置")
        return True
    else:
        print("⚠️ 监控设置保存和加载功能存在问题")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
