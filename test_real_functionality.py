#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能检索和重复检测的真实功能价值
验证这些功能是否真的有用
"""

def test_smart_search_real_value():
    """测试智能检索的真实价值"""
    print("🔍 测试智能检索的真实价值...")
    
    try:
        # 检查RAG搜索引擎是否真的能工作
        from rag_search_engine import RAGSearchEngine
        
        search_engine = RAGSearchEngine()
        
        # 测试场景1：搜索历史邮件
        print("\n📧 测试场景1：搜索历史邮件")
        test_queries = [
            "重要通知",
            "会议安排", 
            "项目进展",
            "合作邀请"
        ]
        
        for query in test_queries:
            print(f"   搜索: {query}")
            try:
                results = search_engine.semantic_search(query, None, 5)
                print(f"   结果: 找到 {len(results)} 个相关邮件")
                
                if results:
                    for i, result in enumerate(results[:2], 1):
                        print(f"     {i}. {result.get('subject', '无主题')} (相关度: {result.get('relevance_score', 0):.3f})")
                else:
                    print("     无相关结果")
            except Exception as e:
                print(f"     搜索失败: {e}")
        
        # 测试场景2：检查数据库中是否有数据
        print("\n📊 测试场景2：检查历史数据")
        try:
            # 尝试获取所有邮件记录
            all_results = search_engine.semantic_search("", None, 100)
            print(f"   数据库中共有 {len(all_results)} 条邮件记录")
            
            if len(all_results) == 0:
                print("   ⚠️ 数据库为空，智能检索功能无法发挥作用")
                return False
            else:
                print("   ✅ 数据库有数据，智能检索功能可以正常使用")
                return True
                
        except Exception as e:
            print(f"   ❌ 数据库检查失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 无法导入RAG搜索引擎: {e}")
        return False
    except Exception as e:
        print(f"❌ 智能检索测试失败: {e}")
        return False

def test_duplicate_detection_real_value():
    """测试重复检测的真实价值"""
    print("\n🔍 测试重复检测的真实价值...")
    
    try:
        from rag_search_engine import RAGSearchEngine
        
        rag_search = RAGSearchEngine()
        
        # 测试场景1：检测真实的重复情况
        print("\n📧 测试场景1：检测重复邮件")
        
        # 模拟真实的邮件数据
        test_cases = [
            {
                "subject": "重要通知",
                "body": "请查收相关文件",
                "recipients": ["<EMAIL>", "<EMAIL>"],
                "sender": "<EMAIL>"
            },
            {
                "subject": "会议安排",
                "body": "明天下午2点开会",
                "recipients": ["<EMAIL>", "<EMAIL>"],
                "sender": "<EMAIL>"
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n   测试用例 {i}: {case['subject']}")
            try:
                result = rag_search.advanced_duplicate_detection(
                    case['subject'], 
                    case['body'], 
                    case['recipients'], 
                    case['sender']
                )
                
                total = result.get('total_recipients', 0)
                safe = len(result.get('safe_recipients', []))
                exact = len(result.get('exact_matches', []))
                similar = len(result.get('similar_matches', []))
                
                print(f"     总收件人: {total}")
                print(f"     安全发送: {safe}")
                print(f"     完全重复: {exact}")
                print(f"     相似重复: {similar}")
                print(f"     建议: {result.get('recommendation', '无')}")
                
                if exact > 0 or similar > 0:
                    print("     ✅ 检测到重复，功能有效")
                else:
                    print("     ℹ️ 无重复检测，正常情况")
                    
            except Exception as e:
                print(f"     ❌ 检测失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 重复检测测试失败: {e}")
        return False

def test_operation_buttons():
    """测试操作按钮的可用性"""
    print("\n🔧 测试操作按钮的可用性...")
    
    try:
        # 检查GUI中是否有相关按钮
        from gui_complete_v3 import EmailSenderGUI
        import tkinter as tk
        
        root = tk.Tk()
        app = EmailSenderGUI(root)
        
        # 检查快速重复检测按钮
        has_quick_duplicate = hasattr(app, 'check_duplicates')
        print(f"   快速重复检测按钮: {'存在' if has_quick_duplicate else '不存在'}")
        
        # 检查智能检索按钮
        has_smart_search = hasattr(app, 'open_smart_search')
        print(f"   智能检索按钮: {'存在' if has_smart_search else '不存在'}")
        
        # 检查高级功能
        has_duplicate_interface = hasattr(app, 'open_duplicate_detection')
        print(f"   高级重复检测: {'存在' if has_duplicate_interface else '不存在'}")
        
        root.destroy()
        
        return has_quick_duplicate and has_smart_search
        
    except Exception as e:
        print(f"❌ 操作按钮测试失败: {e}")
        return False

def analyze_functionality_value():
    """分析功能的实际价值"""
    print("\n📊 功能价值分析:")
    
    print("\n🔍 智能检索功能:")
    print("   实际用途:")
    print("   • 在历史邮件中搜索相关内容")
    print("   • 避免重复发送相同内容")
    print("   • 查找之前的邮件模板和内容")
    print("   • 分析邮件发送历史和效果")
    
    print("\n   使用场景:")
    print("   • 发送邮件前，搜索是否发送过类似内容")
    print("   • 查找特定主题的历史邮件")
    print("   • 分析某个收件人的邮件历史")
    print("   • 寻找邮件模板和参考内容")
    
    print("\n🔍 重复检测功能:")
    print("   实际用途:")
    print("   • 检测即将发送的邮件是否与历史邮件重复")
    print("   • 避免向同一收件人发送重复内容")
    print("   • 提供发送建议和风险评估")
    print("   • 支持批量清理重复收件人")
    
    print("\n   操作功能:")
    print("   • 🗑️ 移除重复: 从收件人列表中移除重复的邮箱")
    print("   • 📤 导出结果: 导出检测报告供分析")
    print("   • 📊 分类显示: 按安全、重复、相似分类显示")
    print("   • 💡 智能建议: 根据检测结果提供发送建议")
    
    print("\n⚠️ 功能限制:")
    print("   • 智能检索需要有历史邮件数据才有效")
    print("   • 重复检测依赖于邮件历史记录的完整性")
    print("   • 操作按钮只在检测结果窗口中可见")
    print("   • 功能效果取决于数据库中的数据质量")

def main():
    """主测试函数"""
    print("🚀 测试智能检索和重复检测的真实功能价值")
    print("=" * 60)
    
    # 执行测试
    smart_search_works = test_smart_search_real_value()
    duplicate_detection_works = test_duplicate_detection_real_value()
    buttons_available = test_operation_buttons()
    
    # 分析功能价值
    analyze_functionality_value()
    
    print("\n" + "=" * 60)
    print("📊 测试结论:")
    
    if smart_search_works and duplicate_detection_works:
        print("✅ 功能基本可用，但实际价值取决于数据库内容")
        print("\n💡 建议:")
        print("   • 确保系统记录了足够的邮件历史数据")
        print("   • 在发送邮件前使用智能检索查看历史")
        print("   • 使用重复检测避免重复发送")
        print("   • 定期使用这些功能维护邮件质量")
    else:
        print("⚠️ 功能存在问题，需要进一步优化")
        print("\n🔧 需要改进:")
        print("   • 确保RAG搜索引擎正常工作")
        print("   • 检查数据库连接和数据完整性")
        print("   • 优化用户界面和操作流程")
    
    print(f"\n📈 功能可用性: {(smart_search_works + duplicate_detection_works + buttons_available) / 3 * 100:.0f}%")

if __name__ == "__main__":
    main()
