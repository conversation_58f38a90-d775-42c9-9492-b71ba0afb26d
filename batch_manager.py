# -*- coding: utf-8 -*-
"""
批次发送管理器 - 解决邮件进入垃圾箱问题
"""

import time
import random
import logging
import datetime
import json
import os
from typing import List, Dict, Tuple
from config import BATCH_CONTROL

class BatchManager:
    """批次发送管理器"""
    
    def __init__(self, send_mode: str = 'standard'):
        """
        初始化批次管理器
        
        Args:
            send_mode: 发送模式 ('fast', 'standard', 'safe')
        """
        self.send_mode = send_mode
        self.logger = logging.getLogger(__name__)
        
        # 获取配置
        self.batch_size = BATCH_CONTROL['batch_sizes'][send_mode]
        self.batch_interval_range = BATCH_CONTROL['batch_intervals'][send_mode]
        self.email_interval_range = BATCH_CONTROL['email_intervals'][send_mode]
        self.daily_limit = BATCH_CONTROL['daily_limits'][send_mode]
        
        # 发送统计
        self.today_sent = 0
        self.current_batch = 0
        self.total_batches = 0
        self.start_time = None

        # 断点继续功能
        self.progress_file = "email_send_progress.json"
        self.is_paused = False
        self.current_email_index = 0
        self.session_id = None
        
        self.logger.info(f"批次管理器初始化 - 模式: {send_mode}")
        self.logger.info(f"批次大小: {self.batch_size}, 每日限制: {self.daily_limit}")
    
    def calculate_batches(self, total_emails: int) -> Dict:
        """
        计算批次信息
        
        Args:
            total_emails: 总邮件数量
            
        Returns:
            Dict: 批次信息
        """
        # 检查每日限制
        if total_emails > self.daily_limit:
            self.logger.warning(f"邮件数量 ({total_emails}) 超过每日限制 ({self.daily_limit})")
            
        # 计算批次数量
        self.total_batches = (total_emails + self.batch_size - 1) // self.batch_size
        
        # 估算总耗时
        avg_batch_interval = sum(self.batch_interval_range) / 2
        avg_email_interval = sum(self.email_interval_range) / 2
        
        # 批次内发送时间 + 批次间隔时间
        estimated_time = (
            (total_emails * avg_email_interval / 60) +  # 邮件发送时间（分钟）
            ((self.total_batches - 1) * avg_batch_interval)  # 批次间隔时间（分钟）
        )
        
        batch_info = {
            'total_emails': total_emails,
            'total_batches': self.total_batches,
            'batch_size': self.batch_size,
            'estimated_time_minutes': estimated_time,
            'estimated_time_hours': estimated_time / 60,
            'daily_limit': self.daily_limit,
            'within_limit': total_emails <= self.daily_limit,
            'send_mode': self.send_mode
        }
        
        self.logger.info(f"批次计算完成: {self.total_batches} 个批次, 预计耗时 {estimated_time:.1f} 分钟")
        return batch_info
    
    def get_batch_emails(self, email_list: List, batch_number: int) -> List:
        """
        获取指定批次的邮件列表
        
        Args:
            email_list: 完整邮件列表
            batch_number: 批次号（从1开始）
            
        Returns:
            List: 该批次的邮件列表
        """
        start_idx = (batch_number - 1) * self.batch_size
        end_idx = min(start_idx + self.batch_size, len(email_list))
        
        batch_emails = email_list[start_idx:end_idx]
        self.logger.info(f"获取第 {batch_number} 批次邮件: {len(batch_emails)} 封 (索引 {start_idx}-{end_idx-1})")
        
        return batch_emails
    
    def get_email_interval(self) -> float:
        """
        获取邮件间隔时间（秒）
        
        Returns:
            float: 随机间隔时间
        """
        interval = random.uniform(self.email_interval_range[0], self.email_interval_range[1])
        return interval
    
    def get_batch_interval(self) -> float:
        """
        获取批次间隔时间（分钟）
        
        Returns:
            float: 随机间隔时间
        """
        interval = random.uniform(self.batch_interval_range[0], self.batch_interval_range[1])
        return interval
    
    def should_wait_between_batches(self, current_batch: int) -> Tuple[bool, float]:
        """
        判断是否需要在批次间等待
        
        Args:
            current_batch: 当前批次号
            
        Returns:
            Tuple[bool, float]: (是否需要等待, 等待时间分钟)
        """
        # 最后一个批次不需要等待
        if current_batch >= self.total_batches:
            return False, 0.0
            
        wait_minutes = self.get_batch_interval()
        return True, wait_minutes
    
    def log_batch_start(self, batch_number: int, batch_size: int):
        """记录批次开始"""
        self.current_batch = batch_number
        if self.start_time is None:
            self.start_time = datetime.datetime.now()
            
        self.logger.info(f"🚀 开始第 {batch_number}/{self.total_batches} 批次发送 ({batch_size} 封邮件)")
        
    def log_batch_complete(self, batch_number: int, success_count: int, failed_count: int):
        """记录批次完成"""
        self.today_sent += success_count
        
        self.logger.info(f"✅ 第 {batch_number} 批次完成 - 成功: {success_count}, 失败: {failed_count}")
        self.logger.info(f"📊 今日已发送: {self.today_sent} 封, 剩余额度: {self.daily_limit - self.today_sent}")
        
    def log_batch_wait(self, wait_minutes: float):
        """记录批次等待"""
        wait_seconds = wait_minutes * 60
        hours = int(wait_minutes // 60)
        minutes = int(wait_minutes % 60)
        seconds = int(wait_seconds % 60)
        
        if hours > 0:
            time_str = f"{hours}小时{minutes}分{seconds}秒"
        elif minutes > 0:
            time_str = f"{minutes}分{seconds}秒"
        else:
            time_str = f"{wait_seconds:.1f}秒"
            
        self.logger.info(f"⏱️ 批次间隔等待: {time_str} (避免进入垃圾箱)")
        
    def get_progress_info(self) -> Dict:
        """
        获取发送进度信息
        
        Returns:
            Dict: 进度信息
        """
        if self.start_time is None:
            return {}
            
        elapsed = datetime.datetime.now() - self.start_time
        
        return {
            'current_batch': self.current_batch,
            'total_batches': self.total_batches,
            'today_sent': self.today_sent,
            'daily_limit': self.daily_limit,
            'elapsed_time': str(elapsed).split('.')[0],  # 去掉微秒
            'send_mode': self.send_mode,
            'batch_size': self.batch_size
        }
    
    def get_recommendations(self, total_emails: int) -> List[str]:
        """
        获取发送建议
        
        Args:
            total_emails: 总邮件数量
            
        Returns:
            List[str]: 建议列表
        """
        recommendations = []
        
        # 数量建议
        if total_emails > self.daily_limit:
            recommendations.append(f"⚠️ 邮件数量超过每日建议限制，建议分多天发送")
            
        if total_emails > 100:
            recommendations.append("📧 大量邮件发送，建议使用'安全模式'以提高送达率")
            
        # 时间建议
        current_hour = datetime.datetime.now().hour
        if current_hour < 9 or current_hour > 18:
            recommendations.append("🕐 建议在工作时间(9:00-18:00)发送，提高打开率")
            
        # 内容建议
        recommendations.extend([
            "✉️ 确保邮件主题简洁明了，避免使用过多感叹号",
            "📝 邮件内容要有实际价值，避免纯广告内容",
            "🔗 如有链接，确保链接有效且来源可信",
            "📎 附件大小控制在25MB以内，格式要常见"
        ])
        
        return recommendations

    def save_progress(self, session_id: str, total_emails: int, current_email_index: int,
                     current_batch: int, success_count: int, failed_count: int):
        """
        保存发送进度到文件

        Args:
            session_id: 发送会话ID
            total_emails: 总邮件数量
            current_email_index: 当前邮件索引
            current_batch: 当前批次
            success_count: 成功数量
            failed_count: 失败数量
        """
        progress_data = {
            'session_id': session_id,
            'send_mode': self.send_mode,
            'total_emails': total_emails,
            'current_email_index': current_email_index,
            'current_batch': current_batch,
            'total_batches': self.total_batches,
            'success_count': success_count,
            'failed_count': failed_count,
            'today_sent': self.today_sent,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'save_time': datetime.datetime.now().isoformat(),
            'batch_size': self.batch_size,
            'is_paused': self.is_paused
        }

        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
            self.logger.info(f"💾 进度已保存: 第{current_batch}批次, 第{current_email_index}封邮件")
        except Exception as e:
            self.logger.error(f"保存进度失败: {str(e)}")

    def load_progress(self) -> Dict:
        """
        从文件加载发送进度

        Returns:
            Dict: 进度数据，如果没有则返回空字典
        """
        if not os.path.exists(self.progress_file):
            return {}

        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)

            self.logger.info(f"📂 找到保存的进度: 会话{progress_data.get('session_id', 'unknown')}")
            return progress_data
        except Exception as e:
            self.logger.error(f"加载进度失败: {str(e)}")
            return {}

    def can_resume(self) -> Tuple[bool, Dict]:
        """
        检查是否可以恢复发送

        Returns:
            Tuple[bool, Dict]: (是否可以恢复, 进度数据)
        """
        progress_data = self.load_progress()

        if not progress_data:
            return False, {}

        # 检查是否是同一发送模式
        if progress_data.get('send_mode') != self.send_mode:
            self.logger.warning(f"发送模式不匹配: 保存的是{progress_data.get('send_mode')}, 当前是{self.send_mode}")
            return False, progress_data

        # 检查保存时间（超过24小时的进度可能已过期）
        save_time_str = progress_data.get('save_time')
        if save_time_str:
            save_time = datetime.datetime.fromisoformat(save_time_str)
            if (datetime.datetime.now() - save_time).total_seconds() > 24 * 3600:
                self.logger.warning("保存的进度已超过24小时，可能已过期")
                return False, progress_data

        return True, progress_data

    def resume_from_progress(self, progress_data: Dict):
        """
        从进度数据恢复状态

        Args:
            progress_data: 进度数据
        """
        self.session_id = progress_data.get('session_id')
        self.current_batch = progress_data.get('current_batch', 0)
        self.total_batches = progress_data.get('total_batches', 0)
        self.current_email_index = progress_data.get('current_email_index', 0)
        self.today_sent = progress_data.get('success_count', 0)  # 使用成功数量作为今日发送量

        start_time_str = progress_data.get('start_time')
        if start_time_str:
            self.start_time = datetime.datetime.fromisoformat(start_time_str)

        self.logger.info(f"🔄 恢复发送状态: 第{self.current_batch}批次, 第{self.current_email_index}封邮件")
        self.logger.info(f"📊 已发送: {self.today_sent}封, 剩余额度: {self.daily_limit - self.today_sent}")

    def clear_progress(self):
        """清除保存的进度文件"""
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                self.logger.info("🗑️ 进度文件已清除")
        except Exception as e:
            self.logger.error(f"清除进度文件失败: {str(e)}")

    def pause_sending(self):
        """暂停发送"""
        self.is_paused = True
        self.logger.info("⏸️ 发送已暂停")

    def resume_sending(self):
        """恢复发送"""
        self.is_paused = False
        self.logger.info("▶️ 发送已恢复")

    def is_sending_paused(self) -> bool:
        """检查是否处于暂停状态"""
        return self.is_paused
