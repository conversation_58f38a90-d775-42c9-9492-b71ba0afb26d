# -*- coding: utf-8 -*-
"""
邮箱地址解析测试脚本
"""

def parse_emails(recipient_emails):
    """增强的邮箱地址解析功能"""
    print(f"原始输入: {repr(recipient_emails)}")

    # 多步骤解析，优先处理分号分隔（最常用）
    # 1. 先按分号分割
    if ';' in recipient_emails:
        print("检测到分号分隔符，按分号解析")
        email_parts = [email.strip() for email in recipient_emails.split(';')]
    # 2. 再按换行分割
    elif '\n' in recipient_emails:
        print("检测到换行分隔符，按换行解析")
        email_parts = [email.strip() for email in recipient_emails.split('\n')]
    # 3. 最后按逗号分割
    else:
        print("使用逗号分隔符解析")
        # 处理中英文逗号
        normalized_emails = recipient_emails.replace('，', ',')
        email_parts = [email.strip() for email in normalized_emails.split(',')]

    # 进一步处理混合分隔符的情况
    final_email_parts = []
    for part in email_parts:
        # 如果某个部分还包含其他分隔符，继续分割
        if ';' in part:
            final_email_parts.extend([email.strip() for email in part.split(';')])
        elif ',' in part or '，' in part:
            part = part.replace('，', ',')
            final_email_parts.extend([email.strip() for email in part.split(',')])
        elif '\n' in part:
            final_email_parts.extend([email.strip() for email in part.split('\n')])
        else:
            final_email_parts.append(part.strip())

    email_parts = final_email_parts
    print(f"分割后的部分: {email_parts}")

    # 清理和验证邮箱地址
    recipient_list = []
    invalid_emails = []

    for email in email_parts:
        email = email.strip()
        if email:
            if is_valid_email(email):
                recipient_list.append(email)
            else:
                invalid_emails.append(email)

    return recipient_list, invalid_emails

def is_valid_email(email):
    """验证邮箱地址格式"""
    if not email or '@' not in email:
        return False

    parts = email.split('@')
    if len(parts) != 2:
        return False

    local, domain = parts

    # 检查本地部分
    if not local or len(local) > 64:
        return False

    # 检查域名部分
    if not domain or '.' not in domain or len(domain) > 255:
        return False

    # 检查域名不能以点开头或结尾
    if domain.startswith('.') or domain.endswith('.'):
        return False

    # 检查不能有连续的点
    if '..' in domain:
        return False

    return True

def test_email_parsing():
    """测试邮箱解析功能"""
    print("=" * 50)
    print("    邮箱地址解析测试")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        # 逗号分隔
        "<EMAIL>, <EMAIL>, <EMAIL>",

        # 分号分隔（最常用）
        "<EMAIL>; <EMAIL>; <EMAIL>",

        # 换行分隔
        "<EMAIL>\<EMAIL>\<EMAIL>",

        # 混合分隔
        "<EMAIL>, <EMAIL>\<EMAIL>; <EMAIL>",

        # 包含无效邮箱
        "<EMAIL>, invalid_email, <EMAIL>, @invalid.com",

        # 您的实际测试用例
        "<EMAIL>;<EMAIL>",

        # 大量邮箱测试（分号分隔）
        "<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>",

        # 包含空格的分号分隔
        "<EMAIL> ; <EMAIL> ; <EMAIL>",

        # 混合无效邮箱的大量测试
        "<EMAIL>;invalid_email;<EMAIL>;@invalid.com;<EMAIL>;invalid.domain;<EMAIL>",

        # 中文逗号混合
        "<EMAIL>，<EMAIL>；<EMAIL>",

        # 极端情况：只有分号
        ";;;;",

        # 极端情况：空字符串
        "",

        # 120个邮箱的模拟（简化版）
        ";".join([f"user{i}@test{i%5}.com" for i in range(1, 21)]) + ";invalid_email;another@invalid"
    ]
    
    for i, test_input in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {repr(test_input)}")
        
        valid_emails, invalid_emails = parse_emails(test_input)
        
        print(f"有效邮箱 ({len(valid_emails)} 个):")
        for email in valid_emails:
            print(f"  ✓ {email}")
        
        if invalid_emails:
            print(f"无效邮箱 ({len(invalid_emails)} 个):")
            for email in invalid_emails:
                print(f"  ✗ {email}")
        
        print("-" * 30)

if __name__ == "__main__":
    test_email_parsing()
    
    print("\n" + "=" * 50)
    print("    交互式测试")
    print("=" * 50)
    print("请输入邮箱地址列表（支持逗号、分号、换行分隔）:")
    print("输入 'quit' 退出")
    
    while True:
        try:
            user_input = input("\n邮箱列表: ").strip()
            if user_input.lower() == 'quit':
                break
            
            if not user_input:
                continue
                
            valid_emails, invalid_emails = parse_emails(user_input)
            
            print(f"\n解析结果:")
            print(f"有效邮箱: {len(valid_emails)} 个")
            for email in valid_emails:
                print(f"  ✓ {email}")
            
            if invalid_emails:
                print(f"无效邮箱: {len(invalid_emails)} 个")
                for email in invalid_emails:
                    print(f"  ✗ {email}")
            
        except KeyboardInterrupt:
            print("\n\n测试结束")
            break
        except Exception as e:
            print(f"错误: {str(e)}")
    
    print("再见！")
