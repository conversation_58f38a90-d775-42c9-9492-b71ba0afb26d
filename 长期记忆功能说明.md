# 🧠 长期记忆功能使用说明

## 📋 功能概述

长期记忆功能为2.0系统添加了完整的数据持久化能力，确保重启后所有用户数据都能恢复。

## 🔧 自动保存的数据

### 用户界面设置
- ✅ 发送模式（标准/快速/安全）
- ✅ 个性化设置（邮件编号和时间戳）
- ✅ 自动回复监控开关
- ✅ 自动队列模式开关

### 邮件内容
- ✅ 发件人邮箱地址
- ✅ 收件人列表
- ✅ 邮件主题
- ✅ 邮件正文
- ✅ 附件列表

### 发送配置
- ✅ 邮件发送队列
- ✅ 授权码管理
- ✅ 全功能模式配置

### 高级功能数据
- ✅ 监控设置
- ✅ 质量数据库
- ✅ 应急管理配置
- ✅ 反垃圾邮件设置

## 🚀 使用方法

1. **自动保存**: 系统每30秒自动保存一次数据
2. **退出保存**: 程序退出时自动保存所有数据
3. **启动恢复**: 程序启动时自动恢复所有数据

## 📁 数据存储位置

- 用户数据: `user_data/user_settings.db`
- 配置备份: `config_backup/`
- 邮件模板: 数据库中的email_templates表
- 收件人组: 数据库中的recipient_groups表

## 💡 使用建议

1. **定期备份**: 建议定期备份user_data目录
2. **模板管理**: 使用邮件模板功能保存常用邮件
3. **收件人组**: 使用收件人组功能管理常用收件人列表
4. **数据迁移**: 可以通过复制user_data目录迁移数据

## 🔧 故障排除

如果数据恢复失败：
1. 检查user_data目录是否存在
2. 检查user_settings.db文件是否损坏
3. 查看日志中的错误信息
4. 从config_backup目录恢复配置文件

## 🎉 新增功能

- 📧 邮件模板管理
- 👥 收件人组管理
- ⚙️ 用户偏好设置
- 🔄 自动数据同步
