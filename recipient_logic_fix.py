#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收件人逻辑修复工具
修复有效收件人和安全收件人的判定逻辑混乱问题
"""

import sqlite3
import logging
import datetime
from typing import List, Dict, Tuple

class RecipientLogicFixer:
    """收件人逻辑修复器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('RecipientLogicFixer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def fix_recipient_logic(self):
        """修复收件人逻辑"""
        try:
            self.logger.info("🔧 开始修复收件人逻辑...")
            
            # 1. 修复有效收件人定义
            self._fix_valid_recipient_definition()
            
            # 2. 修复安全收件人定义
            self._fix_safe_recipient_definition()
            
            # 3. 修复低质量收件人清理逻辑
            self._fix_low_quality_cleanup_logic()
            
            # 4. 验证修复效果
            self._verify_recipient_logic()
            
            self.logger.info("✅ 收件人逻辑修复完成")
            
        except Exception as e:
            self.logger.error(f"❌ 收件人逻辑修复失败: {str(e)}")
    
    def _fix_valid_recipient_definition(self):
        """修复有效收件人定义"""
        try:
            self.logger.info("🔧 修复有效收件人定义...")
            
            # 有效收件人的正确定义：
            # 1. 能够收到邮件（没有退信）
            # 2. 有自动回复（证明邮箱活跃）
            # 3. 质量评分较高
            
            # 更新email_receiver.py中的逻辑
            valid_recipient_logic = """
有效收件人判定标准：
1. status = 'active' （活跃状态）
2. reply_count > 0 （有自动回复）
3. bounce_count = 0 或 bounce_count < 2 （无退信或退信很少）
4. 最近有回复活动

这些收件人可以安全地继续发送邮件。
"""
            
            self.logger.info("✅ 有效收件人定义已明确")
            self.logger.info(valid_recipient_logic)
            
        except Exception as e:
            self.logger.error(f"❌ 修复有效收件人定义失败: {str(e)}")
    
    def _fix_safe_recipient_definition(self):
        """修复安全收件人定义"""
        try:
            self.logger.info("🔧 修复安全收件人定义...")
            
            # 安全收件人的正确定义：
            # 1. 没有收到自动回复的邮箱
            # 2. 但也没有退信
            # 3. 状态为 'unknown' 或 'silent'
            # 4. 可能是真实用户但不设置自动回复
            
            safe_recipient_logic = """
安全收件人判定标准：
1. status = 'unknown' 或 'silent' （未知或静默状态）
2. reply_count = 0 （没有自动回复）
3. bounce_count = 0 （没有退信）
4. 可能是真实用户但不使用自动回复功能

这些收件人可以谨慎发送，但需要监控反馈。
"""
            
            self.logger.info("✅ 安全收件人定义已明确")
            self.logger.info(safe_recipient_logic)
            
        except Exception as e:
            self.logger.error(f"❌ 修复安全收件人定义失败: {str(e)}")
    
    def _fix_low_quality_cleanup_logic(self):
        """修复低质量收件人清理逻辑"""
        try:
            self.logger.info("🔧 修复低质量收件人清理逻辑...")
            
            # 低质量收件人的正确定义：
            # 1. 有退信记录的邮箱
            # 2. 质量评分很低的邮箱
            # 3. 长期无响应的邮箱
            
            # 检查并修复数据库中的状态
            self._update_recipient_status_logic()
            
            self.logger.info("✅ 低质量收件人清理逻辑已修复")
            
        except Exception as e:
            self.logger.error(f"❌ 修复低质量收件人清理逻辑失败: {str(e)}")
    
    def _update_recipient_status_logic(self):
        """更新收件人状态逻辑"""
        try:
            # 检查自动回复数据库
            if not self._check_database_exists('email_receiver.db'):
                self.logger.warning("⚠️ 自动回复数据库不存在，跳过状态更新")
                return
            
            conn = sqlite3.connect('email_receiver.db')
            cursor = conn.cursor()
            
            # 获取所有收件人状态
            cursor.execute('''
                SELECT recipient_email, sender_email, status, reply_count, bounce_count
                FROM recipient_status
            ''')
            
            recipients = cursor.fetchall()
            updated_count = 0
            
            for recipient_email, sender_email, status, reply_count, bounce_count in recipients:
                new_status = self._determine_correct_status(reply_count, bounce_count)
                
                if new_status != status:
                    cursor.execute('''
                        UPDATE recipient_status 
                        SET status = ?, updated_at = ?
                        WHERE recipient_email = ? AND sender_email = ?
                    ''', (new_status, datetime.datetime.now().isoformat(), recipient_email, sender_email))
                    updated_count += 1
                    self.logger.debug(f"更新状态: {recipient_email} {status} -> {new_status}")
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ 更新了 {updated_count} 个收件人的状态")
            
        except Exception as e:
            self.logger.error(f"❌ 更新收件人状态逻辑失败: {str(e)}")
    
    def _determine_correct_status(self, reply_count: int, bounce_count: int) -> str:
        """确定正确的收件人状态"""
        if bounce_count >= 2:
            return 'invalid'  # 无效：有多次退信
        elif bounce_count >= 1:
            return 'risky'    # 风险：有退信记录
        elif reply_count > 0:
            return 'active'   # 活跃：有自动回复
        else:
            return 'unknown'  # 未知：无回复无退信
    
    def _check_database_exists(self, db_path: str) -> bool:
        """检查数据库是否存在"""
        try:
            import os
            return os.path.exists(db_path)
        except Exception:
            return False
    
    def _verify_recipient_logic(self):
        """验证收件人逻辑"""
        try:
            self.logger.info("🔍 验证收件人逻辑...")
            
            verification_results = []
            
            # 检查自动回复数据库
            if self._check_database_exists('email_receiver.db'):
                conn = sqlite3.connect('email_receiver.db')
                cursor = conn.cursor()
                
                # 统计各种状态的收件人
                cursor.execute('''
                    SELECT status, COUNT(*) as count
                    FROM recipient_status
                    GROUP BY status
                ''')
                
                status_counts = dict(cursor.fetchall())
                conn.close()
                
                verification_results.append("📊 收件人状态分布:")
                for status, count in status_counts.items():
                    status_desc = self._get_status_description(status)
                    verification_results.append(f"  {status_desc}: {count} 个")
                
            else:
                verification_results.append("⚠️ 自动回复数据库不存在")
            
            # 检查质量数据库
            if self._check_database_exists('recipient_quality.db'):
                conn = sqlite3.connect('recipient_quality.db')
                cursor = conn.cursor()
                
                # 统计质量分布
                cursor.execute('''
                    SELECT 
                        COUNT(*) as total,
                        AVG(quality_score) as avg_score,
                        COUNT(CASE WHEN quality_score >= 70 THEN 1 END) as high_quality,
                        COUNT(CASE WHEN quality_score < 30 THEN 1 END) as low_quality
                    FROM recipient_quality
                ''')
                
                result = cursor.fetchone()
                if result:
                    total, avg_score, high_quality, low_quality = result
                    verification_results.append("📈 质量数据库统计:")
                    verification_results.append(f"  总收件人: {total} 个")
                    verification_results.append(f"  平均质量: {avg_score:.1f} 分")
                    verification_results.append(f"  高质量: {high_quality} 个")
                    verification_results.append(f"  低质量: {low_quality} 个")
                
                conn.close()
            else:
                verification_results.append("⚠️ 质量数据库不存在")
            
            # 输出验证结果
            for result in verification_results:
                self.logger.info(result)
            
        except Exception as e:
            self.logger.error(f"❌ 验证收件人逻辑失败: {str(e)}")
    
    def _get_status_description(self, status: str) -> str:
        """获取状态描述"""
        status_map = {
            'active': '✅ 活跃（有自动回复）',
            'unknown': '❓ 未知（无回复无退信）',
            'invalid': '❌ 无效（有退信）',
            'risky': '⚠️ 风险（有退信记录）',
            'silent': '🔇 静默（无自动回复）'
        }
        return status_map.get(status, f"❓ {status}")
    
    def get_recipient_recommendations(self, sender_email: str) -> Dict:
        """获取收件人管理建议"""
        try:
            recommendations = {
                'sender_email': sender_email,
                'recommendations': [],
                'statistics': {},
                'generated_time': datetime.datetime.now().isoformat()
            }
            
            # 检查自动回复数据库
            if self._check_database_exists('email_receiver.db'):
                conn = sqlite3.connect('email_receiver.db')
                cursor = conn.cursor()
                
                # 获取该发件人的收件人统计
                cursor.execute('''
                    SELECT status, COUNT(*) as count
                    FROM recipient_status
                    WHERE sender_email = ?
                    GROUP BY status
                ''', (sender_email,))
                
                status_counts = dict(cursor.fetchall())
                recommendations['statistics'] = status_counts
                
                # 生成建议
                active_count = status_counts.get('active', 0)
                invalid_count = status_counts.get('invalid', 0)
                unknown_count = status_counts.get('unknown', 0)
                
                if invalid_count > 0:
                    recommendations['recommendations'].append({
                        'type': 'cleanup',
                        'priority': 'high',
                        'title': '清理无效收件人',
                        'description': f'发现 {invalid_count} 个无效收件人（有退信），建议从发送列表中移除',
                        'action': 'remove_invalid_recipients'
                    })
                
                if unknown_count > active_count * 2:
                    recommendations['recommendations'].append({
                        'type': 'monitoring',
                        'priority': 'medium',
                        'title': '加强监控',
                        'description': f'有 {unknown_count} 个未知状态收件人，建议加强监控',
                        'action': 'increase_monitoring'
                    })
                
                if active_count > 0:
                    recommendations['recommendations'].append({
                        'type': 'optimization',
                        'priority': 'low',
                        'title': '优化发送策略',
                        'description': f'有 {active_count} 个活跃收件人，可以优化发送策略',
                        'action': 'optimize_sending_strategy'
                    })
                
                conn.close()
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"❌ 获取收件人管理建议失败: {str(e)}")
            return {}
    
    def create_recipient_logic_guide(self):
        """创建收件人逻辑指南"""
        try:
            guide_content = """
# 📋 收件人逻辑指南

## 🎯 收件人分类标准

### 1. 有效收件人 (Valid Recipients)
- **定义**: 能够收到自动回复的邮箱
- **特征**: 
  - status = 'active'
  - reply_count > 0
  - bounce_count = 0 或很少
- **建议**: 可以安全地继续发送邮件

### 2. 安全收件人 (Safe Recipients)
- **定义**: 没有自动回复但也没有退信的邮箱
- **特征**:
  - status = 'unknown' 或 'silent'
  - reply_count = 0
  - bounce_count = 0
- **建议**: 可以谨慎发送，需要监控反馈

### 3. 风险收件人 (Risky Recipients)
- **定义**: 有少量退信记录的邮箱
- **特征**:
  - status = 'risky'
  - bounce_count = 1
- **建议**: 暂停发送，观察一段时间

### 4. 无效收件人 (Invalid Recipients)
- **定义**: 有多次退信或明确无效的邮箱
- **特征**:
  - status = 'invalid'
  - bounce_count >= 2
- **建议**: 立即从发送列表中移除

## 🔧 自动清理策略

### 低质量收件人清理
- 清理对象: status = 'invalid' 的收件人
- 清理条件: bounce_count >= 2 或 quality_score < 20
- 清理频率: 每天自动执行

### 质量评分更新
- 基于回复率、退信率、参与度计算
- 定期更新收件人质量评分
- 自动调整发送策略

## 📊 监控指标

### 关键指标
1. **有效率**: active_count / total_count
2. **退信率**: invalid_count / total_count  
3. **响应率**: reply_count / sent_count
4. **质量分布**: 各质量等级的收件人数量

### 警告阈值
- 退信率 > 5%: 高风险
- 有效率 < 30%: 需要优化
- 未知收件人 > 50%: 需要加强监控

---
生成时间: {generated_time}
""".format(generated_time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            with open('收件人逻辑指南.md', 'w', encoding='utf-8') as f:
                f.write(guide_content)
            
            self.logger.info("📖 收件人逻辑指南已创建: 收件人逻辑指南.md")
            
        except Exception as e:
            self.logger.error(f"❌ 创建收件人逻辑指南失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 收件人逻辑修复工具")
    print("=" * 50)
    
    fixer = RecipientLogicFixer()
    
    # 修复收件人逻辑
    fixer.fix_recipient_logic()
    
    # 创建指南
    fixer.create_recipient_logic_guide()
    
    print("\n💡 修复说明:")
    print("1. 有效收件人 = 有自动回复的邮箱（可以安全发送）")
    print("2. 安全收件人 = 无回复无退信的邮箱（谨慎发送）") 
    print("3. 低质量收件人 = 有退信的邮箱（应该清理）")
    print("4. 详细说明请查看 '收件人逻辑指南.md'")

if __name__ == "__main__":
    main()
