# -*- coding: utf-8 -*-
"""
邮件发送诊断工具
帮助用户快速定位发送失败的原因
"""

import smtplib
import socket
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from config import SMTP_CONFIG

def test_network_connection():
    """测试网络连接"""
    print("=" * 50)
    print("1. 测试网络连接")
    print("=" * 50)
    
    try:
        # 测试DNS解析
        import socket
        ip = socket.gethostbyname('smtp.qq.com')
        print(f"✓ DNS解析成功: smtp.qq.com -> {ip}")
        
        # 测试端口连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex(('smtp.qq.com', 587))
        sock.close()
        
        if result == 0:
            print("✓ 端口587连接成功")
            return True
        else:
            print("✗ 端口587连接失败")
            return False
            
    except Exception as e:
        print(f"✗ 网络连接测试失败: {str(e)}")
        return False

def test_smtp_config():
    """测试SMTP配置"""
    print("\n" + "=" * 50)
    print("2. 检查SMTP配置")
    print("=" * 50)
    
    print(f"SMTP服务器: {SMTP_CONFIG['server']}")
    print(f"端口: {SMTP_CONFIG['port']}")
    print(f"TLS加密: {SMTP_CONFIG['use_tls']}")
    print(f"用户名: {SMTP_CONFIG['username'] or '❌ 未设置'}")
    print(f"密码: {'✓ 已设置' if SMTP_CONFIG['password'] else '❌ 未设置'}")
    
    if not SMTP_CONFIG['username']:
        print("\n❌ 错误：用户名未设置！")
        print("解决方案：在图形界面的'发送者邮箱'中输入您的QQ邮箱地址")
        return False
    
    if not SMTP_CONFIG['password']:
        print("\n❌ 错误：SMTP授权码未设置！")
        print("解决方案：")
        print("1. 登录QQ邮箱网页版")
        print("2. 设置 -> 账户 -> POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务")
        print("3. 开启SMTP服务并获取授权码")
        print("4. 将授权码填入config.py文件")
        return False
    
    print("✓ SMTP配置检查通过")
    return True

def test_smtp_connection(sender_email):
    """测试SMTP连接和认证"""
    print("\n" + "=" * 50)
    print("3. 测试SMTP连接和认证")
    print("=" * 50)
    
    try:
        print("正在连接SMTP服务器...")
        server = smtplib.SMTP(SMTP_CONFIG['server'], SMTP_CONFIG['port'])
        print("✓ SMTP服务器连接成功")
        
        print("正在启用TLS加密...")
        server.starttls()
        print("✓ TLS加密启用成功")
        
        print("正在进行SMTP认证...")
        server.login(sender_email, SMTP_CONFIG['password'])
        print("✓ SMTP认证成功")
        
        server.quit()
        print("✓ SMTP连接测试完成")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ SMTP认证失败: {str(e)}")
        print("\n可能的原因：")
        print("1. 邮箱地址错误")
        print("2. SMTP授权码错误")
        print("3. 未开启SMTP服务")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ SMTP连接失败: {str(e)}")
        print("\n可能的原因：")
        print("1. 网络连接问题")
        print("2. 防火墙阻止")
        print("3. SMTP服务器问题")
        return False
        
    except Exception as e:
        print(f"❌ SMTP测试失败: {str(e)}")
        return False

def test_send_email(sender_email, test_recipient):
    """测试发送邮件"""
    print("\n" + "=" * 50)
    print("4. 测试发送邮件")
    print("=" * 50)
    
    try:
        # 创建测试邮件
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = test_recipient
        msg['Subject'] = "邮件发送测试"
        
        body = "这是一封测试邮件，用于验证邮件发送功能是否正常。"
        msg.attach(MIMEText(body, 'plain', 'utf-8'))
        
        # 发送邮件
        print(f"正在发送测试邮件到: {test_recipient}")
        server = smtplib.SMTP(SMTP_CONFIG['server'], SMTP_CONFIG['port'])
        server.starttls()
        server.login(sender_email, SMTP_CONFIG['password'])
        
        text = msg.as_string()
        server.sendmail(sender_email, [test_recipient], text)
        server.quit()
        
        print("✓ 测试邮件发送成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试邮件发送失败: {str(e)}")
        return False

def main():
    """主诊断流程"""
    print("邮件发送诊断工具")
    print("=" * 50)
    
    # 获取用户输入
    sender_email = input("请输入您的QQ邮箱地址: ").strip()
    if not sender_email:
        print("❌ 邮箱地址不能为空")
        return
    
    if not sender_email.endswith('@qq.com'):
        print("❌ 请输入有效的QQ邮箱地址")
        return
    
    # 更新配置中的用户名
    SMTP_CONFIG['username'] = sender_email
    
    # 执行诊断步骤
    step1 = test_network_connection()
    step2 = test_smtp_config()
    
    if not step1 or not step2:
        print("\n" + "=" * 50)
        print("诊断结果：基础配置有问题，请先解决上述问题")
        print("=" * 50)
        return
    
    step3 = test_smtp_connection(sender_email)
    
    if not step3:
        print("\n" + "=" * 50)
        print("诊断结果：SMTP连接或认证失败")
        print("=" * 50)
        return
    
    # 询问是否进行发送测试
    test_send = input("\n是否进行实际发送测试？(y/n): ").strip().lower()
    if test_send == 'y':
        test_recipient = input("请输入测试收件人邮箱: ").strip()
        if test_recipient:
            test_send_email(sender_email, test_recipient)
    
    print("\n" + "=" * 50)
    print("诊断完成！")
    print("=" * 50)
    
    if step1 and step2 and step3:
        print("✓ 所有测试通过，邮件发送功能应该正常工作")
        print("\n如果图形界面仍然发送失败，请检查：")
        print("1. 确保在图形界面中输入了正确的发送者邮箱")
        print("2. 检查收件人邮箱地址是否正确")
        print("3. 查看详细的错误日志")
    else:
        print("❌ 存在问题，请根据上述提示解决")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n诊断已取消")
    except Exception as e:
        print(f"\n诊断工具出错: {str(e)}")
    
    input("\n按回车键退出...")
