#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试QQ邮箱授权码
"""

import smtplib
import imaplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import datetime

def test_qq_email_auth():
    """测试QQ邮箱授权码"""
    
    # 您的邮箱信息
    sender_email = "<EMAIL>"  # 您的QQ邮箱
    auth_code = "cwnzcpaczwngdgfa"      # 您为*****************生成的授权码
    
    print("🔧 QQ邮箱授权码快速测试")
    print("=" * 50)
    print(f"📧 测试邮箱: {sender_email}")
    print(f"🔑 授权码: {auth_code}")
    print()
    
    # 测试SMTP发送功能
    print("🔍 测试1: SMTP发送功能")
    print("-" * 30)
    
    try:
        # 连接SMTP服务器
        print("🔗 正在连接SMTP服务器...")
        server = smtplib.SMTP('smtp.qq.com', 587)
        print("✅ SMTP服务器连接成功")
        
        # 启动TLS加密
        print("🔐 启动TLS加密...")
        server.starttls()
        print("✅ TLS加密启动成功")
        
        # 登录验证
        print("🔑 正在验证授权码...")
        server.login(sender_email, auth_code)
        print("✅ 授权码验证成功！")
        
        server.quit()
        print("🎉 SMTP测试完全通过！")
        smtp_success = True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ SMTP认证失败: {str(e)}")
        print("💡 请检查授权码是否正确")
        smtp_success = False
    except Exception as e:
        print(f"❌ SMTP测试失败: {str(e)}")
        smtp_success = False
    
    print()
    
    # 测试IMAP接收功能
    print("🔍 测试2: IMAP接收功能")
    print("-" * 30)
    
    try:
        # 连接IMAP服务器
        print("🔗 正在连接IMAP服务器...")
        mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
        print("✅ IMAP服务器连接成功")
        
        # 登录验证
        print("🔑 正在验证授权码...")
        mail.login(sender_email, auth_code)
        print("✅ 授权码验证成功！")
        
        # 测试邮箱访问
        print("📂 测试邮箱访问...")
        mail.select('INBOX')
        print("✅ 邮箱访问成功")
        
        mail.logout()
        print("🎉 IMAP测试完全通过！")
        imap_success = True
        
    except imaplib.IMAP4.error as e:
        print(f"❌ IMAP认证失败: {str(e)}")
        print("💡 请检查授权码是否正确")
        imap_success = False
    except Exception as e:
        print(f"❌ IMAP测试失败: {str(e)}")
        imap_success = False
    
    print()
    
    # 测试发送邮件
    if smtp_success:
        print("🔍 测试3: 发送测试邮件")
        print("-" * 30)
        
        try:
            # 创建测试邮件
            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = sender_email  # 发给自己测试
            msg['Subject'] = "📧 QQ邮箱授权码测试成功"
            
            body = f"""恭喜！您的QQ邮箱授权码配置成功！

测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
发件人: {sender_email}
授权码: {auth_code}

✅ SMTP发送功能正常
✅ IMAP接收功能正常
✅ 邮件发送测试成功

现在您可以正常使用邮件发送系统了！

此邮件由授权码测试工具自动发送。"""
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            print("📤 正在发送测试邮件...")
            server = smtplib.SMTP('smtp.qq.com', 587)
            server.starttls()
            server.login(sender_email, auth_code)
            
            text = msg.as_string()
            server.sendmail(sender_email, sender_email, text)
            server.quit()
            
            print("✅ 测试邮件发送成功！")
            print("💡 请检查您的QQ邮箱收件箱")
            send_success = True
            
        except Exception as e:
            print(f"❌ 发送测试邮件失败: {str(e)}")
            send_success = False
    else:
        send_success = False
    
    # 总结
    print()
    print("📊 测试结果总结")
    print("=" * 50)
    print(f"SMTP发送功能: {'✅ 正常' if smtp_success else '❌ 失败'}")
    print(f"IMAP接收功能: {'✅ 正常' if imap_success else '❌ 失败'}")
    print(f"邮件发送测试: {'✅ 成功' if send_success else '❌ 失败'}")
    
    if smtp_success and imap_success:
        print()
        print("🎉 恭喜！您的QQ邮箱配置完全正确！")
        print("✅ 现在可以正常使用邮件发送系统了")
        print("✅ 自动回复监控功能可以正常工作")
        print("✅ QQ应急系统可以正常运行")
        
        print()
        print("💡 下一步操作:")
        print("1. 在邮件发送程序中使用以下配置:")
        print(f"   邮箱: {sender_email}")
        print(f"   密码: {auth_code}")
        print("2. 确保程序中使用的是授权码而不是QQ密码")
        print("3. 重新尝试发送邮件")
        
    else:
        print()
        print("⚠️ 仍有问题需要解决")
        print("💡 建议:")
        print("1. 确认QQ邮箱中已开启IMAP/SMTP服务")
        print("2. 重新生成授权码")
        print("3. 检查网络连接")
        print("4. 联系QQ邮箱客服确认账户状态")

if __name__ == "__main__":
    test_qq_email_auth()
