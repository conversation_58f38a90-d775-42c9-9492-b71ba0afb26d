#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全自动模式修复工具
修复全自动模式未真实启用的问题
"""

import os
import json
import logging
import datetime
from typing import Dict, List

class FullAutomationFixer:
    """全自动模式修复器"""
    
    def __init__(self):
        self.logger = self._setup_logger()
        self.config_files = [
            'all_features_config.json',
            'auto_reply_config.json',
            'startup_config.json',
            'monitor_settings.json'
        ]
        
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger('FullAutomationFixer')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def fix_full_automation_mode(self):
        """修复全自动模式"""
        try:
            self.logger.info("🔧 开始修复全自动模式...")
            
            # 1. 修复配置文件
            self._fix_config_files()
            
            # 2. 修复VBS启动器
            self._fix_vbs_launcher()
            
            # 3. 修复自动化工作流
            self._fix_automation_workflow()
            
            # 4. 验证修复效果
            self._verify_automation_fix()
            
            self.logger.info("✅ 全自动模式修复完成")
            
        except Exception as e:
            self.logger.error(f"❌ 全自动模式修复失败: {str(e)}")
    
    def _fix_config_files(self):
        """修复配置文件"""
        try:
            self.logger.info("🔧 修复配置文件...")
            
            # 修复全功能配置
            all_features_config = {
                "auto_mode_enabled": True,
                "auto_reply_monitoring": True,
                "auto_quality_import": True,
                "auto_emergency_check": True,
                "auto_coordination": True,
                "auto_queue_processing": True,
                "auto_cleanup": True,
                "auto_optimization": True,
                "last_updated": datetime.datetime.now().isoformat(),
                "version": "2.0"
            }
            
            with open('all_features_config.json', 'w', encoding='utf-8') as f:
                json.dump(all_features_config, f, ensure_ascii=False, indent=2)
            
            # 修复自动回复配置
            auto_reply_config = {
                "enabled": True,
                "auto_start": True,
                "default_duration": 2,
                "default_interval": 30,
                "auto_import_to_quality_db": True,
                "auto_cleanup_invalid": True,
                "auto_emergency_check": True,
                "notification_enabled": True,
                "last_updated": datetime.datetime.now().isoformat()
            }
            
            with open('auto_reply_config.json', 'w', encoding='utf-8') as f:
                json.dump(auto_reply_config, f, ensure_ascii=False, indent=2)
            
            # 修复启动配置
            startup_config = {
                "auto_start_all_features": True,
                "auto_load_last_session": True,
                "auto_restore_settings": True,
                "enable_full_automation": True,
                "skip_confirmation_dialogs": False,
                "auto_minimize_to_tray": False,
                "last_updated": datetime.datetime.now().isoformat()
            }
            
            with open('startup_config.json', 'w', encoding='utf-8') as f:
                json.dump(startup_config, f, ensure_ascii=False, indent=2)
            
            # 修复监控设置
            monitor_settings = {
                "auto_start_monitoring": True,
                "default_interval": 30,
                "default_duration": 2,
                "auto_save_results": True,
                "auto_import_valid_recipients": True,
                "auto_cleanup_invalid_recipients": True,
                "enable_real_time_notifications": True,
                "last_updated": datetime.datetime.now().isoformat()
            }
            
            with open('monitor_settings.json', 'w', encoding='utf-8') as f:
                json.dump(monitor_settings, f, ensure_ascii=False, indent=2)
            
            self.logger.info("✅ 配置文件修复完成")
            
        except Exception as e:
            self.logger.error(f"❌ 修复配置文件失败: {str(e)}")
    
    def _fix_vbs_launcher(self):
        """修复VBS启动器"""
        try:
            self.logger.info("🔧 修复VBS启动器...")
            
            # 检查快速启动.vbs是否存在
            if os.path.exists('快速启动.vbs'):
                self.logger.info("✅ 快速启动.vbs 存在")
            else:
                self.logger.warning("⚠️ 快速启动.vbs 不存在，创建默认版本")
                self._create_default_vbs_launcher()
            
            # 检查主要的Python文件是否存在
            required_files = [
                'gui_main.py',
                'gui_complete_v3.py',
                'email_sender.py',
                'email_receiver.py',
                'recipient_quality_manager.py',
                'qq_email_anti_spam.py',
                'system_coordinator.py'
            ]
            
            missing_files = []
            for file in required_files:
                if not os.path.exists(file):
                    missing_files.append(file)
            
            if missing_files:
                self.logger.warning(f"⚠️ 缺少关键文件: {', '.join(missing_files)}")
            else:
                self.logger.info("✅ 所有关键文件都存在")
            
        except Exception as e:
            self.logger.error(f"❌ 修复VBS启动器失败: {str(e)}")
    
    def _create_default_vbs_launcher(self):
        """创建默认VBS启动器"""
        vbs_content = '''
' 邮件系统 2.0 版本 - 快速启动图形界面
' 版本: 2.1 (修复版)
' 系统: 2.0 完整功能版本
Option Explicit

Dim objShell, objFSO, currentDir
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 检查主脚本文件
Dim scriptPath
scriptPath = currentDir & "\\gui_main.py"

If Not objFSO.FileExists(scriptPath) Then
    MsgBox "错误：找不到gui_main.py文件！" & vbCrLf & vbCrLf & _
           "当前目录：" & currentDir & vbCrLf & vbCrLf & _
           "请确保gui_main.py文件在同一目录中。", vbCritical, "文件不存在"
    WScript.Quit
End If

' 启动图形界面
On Error Resume Next
Dim command, result

' 构建启动命令
command = "cmd /c ""cd /d """ & currentDir & """ && python gui_main.py"""

' 尝试启动
result = objShell.Run(command, 1, False)

' 检查是否有错误
If Err.Number <> 0 Then
    ' 如果有错误，尝试python3
    Err.Clear
    command = "cmd /c ""cd /d """ & currentDir & """ && python3 gui_main.py"""
    result = objShell.Run(command, 1, False)

    ' 如果还是有错误，显示错误信息
    If Err.Number <> 0 Then
        On Error GoTo 0
        MsgBox "启动失败！" & vbCrLf & vbCrLf & _
               "错误信息：" & Err.Description & vbCrLf & vbCrLf & _
               "请尝试以下解决方案：" & vbCrLf & _
               "1. 手动运行：python gui_main.py" & vbCrLf & _
               "2. 检查Python是否正确安装" & vbCrLf & _
               "3. 检查Python是否添加到PATH环境变量" & vbCrLf & _
               "4. 尝试重新安装Python", vbCritical, "启动错误"
        WScript.Quit
    End If
End If

On Error GoTo 0
'''
        
        with open('快速启动.vbs', 'w', encoding='utf-8') as f:
            f.write(vbs_content)
        
        self.logger.info("✅ 默认VBS启动器已创建")
    
    def _fix_automation_workflow(self):
        """修复自动化工作流"""
        try:
            self.logger.info("🔧 修复自动化工作流...")
            
            # 创建自动化工作流配置
            workflow_config = {
                "enabled": True,
                "steps": [
                    {
                        "name": "auto_reply_monitoring",
                        "enabled": True,
                        "trigger": "after_send",
                        "delay": 5,
                        "description": "发送后自动启动回复监控"
                    },
                    {
                        "name": "quality_db_import",
                        "enabled": True,
                        "trigger": "after_monitoring",
                        "delay": 10,
                        "description": "监控完成后自动导入质量数据库"
                    },
                    {
                        "name": "emergency_check",
                        "enabled": True,
                        "trigger": "after_import",
                        "delay": 5,
                        "description": "导入完成后自动检查应急状态"
                    },
                    {
                        "name": "coordination_sync",
                        "enabled": True,
                        "trigger": "after_emergency",
                        "delay": 5,
                        "description": "应急检查后自动同步协调系统"
                    },
                    {
                        "name": "cleanup_invalid",
                        "enabled": True,
                        "trigger": "periodic",
                        "interval": 3600,
                        "description": "定期清理无效收件人"
                    }
                ],
                "error_handling": {
                    "retry_count": 3,
                    "retry_delay": 30,
                    "continue_on_error": True,
                    "log_errors": True
                },
                "last_updated": datetime.datetime.now().isoformat()
            }
            
            with open('automation_workflow.json', 'w', encoding='utf-8') as f:
                json.dump(workflow_config, f, ensure_ascii=False, indent=2)
            
            self.logger.info("✅ 自动化工作流配置已创建")
            
        except Exception as e:
            self.logger.error(f"❌ 修复自动化工作流失败: {str(e)}")
    
    def _verify_automation_fix(self):
        """验证修复效果"""
        try:
            self.logger.info("🔍 验证修复效果...")
            
            verification_results = []
            
            # 检查配置文件
            for config_file in self.config_files:
                if os.path.exists(config_file):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        verification_results.append(f"✅ {config_file} 配置正确")
                    except Exception as e:
                        verification_results.append(f"❌ {config_file} 配置错误: {str(e)}")
                else:
                    verification_results.append(f"⚠️ {config_file} 不存在")
            
            # 检查VBS启动器
            if os.path.exists('快速启动.vbs'):
                verification_results.append("✅ VBS启动器存在")
            else:
                verification_results.append("❌ VBS启动器不存在")
            
            # 检查自动化工作流配置
            if os.path.exists('automation_workflow.json'):
                verification_results.append("✅ 自动化工作流配置存在")
            else:
                verification_results.append("❌ 自动化工作流配置不存在")
            
            # 输出验证结果
            self.logger.info("验证结果:")
            for result in verification_results:
                self.logger.info(f"  {result}")
            
            # 计算成功率
            success_count = len([r for r in verification_results if r.startswith("✅")])
            total_count = len(verification_results)
            success_rate = (success_count / total_count) * 100
            
            self.logger.info(f"修复成功率: {success_rate:.1f}% ({success_count}/{total_count})")
            
            if success_rate >= 80:
                self.logger.info("✅ 修复效果良好")
            else:
                self.logger.warning("⚠️ 修复效果一般，可能需要手动检查")
            
        except Exception as e:
            self.logger.error(f"❌ 验证修复效果失败: {str(e)}")
    
    def create_automation_test_script(self):
        """创建自动化测试脚本"""
        try:
            self.logger.info("📝 创建自动化测试脚本...")
            
            test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全自动模式测试脚本
测试全自动模式是否正常工作
"""

import json
import os
import datetime

def test_full_automation():
    """测试全自动模式"""
    print("🧪 全自动模式测试")
    print("=" * 50)
    
    # 测试配置文件
    config_files = [
        'all_features_config.json',
        'auto_reply_config.json', 
        'startup_config.json',
        'monitor_settings.json',
        'automation_workflow.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ {config_file} 配置正确")
            except Exception as e:
                print(f"❌ {config_file} 配置错误: {str(e)}")
        else:
            print(f"⚠️ {config_file} 不存在")
    
    # 测试模块导入
    modules = [
        'email_sender',
        'email_receiver', 
        'recipient_quality_manager',
        'qq_email_anti_spam',
        'system_coordinator'
    ]
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module} 模块可正常导入")
        except ImportError as e:
            print(f"❌ {module} 模块导入失败: {str(e)}")
    
    print("\\n🎯 测试完成")
    print("💡 如果所有项目都显示 ✅，说明全自动模式配置正确")

if __name__ == "__main__":
    test_full_automation()
'''
            
            with open('test_full_automation.py', 'w', encoding='utf-8') as f:
                f.write(test_script)
            
            self.logger.info("✅ 自动化测试脚本已创建: test_full_automation.py")
            
        except Exception as e:
            self.logger.error(f"❌ 创建自动化测试脚本失败: {str(e)}")
    
    def get_automation_status_report(self) -> Dict:
        """获取自动化状态报告"""
        try:
            report = {
                'status': 'unknown',
                'config_files': {},
                'missing_files': [],
                'automation_enabled': False,
                'last_check': datetime.datetime.now().isoformat()
            }
            
            # 检查配置文件
            for config_file in self.config_files:
                if os.path.exists(config_file):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)
                        report['config_files'][config_file] = 'valid'
                    except Exception:
                        report['config_files'][config_file] = 'invalid'
                else:
                    report['config_files'][config_file] = 'missing'
                    report['missing_files'].append(config_file)
            
            # 检查自动化是否启用
            try:
                with open('all_features_config.json', 'r', encoding='utf-8') as f:
                    config = json.load(f)
                report['automation_enabled'] = config.get('auto_mode_enabled', False)
            except Exception:
                pass
            
            # 确定总体状态
            valid_configs = len([status for status in report['config_files'].values() if status == 'valid'])
            total_configs = len(self.config_files)
            
            if valid_configs == total_configs and report['automation_enabled']:
                report['status'] = 'fully_enabled'
            elif valid_configs >= total_configs * 0.8:
                report['status'] = 'mostly_enabled'
            elif valid_configs > 0:
                report['status'] = 'partially_enabled'
            else:
                report['status'] = 'disabled'
            
            return report
            
        except Exception as e:
            self.logger.error(f"❌ 获取自动化状态报告失败: {str(e)}")
            return {}

def main():
    """主函数"""
    print("🔧 全自动模式修复工具")
    print("=" * 50)
    
    fixer = FullAutomationFixer()
    
    # 修复全自动模式
    fixer.fix_full_automation_mode()
    
    # 创建测试脚本
    fixer.create_automation_test_script()
    
    # 获取状态报告
    report = fixer.get_automation_status_report()
    
    if report:
        print(f"\\n📊 自动化状态: {report['status']}")
        print(f"自动化启用: {'是' if report['automation_enabled'] else '否'}")
        
        if report['missing_files']:
            print(f"缺少文件: {', '.join(report['missing_files'])}")
    
    print("\\n💡 使用说明:")
    print("1. 运行 python test_full_automation.py 测试配置")
    print("2. 使用 快速启动.vbs 启动系统")
    print("3. 在GUI中点击'一键启用所有功能'")

if __name__ == "__main__":
    main()
