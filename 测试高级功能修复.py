#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级功能修复效果
验证所有高级功能按钮是否正常工作
"""

import tkinter as tk
from tkinter import messagebox
import sys
import os
import traceback

def test_gui_functions():
    """测试GUI功能"""
    print("🔧 开始测试高级功能修复效果...")
    print("=" * 50)
    
    try:
        # 导入修复后的GUI
        from gui_complete_v3 import EmailSenderGUI
        
        print("✅ GUI模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        app = EmailSenderGUI(root)
        
        print("✅ GUI界面创建成功")
        
        # 测试各个功能方法是否存在
        test_methods = [
            # 历史记录功能
            ('view_email_history', '查看邮件历史'),
            ('show_statistics', '显示统计信息'),
            ('export_history', '导出历史记录'),
            
            # 智能搜索功能
            ('open_smart_search', '打开智能搜索'),
            
            # 队列系统功能
            ('add_to_queue', '添加到队列'),
            ('open_queue_system', '打开队列系统'),
            ('start_queue_sending', '开始队列发送'),
            ('pause_queue_sending', '暂停队列发送'),
            ('clear_queue', '清空队列'),
            ('on_auto_queue_changed', '自动队列模式变化'),
            
            # 监控功能
            ('test_monitor', '测试监控功能'),
            ('reset_monitor', '重置监控'),
            ('clear_log', '清空日志'),
            ('save_log', '保存日志'),
            
            # 高级功能
            ('open_quality_manager', '打开质量数据库'),
            ('open_anti_spam', '打开反垃圾邮件'),
            ('open_emergency_manager', '打开应急管理'),
            ('open_reply_monitor', '打开自动回复监控'),
            
            # 辅助方法
            ('update_queue_status', '更新队列状态'),
            ('_create_history_interface', '创建历史界面'),
            ('_create_search_interface', '创建搜索界面'),
            ('_search_history', '搜索历史记录'),
            ('_perform_smart_search', '执行智能搜索'),
        ]
        
        print("\n🔍 检查功能方法:")
        print("-" * 30)
        
        missing_methods = []
        working_methods = []
        
        for method_name, description in test_methods:
            if hasattr(app, method_name):
                method = getattr(app, method_name)
                if callable(method):
                    working_methods.append((method_name, description))
                    print(f"✅ {description} ({method_name})")
                else:
                    missing_methods.append((method_name, description))
                    print(f"❌ {description} ({method_name}) - 不是可调用方法")
            else:
                missing_methods.append((method_name, description))
                print(f"❌ {description} ({method_name}) - 方法不存在")
        
        print(f"\n📊 测试结果:")
        print(f"✅ 正常工作的功能: {len(working_methods)} 个")
        print(f"❌ 缺失或有问题的功能: {len(missing_methods)} 个")
        
        if missing_methods:
            print(f"\n⚠️ 需要修复的功能:")
            for method_name, description in missing_methods:
                print(f"  - {description} ({method_name})")
        
        # 测试后端模块是否可用
        print(f"\n🔧 检查后端模块:")
        print("-" * 30)
        
        backend_modules = [
            ('email_history_manager', 'EmailHistoryManager', '邮件历史管理器'),
            ('rag_search_engine', 'RAGSearchEngine', '智能搜索引擎'),
            ('queue_system', 'QueueSystemWindow', '队列系统'),
            ('email_receiver', 'EmailReceiver', '邮件接收器'),
            ('recipient_quality_manager', 'RecipientQualityManager', '质量数据库管理器'),
            ('anti_spam_manager', 'AntiSpamManager', '反垃圾邮件管理器'),
            ('qq_email_anti_spam', 'QQEmailAntiSpamManager', 'QQ应急管理器'),
        ]
        
        available_modules = []
        missing_modules = []
        
        for module_name, class_name, description in backend_modules:
            try:
                module = __import__(module_name)
                if hasattr(module, class_name):
                    available_modules.append((module_name, class_name, description))
                    print(f"✅ {description} ({module_name}.{class_name})")
                else:
                    missing_modules.append((module_name, class_name, description))
                    print(f"❌ {description} ({module_name}.{class_name}) - 类不存在")
            except ImportError as e:
                missing_modules.append((module_name, class_name, description))
                print(f"❌ {description} ({module_name}) - 模块导入失败: {str(e)}")
        
        print(f"\n📊 后端模块检查结果:")
        print(f"✅ 可用模块: {len(available_modules)} 个")
        print(f"❌ 缺失模块: {len(missing_modules)} 个")
        
        # 计算总体修复率
        total_functions = len(test_methods)
        working_functions = len(working_methods)
        fix_rate = (working_functions / total_functions) * 100
        
        total_modules = len(backend_modules)
        available_module_count = len(available_modules)
        module_rate = (available_module_count / total_modules) * 100
        
        overall_rate = (fix_rate + module_rate) / 2
        
        print(f"\n🎯 总体修复效果:")
        print(f"📊 功能修复率: {fix_rate:.1f}% ({working_functions}/{total_functions})")
        print(f"📦 模块可用率: {module_rate:.1f}% ({available_module_count}/{total_modules})")
        print(f"🎉 总体修复率: {overall_rate:.1f}%")
        
        if overall_rate >= 90:
            print(f"\n🎉 修复效果优秀！")
            print("✅ 大部分高级功能已经正常工作")
            print("✅ 后端模块基本可用")
            print("💡 建议：可以开始使用修复后的系统")
        elif overall_rate >= 70:
            print(f"\n✅ 修复效果良好！")
            print("✅ 主要功能已经修复")
            print("⚠️ 少数功能可能需要进一步优化")
            print("💡 建议：可以正常使用，遇到问题时再修复")
        else:
            print(f"\n⚠️ 修复效果一般")
            print("❌ 仍有较多功能需要修复")
            print("💡 建议：继续修复缺失的功能")
        
        # 关闭测试窗口
        root.destroy()
        
        return overall_rate >= 70
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🚀 启动高级功能修复测试...")
    
    try:
        success = test_gui_functions()
        
        if success:
            print(f"\n🎉 测试完成！修复效果良好")
            print("💡 现在可以启动完整功能版本测试实际效果")
            print("📝 建议运行: python gui_complete_v3.py")
        else:
            print(f"\n⚠️ 测试发现问题，需要进一步修复")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
