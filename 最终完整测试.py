#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终完整测试 - 验证所有修复
"""

import tkinter as tk
import time
from gui_main import EmailSenderGUI

def test_complete_system():
    """测试完整系统"""
    print("🚀 最终完整测试 - 验证所有修复")
    print("=" * 80)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建GUI实例
        app = EmailSenderGUI(root)
        
        print("✅ GUI创建成功")
        
        # 设置测试邮箱
        test_email = "<EMAIL>"
        app.sender_email.delete(0, tk.END)
        app.sender_email.insert(0, test_email)
        
        # 设置收件人
        app.recipient_emails.delete(1.0, tk.END)
        app.recipient_emails.insert(1.0, test_email)
        
        print(f"✅ 设置测试邮箱: {test_email}")
        
        # 测试一键启用功能
        print(f"\n🚀 测试一键启用功能...")
        
        # 模拟启用过程
        test_steps = [
            ("初始化系统组件", app._init_all_components),
            ("启用自动回复监控", app._enable_auto_reply_monitoring),
            ("启用质量数据库", app._enable_quality_database),
            ("启用反垃圾邮件", app._enable_anti_spam),
            ("启用QQ应急管理", app._enable_qq_emergency),
            ("启用智能队列", app._enable_smart_queue),
            ("启用深度协调", app._enable_deep_coordination),
            ("完成全功能配置", app._finalize_all_features)
        ]
        
        enable_success = True
        for step_name, step_func in test_steps:
            try:
                result = step_func(test_email)
                if result['success']:
                    print(f"  ✅ {step_name}")
                else:
                    print(f"  ❌ {step_name}: {result['error']}")
                    enable_success = False
            except Exception as e:
                print(f"  ❌ {step_name}: {str(e)}")
                enable_success = False
        
        if enable_success:
            print(f"\n📈 启用后状态:")
            print(f"  全功能模式: {getattr(app, 'all_features_enabled', False)}")
            print(f"  一键启用按钮状态: {app.enable_all_button['state']}")
            print(f"  重置按钮状态: {app.reset_all_button['state']}")
        
        # 测试QQ应急管理器
        print(f"\n🆘 测试QQ应急管理器...")
        
        if hasattr(app, 'qq_emergency_manager') and app.qq_emergency_manager:
            # 测试重置方法
            try:
                app.qq_emergency_manager.reset_consecutive_no_reply(test_email)
                print("  ✅ 重置连续无回复计数成功")
            except Exception as e:
                print(f"  ❌ 重置连续无回复计数失败: {str(e)}")
                enable_success = False
            
            # 测试增加方法
            try:
                count = app.qq_emergency_manager.increment_consecutive_no_reply(test_email)
                print(f"  ✅ 增加连续无回复计数成功: {count}")
            except Exception as e:
                print(f"  ❌ 增加连续无回复计数失败: {str(e)}")
                enable_success = False
        else:
            print("  ⚠️ QQ应急管理器未初始化")
        
        # 测试自动回复识别
        print(f"\n🔍 测试自动回复识别...")
        
        try:
            from email_receiver import EmailReceiver
            receiver = EmailReceiver(test_email, "test_password")
            
            # 测试用例
            test_email_content = {
                'subject': 'QQ邮箱自动回复',
                'body': '谢谢您的邮件，我目前不在办公室。',
                'from': '<EMAIL>',
                'headers': {}
            }
            
            is_auto, reply_type = receiver.is_auto_reply(test_email_content)
            if is_auto and reply_type == 'auto_reply':
                print("  ✅ 自动回复识别正常")
            else:
                print(f"  ❌ 自动回复识别异常: {reply_type}")
                enable_success = False
                
        except Exception as e:
            print(f"  ❌ 自动回复识别测试失败: {str(e)}")
            enable_success = False
        
        # 测试数据库修复
        print(f"\n🗄️ 测试数据库修复...")
        
        try:
            from 深度系统协调实现 import IntelligentDecisionEngine, SystemDataCenter
            
            data_center = SystemDataCenter()
            decision_engine = IntelligentDecisionEngine(data_center)
            
            # 测试发送历史数据获取
            history_data = decision_engine._get_sending_history_data(test_email)
            
            if 'recent_24h' in history_data:
                print("  ✅ 发送历史数据获取正常")
            else:
                print("  ❌ 发送历史数据获取异常")
                enable_success = False
                
        except Exception as e:
            print(f"  ❌ 数据库修复测试失败: {str(e)}")
            enable_success = False
        
        # 测试自动监控功能
        print(f"\n📡 测试自动监控功能...")
        
        try:
            # 启用自动回复监控
            app.auto_reply_monitoring.set(True)
            
            # 测试QQ应急状态更新
            if hasattr(app, '_update_qq_emergency_with_reply'):
                app._update_qq_emergency_with_reply(test_email, test_email)
                print("  ✅ QQ应急状态更新正常")
            else:
                print("  ❌ QQ应急状态更新方法不存在")
                enable_success = False
            
            # 测试自动监控方法
            if hasattr(app, 'auto_start_reply_monitoring'):
                print("  ✅ 自动监控方法存在")
            else:
                print("  ❌ 自动监控方法不存在")
                enable_success = False
                
        except Exception as e:
            print(f"  ❌ 自动监控功能测试失败: {str(e)}")
            enable_success = False
        
        # 测试重置功能
        print(f"\n🔄 测试重置功能...")
        
        reset_steps = [
            ("重置自动回复监控", app._reset_auto_reply_monitoring),
            ("重置质量数据库", app._reset_quality_database),
            ("重置反垃圾邮件", app._reset_anti_spam),
            ("重置QQ应急管理", app._reset_qq_emergency),
            ("重置智能队列", app._reset_smart_queue),
            ("重置深度协调", app._reset_deep_coordination),
            ("清理配置文件", app._cleanup_all_features_config),
            ("完成重置操作", app._finalize_reset)
        ]
        
        reset_success = True
        for step_name, step_func in reset_steps:
            try:
                result = step_func()
                if result['success']:
                    print(f"  ✅ {step_name}")
                else:
                    print(f"  ❌ {step_name}: {result['error']}")
                    reset_success = False
            except Exception as e:
                print(f"  ❌ {step_name}: {str(e)}")
                reset_success = False
        
        if reset_success:
            print(f"\n📉 重置后状态:")
            print(f"  全功能模式: {getattr(app, 'all_features_enabled', False)}")
            print(f"  一键启用按钮状态: {app.enable_all_button['state']}")
            print(f"  重置按钮状态: {app.reset_all_button['state']}")
        
        # 关闭窗口
        root.destroy()
        
        # 最终结果
        print(f"\n" + "=" * 80)
        if enable_success and reset_success:
            print("🎉 最终完整测试完全成功！")
            print("✅ 一键启用功能正常工作")
            print("✅ 一键重置功能正常工作")
            print("✅ QQ应急管理器完全修复")
            print("✅ 自动回复识别逻辑改进")
            print("✅ 数据库错误完全修复")
            print("✅ 自动监控功能完整")
            print("✅ 按钮状态切换正确")
            
            print("\n💡 系统现在完全可用，您可以：")
            print("  🚀 使用一键启用功能启用所有高级功能")
            print("  🔄 使用一键重置功能恢复到基础模式")
            print("  📧 发送邮件后自动启动回复监控")
            print("  🔍 准确识别自动回复和退信")
            print("  📊 实时更新QQ应急状态")
            print("  🆘 自动处理连续无回复情况")
            print("  📡 享受完整的邮件发送体验")
            
            return True
        else:
            print("⚠️ 最终完整测试存在问题")
            print(f"  启用功能: {'✅ 正常' if enable_success else '❌ 异常'}")
            print(f"  重置功能: {'✅ 正常' if reset_success else '❌ 异常'}")
            return False
        
    except Exception as e:
        print(f"❌ 最终完整测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 开始最终完整测试")
    print("=" * 100)
    
    # 测试完整系统
    success = test_complete_system()
    
    # 总结
    print("\n" + "=" * 100)
    if success:
        print("🎉 恭喜！所有功能完全修复并正常工作！")
        print("🚀 一键启用和重置功能已完全修复")
        print("📡 自动监控功能正常工作")
        print("🗄️ 数据库系统运行正常")
        print("🔍 自动回复识别逻辑已改进")
        print("🆘 QQ应急管理器完全修复")
        print("\n💡 现在您可以放心使用所有功能了！")
        return True
    else:
        print("⚠️ 部分功能存在问题，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
