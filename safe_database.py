#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的数据库操作包装器
"""

import sqlite3
import threading
import time
from contextlib import contextmanager

class SafeDatabase:
    """安全的数据库操作类"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.lock = threading.RLock()
    
    @contextmanager
    def get_connection(self, retries=3):
        """获取数据库连接"""
        for attempt in range(retries):
            try:
                with self.lock:
                    conn = sqlite3.connect(
                        self.db_path,
                        timeout=30.0,
                        check_same_thread=False
                    )
                    
                    # 设置优化参数
                    conn.execute("PRAGMA journal_mode=WAL")
                    conn.execute("PRAGMA synchronous=NORMAL")
                    conn.execute("PRAGMA busy_timeout=30000")
                    
                    try:
                        yield conn
                        conn.commit()
                        break
                    except Exception as e:
                        conn.rollback()
                        if attempt == retries - 1:
                            raise
                        time.sleep(0.1 * (attempt + 1))
                    finally:
                        conn.close()
                        
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < retries - 1:
                    time.sleep(0.5 * (attempt + 1))
                    continue
                raise
    
    def execute_query(self, query, params=None):
        """执行查询"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.fetchall()
    
    def execute_update(self, query, params=None):
        """执行更新"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            return cursor.rowcount

# 使用示例:
# db = SafeDatabase("your_database.db")
# results = db.execute_query("SELECT * FROM table")
# db.execute_update("UPDATE table SET column=? WHERE id=?", (value, id))
