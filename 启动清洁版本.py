#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动清洁版本的邮件系统
无拼写警告，优化布局，三栏设计
"""

import tkinter as tk
import sys
import os

def main():
    """启动清洁版本的邮件系统"""
    print("🎨 启动清洁版本的邮件系统...")
    print("✨ 特点：")
    print("   • 无拼写检查警告")
    print("   • 三栏优化布局")
    print("   • 简洁清晰的代码")
    print("   • 完整的功能实现")
    
    try:
        # 导入清洁版本GUI
        from gui_clean import EmailSenderGUI
        
        # 创建主窗口
        root = tk.Tk()
        
        # 创建应用实例
        app = EmailSenderGUI(root)
        
        print("✅ 清洁版本启动成功！")
        print("📐 布局说明：")
        print("   • 左侧：邮件配置、内容编辑、操作日志")
        print("   • 中间：快速操作、队列管理、附件管理")
        print("   • 右侧：系统监控、状态显示")
        print("   • 无IDE拼写警告")
        print("   • 代码简洁清晰")
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 gui_clean.py 文件存在且可正常导入")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查系统环境和依赖")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
