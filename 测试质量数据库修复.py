#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试质量数据库修复
验证 remove_recipient 方法是否正常工作
"""

import datetime
import os

def test_recipient_quality_manager():
    """测试收件人质量管理器的所有功能"""
    print("🧪 测试收件人质量管理器功能")
    print("="*50)
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        
        # 创建管理器实例
        quality_manager = RecipientQualityManager()
        print("✅ 质量管理器初始化成功")
        
        # 1. 测试添加收件人
        print("\n📝 步骤1: 测试添加收件人")
        test_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        added_count = 0
        for email in test_emails:
            if quality_manager.add_recipient(
                email=email,
                sender_email="<EMAIL>",
                initial_score=75 if "invalid" not in email else 15,
                source="测试添加"
            ):
                added_count += 1
        
        print(f"✅ 成功添加 {added_count}/{len(test_emails)} 个收件人")
        
        # 2. 测试获取收件人信息
        print("\n📊 步骤2: 测试获取收件人信息")
        for email in test_emails:
            info = quality_manager.get_recipient_quality(email, "<EMAIL>")
            if info:
                print(f"  📧 {email}: 评分 {info['quality_score']}, 状态 {info['status']}")
            else:
                print(f"  ❌ {email}: 未找到")
        
        # 3. 测试删除单个收件人
        print("\n🗑️ 步骤3: 测试删除单个收件人")
        test_remove_email = "<EMAIL>"
        
        if quality_manager.remove_recipient(test_remove_email, "<EMAIL>"):
            print(f"✅ 成功删除收件人: {test_remove_email}")
        else:
            print(f"❌ 删除收件人失败: {test_remove_email}")
        
        # 验证删除结果
        info = quality_manager.get_recipient_quality(test_remove_email, "<EMAIL>")
        if info is None:
            print(f"✅ 验证成功: {test_remove_email} 已被删除")
        else:
            print(f"❌ 验证失败: {test_remove_email} 仍然存在")
        
        # 4. 测试批量删除收件人
        print("\n🗑️ 步骤4: 测试批量删除收件人")
        batch_remove_emails = ["<EMAIL>", "<EMAIL>"]
        
        deleted_count = quality_manager.remove_recipients_batch(
            batch_remove_emails, 
            "<EMAIL>"
        )
        print(f"✅ 批量删除完成: {deleted_count}/{len(batch_remove_emails)} 个收件人")
        
        # 5. 测试智能批次创建
        print("\n🎯 步骤5: 测试智能批次创建")
        
        # 先添加一些高质量收件人用于测试
        high_quality_emails = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in high_quality_emails:
            quality_manager.add_recipient(
                email=email,
                sender_email="<EMAIL>",
                initial_score=85,
                source="测试高质量"
            )
        
        # 创建智能批次
        batch_result = quality_manager.create_smart_batches(
            batch_name="测试批次",
            total_recipients=100,
            quality_threshold=70.0,
            max_batch_size=3,
            strategy="quality_balanced"
        )
        
        if batch_result.get('success'):
            print(f"✅ 智能批次创建成功:")
            print(f"   📦 批次数量: {batch_result.get('batch_count', 0)}")
            print(f"   👥 总收件人: {batch_result.get('total_recipients', 0)}")
            
            batches = batch_result.get('batches', [])
            for batch in batches:
                print(f"   📋 {batch['batch_name']}: {batch['recipient_count']} 个收件人, 平均质量 {batch['avg_quality_score']}")
        else:
            print(f"❌ 智能批次创建失败: {batch_result.get('error', '未知错误')}")
        
        # 6. 测试获取高质量收件人
        print("\n⭐ 步骤6: 测试获取高质量收件人")
        high_quality_recipients = quality_manager.get_quality_recipients(
            min_quality_score=70.0,
            limit=10
        )
        
        print(f"✅ 找到 {len(high_quality_recipients)} 个高质量收件人:")
        for recipient in high_quality_recipients:
            print(f"  📧 {recipient.email}: {recipient.quality_score:.1f}分 ({recipient.status})")
        
        # 7. 测试清空数据库
        print("\n🧹 步骤7: 测试清空数据库")
        if quality_manager.clear_all_recipients("<EMAIL>"):
            print("✅ 成功清空测试数据")
        else:
            print("❌ 清空测试数据失败")
        
        print("\n🎉 所有测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_error_scenarios():
    """测试错误场景"""
    print("\n🚨 测试错误场景处理")
    print("-"*30)
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        
        # 测试删除不存在的收件人
        print("1. 测试删除不存在的收件人")
        result = quality_manager.remove_recipient("<EMAIL>")
        if not result:
            print("✅ 正确处理了不存在的收件人")
        else:
            print("❌ 错误处理了不存在的收件人")
        
        # 测试批量删除空列表
        print("2. 测试批量删除空列表")
        result = quality_manager.remove_recipients_batch([])
        if result == 0:
            print("✅ 正确处理了空列表")
        else:
            print("❌ 错误处理了空列表")
        
        # 测试获取不存在收件人的信息
        print("3. 测试获取不存在收件人的信息")
        info = quality_manager.get_recipient_quality("<EMAIL>")
        if info is None:
            print("✅ 正确返回了None")
        else:
            print("❌ 错误返回了数据")
        
        print("✅ 错误场景测试完成")
        
    except Exception as e:
        print(f"❌ 错误场景测试失败: {str(e)}")

def show_method_summary():
    """显示方法总结"""
    print("\n📋 RecipientQualityManager 新增方法总结")
    print("="*60)
    
    methods = [
        {
            'name': 'remove_recipient(email, sender_email=None)',
            'description': '删除指定收件人',
            'return': 'bool - 是否删除成功'
        },
        {
            'name': 'remove_recipients_batch(emails, sender_email=None)',
            'description': '批量删除收件人',
            'return': 'int - 成功删除的数量'
        },
        {
            'name': 'clear_all_recipients(sender_email=None)',
            'description': '清空所有收件人数据',
            'return': 'bool - 是否清空成功'
        },
        {
            'name': 'add_recipient(email, sender_email, initial_score, source)',
            'description': '添加新收件人',
            'return': 'bool - 是否添加成功'
        },
        {
            'name': 'update_recipient_score(email, new_score, sender_email=None)',
            'description': '更新收件人评分',
            'return': 'bool - 是否更新成功'
        },
        {
            'name': 'get_recipient_quality(email, sender_email=None)',
            'description': '获取单个收件人的质量信息',
            'return': 'Dict or None - 收件人信息'
        }
    ]
    
    for method in methods:
        print(f"🔧 {method['name']}")
        print(f"   📝 {method['description']}")
        print(f"   📤 返回: {method['return']}")
        print()

if __name__ == "__main__":
    print("🧪 质量数据库修复测试工具")
    print("="*60)
    
    # 运行主要功能测试
    test_recipient_quality_manager()
    
    # 运行错误场景测试
    test_error_scenarios()
    
    # 显示方法总结
    show_method_summary()
    
    print("🎊 测试程序结束")
    print("💡 现在 RecipientQualityManager 的 remove_recipient 方法已经可以正常使用了！")
