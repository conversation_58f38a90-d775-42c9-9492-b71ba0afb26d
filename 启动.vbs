' 自动化邮件发送助手 - VBS启动器
Option Explicit

Dim objShell, objFSO, currentDir
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 主菜单循环
Do
    Dim choice
    choice = ShowMenu()
    
    Select Case choice
        Case "1"
            RunPythonScript "main.py", "发送单封邮件"
        Case "2"
            RunPythonScript "main.py --batch", "批量发送邮件"
        Case "3"
            RunPythonScript "gui_main.py", "图形界面版本"
        Case "4"
            RunPythonScript "test_email.py", "测试邮件功能"
        Case "5"
            RunPythonScript "setup.py", "环境检查与设置"
        Case "6"
            OpenFile "README.md"
        Case "7"
            MsgBox "感谢使用自动化邮件发送助手！", vbInformation, "再见"
            Exit Do
        Case Else
            If choice <> "" Then
                MsgBox "无效选项，请重新选择！", vbExclamation, "错误"
            Else
                Exit Do ' 用户点击了取消
            End If
    End Select
Loop

' 显示主菜单
Function ShowMenu()
    Dim menu
    menu = "自动化邮件发送助手" & vbCrLf & vbCrLf & _
           "请选择功能：" & vbCrLf & vbCrLf & _
           "1. 发送单封邮件（命令行）" & vbCrLf & _
           "2. 批量发送邮件（命令行）" & vbCrLf & _
           "3. 图形界面版本（推荐）" & vbCrLf & _
           "4. 测试邮件功能" & vbCrLf & _
           "5. 环境检查与设置" & vbCrLf & _
           "6. 查看使用说明" & vbCrLf & _
           "7. 退出程序" & vbCrLf & vbCrLf & _
           "请输入选项 (1-7)："
    
    ShowMenu = InputBox(menu, "自动化邮件发送助手", "3")
End Function

' 运行Python脚本
Sub RunPythonScript(scriptName, description)
    On Error Resume Next
    
    ' 检查Python是否可用
    Dim pythonCheck
    pythonCheck = objShell.Run("python --version", 0, True)
    
    If pythonCheck <> 0 Then
        MsgBox "错误：未找到Python环境！" & vbCrLf & vbCrLf & _
               "请确保已安装Python 3.6或更高版本。", vbCritical, "Python环境错误"
        Exit Sub
    End If
    
    ' 检查脚本文件是否存在
    Dim scriptPath
    scriptPath = currentDir & "\" & Split(scriptName, " ")(0)
    
    If Not objFSO.FileExists(scriptPath) Then
        MsgBox "错误：找不到脚本文件！" & vbCrLf & vbCrLf & _
               "文件路径：" & scriptPath, vbCritical, "文件不存在"
        Exit Sub
    End If
    
    ' 运行Python脚本
    Dim command
    command = "cmd /k ""cd /d """ & currentDir & """ && python " & scriptName & """"
    objShell.Run command, 1, False

    ' 对于图形界面版本，直接退出VBS
    If InStr(scriptName, "gui_main.py") > 0 Then
        WScript.Quit
    End If
    
    If Err.Number <> 0 Then
        MsgBox "启动失败！" & vbCrLf & vbCrLf & _
               "错误信息：" & Err.Description & vbCrLf & _
               "错误代码：" & Err.Number, vbCritical, "启动错误"
        Err.Clear
    End If
End Sub

' 打开文件
Sub OpenFile(fileName)
    On Error Resume Next
    
    Dim filePath
    filePath = currentDir & "\" & fileName
    
    If objFSO.FileExists(filePath) Then
        objShell.Run """" & filePath & """", 1, False
        If Err.Number <> 0 Then
            MsgBox "无法打开文件：" & fileName & vbCrLf & vbCrLf & _
                   "请手动打开该文件。", vbExclamation, "打开文件失败"
            Err.Clear
        End If
    Else
        MsgBox "文件不存在：" & fileName & vbCrLf & vbCrLf & _
               "完整路径：" & filePath, vbExclamation, "文件不存在"
    End If
End Sub
