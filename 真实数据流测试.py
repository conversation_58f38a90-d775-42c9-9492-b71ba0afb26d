#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 真实数据流测试
验证从自动监控到质量数据库的真实数据流是否正常工作
"""

import sys
import os
import sqlite3
import datetime

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_real_data_flow():
    """测试真实数据流"""
    print("🧪 真实数据流测试")
    print("=" * 60)
    
    try:
        # 1. 检查数据库文件是否存在
        print("\n📁 步骤1: 检查数据库文件")
        
        databases = {
            'email_receiver.db': '自动回复监控数据库',
            'email_history.db': '邮件发送历史数据库',
            'recipient_quality.db': '收件人质量数据库'
        }
        
        existing_dbs = {}
        for db_file, desc in databases.items():
            if os.path.exists(db_file):
                print(f"✅ {desc}: {db_file} 存在")
                existing_dbs[db_file] = desc
            else:
                print(f"❌ {desc}: {db_file} 不存在")
        
        # 2. 检查自动回复监控数据
        print("\n📬 步骤2: 检查自动回复监控数据")
        
        if 'email_receiver.db' in existing_dbs:
            conn = sqlite3.connect('email_receiver.db')
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"📋 监控数据库表: {tables}")
            
            # 检查自动回复数据
            if 'auto_replies' in tables:
                cursor.execute("SELECT COUNT(*) FROM auto_replies")
                reply_count = cursor.fetchone()[0]
                print(f"📊 自动回复记录数: {reply_count}")
                
                if reply_count > 0:
                    cursor.execute("""
                        SELECT sender_email, recipient_email, reply_type, reply_time 
                        FROM auto_replies 
                        ORDER BY reply_time DESC 
                        LIMIT 5
                    """)
                    recent_replies = cursor.fetchall()
                    print("📝 最近的自动回复:")
                    for reply in recent_replies:
                        print(f"   {reply[0]} -> {reply[1]} ({reply[2]}) at {reply[3]}")
            
            # 检查收件人状态
            if 'recipient_status' in tables:
                cursor.execute("SELECT COUNT(*) FROM recipient_status")
                status_count = cursor.fetchone()[0]
                print(f"📊 收件人状态记录数: {status_count}")
            
            conn.close()
        else:
            print("⚠️ 自动回复监控数据库不存在，无法检查数据")
        
        # 3. 检查邮件发送历史数据
        print("\n📧 步骤3: 检查邮件发送历史数据")
        
        if 'email_history.db' in existing_dbs:
            conn = sqlite3.connect('email_history.db')
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"📋 历史数据库表: {tables}")
            
            # 检查发送记录
            if 'sent_emails' in tables:
                cursor.execute("SELECT COUNT(*) FROM sent_emails")
                sent_count = cursor.fetchone()[0]
                print(f"📊 发送记录数: {sent_count}")
                
                if sent_count > 0:
                    cursor.execute("""
                        SELECT sender, recipient, subject, send_time, success 
                        FROM sent_emails 
                        ORDER BY send_time DESC 
                        LIMIT 5
                    """)
                    recent_sends = cursor.fetchall()
                    print("📝 最近的发送记录:")
                    for send in recent_sends:
                        status = "成功" if send[4] else "失败"
                        print(f"   {send[0]} -> {send[1]} ({status}) at {send[3]}")
            
            conn.close()
        else:
            print("⚠️ 邮件发送历史数据库不存在，无法检查数据")
        
        # 4. 检查质量数据库数据
        print("\n📈 步骤4: 检查质量数据库数据")
        
        if 'recipient_quality.db' in existing_dbs:
            from recipient_quality_manager import RecipientQualityManager
            quality_manager = RecipientQualityManager()
            
            conn = sqlite3.connect('recipient_quality.db')
            cursor = conn.cursor()
            
            # 检查表结构
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            print(f"📋 质量数据库表: {tables}")
            
            # 检查收件人质量数据
            if 'recipient_quality' in tables:
                cursor.execute("SELECT COUNT(*) FROM recipient_quality")
                quality_count = cursor.fetchone()[0]
                print(f"📊 质量记录数: {quality_count}")
                
                if quality_count > 0:
                    cursor.execute("""
                        SELECT email, quality_score, status, total_sent, total_replies 
                        FROM recipient_quality 
                        ORDER BY quality_score DESC 
                        LIMIT 5
                    """)
                    top_quality = cursor.fetchall()
                    print("📝 质量最高的收件人:")
                    for recipient in top_quality:
                        print(f"   {recipient[0]} (评分: {recipient[1]}, 状态: {recipient[2]}, 发送: {recipient[3]}, 回复: {recipient[4]})")
                
                # 获取真实的质量分析
                print("\n📊 真实质量分析:")
                analytics = quality_manager.get_quality_analytics()
                if analytics:
                    overview = analytics.get('overview', {})
                    print(f"   总收件人: {overview.get('total_recipients', 0)}")
                    print(f"   平均质量: {overview.get('avg_quality_score', 0):.1f}")
                    print(f"   平均回复率: {overview.get('avg_response_rate', 0):.1f}%")
                    print(f"   总发送数: {overview.get('total_emails_sent', 0)}")
                    print(f"   总回复数: {overview.get('total_replies_received', 0)}")
                else:
                    print("   ❌ 无法获取质量分析数据")
            
            conn.close()
        else:
            print("⚠️ 质量数据库不存在，无法检查数据")
        
        # 5. 测试数据同步功能
        print("\n🔄 步骤5: 测试数据同步功能")
        
        if 'email_receiver.db' in existing_dbs and 'recipient_quality.db' in existing_dbs:
            print("🧪 模拟数据同步过程...")
            
            # 从监控数据库获取有效收件人
            conn = sqlite3.connect('email_receiver.db')
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    SELECT DISTINCT recipient_email, sender_email 
                    FROM auto_replies 
                    WHERE reply_type = 'auto_reply'
                    LIMIT 3
                """)
                valid_recipients = cursor.fetchall()
                
                if valid_recipients:
                    print(f"📬 找到 {len(valid_recipients)} 个有效收件人")
                    
                    # 测试同步到质量数据库
                    from recipient_quality_manager import RecipientQualityManager
                    quality_manager = RecipientQualityManager()
                    
                    sync_count = 0
                    for recipient_email, sender_email in valid_recipients:
                        # 检查是否已存在
                        existing = quality_manager.get_recipient_quality(recipient_email, sender_email)
                        
                        if not existing:
                            # 添加新收件人
                            success = quality_manager.add_recipient(
                                email=recipient_email,
                                sender_email=sender_email,
                                initial_score=80.0,
                                source="真实数据流测试"
                            )
                            if success:
                                sync_count += 1
                                print(f"   ✅ 同步收件人: {recipient_email}")
                        else:
                            print(f"   ℹ️ 收件人已存在: {recipient_email}")
                    
                    print(f"🔄 数据同步完成，新增 {sync_count} 个收件人")
                else:
                    print("⚠️ 监控数据库中没有有效收件人数据")
                
            except Exception as e:
                print(f"❌ 数据同步测试失败: {str(e)}")
            
            conn.close()
        else:
            print("⚠️ 缺少必要的数据库文件，无法测试数据同步")
        
        # 6. 测试智能批次创建
        print("\n🎯 步骤6: 测试智能批次创建")
        
        if 'recipient_quality.db' in existing_dbs:
            from recipient_quality_manager import RecipientQualityManager
            quality_manager = RecipientQualityManager()
            
            # 获取高质量收件人
            high_quality = quality_manager.get_high_quality_recipients(min_score=70.0)
            print(f"📊 高质量收件人数量: {len(high_quality)}")
            
            if len(high_quality) >= 5:
                print("🧪 测试创建智能批次...")
                
                batch_result = quality_manager.create_smart_batches(
                    batch_name="真实数据测试批次",
                    total_recipients=len(high_quality),
                    quality_threshold=70.0,
                    max_batch_size=10,
                    strategy="quality_balanced"
                )
                
                if batch_result.get('success'):
                    print(f"✅ 智能批次创建成功:")
                    print(f"   批次数量: {batch_result.get('batch_count', 0)}")
                    print(f"   总收件人: {batch_result.get('total_recipients', 0)}")
                    
                    # 显示批次详情
                    batches = batch_result.get('batches', [])
                    for batch in batches[:2]:  # 只显示前2个批次
                        print(f"   📦 {batch['batch_name']}: {batch['recipient_count']}人, 平均质量{batch['avg_quality_score']}")
                else:
                    print(f"❌ 智能批次创建失败: {batch_result.get('error', '未知错误')}")
            else:
                print("⚠️ 高质量收件人不足，无法创建智能批次")
        
        # 7. 总结
        print("\n📋 测试总结")
        print("=" * 40)
        
        issues = []
        if 'email_receiver.db' not in existing_dbs:
            issues.append("缺少自动回复监控数据库")
        if 'email_history.db' not in existing_dbs:
            issues.append("缺少邮件发送历史数据库")
        if 'recipient_quality.db' not in existing_dbs:
            issues.append("缺少收件人质量数据库")
        
        if not issues:
            print("✅ 所有数据库文件都存在")
            print("✅ 真实数据流基础设施完整")
            print("💡 建议: 确保有真实的发送和回复数据来测试完整流程")
        else:
            print("❌ 发现以下问题:")
            for issue in issues:
                print(f"   • {issue}")
            print("💡 建议: 先进行邮件发送和自动回复监控以生成真实数据")
        
        print("\n🎯 下一步操作建议:")
        print("1. 发送一些测试邮件")
        print("2. 启动自动回复监控")
        print("3. 等待收到自动回复")
        print("4. 验证数据同步到质量数据库")
        print("5. 测试智能批次创建功能")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_real_data_flow()
