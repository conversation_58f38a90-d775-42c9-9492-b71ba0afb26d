#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试2.0系统长期记忆功能
验证数据持久化是否正常工作
"""

import os
import json
import datetime

def main():
    """主测试函数"""
    print("🧠 测试2.0系统长期记忆功能")
    print("="*60)
    
    # 1. 测试长期记忆管理器
    test_memory_manager()
    
    # 2. 测试数据库创建
    test_database_creation()
    
    # 3. 测试数据保存和加载
    test_data_persistence()
    
    # 4. 测试配置文件备份
    test_config_backup()
    
    # 5. 生成测试报告
    generate_test_report()

def test_memory_manager():
    """测试长期记忆管理器"""
    print("\n📋 测试长期记忆管理器...")
    
    try:
        from 长期记忆功能增强 import LongTermMemoryManager
        memory_manager = LongTermMemoryManager()
        
        print("   ✅ 长期记忆管理器创建成功")
        
        # 测试目录创建
        if os.path.exists("user_data"):
            print("   ✅ user_data目录已创建")
        else:
            print("   ❌ user_data目录创建失败")
        
        # 测试数据库文件
        db_path = os.path.join("user_data", "user_settings.db")
        if os.path.exists(db_path):
            print("   ✅ 数据库文件已创建")
        else:
            print("   ❌ 数据库文件创建失败")
        
        return memory_manager
        
    except Exception as e:
        print(f"   ❌ 长期记忆管理器测试失败: {str(e)}")
        return None

def test_database_creation():
    """测试数据库创建"""
    print("\n🗄️ 测试数据库创建...")
    
    try:
        import sqlite3
        db_path = os.path.join("user_data", "user_settings.db")
        
        if not os.path.exists(db_path):
            print("   ❌ 数据库文件不存在")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查表是否存在
        tables = [
            'user_settings',
            'email_templates', 
            'recipient_groups',
            'user_preferences'
        ]
        
        for table in tables:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
            if cursor.fetchone():
                print(f"   ✅ 表 {table} 已创建")
            else:
                print(f"   ❌ 表 {table} 创建失败")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 数据库测试失败: {str(e)}")
        return False

def test_data_persistence():
    """测试数据保存和加载"""
    print("\n💾 测试数据持久化...")
    
    try:
        from 长期记忆功能增强 import LongTermMemoryManager
        memory_manager = LongTermMemoryManager()
        
        # 测试保存设置
        test_data = {
            "send_mode": "fast",
            "add_personalization": True,
            "auto_reply_monitoring": False,
            "sender_email": "<EMAIL>",
            "subject": "测试邮件主题",
            "body": "这是测试邮件正文",
            "recipients": "<EMAIL>;<EMAIL>",
            "attachments": ["test1.txt", "test2.pdf"]
        }
        
        # 保存测试数据
        memory_manager.save_setting("ui", "send_mode", test_data["send_mode"])
        memory_manager.save_setting("ui", "add_personalization", test_data["add_personalization"], "bool")
        memory_manager.save_setting("ui", "auto_reply_monitoring", test_data["auto_reply_monitoring"], "bool")
        memory_manager.save_setting("email", "sender_email", test_data["sender_email"])
        memory_manager.save_setting("email", "subject", test_data["subject"])
        memory_manager.save_setting("email", "body", test_data["body"])
        memory_manager.save_setting("email", "recipients", test_data["recipients"])
        memory_manager.save_setting("email", "attachments", test_data["attachments"], "list")
        
        print("   ✅ 测试数据已保存")
        
        # 加载测试数据
        loaded_data = {
            "send_mode": memory_manager.load_setting("ui", "send_mode"),
            "add_personalization": memory_manager.load_setting("ui", "add_personalization"),
            "auto_reply_monitoring": memory_manager.load_setting("ui", "auto_reply_monitoring"),
            "sender_email": memory_manager.load_setting("email", "sender_email"),
            "subject": memory_manager.load_setting("email", "subject"),
            "body": memory_manager.load_setting("email", "body"),
            "recipients": memory_manager.load_setting("email", "recipients"),
            "attachments": memory_manager.load_setting("email", "attachments")
        }
        
        print("   ✅ 测试数据已加载")
        
        # 验证数据一致性
        success_count = 0
        total_count = len(test_data)
        
        for key, original_value in test_data.items():
            loaded_value = loaded_data[key]
            if original_value == loaded_value:
                print(f"   ✅ {key}: 数据一致")
                success_count += 1
            else:
                print(f"   ❌ {key}: 数据不一致 (原始: {original_value}, 加载: {loaded_value})")
        
        print(f"   📊 数据一致性: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        # 测试邮件模板
        memory_manager.save_email_template(
            "测试模板",
            "测试邮件主题",
            "这是测试邮件模板正文",
            ["template_attachment.pdf"]
        )
        
        templates = memory_manager.load_email_templates()
        if templates and len(templates) > 0:
            print("   ✅ 邮件模板保存和加载成功")
        else:
            print("   ❌ 邮件模板保存或加载失败")
        
        # 测试收件人组
        memory_manager.save_recipient_group(
            "测试组",
            ["<EMAIL>", "<EMAIL>", "<EMAIL>"],
            "这是一个测试收件人组"
        )
        
        groups = memory_manager.load_recipient_groups()
        if groups and len(groups) > 0:
            print("   ✅ 收件人组保存和加载成功")
        else:
            print("   ❌ 收件人组保存或加载失败")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"   ❌ 数据持久化测试失败: {str(e)}")
        return False

def test_config_backup():
    """测试配置文件备份"""
    print("\n📁 测试配置文件备份...")
    
    backup_dir = "config_backup"
    if os.path.exists(backup_dir):
        print("   ✅ 备份目录已创建")
        
        # 检查备份文件
        config_files = [
            'all_features_config.json.backup',
            'auth_codes.json.backup',
            'monitor_settings.json.backup',
            'startup_config.json.backup',
            'automation_workflow.json.backup'
        ]
        
        backup_count = 0
        for config_file in config_files:
            backup_path = os.path.join(backup_dir, config_file)
            if os.path.exists(backup_path):
                print(f"   ✅ 已备份: {config_file}")
                backup_count += 1
            else:
                print(f"   ⚠️ 未找到备份: {config_file}")
        
        print(f"   📊 备份文件: {backup_count}/{len(config_files)} 个")
        return backup_count > 0
        
    else:
        print("   ❌ 备份目录不存在")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成测试报告...")
    
    report = {
        "test_time": datetime.datetime.now().isoformat(),
        "test_results": {
            "memory_manager": "测试完成",
            "database_creation": "测试完成",
            "data_persistence": "测试完成",
            "config_backup": "测试完成"
        },
        "files_created": [],
        "directories_created": [],
        "recommendations": []
    }
    
    # 检查创建的文件和目录
    if os.path.exists("user_data"):
        report["directories_created"].append("user_data/")
        
        db_path = os.path.join("user_data", "user_settings.db")
        if os.path.exists(db_path):
            report["files_created"].append("user_data/user_settings.db")
    
    if os.path.exists("config_backup"):
        report["directories_created"].append("config_backup/")
    
    if os.path.exists("gui_memory_patch.py"):
        report["files_created"].append("gui_memory_patch.py")
    
    if os.path.exists("长期记忆功能说明.md"):
        report["files_created"].append("长期记忆功能说明.md")
    
    # 生成建议
    report["recommendations"] = [
        "重启2.0系统以应用长期记忆功能",
        "测试邮件内容的保存和恢复",
        "验证用户设置的持久化",
        "定期备份user_data目录",
        "检查自动保存功能是否正常工作"
    ]
    
    # 保存报告
    with open('memory_test_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print("   ✅ 测试报告已保存: memory_test_report.json")
    
    # 显示总结
    print("\n🎯 测试总结:")
    print(f"   📁 创建目录: {len(report['directories_created'])} 个")
    print(f"   📄 创建文件: {len(report['files_created'])} 个")
    print(f"   💡 建议事项: {len(report['recommendations'])} 个")
    
    print("\n✅ 长期记忆功能测试完成!")
    print("💡 现在您可以重启2.0系统，享受数据持久化功能了！")

def show_usage_instructions():
    """显示使用说明"""
    print("\n📖 长期记忆功能使用说明:")
    print("="*60)
    
    instructions = """
🧠 长期记忆功能已成功集成到2.0系统中！

📋 自动保存的数据:
• 发送模式设置（标准/快速/安全）
• 个性化设置开关
• 自动回复监控开关
• 自动队列模式开关
• 发件人邮箱地址
• 收件人列表
• 邮件主题和正文
• 附件列表
• 邮件发送队列
• 授权码管理

🔄 自动保存机制:
• 每30秒自动保存一次
• 程序退出时自动保存
• 程序启动时自动恢复

📁 数据存储位置:
• 用户数据: user_data/user_settings.db
• 配置备份: config_backup/
• 邮件模板和收件人组也保存在数据库中

💡 使用建议:
1. 重启2.0系统以应用长期记忆功能
2. 正常使用系统，数据会自动保存
3. 定期备份user_data目录
4. 如需迁移数据，复制整个user_data目录即可

🎉 现在您再也不用担心重启后数据丢失了！
"""
    
    print(instructions)

if __name__ == "__main__":
    main()
    show_usage_instructions()
