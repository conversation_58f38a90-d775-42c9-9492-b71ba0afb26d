#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统启动脚本 - 清洁版本
无拼写警告，直接启动完整功能版
"""

import sys
import os

def main():
    """主启动函数"""
    try:
        # 尝试启动完整功能版
        if os.path.exists("gui_complete_v3.py"):
            print("🚀 启动完整功能版...")
            import gui_complete_v3
            gui_complete_v3.main()
        elif os.path.exists("gui_main.py"):
            print("🚀 启动原版...")
            import gui_main
            gui_main.main()
        else:
            print("❌ 找不到可启动的文件")
            sys.exit(1)
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保所有依赖已安装")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
