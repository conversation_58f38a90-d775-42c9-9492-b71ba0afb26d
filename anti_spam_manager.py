#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反垃圾邮件管理器
智能控制发送频率，避免邮件进入垃圾箱
"""

import sqlite3
import datetime
import time
import logging
import json
import random
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

@dataclass
class SendingPattern:
    """发送模式配置"""
    name: str
    initial_rate: int  # 初始发送速率（邮件/小时）
    max_rate: int      # 最大发送速率
    warmup_days: int   # 预热天数
    batch_size: int    # 批次大小
    interval_min: int  # 最小间隔（秒）
    interval_max: int  # 最大间隔（秒）
    daily_limit: int   # 每日限制

class AntiSpamManager:
    """反垃圾邮件管理器"""
    
    def __init__(self, db_path: str = "anti_spam.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._init_database()
        
        # 预定义发送模式
        self.sending_patterns = {
            'conservative': SendingPattern(
                name='保守模式',
                initial_rate=10,    # 每小时10封
                max_rate=50,        # 最大每小时50封
                warmup_days=7,      # 7天预热
                batch_size=5,       # 每批5封
                interval_min=300,   # 最小5分钟间隔
                interval_max=900,   # 最大15分钟间隔
                daily_limit=200     # 每日200封
            ),
            'moderate': SendingPattern(
                name='适中模式',
                initial_rate=20,    # 每小时20封
                max_rate=100,       # 最大每小时100封
                warmup_days=5,      # 5天预热
                batch_size=10,      # 每批10封
                interval_min=180,   # 最小3分钟间隔
                interval_max=600,   # 最大10分钟间隔
                daily_limit=500     # 每日500封
            ),
            'aggressive': SendingPattern(
                name='积极模式',
                initial_rate=30,    # 每小时30封
                max_rate=200,       # 最大每小时200封
                warmup_days=3,      # 3天预热
                batch_size=15,      # 每批15封
                interval_min=120,   # 最小2分钟间隔
                interval_max=300,   # 最大5分钟间隔
                daily_limit=1000    # 每日1000封
            )
        }
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 发送统计表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sending_stats (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_email TEXT NOT NULL,
                send_date TEXT NOT NULL,
                hour INTEGER NOT NULL,
                emails_sent INTEGER DEFAULT 0,
                emails_delivered INTEGER DEFAULT 0,
                emails_bounced INTEGER DEFAULT 0,
                emails_replied INTEGER DEFAULT 0,
                spam_rate REAL DEFAULT 0.0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(sender_email, send_date, hour)
            )
        ''')
        
        # 发送模式配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sender_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_email TEXT UNIQUE NOT NULL,
                sending_pattern TEXT DEFAULT 'moderate',
                warmup_start_date TEXT,
                current_rate INTEGER DEFAULT 20,
                reputation_score REAL DEFAULT 50.0,
                last_spam_detection TEXT,
                is_warmed_up INTEGER DEFAULT 0,
                custom_settings TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 垃圾邮件检测记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS spam_detection (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_email TEXT NOT NULL,
                detection_time TEXT NOT NULL,
                detection_type TEXT NOT NULL,
                threshold_value REAL,
                actual_value REAL,
                action_taken TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 发送队列表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sending_queue (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_email TEXT NOT NULL,
                recipient_email TEXT NOT NULL,
                subject TEXT,
                body TEXT,
                priority INTEGER DEFAULT 5,
                scheduled_time TEXT,
                status TEXT DEFAULT 'pending',
                retry_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                sent_at TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        self.logger.info("反垃圾邮件数据库初始化完成")
    
    def initialize_sender(self, sender_email: str, pattern: str = 'moderate') -> bool:
        """初始化发件人配置"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.datetime.now().isoformat()
            pattern_config = self.sending_patterns.get(pattern, self.sending_patterns['moderate'])
            
            cursor.execute('''
                INSERT OR REPLACE INTO sender_config 
                (sender_email, sending_pattern, warmup_start_date, current_rate, 
                 reputation_score, is_warmed_up, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (sender_email, pattern, current_time, pattern_config.initial_rate, 
                  50.0, 0, current_time))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"发件人 {sender_email} 初始化完成，使用 {pattern_config.name}")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化发件人失败: {str(e)}")
            return False
    
    def check_sending_permission(self, sender_email: str, recipient_count: int) -> Dict:
        """检查发送权限"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取发件人配置
            cursor.execute('''
                SELECT sending_pattern, current_rate, reputation_score, 
                       warmup_start_date, is_warmed_up, last_spam_detection
                FROM sender_config WHERE sender_email = ?
            ''', (sender_email,))
            
            config = cursor.fetchone()
            if not config:
                # 自动初始化
                self.initialize_sender(sender_email)
                return self.check_sending_permission(sender_email, recipient_count)
            
            pattern_name, current_rate, reputation, warmup_start, is_warmed, last_spam = config
            pattern = self.sending_patterns[pattern_name]
            
            # 检查今日发送统计
            today = datetime.datetime.now().strftime('%Y-%m-%d')
            cursor.execute('''
                SELECT SUM(emails_sent) FROM sending_stats 
                WHERE sender_email = ? AND send_date = ?
            ''', (sender_email, today))
            
            today_sent = cursor.fetchone()[0] or 0
            
            # 检查当前小时发送统计
            current_hour = datetime.datetime.now().hour
            cursor.execute('''
                SELECT emails_sent FROM sending_stats 
                WHERE sender_email = ? AND send_date = ? AND hour = ?
            ''', (sender_email, today, current_hour))
            
            hour_sent = cursor.fetchone()
            hour_sent = hour_sent[0] if hour_sent else 0
            
            conn.close()
            
            # 计算限制
            daily_remaining = pattern.daily_limit - today_sent
            hourly_remaining = current_rate - hour_sent
            
            # 检查垃圾邮件风险
            spam_risk = self._calculate_spam_risk(sender_email, recipient_count)
            
            # 生成建议
            recommendations = self._generate_sending_recommendations(
                sender_email, recipient_count, daily_remaining, hourly_remaining, spam_risk
            )
            
            return {
                'can_send': daily_remaining > 0 and hourly_remaining > 0,
                'daily_remaining': daily_remaining,
                'hourly_remaining': hourly_remaining,
                'current_rate': current_rate,
                'reputation_score': reputation,
                'spam_risk': spam_risk,
                'is_warmed_up': bool(is_warmed),
                'recommendations': recommendations,
                'suggested_batch_size': min(pattern.batch_size, hourly_remaining, recipient_count),
                'suggested_interval': self._calculate_optimal_interval(pattern, spam_risk)
            }
            
        except Exception as e:
            self.logger.error(f"检查发送权限失败: {str(e)}")
            return {'can_send': False, 'error': str(e)}
    
    def _calculate_spam_risk(self, sender_email: str, recipient_count: int) -> float:
        """计算垃圾邮件风险"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取最近7天的发送统计
            week_ago = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
            cursor.execute('''
                SELECT AVG(spam_rate), COUNT(*) FROM sending_stats 
                WHERE sender_email = ? AND send_date >= ?
            ''', (sender_email, week_ago))
            
            result = cursor.fetchone()
            avg_spam_rate = result[0] or 0.0
            days_active = result[1] or 0
            
            # 获取声誉分数
            cursor.execute('''
                SELECT reputation_score, is_warmed_up FROM sender_config 
                WHERE sender_email = ?
            ''', (sender_email,))
            
            config = cursor.fetchone()
            reputation = config[0] if config else 50.0
            is_warmed = config[1] if config else 0
            
            conn.close()
            
            # 计算风险因子
            risk_factors = []
            
            # 1. 历史垃圾邮件率
            risk_factors.append(avg_spam_rate * 0.4)
            
            # 2. 声誉分数（分数越低风险越高）
            reputation_risk = max(0, (50 - reputation) / 50) * 0.3
            risk_factors.append(reputation_risk)
            
            # 3. 预热状态
            warmup_risk = 0.2 if not is_warmed else 0.0
            risk_factors.append(warmup_risk)
            
            # 4. 发送量风险
            volume_risk = min(0.1, recipient_count / 1000) * 0.1
            risk_factors.append(volume_risk)
            
            total_risk = sum(risk_factors)
            return min(1.0, total_risk)
            
        except Exception as e:
            self.logger.error(f"计算垃圾邮件风险失败: {str(e)}")
            return 0.5  # 默认中等风险
    
    def _calculate_optimal_interval(self, pattern: SendingPattern, spam_risk: float) -> int:
        """计算最优发送间隔"""
        # 基础间隔
        base_interval = (pattern.interval_min + pattern.interval_max) // 2
        
        # 根据垃圾邮件风险调整
        risk_multiplier = 1 + spam_risk
        
        # 添加随机性避免模式识别
        randomness = random.uniform(0.8, 1.2)
        
        optimal_interval = int(base_interval * risk_multiplier * randomness)
        
        # 确保在合理范围内
        return max(pattern.interval_min, min(pattern.interval_max * 2, optimal_interval))
    
    def _generate_sending_recommendations(self, sender_email: str, recipient_count: int, 
                                        daily_remaining: int, hourly_remaining: int, 
                                        spam_risk: float) -> List[str]:
        """生成发送建议"""
        recommendations = []
        
        if daily_remaining <= 0:
            recommendations.append("⚠️ 今日发送量已达上限，建议明天继续")
        elif daily_remaining < recipient_count:
            recommendations.append(f"⚠️ 今日剩余额度 {daily_remaining} 封，建议分批发送")
        
        if hourly_remaining <= 0:
            recommendations.append("⚠️ 当前小时发送量已达上限，建议等待下个小时")
        elif hourly_remaining < recipient_count:
            recommendations.append(f"⚠️ 当前小时剩余额度 {hourly_remaining} 封，建议分批发送")
        
        if spam_risk > 0.7:
            recommendations.append("🚨 垃圾邮件风险较高，建议降低发送频率")
        elif spam_risk > 0.5:
            recommendations.append("⚠️ 垃圾邮件风险中等，建议适当控制发送速度")
        
        if not recommendations:
            recommendations.append("✅ 发送条件良好，可以正常发送")
        
        return recommendations

    def record_sending_result(self, sender_email: str, recipient_email: str,
                            success: bool, bounced: bool = False,
                            replied: bool = False) -> bool:
        """记录发送结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            current_time = datetime.datetime.now()
            today = current_time.strftime('%Y-%m-%d')
            hour = current_time.hour

            # 更新发送统计
            cursor.execute('''
                INSERT OR IGNORE INTO sending_stats
                (sender_email, send_date, hour, emails_sent, emails_delivered,
                 emails_bounced, emails_replied)
                VALUES (?, ?, ?, 0, 0, 0, 0)
            ''', (sender_email, today, hour))

            # 更新计数
            if success:
                cursor.execute('''
                    UPDATE sending_stats
                    SET emails_sent = emails_sent + 1,
                        emails_delivered = emails_delivered + 1
                    WHERE sender_email = ? AND send_date = ? AND hour = ?
                ''', (sender_email, today, hour))
            else:
                cursor.execute('''
                    UPDATE sending_stats
                    SET emails_sent = emails_sent + 1
                    WHERE sender_email = ? AND send_date = ? AND hour = ?
                ''', (sender_email, today, hour))

            if bounced:
                cursor.execute('''
                    UPDATE sending_stats
                    SET emails_bounced = emails_bounced + 1
                    WHERE sender_email = ? AND send_date = ? AND hour = ?
                ''', (sender_email, today, hour))

            if replied:
                cursor.execute('''
                    UPDATE sending_stats
                    SET emails_replied = emails_replied + 1
                    WHERE sender_email = ? AND send_date = ? AND hour = ?
                ''', (sender_email, today, hour))

            conn.commit()
            conn.close()

            # 检查是否需要调整发送策略
            self._auto_adjust_strategy(sender_email)

            return True

        except Exception as e:
            self.logger.error(f"记录发送结果失败: {str(e)}")
            return False

    def detect_spam_pattern(self, sender_email: str) -> Dict:
        """检测垃圾邮件模式"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取最近的发送统计
            cursor.execute('''
                SELECT send_date, hour, emails_sent, emails_delivered,
                       emails_bounced, emails_replied
                FROM sending_stats
                WHERE sender_email = ?
                ORDER BY send_date DESC, hour DESC
                LIMIT 24
            ''', (sender_email,))

            recent_stats = cursor.fetchall()
            conn.close()

            if len(recent_stats) < 3:
                return {'spam_detected': False, 'reason': '数据不足'}

            # 分析模式
            detections = []

            # 1. 检查回复率急剧下降
            reply_rates = []
            for stat in recent_stats:
                sent, delivered, replied = stat[2], stat[3], stat[5]
                if delivered > 0:
                    reply_rate = replied / delivered
                    reply_rates.append(reply_rate)

            if len(reply_rates) >= 3:
                recent_avg = sum(reply_rates[:3]) / 3
                earlier_avg = sum(reply_rates[3:6]) / min(3, len(reply_rates[3:6])) if len(reply_rates) > 3 else recent_avg

                if earlier_avg > 0 and recent_avg < earlier_avg * 0.3:  # 回复率下降70%
                    detections.append({
                        'type': 'reply_rate_drop',
                        'severity': 'high',
                        'description': f'回复率从 {earlier_avg:.2%} 下降到 {recent_avg:.2%}',
                        'threshold': 0.3,
                        'actual': recent_avg / earlier_avg if earlier_avg > 0 else 0
                    })

            # 2. 检查退信率异常
            bounce_rates = []
            for stat in recent_stats:
                sent, bounced = stat[2], stat[4]
                if sent > 0:
                    bounce_rate = bounced / sent
                    bounce_rates.append(bounce_rate)

            if bounce_rates:
                avg_bounce_rate = sum(bounce_rates) / len(bounce_rates)
                if avg_bounce_rate > 0.1:  # 退信率超过10%
                    detections.append({
                        'type': 'high_bounce_rate',
                        'severity': 'medium',
                        'description': f'退信率过高: {avg_bounce_rate:.2%}',
                        'threshold': 0.1,
                        'actual': avg_bounce_rate
                    })

            # 3. 检查发送量突然增加
            send_counts = [stat[2] for stat in recent_stats]
            if len(send_counts) >= 5:
                recent_avg = sum(send_counts[:3]) / 3
                baseline_avg = sum(send_counts[3:]) / len(send_counts[3:])

                if baseline_avg > 0 and recent_avg > baseline_avg * 3:  # 发送量增加3倍
                    detections.append({
                        'type': 'volume_spike',
                        'severity': 'low',
                        'description': f'发送量突然增加: 从平均 {baseline_avg:.1f} 增加到 {recent_avg:.1f}',
                        'threshold': 3.0,
                        'actual': recent_avg / baseline_avg if baseline_avg > 0 else 0
                    })

            # 记录检测结果
            if detections:
                self._record_spam_detection(sender_email, detections)

            return {
                'spam_detected': len(detections) > 0,
                'detections': detections,
                'risk_level': self._calculate_risk_level(detections),
                'recommendations': self._generate_anti_spam_recommendations(detections)
            }

        except Exception as e:
            self.logger.error(f"检测垃圾邮件模式失败: {str(e)}")
            return {'spam_detected': False, 'error': str(e)}

    def _record_spam_detection(self, sender_email: str, detections: List[Dict]):
        """记录垃圾邮件检测结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            current_time = datetime.datetime.now().isoformat()

            for detection in detections:
                cursor.execute('''
                    INSERT INTO spam_detection
                    (sender_email, detection_time, detection_type, threshold_value,
                     actual_value, action_taken, notes)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (sender_email, current_time, detection['type'],
                      detection['threshold'], detection['actual'],
                      'auto_detected', detection['description']))

            # 更新发件人配置
            cursor.execute('''
                UPDATE sender_config
                SET last_spam_detection = ?, updated_at = ?
                WHERE sender_email = ?
            ''', (current_time, current_time, sender_email))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"记录垃圾邮件检测失败: {str(e)}")

    def _calculate_risk_level(self, detections: List[Dict]) -> str:
        """计算风险等级"""
        if not detections:
            return 'low'

        high_severity = sum(1 for d in detections if d['severity'] == 'high')
        medium_severity = sum(1 for d in detections if d['severity'] == 'medium')

        if high_severity >= 2:
            return 'critical'
        elif high_severity >= 1 or medium_severity >= 2:
            return 'high'
        elif medium_severity >= 1:
            return 'medium'
        else:
            return 'low'

    def _generate_anti_spam_recommendations(self, detections: List[Dict]) -> List[str]:
        """生成反垃圾邮件建议"""
        recommendations = []

        for detection in detections:
            if detection['type'] == 'reply_rate_drop':
                recommendations.append("🚨 立即停止发送，检查邮件内容和收件人质量")
                recommendations.append("📧 考虑更换邮件模板或发件人地址")
                recommendations.append("⏰ 等待24-48小时后再尝试小批量发送")

            elif detection['type'] == 'high_bounce_rate':
                recommendations.append("🧹 清理收件人列表，移除无效邮箱")
                recommendations.append("📊 使用邮箱验证服务检查收件人有效性")
                recommendations.append("📉 降低发送频率，使用更小的批次")

            elif detection['type'] == 'volume_spike':
                recommendations.append("📉 降低发送速度，逐步增加发送量")
                recommendations.append("⏰ 增加发送间隔时间")
                recommendations.append("📦 使用更小的批次进行发送")

        if not recommendations:
            recommendations.append("✅ 暂无异常，继续监控发送效果")

        return list(set(recommendations))  # 去重

    def _auto_adjust_strategy(self, sender_email: str):
        """自动调整发送策略"""
        try:
            # 检测垃圾邮件模式
            detection_result = self.detect_spam_pattern(sender_email)

            if detection_result['spam_detected']:
                risk_level = detection_result['risk_level']

                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()

                # 根据风险等级调整策略
                if risk_level in ['critical', 'high']:
                    # 降低发送速率50%
                    cursor.execute('''
                        UPDATE sender_config
                        SET current_rate = CAST(current_rate * 0.5 AS INTEGER),
                            updated_at = ?
                        WHERE sender_email = ?
                    ''', (datetime.datetime.now().isoformat(), sender_email))

                    self.logger.warning(f"检测到高风险，降低 {sender_email} 的发送速率")

                elif risk_level == 'medium':
                    # 降低发送速率25%
                    cursor.execute('''
                        UPDATE sender_config
                        SET current_rate = CAST(current_rate * 0.75 AS INTEGER),
                            updated_at = ?
                        WHERE sender_email = ?
                    ''', (datetime.datetime.now().isoformat(), sender_email))

                    self.logger.info(f"检测到中等风险，适度降低 {sender_email} 的发送速率")

                conn.commit()
                conn.close()

        except Exception as e:
            self.logger.error(f"自动调整策略失败: {str(e)}")

    def get_sending_analytics(self, sender_email: str, days: int = 7) -> Dict:
        """获取发送分析报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取指定天数的统计
            start_date = (datetime.datetime.now() - datetime.timedelta(days=days)).strftime('%Y-%m-%d')

            cursor.execute('''
                SELECT send_date, SUM(emails_sent) as sent, SUM(emails_delivered) as delivered,
                       SUM(emails_bounced) as bounced, SUM(emails_replied) as replied
                FROM sending_stats
                WHERE sender_email = ? AND send_date >= ?
                GROUP BY send_date
                ORDER BY send_date DESC
            ''', (sender_email, start_date))

            daily_stats = cursor.fetchall()

            # 获取垃圾邮件检测记录
            cursor.execute('''
                SELECT detection_time, detection_type, actual_value
                FROM spam_detection
                WHERE sender_email = ? AND detection_time >= ?
                ORDER BY detection_time DESC
            ''', (sender_email, start_date))

            detections = cursor.fetchall()

            conn.close()

            # 计算总体指标
            total_sent = sum(stat[1] for stat in daily_stats)
            total_delivered = sum(stat[2] for stat in daily_stats)
            total_bounced = sum(stat[3] for stat in daily_stats)
            total_replied = sum(stat[4] for stat in daily_stats)

            delivery_rate = (total_delivered / total_sent * 100) if total_sent > 0 else 0
            bounce_rate = (total_bounced / total_sent * 100) if total_sent > 0 else 0
            reply_rate = (total_replied / total_delivered * 100) if total_delivered > 0 else 0

            return {
                'period_days': days,
                'total_sent': total_sent,
                'total_delivered': total_delivered,
                'total_bounced': total_bounced,
                'total_replied': total_replied,
                'delivery_rate': round(delivery_rate, 2),
                'bounce_rate': round(bounce_rate, 2),
                'reply_rate': round(reply_rate, 2),
                'daily_stats': [
                    {
                        'date': stat[0],
                        'sent': stat[1],
                        'delivered': stat[2],
                        'bounced': stat[3],
                        'replied': stat[4],
                        'delivery_rate': round((stat[2] / stat[1] * 100) if stat[1] > 0 else 0, 2),
                        'reply_rate': round((stat[4] / stat[2] * 100) if stat[2] > 0 else 0, 2)
                    } for stat in daily_stats
                ],
                'spam_detections': len(detections),
                'recent_detections': [
                    {
                        'time': det[0],
                        'type': det[1],
                        'value': det[2]
                    } for det in detections[:5]
                ]
            }

        except Exception as e:
            self.logger.error(f"获取发送分析失败: {str(e)}")
            return {}
