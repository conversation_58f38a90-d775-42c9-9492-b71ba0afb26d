#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制自动化触发器
独立运行此脚本来强制触发自动化
"""

import json
import datetime

def force_trigger_automation():
    """强制触发自动化"""
    print("🚀 强制触发自动化系统...")
    
    try:
        # 1. 确保配置文件正确
        ensure_configs()
        
        # 2. 测试深度协调系统
        test_coordination_system()
        
        print("✅ 强制触发完成")
        
    except Exception as e:
        print(f"❌ 强制触发失败: {str(e)}")

def ensure_configs():
    """确保配置正确"""
    config = {
        "enabled": True,
        "sender_email": "@qq.com",
        "enabled_time": datetime.datetime.now().isoformat(),
        "features": {
            "auto_reply_monitoring": True,
            "quality_database": True,
            "anti_spam": True,
            "qq_emergency": True,
            "smart_queue": True,
            "deep_coordination": True
        }
    }
    
    with open('all_features_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ 配置文件已强制更新")

def test_coordination_system():
    """测试协调系统"""
    try:
        from 深度系统协调实现 import get_coordinator, SystemEvent
        coordinator = get_coordinator()
        
        test_data = {
            'sender_email': '<EMAIL>',
            'recipient_count': 3,
            'recipients': ['<EMAIL>', '<EMAIL>'],
            'send_time': datetime.datetime.now().isoformat()
        }
        
        coordinator.data_center.emit_event(SystemEvent.EMAIL_SENT, test_data)
        print("✅ 邮件发送事件已模拟触发")
        
    except Exception as e:
        print(f"❌ 协调系统测试失败: {str(e)}")

if __name__ == "__main__":
    force_trigger_automation()
