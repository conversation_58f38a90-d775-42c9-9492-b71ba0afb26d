{"cSpell.words": ["levelname", "insertcolor", "initialvalue", "minvalue", "maxvalue", "columnspan", "foxmail", "tearoff", "csvfile", "borderwidth", "focuscolor", "highlightthickness", "insertbackground", "selectbackground", "fieldbackground", "scrollregion", "yscrollcommand", "textvariable", "padx", "pady", "excepthook", "keysym", "startfile", "seismo", "ji<PERSON>a", "smtp", "imap", "vbs", "gui", "api", "json", "sql", "utf", "ascii", "regex", "o<PERSON>h", "ssl", "tls", "qq", "rag", "coord", "antispam", "auth", "config", "tkinter", "ttk", "messagebox", "filedialog", "scrolledtext", "obj<PERSON><PERSON>", "objfso", "currentdir", "scriptpath", "nul", "ubound", "folderexists", "createfolder", "fileexists", "getparentfoldername", "scriptful<PERSON>ame", "wscript", "msgbox", "vbcrlf", "vbcritical", "vbinformation", "vbexclamation", "createobject", "chinesegmentercheck", "chinesegmenterresult", "installchinesegmenter", "missingfiles", "corefiles", "basicsystemrepair", "comprehensivestabilitycheck", "stabilitycommand", "stabilityresult", "basicrepairresult", "recoverycommand", "recoveryresult", "savestatecommand"], "cSpell.enableFiletypes": ["python", "vbs", "markdown", "json", "yaml", "txt", "bat", "cmd"], "cSpell.language": "en,zh-CN", "cSpell.allowCompoundWords": true, "cSpell.minWordLength": 3, "cSpell.enabled": true, "cSpell.showStatus": true, "cSpell.diagnosticLevel": "Information", "cSpell.ignorePaths": ["**/__pycache__/**", "**/node_modules/**", "**/.git/**", "**/logs/**", "**/*.log", "**/*.backup", "**/*.bak", "**/*.tmp", "**/*.temp"]}