' 邮件系统启动脚本
' 支持启动不同版本的邮件系统

Option Explicit

Dim objShell, objFSO, currentDir, pythonPath, scriptChoice
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取当前目录
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 显示版本选择菜单
scriptChoice = ShowVersionMenu()

If scriptChoice <> "" Then
    ' 检查Python环境
    If CheckPythonEnvironment() Then
        ' 启动选择的版本
        LaunchEmailSystem(scriptChoice)
    Else
        MsgBox "未找到Python环境！" & vbCrLf & vbCrLf & _
               "请确保已安装Python并添加到系统PATH中。", _
               vbCritical, "Python环境检查"
    End If
End If

' 显示版本选择菜单
Function ShowVersionMenu()
    Dim message, title, choice
    
    message = "请选择要启动的邮件系统版本：" & vbCrLf & vbCrLf & _
              "1. 原版系统 (gui_main.py)" & vbCrLf & _
              "   - 包含所有原始功能" & vbCrLf & _
              "   - 可能有拼写警告" & vbCrLf & vbCrLf & _
              "2. 清洁版本 (gui_clean.py)" & vbCrLf & _
              "   - 无拼写警告" & vbCrLf & _
              "   - 简化界面" & vbCrLf & vbCrLf & _
              "3. 修复版本 (gui_fixed.py)" & vbCrLf & _
              "   - 修复功能响应问题" & vbCrLf & _
              "   - 优化用户体验" & vbCrLf & vbCrLf & _
              "4. 完整功能版 (gui_complete_v3.py)" & vbCrLf & _
              "   - 包含所有2.0功能" & vbCrLf & _
              "   - 四栏优化布局" & vbCrLf & _
              "   - 撤回、监控等高级功能" & vbCrLf & vbCrLf & _
              "请输入数字 (1-4)："
    
    title = "邮件系统启动器"
    
    choice = InputBox(message, title, "4")
    
    Select Case choice
        Case "1"
            ShowVersionMenu = "gui_main.py"
        Case "2"
            ShowVersionMenu = "gui_clean.py"
        Case "3"
            ShowVersionMenu = "gui_fixed.py"
        Case "4"
            ShowVersionMenu = "gui_complete_v3.py"
        Case Else
            ShowVersionMenu = ""
    End Select
End Function

' 检查Python环境
Function CheckPythonEnvironment()
    Dim result
    On Error Resume Next
    
    ' 尝试运行python --version
    result = objShell.Run("python --version", 0, True)
    
    If Err.Number = 0 And result = 0 Then
        CheckPythonEnvironment = True
    Else
        ' 尝试python3
        Err.Clear
        result = objShell.Run("python3 --version", 0, True)
        If Err.Number = 0 And result = 0 Then
            CheckPythonEnvironment = True
            pythonPath = "python3"
        Else
            CheckPythonEnvironment = False
        End If
    End If
    
    If pythonPath = "" Then pythonPath = "python"
    On Error GoTo 0
End Function

' 启动邮件系统
Sub LaunchEmailSystem(scriptName)
    Dim fullPath, command, result
    
    ' 构建完整路径
    fullPath = currentDir & "\" & scriptName
    
    ' 检查文件是否存在
    If Not objFSO.FileExists(fullPath) Then
        MsgBox "文件不存在：" & fullPath & vbCrLf & vbCrLf & _
               "请确保选择的版本文件存在。", _
               vbCritical, "文件检查"
        Exit Sub
    End If
    
    ' 显示启动信息
    MsgBox "正在启动邮件系统..." & vbCrLf & vbCrLf & _
           "版本：" & scriptName & vbCrLf & _
           "路径：" & fullPath & vbCrLf & vbCrLf & _
           "请稍候...", _
           vbInformation, "启动中"
    
    ' 构建启动命令
    command = """" & pythonPath & """ """ & fullPath & """"
    
    ' 启动Python脚本
    On Error Resume Next
    result = objShell.Run(command, 1, False)
    
    If Err.Number <> 0 Then
        MsgBox "启动失败！" & vbCrLf & vbCrLf & _
               "错误信息：" & Err.Description & vbCrLf & _
               "命令：" & command & vbCrLf & vbCrLf & _
               "请检查Python环境和文件路径。", _
               vbCritical, "启动错误"
    Else
        ' 启动成功，显示提示
        MsgBox "邮件系统启动成功！" & vbCrLf & vbCrLf & _
               "如果系统没有显示，请检查任务栏。" & vbCrLf & _
               "如遇问题，请查看控制台输出。", _
               vbInformation, "启动成功"
    End If
    
    On Error GoTo 0
End Sub
