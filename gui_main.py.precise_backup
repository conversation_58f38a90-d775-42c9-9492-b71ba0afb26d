# -*- coding: utf-8 -*-
"""
自动化邮件发送助手 - 图形界面版本
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import datetime
import json
import time
import math
from email_sender import EmailSender
from email_history_manager import EmailHistoryManager
from rag_search_engine import RAGSearchEngine

# 导入深度协调系统
try:
    from 深度系统协调实现 import get_coordinator, SystemEvent
    DEEP_COORDINATION_AVAILABLE = True
except ImportError:
    DEEP_COORDINATION_AVAILABLE = False
    print("⚠️ 深度协调系统不可用，使用基础功能")

class EmailSenderGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("📧 智能邮件系统 v3.0 - 深度协调版")
        self.root.geometry("1200x900")
        self.root.resizable(True, True)

        # 设置窗口居中
        self.center_window()

        # 设置现代化背景色
        self.root.configure(bg='#f0f0f0')

        # 设置详细日志记录
        self.setup_detailed_logging()

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass

        # 设置现代化样式主题
        self.setup_modern_theme()

        # 应用字体颜色修复
        self.apply_font_color_fixes()

        # 发送模式变量
        self.send_mode = tk.StringVar(value="standard")  # standard: 标准发送(2秒), fast: 快速发送(1秒), safe: 安全发送(3秒)

        # 个性化设置变量
        self.add_personalization = tk.BooleanVar(value=False)  # 是否添加个性化后缀（邮件编号和时间戳）

        # 发送控制变量
        self.is_sending = False  # 是否正在发送
        self.should_stop = False  # 是否应该停止发送
        self.should_pause = False  # 是否应该暂停发送
        self.is_paused = False  # 是否处于暂停状态
        self.current_send_thread = None  # 当前发送线程
        self.current_batch_manager = None  # 当前批次管理器
        self.current_session_id = None  # 当前发送会话ID
        self.resume_from_breakpoint = False  # 是否从断点恢复
        self.breakpoint_session_id = None  # 断点会话ID

        # 邮件记录变量
        self.sent_emails = []  # 已发送的邮件记录
        self.current_batch_info = None  # 当前批次信息

        # 邮件队列变量
        self.email_queue = []  # 邮件发送队列
        self.current_queue_index = 0  # 当前队列索引
        self.queue_mode = False  # 是否为队列模式

        # 授权码管理
        self.auth_codes = {}  # 存储不同邮箱的授权码

        # 历史记录管理
        self.history_manager = EmailHistoryManager()
        self.rag_search = RAGSearchEngine()

        self.create_widgets()
        self.load_auth_codes()  # 在界面创建后加载保存的授权码

        # 初始化深度协调系统（在界面创建后）
        self.setup_deep_coordination()

        # 检查是否有未完成的发送任务（智能检查）
        self.root.after(2000, self.smart_startup_check)  # 延迟2秒检查，确保界面完全加载

    def setup_detailed_logging(self):
        """设置详细的日志记录"""
        import logging
        import sys
        from datetime import datetime

        # 创建日志目录
        import os
        if not os.path.exists('logs'):
            os.makedirs('logs')

        # 设置日志文件名（按日期）
        log_filename = f"logs/email_system_{datetime.now().strftime('%Y%m%d')}.log"

        # 配置日志格式
        log_format = '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'

        # 配置根日志记录器
        logging.basicConfig(
            level=logging.DEBUG,
            format=log_format,
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )

        self.logger = logging.getLogger(__name__)
        self.logger.info("=" * 50)
        self.logger.info("邮件系统启动 - 详细日志记录已启用")
        self.logger.info("=" * 50)

        # 设置全局异常处理器
        def handle_exception(exc_type, exc_value, exc_trace):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_trace)
                return

            self.logger.critical("未捕获的异常:", exc_info=(exc_type, exc_value, exc_trace))

            # 同时在GUI中显示错误
            try:
                error_msg = f"系统错误: {exc_type.__name__}: {str(exc_value)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("系统错误", error_msg)
            except:
                pass

        sys.excepthook = handle_exception

    def center_window(self):
        """窗口居中显示"""
        self.root.update_idletasks()
        width = 1200
        height = 900
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

    def setup_modern_theme(self):
        """设置现代化主题样式"""
        style = ttk.Style()

        # 使用对比度更好的主题
        try:
            style.theme_use('clam')  # 使用clam主题，对比度更好
        except:
            try:
                style.theme_use('default')  # 备选主题
            except:
                style.theme_use('alt')

        # 定义柔和优雅的颜色方案
        colors = {
            'primary': '#3b82f6',      # 柔和蓝色主色调
            'secondary': '#6b7280',    # 中性灰色辅助色
            'success': '#10b981',      # 柔和绿色成功色
            'warning': '#f59e0b',      # 柔和橙色警告色
            'danger': '#ef4444',       # 柔和红色危险色
            'light': '#f8fafc',        # 浅色背景
            'dark': '#374151',         # 柔和深色文字
            'border': '#e5e7eb',       # 边框色
            'hover': '#2563eb',        # 悬停色
            'button_text': '#ffffff',  # 按钮文字（白色）
            'label_text': '#4b5563',   # 标签文字（中性灰色）
            'soft_bg': '#f1f5f9'       # 柔和背景色
        }

        # 配置标题样式
        style.configure('Title.TLabel',
                       font=('Microsoft YaHei UI', 20, 'bold'),
                       foreground=colors['primary'],
                       background=colors['light'])

        style.configure('Subtitle.TLabel',
                       font=('Microsoft YaHei UI', 12, 'bold'),
                       foreground=colors['secondary'],
                       background=colors['light'])

        # 配置普通标签样式
        style.configure('TLabel',
                       font=('Microsoft YaHei UI', 9),
                       foreground=colors['label_text'],
                       background=colors['light'])

        # 配置按钮样式 - 柔和优雅的配色
        style.configure('Primary.TButton',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       foreground=colors['button_text'],  # 白色文字
                       background=colors['primary'],      # 柔和蓝色背景
                       relief='flat',
                       padding=(15, 8))

        style.map('Primary.TButton',
                 foreground=[('active', colors['button_text']),
                           ('pressed', colors['button_text'])],
                 background=[('active', colors['hover']),
                           ('pressed', colors['primary'])])

        style.configure('Success.TButton',
                       font=('Microsoft YaHei UI', 9, 'bold'),
                       foreground=colors['button_text'],  # 白色文字
                       background=colors['success'],      # 柔和绿色背景
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(12, 6))

        style.map('Success.TButton',
                 foreground=[('active', colors['button_text']),
                           ('pressed', colors['button_text'])],
                 background=[('active', '#34d399'),
                           ('pressed', colors['success'])])

        style.configure('Warning.TButton',
                       font=('Microsoft YaHei UI', 9, 'bold'),
                       foreground=colors['button_text'],  # 白色文字
                       background=colors['warning'],      # 柔和橙色背景
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(12, 6))

        style.map('Warning.TButton',
                 foreground=[('active', colors['button_text']),
                           ('pressed', colors['button_text'])],
                 background=[('active', '#fbbf24'),
                           ('pressed', colors['warning'])])

        style.configure('Danger.TButton',
                       font=('Microsoft YaHei UI', 9, 'bold'),
                       foreground=colors['button_text'],  # 白色文字
                       background=colors['danger'],       # 柔和红色背景
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(12, 6))

        style.map('Danger.TButton',
                 foreground=[('active', colors['button_text']),
                           ('pressed', colors['button_text'])],
                 background=[('active', '#f87171'),
                           ('pressed', colors['danger'])])

        # 配置默认按钮样式 - 柔和中性色
        style.configure('TButton',
                       font=('Microsoft YaHei UI', 9),
                       foreground=colors['dark'],         # 柔和深色文字
                       background='#f3f4f6',             # 更柔和的浅灰背景
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(10, 6))

        style.map('TButton',
                 foreground=[('active', colors['dark']),
                           ('pressed', colors['dark'])],
                 background=[('active', '#e5e7eb'),
                           ('pressed', '#f3f4f6')])

        # 配置Accent按钮样式 - 柔和强调色
        style.configure('Accent.TButton',
                       font=('Microsoft YaHei UI', 10, 'bold'),
                       foreground=colors['button_text'],  # 白色文字
                       background=colors['primary'],      # 柔和蓝色背景
                       borderwidth=0,
                       focuscolor='none',
                       relief='flat',
                       padding=(15, 8))

        # 配置标签框样式
        style.configure('Modern.TLabelframe',
                       background=colors['light'],
                       borderwidth=1,
                       relief='solid')

        style.configure('Modern.TLabelframe.Label',
                       font=('Microsoft YaHei UI', 11, 'bold'),
                       foreground=colors['primary'],
                       background=colors['light'])

        # 配置输入框样式
        style.configure('Modern.TEntry',
                       font=('Microsoft YaHei UI', 10),
                       foreground=colors['dark'],
                       fieldbackground='white',
                       borderwidth=2,
                       relief='solid',
                       insertcolor=colors['primary'])

        # 配置复选框样式
        style.configure('Modern.TCheckbutton',
                       font=('Microsoft YaHei UI', 9),
                       background=colors['light'],
                       foreground=colors['dark'])

        # 配置单选按钮样式
        style.configure('Modern.TRadiobutton',
                       font=('Microsoft YaHei UI', 9),
                       background=colors['light'],
                       foreground=colors['dark'])

        # 配置笔记本样式
        style.configure('Modern.TNotebook',
                       background='#f0f0f0',
                       borderwidth=0)

        style.configure('Modern.TNotebook.Tab',
                       font=('Microsoft YaHei UI', 10),
                       padding=[20, 8],
                       background='#e2e8f0',
                       foreground=colors['secondary'])

        style.map('Modern.TNotebook.Tab',
                 background=[('selected', colors['primary']),
                           ('active', colors['hover'])],
                 foreground=[('selected', 'white'),
                           ('active', 'white')])

    def apply_font_color_fixes(self):
        """应用字体颜色修复 - 确保所有文字都清晰可见"""
        # 延迟执行修复，确保所有组件都已创建
        self.root.after(500, self._apply_delayed_color_fixes)

    def _apply_delayed_color_fixes(self):
        """延迟应用颜色修复"""
        try:
            # 修复根窗口背景
            self.root.configure(bg='#f9fafb')

            # 递归修复所有组件
            self._fix_widget_colors(self.root)

            # 如果日志组件存在，记录修复信息
            if hasattr(self, 'log_text'):
                self.log_message("🎨 字体颜色已修复 - 所有按钮文字现在清晰可见")

        except Exception as e:
            print(f"字体颜色修复失败: {e}")

    def _fix_widget_colors(self, widget):
        """递归修复组件颜色"""
        try:
            widget_class = widget.winfo_class()

            # 修复不同类型的组件
            if widget_class == 'Frame':
                widget.configure(bg='#f9fafb')
            elif widget_class == 'Label':
                widget.configure(bg='#f9fafb', fg='#374151')
            elif widget_class == 'Button':
                # 根据按钮文本设置柔和颜色
                try:
                    text = widget.cget('text')
                    if any(word in text for word in ['发送', '开始', '启动', '添加', '🚀', '➕']):
                        widget.configure(bg='#3b82f6', fg='white',
                                       font=('Microsoft YaHei UI', 9, 'bold'),
                                       relief='flat', borderwidth=0)
                    elif any(word in text for word in ['停止', '删除', '清空', '🗑️', '⏹️']):
                        widget.configure(bg='#ef4444', fg='white',
                                       font=('Microsoft YaHei UI', 9, 'bold'),
                                       relief='flat', borderwidth=0)
                    elif any(word in text for word in ['暂停', '警告', '⏸️', '⚠️']):
                        widget.configure(bg='#f59e0b', fg='white',
                                       font=('Microsoft YaHei UI', 9, 'bold'),
                                       relief='flat', borderwidth=0)
                    elif any(word in text for word in ['恢复', '成功', '▶️', '✅']):
                        widget.configure(bg='#10b981', fg='white',
                                       font=('Microsoft YaHei UI', 9, 'bold'),
                                       relief='flat', borderwidth=0)
                    else:
                        widget.configure(bg='#6b7280', fg='white',
                                       font=('Microsoft YaHei UI', 9),
                                       relief='flat', borderwidth=0)
                except:
                    # 如果获取文本失败，使用默认样式
                    widget.configure(bg='#6b7280', fg='white',
                                   font=('Microsoft YaHei UI', 9),
                                   relief='raised', borderwidth=1)
            elif widget_class == 'Entry':
                widget.configure(bg='white', fg='#111827', relief='solid', borderwidth=2)
            elif widget_class == 'Text':
                widget.configure(bg='white', fg='#111827', relief='solid', borderwidth=2)
            elif widget_class == 'Listbox':
                widget.configure(bg='white', fg='#111827', relief='solid', borderwidth=2)
            elif widget_class in ['Checkbutton', 'Radiobutton']:
                widget.configure(bg='#f9fafb', fg='#374151')

        except Exception as e:
            # 忽略配置错误，继续处理其他组件
            pass

        # 递归处理子组件
        try:
            for child in widget.winfo_children():
                self._fix_widget_colors(child)
        except:
            pass

    def setup_deep_coordination(self):
        """设置深度协调系统"""
        if DEEP_COORDINATION_AVAILABLE:
            try:
                self.coordinator = get_coordinator()

                # 只有在log_text存在时才记录日志
                if hasattr(self, 'log_text'):
                    self.log_message("🔧 深度协调系统已启用")
                    self.log_message("✅ 系统功能将深度协调配合")

                # 设置事件监听器
                self._setup_coordination_listeners()

            except Exception as e:
                self.coordinator = None
                if hasattr(self, 'log_text'):
                    self.log_message(f"❌ 深度协调系统启用失败: {str(e)}")
        else:
            self.coordinator = None
            if hasattr(self, 'log_text'):
                self.log_message("⚠️ 深度协调系统不可用，使用基础功能")

    def _setup_coordination_listeners(self):
        """设置协调系统事件监听器"""
        if not self.coordinator:
            return

        try:
            # 监听应急激活事件
            self.coordinator.data_center.register_event_listener(
                SystemEvent.EMERGENCY_ACTIVATED,
                self._on_emergency_activated
            )

            # 监听应急恢复事件
            self.coordinator.data_center.register_event_listener(
                SystemEvent.EMERGENCY_DEACTIVATED,
                self._on_emergency_deactivated
            )

            # 监听风险等级变化事件
            self.coordinator.data_center.register_event_listener(
                SystemEvent.RISK_LEVEL_CHANGED,
                self._on_risk_level_changed
            )

            if hasattr(self, 'log_text'):
                self.log_message("✅ 协调系统事件监听器已设置")

        except Exception as e:
            if hasattr(self, 'log_text'):
                self.log_message(f"❌ 设置协调系统监听器失败: {str(e)}")

    def _on_emergency_activated(self, data):
        """处理应急激活事件"""
        sender_email = data.get('sender_email', '')
        reason = data.get('trigger_reason', '')

        self.log_message(f"🆘 应急模式已激活: {sender_email}")
        self.log_message(f"   触发原因: {reason}")

        # 在GUI中显示通知
        self.root.after(0, lambda: self._show_emergency_notification(sender_email, reason))

    def _on_emergency_deactivated(self, data):
        """处理应急恢复事件"""
        sender_email = data.get('sender_email', '')
        reason = data.get('recovery_reason', '')

        self.log_message(f"✅ 应急模式已恢复: {sender_email}")
        self.log_message(f"   恢复原因: {reason}")

    def _on_risk_level_changed(self, data):
        """处理风险等级变化事件"""
        sender_email = data.get('sender_email', '')
        old_level = data.get('old_level', '')
        new_level = data.get('new_level', '')

        self.log_message(f"⚠️ 风险等级变化: {sender_email}")
        self.log_message(f"   {old_level} → {new_level}")

        # 根据风险等级调整界面状态
        if new_level in ['high', 'critical']:
            self.root.after(0, lambda: self._show_risk_warning(sender_email, new_level))

    def _show_emergency_notification(self, sender_email, reason):
        """显示应急模式通知"""
        try:
            messagebox.showwarning(
                "🆘 应急模式激活",
                f"发件人: {sender_email}\n"
                f"触发原因: {reason}\n\n"
                f"系统已自动激活应急保护模式，"
                f"建议检查邮件内容和收件人质量。"
            )
        except Exception as e:
            self.log_message(f"❌ 显示应急通知失败: {str(e)}")

    def _show_risk_warning(self, sender_email, risk_level):
        """显示风险警告"""
        try:
            if risk_level == 'critical':
                messagebox.showerror(
                    "🚨 严重风险警告",
                    f"发件人: {sender_email}\n"
                    f"风险等级: 严重\n\n"
                    f"建议立即停止发送，检查系统状态！"
                )
            elif risk_level == 'high':
                messagebox.showwarning(
                    "⚠️ 高风险警告",
                    f"发件人: {sender_email}\n"
                    f"风险等级: 高\n\n"
                    f"建议降低发送频率，加强监控。"
                )
        except Exception as e:
            self.log_message(f"❌ 显示风险警告失败: {str(e)}")

    def open_system_coordinator(self):
        """打开系统协调器"""
        try:
            if hasattr(self, 'log_text'):
                self.log_message("🔧 系统协调器功能开发中...")
                messagebox.showinfo("系统协调器", "系统协调器功能正在开发中，敬请期待！")
            else:
                messagebox.showinfo("系统协调器", "系统协调器功能正在开发中，敬请期待！")
        except Exception as e:
            if hasattr(self, 'log_text'):
                self.log_message(f"❌ 打开系统协调器失败: {str(e)}")

    def auto_start_reply_monitoring_func(self, sender_email, recipients):
        """自动启动回复监控功能"""
        try:
            self.log_message(f"🚀 自动启动回复监控: {sender_email} -> {len(recipients)} 个收件人")
            # 这里可以调用实际的回复监控功能
            if hasattr(self, 'open_reply_monitor'):
                self.open_reply_monitor()
            else:
                self.log_message("💡 回复监控功能将在后台运行")
        except Exception as e:
            self.log_message(f"❌ 自动启动回复监控失败: {str(e)}")

    def create_widgets(self):
        """创建现代化界面组件"""
        # 创建主容器，使用Canvas实现滚动
        self.main_canvas = tk.Canvas(self.root, bg='#f0f0f0', highlightthickness=0)
        self.main_scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=self.main_canvas.yview)
        self.scrollable_frame = ttk.Frame(self.main_canvas)

        # 配置滚动 - 优化滚动区域更新
        def update_scroll_region(event=None):
            # 更新滚动区域
            self.main_canvas.configure(scrollregion=self.main_canvas.bbox("all"))

        self.scrollable_frame.bind("<Configure>", update_scroll_region)

        # 延迟更新滚动区域，确保所有组件都已加载
        self.root.after(100, update_scroll_region)

        self.main_canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.main_canvas.configure(yscrollcommand=self.main_scrollbar.set)

        # 布局主容器
        self.main_canvas.pack(side="left", fill="both", expand=True)
        self.main_scrollbar.pack(side="right", fill="y")

        # 主框架
        main_frame = ttk.Frame(self.scrollable_frame, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题区域
        self.create_header_section(main_frame)

        # 创建三栏布局（类似截图风格）
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))

        # 左侧邮件配置区 (40%)
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))

        # 中间操作控制区 (30%)
        middle_frame = ttk.Frame(content_frame, width=380)
        middle_frame.pack(side=tk.LEFT, fill=tk.Y, padx=8)
        middle_frame.pack_propagate(False)

        # 右侧监控装饰区 (30%)
        right_frame = ttk.Frame(content_frame, width=380)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(8, 0))
        right_frame.pack_propagate(False)

        # 左侧：邮件配置和内容
        self.create_email_config_section(left_frame)
        self.create_content_section(left_frame)
        self.create_log_section(left_frame)

        # 中间：操作控制
        self.create_quick_actions_section(middle_frame)
        self.create_enhanced_queue_section(middle_frame)
        self.create_attachments_section(middle_frame)

        # 右侧：监控装饰
        self.create_mechanical_decoration(right_frame)
        self.create_status_section(right_frame)

        # 底部操作区
        self.create_action_buttons_section(main_frame)

        # 绑定鼠标滚轮事件
        self.bind_mousewheel_events()

    def create_header_section(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # 主标题
        title_label = ttk.Label(header_frame,
                               text="📧 智能邮件系统",
                               style='Title.TLabel')
        title_label.pack(side=tk.LEFT)

        # 版本和状态信息
        version_frame = ttk.Frame(header_frame)
        version_frame.pack(side=tk.RIGHT)

        version_label = ttk.Label(version_frame,
                                 text="v3.0 深度协调版",
                                 style='Subtitle.TLabel')
        version_label.pack(anchor=tk.E)

        # 系统状态指示器
        self.status_indicator = ttk.Label(version_frame,
                                         text="🟢 系统就绪",
                                         font=('Microsoft YaHei UI', 9),
                                         foreground='#10b981')
        self.status_indicator.pack(anchor=tk.E, pady=(5, 0))

    def create_email_config_section(self, parent):
        """创建邮件配置区域"""
        config_frame = ttk.LabelFrame(parent, text="📧 邮件配置", style='Modern.TLabelframe', padding="15")
        config_frame.pack(fill=tk.X, pady=(0, 15))

        # 发送者邮箱配置
        sender_frame = ttk.Frame(config_frame)
        sender_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(sender_frame, text="发送者邮箱:", font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        sender_input_frame = ttk.Frame(sender_frame)
        sender_input_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        self.sender_email = ttk.Entry(sender_input_frame, style='Modern.TEntry', font=('Microsoft YaHei UI', 10))
        self.sender_email.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        self.sender_email.insert(0, "@qq.com")

        ttk.Button(sender_input_frame, text="🔑 授权码管理",
                  command=self.manage_auth_codes, style='Primary.TButton').pack(side=tk.RIGHT)

        # 收件人邮箱配置
        recipient_frame = ttk.Frame(config_frame)
        recipient_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        recipient_label_frame = ttk.Frame(recipient_frame)
        recipient_label_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(recipient_label_frame, text="收件人邮箱:", font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)
        ttk.Label(recipient_label_frame, text="支持分号(;)、逗号(,)、换行分隔，可输入120+邮箱",
                 font=('Microsoft YaHei UI', 8), foreground='#64748b').pack(side=tk.RIGHT)

        self.recipient_emails = scrolledtext.ScrolledText(recipient_frame, width=60, height=4,
                                                         font=('Microsoft YaHei UI', 10), wrap=tk.WORD,
                                                         relief='solid', borderwidth=1)
        self.recipient_emails.pack(fill=tk.BOTH, expand=True)

        # 发送模式选择
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill=tk.X, pady=(15, 0))

        ttk.Label(mode_frame, text="发送模式:", font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        mode_buttons_frame = ttk.Frame(mode_frame)
        mode_buttons_frame.pack(side=tk.RIGHT)

        ttk.Radiobutton(mode_buttons_frame, text="🚀 快速发送（30-60秒）",
                       variable=self.send_mode, value="fast", style='Modern.TRadiobutton').pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_buttons_frame, text="⚡ 标准发送（1-2分钟）",
                       variable=self.send_mode, value="standard", style='Modern.TRadiobutton').pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_buttons_frame, text="🛡️ 安全发送（3-5分钟）",
                       variable=self.send_mode, value="safe", style='Modern.TRadiobutton').pack(side=tk.LEFT, padx=5)

        # 安全提示
        warning_frame = ttk.Frame(config_frame)
        warning_frame.pack(fill=tk.X, pady=(10, 0))

        warning_label = ttk.Label(warning_frame,
                                 text="⚠️ 重要提示：大批量发送建议选择安全模式，避免进入垃圾箱",
                                 font=('Microsoft YaHei UI', 9), foreground='#f59e0b')
        warning_label.pack()

    def create_content_section(self, parent):
        """创建邮件内容区域"""
        content_frame = ttk.LabelFrame(parent, text="✍️ 邮件内容", style='Modern.TLabelframe', padding="15")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 邮件主题
        subject_frame = ttk.Frame(content_frame)
        subject_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(subject_frame, text="邮件主题:", font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)
        self.subject = ttk.Entry(subject_frame, style='Modern.TEntry', font=('Microsoft YaHei UI', 10))
        self.subject.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        # 邮件正文
        body_label_frame = ttk.Frame(content_frame)
        body_label_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(body_label_frame, text="邮件正文:", font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        body_tools_frame = ttk.Frame(body_label_frame)
        body_tools_frame.pack(side=tk.RIGHT)

        ttk.Button(body_tools_frame, text="😊 表情", command=self.open_emoji_helper,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(body_tools_frame, text="🔍 智能检索", command=self.auto_retrieve_suggestions,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=2)

        # 正文输入框
        self.body = scrolledtext.ScrolledText(content_frame, width=60, height=10,
                                             font=('Microsoft YaHei UI', 11), wrap=tk.WORD,
                                             relief='solid', borderwidth=1,
                                             insertbackground='#2563eb',
                                             selectbackground='#dbeafe')
        self.body.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # 添加占位符文本
        placeholder_text = "请在此输入邮件正文内容...\n\n✨ 支持功能：\n• 支持中文输入和Emoji表情 😊\n• 支持多行文本和格式\n• 右键菜单快速插入内容\n• 智能内容建议"
        self.body.insert(tk.END, placeholder_text)
        self.body.config(foreground='#94a3b8')

        # 绑定焦点事件
        def on_focus_in(event):
            if self.body.get(1.0, tk.END).strip() == placeholder_text.strip():
                self.body.delete(1.0, tk.END)
                self.body.config(foreground='#1e293b')

        def on_focus_out(event):
            if not self.body.get(1.0, tk.END).strip():
                self.body.insert(1.0, placeholder_text)
                self.body.config(foreground='#94a3b8')

        self.body.bind('<FocusIn>', on_focus_in)
        self.body.bind('<FocusOut>', on_focus_out)

        # 个性化选项
        options_frame = ttk.Frame(content_frame)
        options_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Checkbutton(options_frame, text="📝 添加邮件编号和时间戳",
                       variable=self.add_personalization, style='Modern.TCheckbutton').pack(side=tk.LEFT)

        ttk.Checkbutton(options_frame, text="📡 发送后自动启动回复监控",
                       variable=self.auto_start_reply_monitoring, style='Modern.TCheckbutton').pack(side=tk.RIGHT)

    def create_attachments_section(self, parent):
        """创建附件区域 - 紧凑版本适合中间栏"""
        attachment_frame = ttk.LabelFrame(parent, text="📎 附件管理", style='Modern.TLabelframe', padding="10")
        attachment_frame.pack(fill=tk.X, pady=(0, 10))

        # 附件列表 - 更紧凑
        list_frame = ttk.Frame(attachment_frame)
        list_frame.pack(fill=tk.X, pady=(0, 8))

        self.attachment_listbox = tk.Listbox(list_frame, height=2, font=('Microsoft YaHei UI', 8),
                                           relief='solid', borderwidth=1, selectbackground='#dbeafe')
        self.attachment_listbox.pack(fill=tk.X)

        # 附件操作按钮 - 垂直布局更适合中间栏
        btn_frame = ttk.Frame(attachment_frame)
        btn_frame.pack(fill=tk.X)

        ttk.Button(btn_frame, text="📁 添加", command=self.add_attachment,
                  style='Success.TButton').pack(fill=tk.X, pady=1)
        ttk.Button(btn_frame, text="🗑️ 删除", command=self.remove_attachment,
                  style='Warning.TButton').pack(fill=tk.X, pady=1)
        ttk.Button(btn_frame, text="🧹 清空", command=self.clear_attachments,
                  style='Danger.TButton').pack(fill=tk.X, pady=1)

    def create_log_section(self, parent):
        """创建日志区域 - 适合左侧布局"""
        log_frame = ttk.LabelFrame(parent, text="📋 操作日志", style='Modern.TLabelframe', padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(log_toolbar, text="实时日志:", font=('Microsoft YaHei UI', 9, 'bold')).pack(side=tk.LEFT)

        log_buttons = ttk.Frame(log_toolbar)
        log_buttons.pack(side=tk.RIGHT)

        ttk.Button(log_buttons, text="🧹", command=self.clear_log,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=1)
        ttk.Button(log_buttons, text="💾", command=self.save_log,
                  style='Success.TButton').pack(side=tk.LEFT, padx=1)
        ttk.Button(log_buttons, text="📁", command=self.open_log_file,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=1)

        # 日志显示区域 - 调整尺寸适合左侧
        self.log_text = scrolledtext.ScrolledText(log_frame, width=50, height=8,
                                                 font=('Consolas', 8), wrap=tk.WORD,
                                                 relief='solid', borderwidth=1,
                                                 bg='#1e293b', fg='#e2e8f0',
                                                 insertbackground='#3b82f6',
                                                 selectbackground='#374151')
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_quick_actions_section(self, parent):
        """创建快速操作区域 - 优化中间栏布局"""
        actions_frame = ttk.LabelFrame(parent, text="⚡ 快速操作", style='Modern.TLabelframe', padding="12")
        actions_frame.pack(fill=tk.X, pady=(0, 10))

        # 主要操作按钮 - 更大更醒目
        main_actions = [
            ("🚀 发送邮件", self.send_email, 'Primary.TButton'),
            ("⏸️ 暂停发送", self.pause_sending, 'Warning.TButton'),
            ("⏹️ 停止发送", self.stop_sending, 'Danger.TButton'),
            ("▶️ 恢复发送", self.resume_sending, 'Success.TButton'),
            ("🔄 断点继续", self.continue_from_breakpoint, 'Primary.TButton')
        ]

        for i, (text, command, style) in enumerate(main_actions):
            btn = ttk.Button(actions_frame, text=text, command=command, style=style)
            btn.pack(fill=tk.X, pady=3)
            if i == 0:  # 发送按钮
                self.send_button = btn
            elif i == 1:  # 暂停按钮
                self.pause_button = btn
                btn.configure(state='disabled')
            elif i == 2:  # 停止按钮
                self.stop_button = btn
                btn.configure(state='disabled')
            elif i == 3:  # 恢复按钮
                self.resume_button = btn
                btn.configure(state='disabled')
            elif i == 4:  # 继续按钮
                self.continue_button = btn
                btn.configure(state='disabled')

        # 分隔线
        ttk.Separator(actions_frame, orient='horizontal').pack(fill=tk.X, pady=8)

        # 工具操作 - 紧凑布局
        tool_actions = [
            ("🔧 测试连接", self.test_connection),
            ("✅ 验证邮箱", self.validate_emails),
            ("🧹 清空表单", self.clear_form)
        ]

        for text, command in tool_actions:
            ttk.Button(actions_frame, text=text, command=command,
                      style='Primary.TButton').pack(fill=tk.X, pady=2)

    def create_enhanced_queue_section(self, parent):
        """创建增强的队列系统区域 - 优化中间栏布局"""
        queue_frame = ttk.LabelFrame(parent, text="📬 邮件队列系统", style='Modern.TLabelframe', padding="10")
        queue_frame.pack(fill=tk.X, pady=(0, 10))

        # 队列状态显示 - 更紧凑
        status_frame = ttk.Frame(queue_frame)
        status_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(status_frame, text="队列状态:", font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        # 状态指示器 - 横向布局节省空间
        status_indicators = ttk.Frame(status_frame)
        status_indicators.pack(fill=tk.X, pady=(3, 0))

        self.queue_task_count = ttk.Label(status_indicators, text="📊 0个",
                                         font=('Microsoft YaHei UI', 8), foreground='#2563eb')
        self.queue_task_count.pack(side=tk.LEFT)

        self.queue_progress = ttk.Label(status_indicators, text="📈 0%",
                                       font=('Microsoft YaHei UI', 8), foreground='#10b981')
        self.queue_progress.pack(side=tk.LEFT, padx=(10, 0))

        self.queue_speed = ttk.Label(status_indicators, text="⚡ 0封/分",
                                    font=('Microsoft YaHei UI', 8), foreground='#f59e0b')
        self.queue_speed.pack(side=tk.LEFT, padx=(10, 0))

        # 队列操作按钮 - 垂直布局
        queue_ops = [
            ("➕ 添加任务", self.add_to_queue, 'Primary.TButton'),
            ("📋 队列管理", self.open_queue_system, 'Primary.TButton'),
            ("🚀 开始队列", self.start_queue_sending, 'Success.TButton'),
            ("⏸️ 暂停队列", self.pause_queue_sending, 'Warning.TButton'),
            ("🗑️ 清空队列", self.clear_queue, 'Danger.TButton')
        ]

        for text, command, style in queue_ops:
            btn = ttk.Button(queue_frame, text=text, command=command, style=style)
            btn.pack(fill=tk.X, pady=2)

            # 保存按钮引用
            if "开始队列" in text:
                self.start_queue_button = btn
                btn.configure(state='disabled')
            elif "暂停队列" in text:
                self.pause_queue_button = btn
                btn.configure(state='disabled')

        # 自动队列模式 - 紧凑显示
        auto_frame = ttk.Frame(queue_frame)
        auto_frame.pack(fill=tk.X, pady=(8, 0))

        self.auto_queue_mode = tk.BooleanVar(value=True)
        ttk.Checkbutton(auto_frame, text="🤖 自动模式",
                       variable=self.auto_queue_mode,
                       command=self.on_auto_queue_mode_changed,
                       style='Modern.TCheckbutton').pack(anchor=tk.W)

        self.auto_mode_label = ttk.Label(auto_frame,
                                        text="(发送完成后自动启动队列)",
                                        font=('Microsoft YaHei UI', 7), foreground='#10b981')
        self.auto_mode_label.pack(anchor=tk.W, pady=(1, 0))

    def create_mechanical_decoration(self, parent):
        """创建精美地动仪监控装饰 - 优化右侧布局"""
        decoration_frame = ttk.LabelFrame(parent, text="🏛️ 张衡地动仪监控", style='Modern.TLabelframe', padding="8")
        decoration_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建地动仪Canvas - 调整尺寸适合右侧
        self.seismograph_canvas = tk.Canvas(decoration_frame, width=360, height=420,
                                           bg='#2d1810', highlightthickness=0)
        self.seismograph_canvas.pack(fill=tk.BOTH, expand=True)

        # 初始化地动仪组件
        self.init_seismograph_components()

        # 绘制地动仪
        self.draw_seismograph()

        # 创建控制面板
        self.create_seismograph_controls(decoration_frame)

        # 启动地动仪动画
        self.animate_seismograph()

    def init_seismograph_components(self):
        """初始化地动仪组件"""
        # 动画变量
        self.seismo_animation_phase = 0
        self.dragon_glow_phases = [0] * 8
        self.pearl_positions = [0] * 8  # 珠子位置
        self.toad_mouth_states = [False] * 8  # 蟾蜍嘴巴状态

        # 系统状态映射
        self.seismo_status = {
            'email_sending': False,
            'queue_active': False,
            'monitoring_active': False,
            'emergency_mode': False,
            'network_status': True,
            'database_status': True,
            'coordination_active': False,
            'anti_spam_active': False
        }

    def draw_seismograph(self):
        """绘制精美地动仪"""
        canvas = self.seismograph_canvas
        cx, cy = 170, 190  # 中心点

        # 1. 创建底座
        canvas.create_oval(cx-150, cy+70, cx+150, cy+100,
                          fill='#8B4513', outline='#CD853F', width=3)
        canvas.create_oval(cx-130, cy+75, cx+130, cy+95,
                          fill='#A0522D', outline='#DEB887', width=2)

        # 2. 主体酒樽
        canvas.create_oval(cx-90, cy-70, cx+90, cy+70,
                          fill='#B8860B', outline='#DAA520', width=4)
        canvas.create_oval(cx-80, cy-60, cx+80, cy+60,
                          fill='#CD853F', outline='#DEB887', width=2)

        # 3. 铭文区域
        canvas.create_rectangle(cx-70, cy-8, cx+70, cy+8,
                               fill='#8B4513', outline='#CD853F', width=1)
        canvas.create_text(cx, cy, text='候风地动仪',
                          font=('华文中宋', 11, 'bold'), fill='#DAA520')

        # 4. 八龙系统
        self.seismo_dragons = []
        self.seismo_pearls = []
        directions = ['东', '东南', '南', '西南', '西', '西北', '北', '东北']
        colors = ['#FF6B6B', '#FF8E53', '#FF6B9D', '#C44569',
                 '#F8B500', '#6C5CE7', '#00B894', '#0984E3']

        for i in range(8):
            angle = i * 45 * math.pi / 180

            # 龙头位置
            dragon_x = cx + 100 * math.cos(angle)
            dragon_y = cy + 100 * math.sin(angle)

            # 创建龙头
            dragon = self.draw_dragon_head(canvas, dragon_x, dragon_y, angle, colors[i])
            self.seismo_dragons.append(dragon)

            # 创建龙珠
            pearl_x = dragon_x + 12 * math.cos(angle)
            pearl_y = dragon_y + 12 * math.sin(angle)
            pearl = canvas.create_oval(pearl_x-3, pearl_y-3, pearl_x+3, pearl_y+3,
                                      fill='#FFD700', outline='#FFA500', width=2)
            self.seismo_pearls.append(pearl)

            # 方位标识
            text_x = cx + 120 * math.cos(angle)
            text_y = cy + 120 * math.sin(angle)
            canvas.create_text(text_x, text_y, text=directions[i],
                              font=('华文中宋', 9, 'bold'), fill='#DAA520')

        # 5. 八蟾蜍
        self.seismo_toads = []
        for i in range(8):
            angle = i * 45 * math.pi / 180
            toad_x = cx + 130 * math.cos(angle)
            toad_y = cy + 130 * math.sin(angle)

            toad = self.draw_toad(canvas, toad_x, toad_y)
            self.seismo_toads.append(toad)

        # 6. 中央都柱
        canvas.create_rectangle(cx-6, cy-50, cx+6, cy+50,
                               fill='#B8860B', outline='#DAA520', width=2)

        # 都柱装饰环
        for i in range(4):
            ring_y = cy - 30 + i * 20
            canvas.create_rectangle(cx-8, ring_y-1, cx+8, ring_y+1,
                                   fill='#CD853F', outline='#DEB887', width=1)

        # 7. 标题
        canvas.create_text(cx, 30, text='张衡地动仪监控系统',
                          font=('华文中宋', 12, 'bold'), fill='#DAA520')
        canvas.create_text(cx, 50, text='东汉永和年间 · 候风地动仪复刻版',
                          font=('华文中宋', 8), fill='#CD853F')

        # 8. 状态说明
        canvas.create_text(cx, cy+150, text='八龙含珠 · 八蟾承接',
                          font=('华文中宋', 9, 'bold'), fill='#B8860B')
        canvas.create_text(cx, cy+170, text='龙珠落蟾口，即知震动方',
                          font=('华文中宋', 8), fill='#CD853F')

    def draw_dragon_head(self, canvas, x, y, angle, color):
        """绘制龙头"""
        # 龙头主体
        head_points = []
        for dx, dy in [(-6, -4), (6, -4), (10, 0), (6, 4), (-6, 4), (-10, 0)]:
            rx = dx * math.cos(angle) - dy * math.sin(angle)
            ry = dx * math.sin(angle) + dy * math.cos(angle)
            head_points.extend([x + rx, y + ry])

        dragon_head = canvas.create_polygon(head_points, fill=color,
                                           outline='#8B4513', width=2)

        # 龙眼
        eye_x = x + 4 * math.cos(angle)
        eye_y = y + 4 * math.sin(angle)
        canvas.create_oval(eye_x-1, eye_y-1, eye_x+1, eye_y+1,
                          fill='#FF0000', outline='#8B0000', width=1)

        return dragon_head

    def draw_toad(self, canvas, x, y):
        """绘制蟾蜍"""
        # 蟾蜍身体
        body = canvas.create_oval(x-8, y-6, x+8, y+6,
                                 fill='#228B22', outline='#006400', width=2)

        # 蟾蜍头部
        head = canvas.create_oval(x-6, y-8, x+6, y+2,
                                 fill='#32CD32', outline='#006400', width=1)

        # 蟾蜍眼睛
        canvas.create_oval(x-4, y-6, x-2, y-4,
                          fill='#FFD700', outline='#FF8C00', width=1)
        canvas.create_oval(x+2, y-6, x+4, y-4,
                          fill='#FFD700', outline='#FF8C00', width=1)

        # 蟾蜍嘴巴
        mouth = canvas.create_arc(x-4, y-1, x+4, y+4,
                                 start=0, extent=180, fill='#8B0000',
                                 outline='#006400', width=1, style='pieslice')

        return [body, head, mouth]

    def create_seismograph_controls(self, parent):
        """创建地动仪控制面板"""
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=(5, 0))

        # 测试按钮
        ttk.Button(control_frame, text="🐉 测试龙珠落下",
                  command=self.test_seismograph_activation,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=2)

        # 状态重置按钮
        ttk.Button(control_frame, text="🔄 重置状态",
                  command=self.reset_seismograph_status,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=2)

    def animate_seismograph(self):
        """地动仪动画"""
        if not hasattr(self, 'seismograph_canvas'):
            return

        self.seismo_animation_phase += 0.1

        # 根据系统状态触发龙珠落下
        import random
        if self.is_sending and random.random() < 0.05:  # 发送时5%概率
            self.trigger_seismograph_activation(0, "邮件发送活跃")
        elif hasattr(self, 'queue_mode') and self.queue_mode and random.random() < 0.03:
            self.trigger_seismograph_activation(2, "队列处理活跃")

        # 继续动画
        self.root.after(200, self.animate_seismograph)

    def trigger_seismograph_activation(self, direction_index, reason="系统事件"):
        """触发地动仪龙珠落下"""
        if 0 <= direction_index < 8:
            directions = ['东', '东南', '南', '西南', '西', '西北', '北', '东北']
            self.log_message(f"🐉 {directions[direction_index]}方龙珠落下 - {reason}")

            # 这里可以添加珠子落下的动画效果
            # 由于Canvas动画比较复杂，暂时用日志记录

    def test_seismograph_activation(self):
        """测试地动仪激活"""
        import random
        direction = random.randint(0, 7)
        self.trigger_seismograph_activation(direction, "手动测试")

    def reset_seismograph_status(self):
        """重置地动仪状态"""
        self.log_message("🔄 地动仪状态已重置")

    def draw_precision_meters(self):
        """绘制精密仪表"""
        canvas = self.decoration_canvas

        # 左侧圆形仪表 - 发送速度
        meter1_x, meter1_y = 80, 100
        canvas.create_oval(meter1_x-40, meter1_y-40, meter1_x+40, meter1_y+40,
                          outline='#3b82f6', width=2, fill='#f0f9ff')

        # 刻度线
        for i in range(12):
            angle = i * 30 * 3.14159 / 180
            x1 = meter1_x + 35 * math.cos(angle)
            y1 = meter1_y + 35 * math.sin(angle)
            x2 = meter1_x + 30 * math.cos(angle)
            y2 = meter1_y + 30 * math.sin(angle)
            canvas.create_line(x1, y1, x2, y2, fill='#6b7280', width=1)

        # 指针
        self.speed_needle = canvas.create_line(meter1_x, meter1_y, meter1_x+25, meter1_y,
                                              fill='#dc2626', width=2, arrow=tk.LAST)

        canvas.create_text(meter1_x, meter1_y+55, text='发送速度监控',
                          font=('Microsoft YaHei UI', 8), fill='#374151')

        # 右侧方形仪表 - 系统状态
        meter2_x, meter2_y = 240, 100
        canvas.create_rectangle(meter2_x-35, meter2_y-35, meter2_x+35, meter2_y+35,
                               outline='#10b981', width=2, fill='#f0fdf4')

        # 内部网格
        for i in range(1, 4):
            y = meter2_y - 35 + i * 23.33
            canvas.create_line(meter2_x-30, y, meter2_x+30, y, fill='#d1d5db', width=1)

        for i in range(1, 4):
            x = meter2_x - 35 + i * 23.33
            canvas.create_line(x, meter2_y-30, x, meter2_y+30, fill='#d1d5db', width=1)

        # 状态指示点
        self.status_dots = []
        for i in range(3):
            for j in range(3):
                x = meter2_x - 20 + j * 20
                y = meter2_y - 20 + i * 20
                dot = canvas.create_oval(x-3, y-3, x+3, y+3, fill='#10b981', outline='')
                self.status_dots.append(dot)

        canvas.create_text(meter2_x, meter2_y+55, text='系统状态矩阵',
                          font=('Microsoft YaHei UI', 8), fill='#374151')

        # 底部连接线
        canvas.create_line(meter1_x+40, meter1_y, meter2_x-35, meter2_y,
                          fill='#6b7280', width=2, dash=(5, 5))

        # 标题
        canvas.create_text(160, 30, text='🔧 精密系统监控仪表',
                          font=('Microsoft YaHei UI', 10, 'bold'), fill='#374151')

    def animate_decorations(self):
        """动画装饰效果"""
        if not hasattr(self, 'decoration_canvas'):
            return

        import math
        import time

        # 获取当前时间用于动画
        current_time = time.time()

        # 旋转速度指针
        angle = (current_time * 50) % 360  # 每秒50度
        angle_rad = angle * 3.14159 / 180

        meter1_x, meter1_y = 80, 100
        needle_x = meter1_x + 25 * math.cos(angle_rad)
        needle_y = meter1_y + 25 * math.sin(angle_rad)

        self.decoration_canvas.coords(self.speed_needle, meter1_x, meter1_y, needle_x, needle_y)

        # 闪烁状态点
        for i, dot in enumerate(self.status_dots):
            phase = (current_time * 2 + i * 0.3) % 2
            if phase < 1:
                color = '#10b981'
            else:
                color = '#34d399'
            self.decoration_canvas.itemconfig(dot, fill=color)

        # 继续动画
        self.root.after(100, self.animate_decorations)

    def create_status_section(self, parent):
        """创建状态显示区域 - 适合右侧监控区"""
        status_frame = ttk.LabelFrame(parent, text="📊 系统状态", style='Modern.TLabelframe', padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        # 发送统计 - 更紧凑的显示
        stats_frame = ttk.Frame(status_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(stats_frame, text="发送统计:", font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        self.stats_labels = {}
        stats_items = [
            ("已发送", "0"),
            ("成功率", "0%"),
            ("队列任务", "0")
        ]

        for label, value in stats_items:
            item_frame = ttk.Frame(stats_frame)
            item_frame.pack(fill=tk.X, pady=1)
            ttk.Label(item_frame, text=f"{label}:", font=('Microsoft YaHei UI', 8)).pack(side=tk.LEFT)
            self.stats_labels[label] = ttk.Label(item_frame, text=value,
                                               font=('Microsoft YaHei UI', 8, 'bold'),
                                               foreground='#2563eb')
            self.stats_labels[label].pack(side=tk.RIGHT)

        # 深度协调状态
        coordination_frame = ttk.Frame(status_frame)
        coordination_frame.pack(fill=tk.X, pady=(8, 0))

        ttk.Label(coordination_frame, text="深度协调:", font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        self.coordination_status = ttk.Label(coordination_frame, text="🔧 正在初始化...",
                                           font=('Microsoft YaHei UI', 8), foreground='#f59e0b')
        self.coordination_status.pack(anchor=tk.W, pady=(2, 0))

    def create_action_buttons_section(self, parent):
        """创建底部操作按钮区域"""
        # 高级功能区域
        advanced_frame = ttk.LabelFrame(parent, text="🔧 高级功能", style='Modern.TLabelframe', padding="15")
        advanced_frame.pack(fill=tk.X, pady=(20, 10))

        # 分析工具
        analysis_frame = ttk.Frame(advanced_frame)
        analysis_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(analysis_frame, text="📊 分析工具:", font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        analysis_buttons = ttk.Frame(analysis_frame)
        analysis_buttons.pack(side=tk.RIGHT)

        analysis_tools = [
            ("🔍 重复检测", self.check_duplicates),
            ("🔧 调试分析", self.debug_similarity_analysis),
            ("📚 历史记录", self.open_history_manager),
            ("🔍 智能搜索", self.open_rag_search)
        ]

        for text, command in analysis_tools:
            ttk.Button(analysis_buttons, text=text, command=command,
                      style='Primary.TButton').pack(side=tk.LEFT, padx=2)

        # 管理工具
        management_frame = ttk.Frame(advanced_frame)
        management_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(management_frame, text="🛠️ 管理工具:", font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        management_buttons = ttk.Frame(management_frame)
        management_buttons.pack(side=tk.RIGHT)

        management_tools = [
            ("📬 自动回复监控", self.open_reply_monitor),
            ("📊 质量数据库", self.open_quality_manager),
            ("🛡️ 反垃圾邮件", self.open_anti_spam_manager),
            ("🆘 QQ应急管理", self.open_qq_emergency_manager)
        ]

        for text, command in management_tools:
            ttk.Button(management_buttons, text=text, command=command,
                      style='Success.TButton').pack(side=tk.LEFT, padx=2)

        # 系统工具
        system_frame = ttk.Frame(advanced_frame)
        system_frame.pack(fill=tk.X)

        ttk.Label(system_frame, text="⚙️ 系统工具:", font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        system_buttons = ttk.Frame(system_frame)
        system_buttons.pack(side=tk.RIGHT)

        system_tools = [
            ("🔧 系统协调器", self.open_system_coordinator),
            ("📋 队列管理", self.open_queue_system),
            ("📝 发送记录", self.show_send_history)
        ]

        for text, command in system_tools:
            ttk.Button(system_buttons, text=text, command=command,
                      style='Warning.TButton').pack(side=tk.LEFT, padx=2)

        # 队列系统区域
        queue_frame = ttk.LabelFrame(parent, text="📬 邮件队列系统", style='Modern.TLabelframe', padding="15")
        queue_frame.pack(fill=tk.X, pady=(0, 20))

        # 队列控制
        queue_control_frame = ttk.Frame(queue_frame)
        queue_control_frame.pack(fill=tk.X)

        ttk.Button(queue_control_frame, text="➕ 添加到队列", command=self.add_to_queue,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))

        self.start_queue_button = ttk.Button(queue_control_frame, text="🚀 开始队列发送",
                                           command=self.start_queue_sending, style='Success.TButton',
                                           state='disabled')
        self.start_queue_button.pack(side=tk.LEFT, padx=(0, 10))

        self.queue_status_label = ttk.Label(queue_control_frame, text="队列: 0 个任务",
                                          font=('Microsoft YaHei UI', 10, 'bold'), foreground='#2563eb')
        self.queue_status_label.pack(side=tk.RIGHT)

    def bind_mousewheel_events(self):
        """绑定鼠标滚轮事件 - 最佳滚动方案"""
        def _on_mousewheel(event):
            # 最佳滚动方案：自适应滚动，确保响应灵敏
            if abs(event.delta) >= 120:
                # 标准鼠标滚轮
                scroll_amount = int(-1 * (event.delta / 120))
            else:
                # 精密鼠标或触摸板
                scroll_amount = -1 if event.delta > 0 else 1

            # 确保至少滚动1个单位
            if scroll_amount == 0:
                scroll_amount = -1 if event.delta > 0 else 1

            # 执行滚动
            try:
                self.main_canvas.yview_scroll(scroll_amount, "units")
            except Exception as e:
                # 如果Canvas滚动失败，记录错误但不中断
                print(f"滚动错误: {e}")

        # 立即绑定滚轮事件到整个窗口
        self.root.bind_all("<MouseWheel>", _on_mousewheel)

        # 也绑定到Canvas，确保兼容性
        self.main_canvas.bind("<MouseWheel>", _on_mousewheel)

        # 添加键盘滚动支持
        def _on_key_scroll(event):
            try:
                if event.keysym == 'Up':
                    self.main_canvas.yview_scroll(-3, "units")
                elif event.keysym == 'Down':
                    self.main_canvas.yview_scroll(3, "units")
                elif event.keysym == 'Page_Up':
                    self.main_canvas.yview_scroll(-10, "units")
                elif event.keysym == 'Page_Down':
                    self.main_canvas.yview_scroll(10, "units")
                elif event.keysym == 'Home':
                    self.main_canvas.yview_moveto(0)
                elif event.keysym == 'End':
                    self.main_canvas.yview_moveto(1)
            except Exception as e:
                print(f"键盘滚动错误: {e}")

        # 绑定键盘事件
        self.root.bind('<Up>', _on_key_scroll)
        self.root.bind('<Down>', _on_key_scroll)
        self.root.bind('<Page_Up>', _on_key_scroll)
        self.root.bind('<Page_Down>', _on_key_scroll)
        self.root.bind('<Home>', _on_key_scroll)
        self.root.bind('<End>', _on_key_scroll)

        # 确保窗口可以获得焦点
        self.root.focus_set()

    def update_coordination_status(self, status_text, color='#10b981'):
        """更新深度协调状态显示"""
        if hasattr(self, 'coordination_status'):
            self.coordination_status.configure(text=status_text, foreground=color)

        if hasattr(self, 'status_indicator'):
            if '就绪' in status_text or '正常' in status_text:
                self.status_indicator.configure(text="🟢 系统就绪", foreground='#10b981')
            elif '警告' in status_text or '风险' in status_text:
                self.status_indicator.configure(text="🟡 注意状态", foreground='#f59e0b')
            elif '错误' in status_text or '失败' in status_text:
                self.status_indicator.configure(text="🔴 异常状态", foreground='#ef4444')
            else:
                self.status_indicator.configure(text="🔵 运行中", foreground='#2563eb')

    def initialize_variables(self):
        """初始化变量"""
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")

        # 进度条（在需要时创建）
        self.progress = None

        # 初始化其他变量
        self.initialize_other_variables()

        # 初始化日志
        if hasattr(self, 'log_text'):
            self.log_message("程序启动完成，准备就绪")
            self.log_message("🔧 深度协调系统已启用")
            self.log_message("✨ 现代化界面 + 智能协调功能")

    def initialize_other_variables(self):
        """初始化其他变量"""
        self.is_sending = False
        self.should_stop = False
        self.sent_emails = []
        self.email_queue = []
        self.auth_codes = {}
        self.auto_queue_mode = tk.BooleanVar(value=True)

    def on_auto_queue_mode_changed(self):
        """自动队列模式状态改变时的处理"""
        if self.auto_queue_mode.get():
            self.auto_mode_label.config(
                text="(主系统发送完成后自动启动队列发送)",
                foreground='green'
            )
            self.log_message("🤖 已启用自动队列模式")
        else:
            self.auto_mode_label.config(
                text="(主系统发送完成后需手动启动队列)",
                foreground='orange'
            )
            self.log_message("⚠️ 已禁用自动队列模式，需手动启动队列")

    def log_message(self, message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 在GUI中显示
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)  # 自动滚动到底部

        # 同时记录到详细日志文件
        try:
            if hasattr(self, 'logger'):
                # 根据消息内容确定日志级别
                if "❌" in message or "错误" in message or "失败" in message:
                    self.logger.error(message)
                elif "⚠️" in message or "警告" in message:
                    self.logger.warning(message)
                elif "✅" in message or "成功" in message or "完成" in message:
                    self.logger.info(message)
                else:
                    self.logger.debug(message)
        except Exception as e:
            # 如果日志记录失败，不影响主程序运行
            print(f"日志记录失败: {e}")

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("日志已清空")

    def save_log(self):
        """保存日志到文件"""
        try:
            log_content = self.log_text.get(1.0, tk.END)
            filename = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                self.log_message(f"日志已保存到: {filename}")
                messagebox.showinfo("成功", "日志文件保存成功！")
        except Exception as e:
            self.log_message(f"保存日志失败: {str(e)}")
            messagebox.showerror("错误", f"保存日志失败：{str(e)}")

    def open_log_file(self):
        """打开系统日志文件"""
        try:
            log_file = "email_sender.log"
            if os.path.exists(log_file):
                os.startfile(log_file)
                self.log_message("已打开系统日志文件")
            else:
                self.log_message("系统日志文件不存在")
                messagebox.showwarning("提示", "系统日志文件不存在，请先发送邮件生成日志")
        except Exception as e:
            self.log_message(f"打开日志文件失败: {str(e)}")
            messagebox.showerror("错误", f"打开日志文件失败：{str(e)}")

    def validate_emails(self):
        """验证邮箱地址列表"""
        recipient_emails = self.recipient_emails.get(1.0, tk.END).strip()

        if not recipient_emails:
            self.log_message("邮箱验证: 输入框为空")
            messagebox.showwarning("提示", "请先输入邮箱地址")
            return

        self.log_message("开始验证邮箱地址...")

        # 使用相同的增强解析逻辑
        # 1. 先按分号分割（包括中文分号）
        if ';' in recipient_emails or '；' in recipient_emails:
            self.log_message("检测到分号分隔符，按分号解析")
            # 统一处理中英文分号
            normalized_emails = recipient_emails.replace('；', ';')
            email_parts = [email.strip() for email in normalized_emails.split(';')]
        # 2. 再按换行分割
        elif '\n' in recipient_emails:
            self.log_message("检测到换行分隔符，按换行解析")
            email_parts = [email.strip() for email in recipient_emails.split('\n')]
        # 3. 最后按逗号分割
        else:
            self.log_message("使用逗号分隔符解析")
            # 处理中英文逗号
            normalized_emails = recipient_emails.replace('，', ',')
            email_parts = [email.strip() for email in normalized_emails.split(',')]

        # 进一步处理混合分隔符的情况
        final_email_parts = []
        for part in email_parts:
            # 如果某个部分还包含其他分隔符，继续分割
            if ';' in part or '；' in part:
                # 统一处理中英文分号
                part = part.replace('；', ';')
                final_email_parts.extend([email.strip() for email in part.split(';')])
            elif ',' in part or '，' in part:
                part = part.replace('，', ',')
                final_email_parts.extend([email.strip() for email in part.split(',')])
            elif '\n' in part:
                final_email_parts.extend([email.strip() for email in part.split('\n')])
            else:
                final_email_parts.append(part.strip())

        email_parts = final_email_parts

        valid_emails = []
        invalid_emails = []

        for email in email_parts:
            email = email.strip()
            if email:
                if self._is_valid_email(email):
                    valid_emails.append(email)
                    self.log_message(f"✓ 有效邮箱: {email}")
                else:
                    invalid_emails.append(email)
                    self.log_message(f"✗ 无效邮箱: {email}")

        # 显示验证结果
        result_msg = f"邮箱验证完成！\n\n"
        result_msg += f"有效邮箱: {len(valid_emails)} 个\n"
        result_msg += f"无效邮箱: {len(invalid_emails)} 个\n\n"

        if len(valid_emails) > 0:
            result_msg += f"前5个有效邮箱:\n"
            for i, email in enumerate(valid_emails[:5], 1):
                result_msg += f"{i}. {email}\n"
            if len(valid_emails) > 5:
                result_msg += f"... 还有 {len(valid_emails) - 5} 个\n"

        if invalid_emails:
            result_msg += f"\n无效邮箱:\n"
            for email in invalid_emails:
                result_msg += f"• {email}\n"

        messagebox.showinfo("邮箱验证结果", result_msg)
        self.log_message(f"邮箱验证完成: {len(valid_emails)} 个有效, {len(invalid_emails)} 个无效")

    def add_attachment(self):
        """添加附件"""
        files = filedialog.askopenfilenames(
            title="选择附件文件",
            filetypes=[
                ("所有支持的文件", "*.txt;*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.ppt;*.pptx;*.jpg;*.jpeg;*.png;*.gif;*.zip;*.rar;*.7z"),
                ("文档文件", "*.txt;*.pdf;*.doc;*.docx"),
                ("表格文件", "*.xls;*.xlsx"),
                ("演示文件", "*.ppt;*.pptx"),
                ("图片文件", "*.jpg;*.jpeg;*.png;*.gif"),
                ("压缩文件", "*.zip;*.rar;*.7z"),
                ("所有文件", "*.*")
            ]
        )
        
        for file in files:
            if file not in self.attachment_listbox.get(0, tk.END):
                self.attachment_listbox.insert(tk.END, file)
                self.log_message(f"添加附件: {os.path.basename(file)}")
            else:
                self.log_message(f"附件已存在: {os.path.basename(file)}")
                
    def remove_attachment(self):
        """删除选中的附件"""
        selection = self.attachment_listbox.curselection()
        if selection:
            filename = self.attachment_listbox.get(selection[0])
            self.attachment_listbox.delete(selection[0])
            self.log_message(f"删除附件: {os.path.basename(filename)}")

    def clear_attachments(self):
        """清空所有附件"""
        count = self.attachment_listbox.size()
        self.attachment_listbox.delete(0, tk.END)
        if count > 0:
            self.log_message(f"清空了 {count} 个附件")

    def clear_form(self):
        """清空表单"""
        self.recipient_emails.delete(1.0, tk.END)  # 改为多行文本清空
        self.subject.delete(0, tk.END)
        self.body.delete(1.0, tk.END)

        # 重新添加占位符文本
        placeholder_text = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
        self.body.insert(tk.END, placeholder_text)
        self.body.config(foreground='gray')

        self.clear_attachments()
        self.status_var.set("表单已清空")
        self.log_message("表单已清空")
        
    def test_connection(self):
        """测试SMTP连接"""
        sender_email = self.sender_email.get().strip()
        if not sender_email or not sender_email.endswith('@qq.com'):
            self.log_message("连接测试失败: 邮箱地址无效")
            messagebox.showerror("错误", "请输入有效的QQ邮箱地址")
            return

        self.status_var.set("正在测试连接...")
        self.progress.start()
        self.log_message(f"开始测试SMTP连接: {sender_email}")

        def test_thread():
            try:
                # 测试SMTP连接
                import smtplib
                server = smtplib.SMTP('smtp.qq.com', 587)
                server.starttls()
                server.quit()

                self.root.after(0, lambda: self.test_success())
            except Exception as e:
                self.root.after(0, lambda err=str(e): self.test_failed(err))

        threading.Thread(target=test_thread, daemon=True).start()

    def test_success(self):
        """连接测试成功"""
        self.progress.stop()
        self.status_var.set("连接测试成功")
        self.log_message("SMTP连接测试成功")
        messagebox.showinfo("成功", "SMTP连接测试成功！")

    def test_failed(self, error):
        """连接测试失败"""
        self.progress.stop()
        self.status_var.set("连接测试失败")
        self.log_message(f"SMTP连接测试失败: {error}")
        messagebox.showerror("错误", f"连接测试失败：{error}")
        
    def send_email(self):
        """发送邮件"""
        try:
            self.logger.info("开始发送邮件流程")

            # 验证输入
            sender_email = self.sender_email.get().strip()
            recipient_emails = self.recipient_emails.get(1.0, tk.END).strip()  # 改为多行文本获取
            subject = self.subject.get().strip()
            body_raw = self.body.get(1.0, tk.END).strip()

            self.logger.debug(f"发送参数 - 发件人: {sender_email}, 收件人数量: {len(recipient_emails.split()) if recipient_emails else 0}, 主题: {subject[:50]}...")

        except Exception as e:
            self.logger.error(f"获取发送参数时出错: {str(e)}", exc_info=True)
            self.log_message(f"❌ 获取发送参数失败: {str(e)}")
            messagebox.showerror("错误", f"获取发送参数失败：{str(e)}")
            return

        # 处理占位符文本
        placeholder_text = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
        if body_raw == placeholder_text.strip():
            body = ""  # 如果是占位符文本，则视为空正文
        else:
            body = body_raw

        self.log_message("开始验证邮件信息...")

        if not sender_email or not sender_email.endswith('@qq.com'):
            self.log_message("验证失败: 发送者邮箱地址无效")
            messagebox.showerror("错误", "请输入有效的QQ邮箱地址")
            return

        if not recipient_emails:
            self.log_message("验证失败: 收件人邮箱地址为空")
            messagebox.showerror("错误", "请输入收件人邮箱地址")
            return

        if not subject:
            subject = "来自自动化邮件助手的邮件"
            self.log_message("使用默认邮件主题")

        # 允许空正文，不再强制使用默认内容
        if not body:
            self.log_message("邮件正文为空")

        # 记录个性化设置状态
        if self.add_personalization.get():
            self.log_message("已启用个性化设置：将为每封邮件添加编号和时间戳")
        else:
            self.log_message("未启用个性化设置：邮件内容将保持原样")

        # 获取附件列表
        attachments = list(self.attachment_listbox.get(0, tk.END))

        # 解析收件人列表 - 增强分号识别和多种分隔符支持
        self.log_message("开始解析邮箱地址...")

        # 多步骤解析，优先处理分号分隔（最常用）
        # 1. 先按分号分割（包括中文分号）
        if ';' in recipient_emails or '；' in recipient_emails:
            self.log_message("检测到分号分隔符，按分号解析")
            # 统一处理中英文分号
            normalized_emails = recipient_emails.replace('；', ';')
            email_parts = [email.strip() for email in normalized_emails.split(';')]
        # 2. 再按换行分割
        elif '\n' in recipient_emails:
            self.log_message("检测到换行分隔符，按换行解析")
            email_parts = [email.strip() for email in recipient_emails.split('\n')]
        # 3. 最后按逗号分割
        else:
            self.log_message("使用逗号分隔符解析")
            # 处理中英文逗号
            normalized_emails = recipient_emails.replace('，', ',')
            email_parts = [email.strip() for email in normalized_emails.split(',')]

        # 进一步处理混合分隔符的情况
        final_email_parts = []
        for part in email_parts:
            # 如果某个部分还包含其他分隔符，继续分割
            if ';' in part or '；' in part:
                # 统一处理中英文分号
                part = part.replace('；', ';')
                final_email_parts.extend([email.strip() for email in part.split(';')])
            elif ',' in part or '，' in part:
                part = part.replace('，', ',')
                final_email_parts.extend([email.strip() for email in part.split(',')])
            elif '\n' in part:
                final_email_parts.extend([email.strip() for email in part.split('\n')])
            else:
                final_email_parts.append(part.strip())

        email_parts = final_email_parts

        # 清理和验证邮箱地址
        recipient_list = []
        invalid_count = 0

        self.log_message(f"开始验证 {len(email_parts)} 个邮箱地址...")

        for email in email_parts:
            email = email.strip()
            if email:  # 非空邮箱
                if self._is_valid_email(email):
                    recipient_list.append(email)
                    self.log_message(f"✓ 有效邮箱: {email}")
                else:
                    invalid_count += 1
                    self.log_message(f"✗ 跳过无效邮箱: {email}")

        self.log_message(f"邮箱验证完成: {len(recipient_list)} 个有效, {invalid_count} 个无效")

        # 如果没有有效邮箱，停止发送
        if not recipient_list:
            self.log_message("错误: 没有找到有效的邮箱地址")
            messagebox.showerror("错误", "没有找到有效的邮箱地址，请检查输入格式")
            return

        # 显示前5个收件人作为预览
        if len(recipient_list) > 5:
            preview = ', '.join(recipient_list[:5]) + f" ... (共{len(recipient_list)}个)"
        else:
            preview = ', '.join(recipient_list)
        self.log_message(f"收件人预览: {preview}")

        # 确认发送
        mode_names = {
            "fast": "快速发送（30-60秒间隔）",
            "standard": "标准发送（1-2分钟间隔）",
            "safe": "安全发送（3-5分钟间隔）"
        }
        send_mode_text = mode_names.get(self.send_mode.get(), "标准发送")

        confirm_msg = f"确认发送邮件吗？\n\n"
        confirm_msg += f"发送模式: {send_mode_text}\n"
        confirm_msg += f"有效收件人: {len(recipient_list)} 个\n"
        if invalid_count > 0:
            confirm_msg += f"无效邮箱: {invalid_count} 个（已跳过）\n"
        confirm_msg += f"主题: {subject}\n"
        confirm_msg += f"附件数量: {len(attachments)}\n"
        confirm_msg += f"\n注意：所有邮件都是分别发送，保护收件人隐私"

        if not messagebox.askyesno("确认发送", confirm_msg):
            self.log_message("用户取消发送")
            return

        self.log_message(f"用户确认发送，模式: {send_mode_text}")

        # 设置发送状态
        self.is_sending = True
        self.should_stop = False

        # 更新按钮状态
        self.send_button.config(state='disabled')
        self.pause_button.config(state='normal')
        self.stop_button.config(state='normal')
        self.status_var.set("正在发送邮件...")
        self.progress.start()

        def send_thread():
            try:
                # 获取发送者邮箱地址
                current_sender_email = self.sender_email.get().strip()
                self.log_message(f"使用发送者邮箱: {current_sender_email}")

                # 获取授权码 - 修复授权码获取逻辑
                auth_info = self.auth_codes.get(current_sender_email)
                if not auth_info:
                    self.root.after(0, lambda: self.log_message("❌ 缺少授权码，请先设置邮箱授权码"))
                    self.root.after(0, lambda: self.send_error("缺少授权码，请先设置邮箱授权码"))
                    return

                # 提取授权码（支持新旧格式）
                if isinstance(auth_info, dict):
                    auth_code = auth_info.get('auth_code', '')
                else:
                    auth_code = auth_info  # 兼容旧格式

                if not auth_code:
                    self.root.after(0, lambda: self.log_message("❌ 授权码为空，请重新设置"))
                    self.root.after(0, lambda: self.send_error("授权码为空，请重新设置"))
                    return

                sender = EmailSender(current_sender_email)
                sender.smtp_config['password'] = auth_code

                # 准备邮件列表用于批次发送
                email_list = []
                for i, email in enumerate(recipient_list, 1):
                    # 个性化内容
                    personalized_body = self._personalize_content(body, i)

                    email_list.append({
                        'to_emails': [email],
                        'subject': subject,
                        'body': personalized_body,
                        'attachments': attachments if attachments else None
                    })

                # 生成会话ID
                import datetime
                if self.resume_from_breakpoint and self.breakpoint_session_id:
                    session_id = self.breakpoint_session_id
                    self.log_message(f"🔄 使用断点会话ID: {session_id}")
                else:
                    session_id = f"gui_session_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    self.log_message(f"🆕 创建新会话ID: {session_id}")

                self.current_session_id = session_id

                # 定义进度回调
                def progress_callback(progress_info):
                    current_email = progress_info.get('current_email', 0)
                    total_emails = progress_info.get('total_emails', 0)
                    success_count = progress_info.get('success_count', 0)
                    failed_count = progress_info.get('failed_count', 0)
                    current_batch = progress_info.get('current_batch', 0)
                    total_batches = progress_info.get('total_batches', 0)

                    self.root.after(0, lambda: self.log_message(
                        f"📊 批次 {current_batch}/{total_batches} - "
                        f"已发送 {current_email}/{total_emails} 封 "
                        f"(成功: {success_count}, 失败: {failed_count})"
                    ))

                # 定义停止检查回调
                def stop_callback():
                    return self.should_stop

                # 定义暂停检查回调
                def pause_callback():
                    return self.should_pause

                # 使用批次发送
                current_send_mode = self.send_mode.get()
                self.root.after(0, lambda: self.log_message(f"🚀 开始批次发送 - 模式: {current_send_mode}"))

                results = sender.send_batch_emails(
                    email_list=email_list,
                    send_mode=current_send_mode,
                    progress_callback=progress_callback,
                    stop_callback=stop_callback,
                    pause_callback=pause_callback,
                    session_id=session_id,
                    resume_from_progress=self.resume_from_breakpoint
                )

                # 重置断点继续标志
                self.resume_from_breakpoint = False
                self.breakpoint_session_id = None

                success_count = results['success']
                failed_count = results['failed']
                total_count = len(recipient_list)

                # 记录成功发送的邮件（内存和数据库）
                for i in range(success_count):
                    if i < len(email_list):
                        email_info = email_list[i]
                        email_record = {
                            'recipient': email_info['to_emails'][0],
                            'subject': email_info['subject'],
                            'body': email_info['body'],
                            'send_time': datetime.datetime.now(),
                            'batch_id': session_id
                        }
                        self.sent_emails.append(email_record)

                        # 同时记录到历史数据库
                        try:
                            email_attachments = email_info.get('attachments', [])
                            self.history_manager.add_email_record(
                                sender_email=current_sender_email,
                                recipient_email=email_info['to_emails'][0],
                                subject=email_info['subject'],
                                body=email_info['body'],
                                success=True,
                                batch_id=session_id,
                                attachments=email_attachments
                            )
                        except Exception as e:
                            self.root.after(0, lambda err=str(e): self.log_message(f"⚠️ 记录历史失败: {err}"))

                # 发送完成统计
                success_count = results['success']
                failed_count = results['failed']
                total_count = len(recipient_list)
                success_rate = (success_count / total_count * 100) if total_count > 0 else 0

                mode_name = {
                    "fast": "快速发送",
                    "standard": "标准发送",
                    "safe": "安全发送"
                }.get(current_send_mode, "标准发送")

                # 显示发送统计
                self.root.after(0, lambda: self.log_message("=" * 50))
                self.root.after(0, lambda: self.log_message("📊 批次发送统计报告"))
                self.root.after(0, lambda: self.log_message("=" * 50))
                self.root.after(0, lambda: self.log_message(f"发送模式: {mode_name}"))
                self.root.after(0, lambda: self.log_message(f"总邮箱数量: {total_count}"))
                self.root.after(0, lambda: self.log_message(f"发送成功: {success_count} 个 ({success_rate:.1f}%)"))
                self.root.after(0, lambda: self.log_message(f"发送失败: {failed_count} 个"))

                # 显示批次信息
                batch_info = results.get('batch_info', {})
                if batch_info:
                    self.root.after(0, lambda: self.log_message(f"总批次数: {batch_info.get('total_batches', 0)}"))
                    self.root.after(0, lambda: self.log_message(f"批次大小: {batch_info.get('batch_size', 0)}"))

                # 显示错误信息
                errors = results.get('errors', [])
                if errors:
                    self.root.after(0, lambda: self.log_message(""))
                    self.root.after(0, lambda: self.log_message("❌ 发送错误:"))
                    for error in errors[:5]:  # 只显示前5个错误
                        self.root.after(0, lambda e=error: self.log_message(f"  ✗ {e}"))
                    if len(errors) > 5:
                        self.root.after(0, lambda: self.log_message(f"  ... 还有 {len(errors) - 5} 个错误"))

                self.root.after(0, lambda: self.log_message(""))
                self.root.after(0, lambda: self.log_message("🔒 所有邮件均为分别发送，确保收件人隐私安全"))
                self.root.after(0, lambda: self.log_message("=" * 50))

                # 简化的结果消息用于弹窗
                if self.should_stop:
                    simple_msg = f"{mode_name}已停止！已发送: {success_count}/{total_count}"
                elif results.get('paused'):
                    simple_msg = f"{mode_name}已暂停！已发送: {success_count}/{total_count}"
                else:
                    simple_msg = f"{mode_name}完成！成功: {success_count}/{total_count}"

                # 获取发送的收件人列表用于自动监控
                sent_recipient_list = [email_info['to_emails'][0] for email_info in email_list[:success_count]]

                self.root.after(0, lambda: self.send_complete(success_count > 0, simple_msg, sent_recipient_list, current_sender_email))

            except Exception as e:
                error_msg = f"发送过程中出现严重异常: {str(e)}"
                self.root.after(0, lambda msg=error_msg: self.log_message(msg))
                self.root.after(0, lambda err=str(e): self.send_error(err))

        threading.Thread(target=send_thread, daemon=True).start()

    def _is_valid_email(self, email):
        """验证邮箱地址格式"""
        if not email or '@' not in email:
            return False

        parts = email.split('@')
        if len(parts) != 2:
            return False

        local, domain = parts

        # 检查本地部分
        if not local or len(local) > 64:
            return False

        # 检查域名部分
        if not domain or '.' not in domain or len(domain) > 255:
            return False

        # 检查域名不能以点开头或结尾
        if domain.startswith('.') or domain.endswith('.'):
            return False

        # 检查不能有连续的点
        if '..' in domain:
            return False

        return True

    def check_duplicates(self):
        """检查重复邮件发送"""
        try:
            # 获取当前输入
            sender_email = self.sender_email.get().strip()
            subject = self.subject.get().strip()
            body = self.body.get(1.0, tk.END).strip()
            recipient_emails_text = self.recipient_emails.get(1.0, tk.END).strip()

            if not all([sender_email, subject, body, recipient_emails_text]):
                messagebox.showwarning("提示", "请先填写完整的邮件信息")
                return

            # 解析收件人列表
            recipient_list = self._parse_recipient_emails(recipient_emails_text)
            if not recipient_list:
                messagebox.showwarning("提示", "请输入有效的收件人邮箱")
                return

            self.log_message("🔍 开始检查重复邮件...")

            # 使用RAG搜索引擎进行高级重复检测
            result = self.rag_search.advanced_duplicate_detection(
                subject, body, recipient_list, sender_email
            )

            # 显示检测结果
            self._show_duplicate_detection_result(result)

        except Exception as e:
            error_msg = f"重复检测失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _parse_recipient_emails(self, recipient_emails_text):
        """解析收件人邮箱列表"""
        # 使用与验证邮箱相同的解析逻辑
        if ';' in recipient_emails_text or '；' in recipient_emails_text:
            normalized_emails = recipient_emails_text.replace('；', ';')
            email_parts = [email.strip() for email in normalized_emails.split(';')]
        elif '\n' in recipient_emails_text:
            email_parts = [email.strip() for email in recipient_emails_text.split('\n')]
        else:
            normalized_emails = recipient_emails_text.replace('，', ',')
            email_parts = [email.strip() for email in normalized_emails.split(',')]

        # 进一步处理混合分隔符
        final_email_parts = []
        for part in email_parts:
            if ';' in part or '；' in part:
                part = part.replace('；', ';')
                final_email_parts.extend([email.strip() for email in part.split(';')])
            elif ',' in part or '，' in part:
                part = part.replace('，', ',')
                final_email_parts.extend([email.strip() for email in part.split(',')])
            elif '\n' in part:
                final_email_parts.extend([email.strip() for email in part.split('\n')])
            else:
                final_email_parts.append(part.strip())

        # 过滤有效邮箱
        valid_emails = []
        for email in final_email_parts:
            email = email.strip()
            if email and self._is_valid_email(email):
                valid_emails.append(email)

        return valid_emails

    def _show_duplicate_detection_result(self, result):
        """显示重复检测结果"""
        # 创建结果窗口
        result_window = tk.Toplevel(self.root)
        result_window.title("🔍 重复邮件检测结果")
        result_window.geometry("800x600")
        result_window.transient(self.root)
        result_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(result_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="🔍 重复邮件检测结果",
                               font=('Microsoft YaHei UI', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # 统计信息
        stats_frame = ttk.LabelFrame(main_frame, text="检测统计", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        stats_text = f"""总收件人数量: {result['total_recipients']}
安全发送: {len(result['safe_recipients'])} 人
完全重复: {len(result['exact_matches'])} 人
相似内容: {len(result['similar_matches'])} 人

建议: {result['recommendation']}"""

        ttk.Label(stats_frame, text=stats_text, font=('Consolas', 10)).pack(anchor=tk.W)

        # 详细结果
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 安全收件人标签页
        if result['safe_recipients']:
            safe_frame = ttk.Frame(notebook)
            notebook.add(safe_frame, text=f"✅ 安全发送 ({len(result['safe_recipients'])})")

            safe_text = scrolledtext.ScrolledText(safe_frame, height=10)
            safe_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
            safe_text.insert(tk.END, "\n".join(result['safe_recipients']))

        # 完全重复标签页
        if result['exact_matches']:
            exact_frame = ttk.Frame(notebook)
            notebook.add(exact_frame, text=f"⚠️ 完全重复 ({len(result['exact_matches'])})")

            exact_text = scrolledtext.ScrolledText(exact_frame, height=10)
            exact_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            for match in result['exact_matches']:
                exact_text.insert(tk.END, f"收件人: {match['recipient']}\n")
                exact_text.insert(tk.END, f"上次发送: {match['previous_email']['send_time']}\n")
                exact_text.insert(tk.END, f"邮件主题: {match['previous_email']['subject']}\n")
                exact_text.insert(tk.END, "-" * 50 + "\n\n")

        # 相似内容标签页
        if result['similar_matches']:
            similar_frame = ttk.Frame(notebook)
            notebook.add(similar_frame, text=f"🔍 相似内容 ({len(result['similar_matches'])})")

            similar_text = scrolledtext.ScrolledText(similar_frame, height=10)
            similar_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            for match in result['similar_matches']:
                similar_text.insert(tk.END, f"收件人: {match['recipient']}\n")
                similar_text.insert(tk.END, f"相似度: {match['similarity']:.2%}\n")
                similar_text.insert(tk.END, f"上次发送: {match['previous_email']['send_time']}\n")
                similar_text.insert(tk.END, f"邮件主题: {match['previous_email']['subject']}\n")
                similar_text.insert(tk.END, "-" * 50 + "\n\n")

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        if result['safe_recipients']:
            ttk.Button(button_frame, text="✅ 只发送给安全收件人",
                      command=lambda: self._apply_safe_recipients(result['safe_recipients'], result_window)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="关闭",
                  command=result_window.destroy).pack(side=tk.RIGHT, padx=5)

    def _apply_safe_recipients(self, safe_recipients, window):
        """应用安全收件人列表"""
        if safe_recipients:
            self.recipient_emails.delete(1.0, tk.END)
            self.recipient_emails.insert(1.0, "\n".join(safe_recipients))
            self.log_message(f"✅ 已更新收件人列表，只包含 {len(safe_recipients)} 个安全收件人")
            window.destroy()
            messagebox.showinfo("更新完成", f"已更新收件人列表，只包含 {len(safe_recipients)} 个安全收件人")
        else:
            messagebox.showwarning("提示", "没有安全的收件人可以发送")

    def open_history_manager(self):
        """打开历史记录管理器"""
        try:
            # 创建历史记录窗口
            history_window = tk.Toplevel(self.root)
            history_window.title("📚 邮件发送历史记录")
            history_window.geometry("1000x700")
            history_window.transient(self.root)

            # 主框架
            main_frame = ttk.Frame(history_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = ttk.Label(main_frame, text="📚 邮件发送历史记录",
                                   font=('Microsoft YaHei UI', 14, 'bold'))
            title_label.pack(pady=(0, 10))

            # 统计信息
            sender_email = self.sender_email.get().strip()
            stats = self.history_manager.get_statistics(sender_email if sender_email else None)

            stats_frame = ttk.LabelFrame(main_frame, text="统计信息", padding="10")
            stats_frame.pack(fill=tk.X, pady=(0, 10))

            stats_text = f"""总发送邮件: {stats['total_emails']} 封
成功发送: {stats['successful_emails']} 封
失败邮件: {stats['failed_emails']} 封
成功率: {stats['success_rate']:.1f}%
唯一收件人: {stats['unique_recipients']} 人
唯一内容: {stats['unique_contents']} 种
今日发送: {stats['today_sent']} 封"""

            ttk.Label(stats_frame, text=stats_text, font=('Consolas', 10)).pack(anchor=tk.W)

            # 搜索框
            search_frame = ttk.LabelFrame(main_frame, text="搜索历史", padding="10")
            search_frame.pack(fill=tk.X, pady=(0, 10))

            search_var = tk.StringVar()
            search_entry = ttk.Entry(search_frame, textvariable=search_var, width=50)
            search_entry.pack(side=tk.LEFT, padx=(0, 10))

            def search_history():
                query = search_var.get().strip()
                if query:
                    results = self.history_manager.search_email_history(query, sender_email, 100)
                    self._update_history_tree(tree, results)
                else:
                    # 显示所有记录
                    results = self.history_manager.search_email_history("", sender_email, 100)
                    self._update_history_tree(tree, results)

            ttk.Button(search_frame, text="🔍 搜索", command=search_history).pack(side=tk.LEFT, padx=5)
            ttk.Button(search_frame, text="🔄 刷新", command=lambda: search_history()).pack(side=tk.LEFT, padx=5)

            # 历史记录列表
            list_frame = ttk.LabelFrame(main_frame, text="历史记录", padding="10")
            list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # 创建Treeview
            columns = ('时间', '收件人', '主题', '状态')
            tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

            # 设置列标题
            tree.heading('时间', text='发送时间')
            tree.heading('收件人', text='收件人')
            tree.heading('主题', text='邮件主题')
            tree.heading('状态', text='发送状态')

            # 设置列宽
            tree.column('时间', width=150)
            tree.column('收件人', width=200)
            tree.column('主题', width=300)
            tree.column('状态', width=80)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 初始加载数据
            search_history()

            # 操作按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(button_frame, text="📊 导出历史",
                      command=lambda: self._export_history(sender_email)).pack(side=tk.LEFT, padx=5)

            ttk.Button(button_frame, text="🗑️ 清理旧记录",
                      command=self._cleanup_old_records).pack(side=tk.LEFT, padx=5)

            ttk.Button(button_frame, text="关闭",
                      command=history_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            error_msg = f"打开历史记录失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _cleanup_old_records(self):
        """清理旧的历史记录"""
        try:
            import tkinter.simpledialog as simpledialog

            # 询问用户保留天数
            days_input = simpledialog.askinteger(
                "清理旧记录",
                "请输入要保留的天数（超过此天数的记录将被删除）：",
                initialvalue=365,
                minvalue=1,
                maxvalue=3650
            )

            if days_input is None:
                return

            # 确认操作
            confirm_msg = f"确认删除 {days_input} 天前的所有历史记录吗？\n\n此操作不可撤销！"
            if not messagebox.askyesno("确认清理", confirm_msg):
                return

            # 执行清理
            self.log_message(f"🗑️ 开始清理 {days_input} 天前的历史记录...")
            deleted_count = self.history_manager.cleanup_old_records(days_input)

            success_msg = f"✅ 清理完成！删除了 {deleted_count} 条历史记录"
            self.log_message(success_msg)
            messagebox.showinfo("清理完成", success_msg)

        except Exception as e:
            error_msg = f"清理历史记录失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _update_history_tree(self, tree, records):
        """更新历史记录树"""
        # 清空现有项目
        for item in tree.get_children():
            tree.delete(item)

        # 添加记录
        for record in records:
            status = "✅ 成功" if record['success'] else "❌ 失败"
            tree.insert('', tk.END, values=(
                record['send_time'][:19],  # 只显示到秒
                record['recipient_email'],
                record['subject'][:50] + "..." if len(record['subject']) > 50 else record['subject'],
                status
            ))

    def open_rag_search(self):
        """打开RAG智能搜索"""
        try:
            # 创建搜索窗口
            search_window = tk.Toplevel(self.root)
            search_window.title("🔍 智能邮件搜索")
            search_window.geometry("900x600")
            search_window.transient(self.root)

            # 主框架
            main_frame = ttk.Frame(search_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = ttk.Label(main_frame, text="🔍 智能邮件搜索",
                                   font=('Microsoft YaHei UI', 14, 'bold'))
            title_label.pack(pady=(0, 10))

            # 搜索区域
            search_frame = ttk.LabelFrame(main_frame, text="搜索条件", padding="10")
            search_frame.pack(fill=tk.X, pady=(0, 10))

            # 搜索输入
            ttk.Label(search_frame, text="搜索关键词:").pack(anchor=tk.W)
            search_var = tk.StringVar()
            search_entry = ttk.Entry(search_frame, textvariable=search_var, width=60)
            search_entry.pack(fill=tk.X, pady=(5, 10))

            # 搜索选项
            options_frame = ttk.Frame(search_frame)
            options_frame.pack(fill=tk.X)

            sender_only = tk.BooleanVar(value=True)
            ttk.Checkbutton(options_frame, text="只搜索当前发件人",
                           variable=sender_only).pack(side=tk.LEFT, padx=(0, 20))

            # 搜索按钮
            def perform_search():
                query = search_var.get().strip()
                if not query:
                    messagebox.showwarning("提示", "请输入搜索关键词")
                    return

                sender_email = self.sender_email.get().strip() if sender_only.get() else None
                results = self.rag_search.semantic_search(query, sender_email, 50)
                self._update_search_results(results_text, results)

            ttk.Button(options_frame, text="🔍 智能搜索",
                      command=perform_search).pack(side=tk.LEFT)

            # 结果区域
            results_frame = ttk.LabelFrame(main_frame, text="搜索结果", padding="10")
            results_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            results_text = scrolledtext.ScrolledText(results_frame, height=20, font=('Consolas', 9))
            results_text.pack(fill=tk.BOTH, expand=True)

            # 操作按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(button_frame, text="关闭",
                      command=search_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            error_msg = f"打开智能搜索失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _update_search_results(self, text_widget, results):
        """更新搜索结果显示"""
        text_widget.delete(1.0, tk.END)

        if not results:
            text_widget.insert(tk.END, "没有找到匹配的邮件记录。")
            return

        text_widget.insert(tk.END, f"找到 {len(results)} 条匹配记录：\n\n")

        for i, result in enumerate(results, 1):
            text_widget.insert(tk.END, f"【记录 {i}】相关度: {result['relevance_score']:.3f}\n")
            text_widget.insert(tk.END, f"时间: {result['send_time']}\n")
            text_widget.insert(tk.END, f"收件人: {result['recipient_email']}\n")
            text_widget.insert(tk.END, f"主题: {result['subject']}\n")
            text_widget.insert(tk.END, f"内容预览: {result['body_preview']}\n")
            text_widget.insert(tk.END, f"状态: {'✅ 成功' if result['success'] else '❌ 失败'}\n")
            text_widget.insert(tk.END, "-" * 80 + "\n\n")

    def auto_retrieve_suggestions(self):
        """智能检索建议"""
        try:
            # 获取当前输入
            sender_email = self.sender_email.get().strip()
            subject = self.subject.get().strip()
            body = self.body.get(1.0, tk.END).strip()

            # 检查占位符文本
            placeholder_text = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
            if body == placeholder_text.strip():
                body = ""

            if not all([sender_email, subject, body]):
                messagebox.showwarning("提示", "请先填写发件人邮箱、邮件主题和正文内容")
                return

            self.log_message("🤖 开始智能检索...")

            # 创建检索结果窗口
            self._show_auto_retrieve_window(sender_email, subject, body)

        except Exception as e:
            error_msg = f"智能检索失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _show_auto_retrieve_window(self, sender_email, subject, body):
        """显示智能检索结果窗口"""
        # 创建结果窗口
        retrieve_window = tk.Toplevel(self.root)
        retrieve_window.title("🤖 智能检索建议")
        retrieve_window.geometry("1000x700")
        retrieve_window.transient(self.root)
        retrieve_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(retrieve_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="🤖 智能检索建议",
                               font=('Microsoft YaHei UI', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # 进度条
        progress_frame = ttk.Frame(main_frame)
        progress_frame.pack(fill=tk.X, pady=(0, 10))

        progress_label = ttk.Label(progress_frame, text="正在分析邮件内容...")
        progress_label.pack(side=tk.LEFT)

        progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        progress_bar.pack(side=tk.RIGHT, padx=(10, 0))
        progress_bar.start()

        # 结果区域
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 在后台线程中执行检索
        def perform_retrieval():
            try:
                # 1. 智能收件人分析（防重复发送）
                progress_label.config(text="正在分析收件人...")
                recipient_analysis = self.rag_search.suggest_recipients(subject, body, sender_email, 20)

                # 2. 查找相似邮件
                progress_label.config(text="正在查找相似邮件...")
                similar_emails = self.rag_search.find_similar_emails(subject, body, sender_email, 15)

                # 3. 重复检测（如果有收件人输入）
                current_recipients_text = self.recipient_emails.get(1.0, tk.END).strip()
                duplicate_result = None
                if current_recipients_text:
                    progress_label.config(text="正在检测重复发送...")
                    current_recipients = self._parse_recipient_emails(current_recipients_text)
                    if current_recipients:
                        duplicate_result = self.rag_search.advanced_duplicate_detection(
                            subject, body, current_recipients, sender_email
                        )

                # 4. 内容聚类分析
                progress_label.config(text="正在分析内容聚类...")
                content_clusters = self.rag_search.get_content_clusters(sender_email, 2)

                # 在主线程中更新UI
                self.root.after(0, lambda: self._update_retrieve_results(
                    retrieve_window, notebook, progress_frame,
                    recipient_analysis, similar_emails, duplicate_result, content_clusters,
                    sender_email, subject, body
                ))

            except Exception as e:
                self.root.after(0, lambda err=str(e): self._handle_retrieve_error(retrieve_window, err))

        # 启动后台线程
        import threading
        threading.Thread(target=perform_retrieval, daemon=True).start()

    def _update_retrieve_results(self, window, notebook, progress_frame,
                                recipient_analysis, similar_emails, duplicate_result,
                                content_clusters, sender_email, subject, body):
        """更新检索结果显示"""
        try:
            # 隐藏进度条
            progress_frame.pack_forget()

            # 1. 智能收件人分析标签页
            if recipient_analysis and (recipient_analysis['safe_recipients'] or recipient_analysis['duplicate_recipients']):
                analysis_frame = ttk.Frame(notebook)
                notebook.add(analysis_frame, text="🤖 智能收件人分析")

                # 分析结果说明
                result_text = f"""智能分析结果：
{recipient_analysis['recommendation']}

✅ 安全收件人: {len(recipient_analysis['safe_recipients'])} 个
⚠️ 重复风险收件人: {len(recipient_analysis['duplicate_recipients'])} 个"""

                ttk.Label(analysis_frame, text=result_text,
                         font=('Consolas', 10), justify=tk.LEFT).pack(anchor=tk.W, padx=10, pady=5)

                # 如果有安全收件人，显示推荐
                if recipient_analysis['safe_recipients']:
                    ttk.Label(analysis_frame, text="推荐使用以下安全收件人：",
                             font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor=tk.W, padx=10, pady=(10, 5))

                    # 安全收件人列表
                    safe_text = scrolledtext.ScrolledText(analysis_frame, height=6)
                    safe_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
                    safe_text.insert(tk.END, "\n".join(recipient_analysis['safe_recipients']))

                    # 操作按钮
                    btn_frame = ttk.Frame(analysis_frame)
                    btn_frame.pack(fill=tk.X, padx=10, pady=5)

                    ttk.Button(btn_frame, text="✅ 使用安全收件人",
                              command=lambda: self._apply_safe_recipients_analysis(recipient_analysis['safe_recipients'], window)).pack(side=tk.LEFT, padx=5)

                    ttk.Button(btn_frame, text="➕ 添加安全收件人",
                              command=lambda: self._add_safe_recipients_analysis(recipient_analysis['safe_recipients'])).pack(side=tk.LEFT, padx=5)

                # 如果有重复风险，显示详情
                if recipient_analysis['duplicate_recipients']:
                    ttk.Label(analysis_frame, text="⚠️ 以下收件人可能重复发送：",
                             font=('Microsoft YaHei UI', 10, 'bold'), foreground='red').pack(anchor=tk.W, padx=10, pady=(10, 5))

                    # 重复收件人详情
                    duplicate_text = scrolledtext.ScrolledText(analysis_frame, height=4)
                    duplicate_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

                    for detail in recipient_analysis['duplicate_details']:
                        duplicate_text.insert(tk.END, f"📧 {detail['recipient']} (相似度: {detail['similarity']:.2f})\n")
                        duplicate_text.insert(tk.END, f"   上次发送: {detail['last_sent_time']}\n")
                        duplicate_text.insert(tk.END, f"   之前主题: {detail['previous_subject']}\n\n")

            # 2. 相似邮件标签页
            if similar_emails:
                similar_frame = ttk.Frame(notebook)
                notebook.add(similar_frame, text=f"📧 相似邮件 ({len(similar_emails)})")

                similar_text = scrolledtext.ScrolledText(similar_frame, height=15, font=('Consolas', 9))
                similar_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

                for i, email in enumerate(similar_emails, 1):
                    similar_text.insert(tk.END, f"【相似邮件 {i}】相关度: {email['relevance_score']:.3f}\n")
                    similar_text.insert(tk.END, f"时间: {email['send_time']}\n")
                    similar_text.insert(tk.END, f"收件人: {email['recipient_email']}\n")
                    similar_text.insert(tk.END, f"主题: {email['subject']}\n")
                    similar_text.insert(tk.END, f"内容: {email['body_preview']}\n")
                    similar_text.insert(tk.END, "-" * 60 + "\n\n")

            # 3. 重复检测标签页
            if duplicate_result and (duplicate_result['exact_matches'] or duplicate_result['similar_matches']):
                duplicate_frame = ttk.Frame(notebook)
                notebook.add(duplicate_frame, text="⚠️ 重复检测")

                # 统计信息
                stats_text = f"""检测结果：
安全发送: {len(duplicate_result['safe_recipients'])} 人
完全重复: {len(duplicate_result['exact_matches'])} 人
相似内容: {len(duplicate_result['similar_matches'])} 人

建议: {duplicate_result['recommendation']}"""

                ttk.Label(duplicate_frame, text=stats_text, font=('Consolas', 10)).pack(anchor=tk.W, padx=10, pady=5)

                if duplicate_result['safe_recipients']:
                    ttk.Button(duplicate_frame, text="✅ 只发送给安全收件人",
                              command=lambda: self._apply_safe_recipients(duplicate_result['safe_recipients'], window)).pack(padx=10, pady=5)

            # 4. 内容聚类标签页
            if content_clusters:
                clusters_frame = ttk.Frame(notebook)
                notebook.add(clusters_frame, text=f"📊 内容聚类 ({len(content_clusters)})")

                clusters_text = scrolledtext.ScrolledText(clusters_frame, height=15, font=('Consolas', 9))
                clusters_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

                for i, cluster in enumerate(content_clusters, 1):
                    clusters_text.insert(tk.END, f"【聚类 {i}】\n")
                    clusters_text.insert(tk.END, f"主题: {cluster['subject']}\n")
                    clusters_text.insert(tk.END, f"邮件数量: {cluster['email_count']}\n")
                    clusters_text.insert(tk.END, f"唯一收件人: {cluster['unique_recipients']}\n")
                    clusters_text.insert(tk.END, f"首次发送: {cluster['first_sent']}\n")
                    clusters_text.insert(tk.END, f"最近发送: {cluster['last_sent']}\n")
                    clusters_text.insert(tk.END, "-" * 60 + "\n\n")

            # 操作按钮
            button_frame = ttk.Frame(window.children['!frame'])
            button_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(button_frame, text="🔄 重新检索",
                      command=lambda: self._refresh_retrieve_results(window, sender_email, subject, body)).pack(side=tk.LEFT, padx=5)

            ttk.Button(button_frame, text="关闭",
                      command=window.destroy).pack(side=tk.RIGHT, padx=5)

            self.log_message("✅ 智能检索完成")

        except Exception as e:
            self._handle_retrieve_error(window, str(e))

    def _apply_safe_recipients_analysis(self, safe_recipients, window):
        """应用安全收件人分析结果"""
        try:
            # 清空现有收件人
            self.recipient_emails.delete(1.0, tk.END)
            # 添加安全收件人
            self.recipient_emails.insert(1.0, "\n".join(safe_recipients))

            self.log_message(f"✅ 已更新收件人列表，只包含 {len(safe_recipients)} 个安全收件人")
            messagebox.showinfo("成功", f"已应用 {len(safe_recipients)} 个安全收件人，避免重复发送")
            window.destroy()

        except Exception as e:
            self.log_message(f"❌ 应用安全收件人失败: {str(e)}")
            messagebox.showerror("错误", f"应用安全收件人失败：{str(e)}")

    def _add_safe_recipients_analysis(self, safe_recipients):
        """添加安全收件人到现有列表"""
        try:
            current_recipients = self.recipient_emails.get(1.0, tk.END).strip()
            if current_recipients:
                # 合并收件人，去重
                current_list = [r.strip() for r in current_recipients.split('\n') if r.strip()]
                all_recipients = set(current_list + safe_recipients)
                self.recipient_emails.delete(1.0, tk.END)
                self.recipient_emails.insert(1.0, "\n".join(sorted(all_recipients)))
            else:
                self.recipient_emails.insert(1.0, "\n".join(safe_recipients))

            self.log_message(f"✅ 已添加 {len(safe_recipients)} 个安全收件人")
            messagebox.showinfo("成功", f"已添加 {len(safe_recipients)} 个安全收件人")

        except Exception as e:
            self.log_message(f"❌ 添加安全收件人失败: {str(e)}")
            messagebox.showerror("错误", f"添加安全收件人失败：{str(e)}")

    def _apply_suggested_recipients(self, recipients, window):
        """应用推荐的收件人"""
        if recipients:
            self.recipient_emails.delete(1.0, tk.END)
            self.recipient_emails.insert(1.0, "\n".join(recipients))
            self.log_message(f"✅ 已应用 {len(recipients)} 个推荐收件人")
            window.destroy()
            messagebox.showinfo("应用成功", f"已应用 {len(recipients)} 个推荐收件人")
        else:
            messagebox.showwarning("提示", "没有推荐的收件人")

    def _add_suggested_recipients(self, recipients):
        """添加推荐收件人到现有列表"""
        if not recipients:
            messagebox.showwarning("提示", "没有推荐的收件人")
            return

        current_text = self.recipient_emails.get(1.0, tk.END).strip()
        current_recipients = set()

        if current_text:
            current_recipients = set(self._parse_recipient_emails(current_text))

        # 过滤掉已存在的收件人
        new_recipients = [r for r in recipients if r not in current_recipients]

        if new_recipients:
            if current_text:
                self.recipient_emails.insert(tk.END, "\n" + "\n".join(new_recipients))
            else:
                self.recipient_emails.insert(1.0, "\n".join(new_recipients))

            self.log_message(f"✅ 已添加 {len(new_recipients)} 个新推荐收件人")
            messagebox.showinfo("添加成功", f"已添加 {len(new_recipients)} 个新推荐收件人\n（跳过了 {len(recipients) - len(new_recipients)} 个重复收件人）")
        else:
            messagebox.showinfo("提示", "所有推荐收件人都已存在于当前列表中")

    def _refresh_retrieve_results(self, window, sender_email, subject, body):
        """刷新检索结果"""
        window.destroy()
        self._show_auto_retrieve_window(sender_email, subject, body)

    def _handle_retrieve_error(self, window, error_msg):
        """处理检索错误"""
        self.log_message(f"❌ 智能检索失败: {error_msg}")
        messagebox.showerror("检索失败", f"智能检索失败：{error_msg}")
        window.destroy()

    def debug_similarity_analysis(self):
        """调试相似度分析"""
        try:
            # 获取当前输入
            sender_email = self.sender_email.get().strip()
            subject = self.subject.get().strip()
            body = self.body.get(1.0, tk.END).strip()

            # 检查占位符文本
            placeholder_text = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
            if body == placeholder_text.strip():
                body = ""

            if not all([sender_email, subject, body]):
                messagebox.showwarning("提示", "请先填写发件人邮箱、邮件主题和正文内容")
                return

            self.log_message("🔧 开始调试相似度分析...")

            # 执行调试分析
            debug_result = self.rag_search.debug_similarity_analysis(subject, body, sender_email)

            # 显示调试结果
            self._show_debug_analysis_window(debug_result, sender_email, subject, body)

        except Exception as e:
            error_msg = f"调试分析失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _show_debug_analysis_window(self, debug_result, sender_email, subject, body):
        """显示调试分析结果窗口"""
        # 创建调试窗口
        debug_window = tk.Toplevel(self.root)
        debug_window.title("🔧 相似度分析调试")
        debug_window.geometry("1200x800")
        debug_window.transient(self.root)
        debug_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(debug_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="🔧 相似度分析调试报告",
                               font=('Microsoft YaHei UI', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 1. 概览标签页
        overview_frame = ttk.Frame(notebook)
        notebook.add(overview_frame, text="📊 概览")

        overview_text = scrolledtext.ScrolledText(overview_frame, height=20, font=('Consolas', 10))
        overview_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 生成概览信息
        overview_info = f"""🔧 相似度分析调试报告
{'='*60}

📧 当前邮件信息:
发件人: {sender_email}
主题: {subject}
正文: {debug_result.get('current_content', {}).get('body', '未知')}

📊 分析统计:
历史收件人总数: {debug_result.get('total_historical_recipients', 0)}
相似邮件数量: {debug_result.get('total_similar_emails', 0)}
查询词汇: {', '.join(debug_result.get('query_terms', []))}

🎯 不同阈值下的重复检测结果:
阈值 0.6: {len(debug_result.get('duplicates_threshold_0_6', []))} 个重复, {len(debug_result.get('safe_recipients_0_6', []))} 个安全
阈值 0.7: {len(debug_result.get('duplicates_threshold_0_7', []))} 个重复, {len(debug_result.get('safe_recipients_0_7', []))} 个安全
阈值 0.8: {len(debug_result.get('duplicates_threshold_0_8', []))} 个重复, {len(debug_result.get('safe_recipients_0_8', []))} 个安全

📝 历史收件人列表:
{chr(10).join(debug_result.get('historical_recipients', []))}
"""
        overview_text.insert(tk.END, overview_info)

        # 2. 详细分析标签页
        details_frame = ttk.Frame(notebook)
        notebook.add(details_frame, text="🔍 详细分析")

        details_text = scrolledtext.ScrolledText(details_frame, height=20, font=('Consolas', 9))
        details_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 生成详细分析信息
        details_text.insert(tk.END, "🔍 相似邮件详细分析\n")
        details_text.insert(tk.END, "="*80 + "\n\n")

        for i, detail in enumerate(debug_result.get('analysis_details', []), 1):
            details_text.insert(tk.END, f"【相似邮件 {i}】\n")
            details_text.insert(tk.END, f"收件人: {detail['recipient']}\n")
            details_text.insert(tk.END, f"相似度: {detail['similarity']:.4f}\n")
            details_text.insert(tk.END, f"发送时间: {detail['send_time']}\n")
            details_text.insert(tk.END, f"主题: {detail['subject']}\n")
            details_text.insert(tk.END, f"内容预览: {detail['body_preview']}\n")
            details_text.insert(tk.END, f"阈值0.6判定: {'重复' if detail['is_duplicate_0_6'] else '安全'}\n")
            details_text.insert(tk.END, f"阈值0.7判定: {'重复' if detail['is_duplicate_0_7'] else '安全'}\n")
            details_text.insert(tk.END, f"阈值0.8判定: {'重复' if detail['is_duplicate_0_8'] else '安全'}\n")
            details_text.insert(tk.END, "-" * 60 + "\n\n")

        # 3. 推荐结果标签页
        recommendations_frame = ttk.Frame(notebook)
        notebook.add(recommendations_frame, text="💡 推荐结果")

        rec_text = scrolledtext.ScrolledText(recommendations_frame, height=20, font=('Consolas', 10))
        rec_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # 生成推荐信息
        rec_info = f"""💡 智能推荐结果对比
{'='*60}

🎯 阈值 0.6 (当前智能检索使用):
重复收件人: {', '.join(debug_result.get('duplicates_threshold_0_6', [])) or '无'}
安全收件人: {', '.join(debug_result.get('safe_recipients_0_6', [])) or '无'}

🎯 阈值 0.7 (之前使用):
重复收件人: {', '.join(debug_result.get('duplicates_threshold_0_7', [])) or '无'}
安全收件人: {', '.join(debug_result.get('safe_recipients_0_7', [])) or '无'}

🎯 阈值 0.8 (重复检测使用):
重复收件人: {', '.join(debug_result.get('duplicates_threshold_0_8', [])) or '无'}
安全收件人: {', '.join(debug_result.get('safe_recipients_0_8', [])) or '无'}

💡 建议:
- 如果安全收件人太少，可以考虑提高相似度阈值
- 如果重复收件人太多，可以考虑降低相似度阈值
- 当前系统使用阈值0.6，在准确性和实用性之间取得平衡
"""
        rec_text.insert(tk.END, rec_info)

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="关闭",
                  command=debug_window.destroy).pack(side=tk.RIGHT, padx=5)

        ttk.Button(button_frame, text="导出报告",
                  command=lambda: self._export_debug_report(debug_result, sender_email, subject)).pack(side=tk.RIGHT, padx=5)

        self.log_message("✅ 调试分析完成")

    def _export_debug_report(self, debug_result, sender_email, subject):
        """导出调试报告"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"debug_report_{timestamp}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write("🔧 相似度分析调试报告\n")
                f.write("="*60 + "\n\n")
                f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"发件人: {sender_email}\n")
                f.write(f"主题: {subject}\n\n")

                f.write("📊 统计信息:\n")
                f.write(f"历史收件人总数: {debug_result.get('total_historical_recipients', 0)}\n")
                f.write(f"相似邮件数量: {debug_result.get('total_similar_emails', 0)}\n")
                f.write(f"查询词汇: {', '.join(debug_result.get('query_terms', []))}\n\n")

                f.write("🎯 不同阈值下的结果:\n")
                f.write(f"阈值 0.6: {len(debug_result.get('duplicates_threshold_0_6', []))} 个重复, {len(debug_result.get('safe_recipients_0_6', []))} 个安全\n")
                f.write(f"阈值 0.7: {len(debug_result.get('duplicates_threshold_0_7', []))} 个重复, {len(debug_result.get('safe_recipients_0_7', []))} 个安全\n")
                f.write(f"阈值 0.8: {len(debug_result.get('duplicates_threshold_0_8', []))} 个重复, {len(debug_result.get('safe_recipients_0_8', []))} 个安全\n\n")

                f.write("🔍 详细分析:\n")
                for i, detail in enumerate(debug_result.get('analysis_details', []), 1):
                    f.write(f"\n【相似邮件 {i}】\n")
                    f.write(f"收件人: {detail['recipient']}\n")
                    f.write(f"相似度: {detail['similarity']:.4f}\n")
                    f.write(f"发送时间: {detail['send_time']}\n")
                    f.write(f"主题: {detail['subject']}\n")
                    f.write(f"内容预览: {detail['body_preview']}\n")

            self.log_message(f"✅ 调试报告已导出到: {filename}")
            messagebox.showinfo("导出成功", f"调试报告已导出到:\n{filename}")

        except Exception as e:
            error_msg = f"导出调试报告失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导出失败", error_msg)

    def open_reply_monitor(self):
        """打开自动回复监控窗口"""
        try:
            # 获取当前发件人邮箱
            sender_email = self.sender_email.get().strip()
            if not sender_email:
                messagebox.showwarning("提示", "请先填写发件人邮箱")
                return

            # 获取当前收件人列表
            recipient_emails_text = self.recipient_emails.get(1.0, tk.END).strip()
            recipient_list = self._parse_recipient_emails(recipient_emails_text) if recipient_emails_text else []

            self.log_message("📬 打开自动回复监控...")
            self._show_reply_monitor_window(sender_email, recipient_list)

        except Exception as e:
            error_msg = f"打开自动回复监控失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _show_reply_monitor_window(self, sender_email, recipient_list):
        """显示自动回复监控窗口"""
        # 创建监控窗口
        monitor_window = tk.Toplevel(self.root)
        monitor_window.title("📬 自动回复监控")
        monitor_window.geometry("1200x800")
        monitor_window.transient(self.root)
        monitor_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(monitor_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="📬 自动回复监控系统",
                               font=('Microsoft YaHei UI', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # 配置框架
        config_frame = ttk.LabelFrame(main_frame, text="监控配置", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 10))

        # 第一行：邮箱和密码
        row1_frame = ttk.Frame(config_frame)
        row1_frame.grid(row=0, column=0, columnspan=4, sticky=tk.W+tk.E, pady=(0, 10))

        ttk.Label(row1_frame, text="发件人邮箱:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        email_var = tk.StringVar(value=sender_email)
        ttk.Entry(row1_frame, textvariable=email_var, width=25, state='readonly').grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(row1_frame, text="IMAP密码:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        password_var = tk.StringVar()

        # 尝试从保存的授权码中获取密码
        saved_auth_info = self.auth_codes.get(sender_email, {})
        if isinstance(saved_auth_info, dict):
            saved_password = saved_auth_info.get('auth_code', '')
        else:
            # 兼容旧格式（直接是字符串）
            saved_password = saved_auth_info if isinstance(saved_auth_info, str) else ''

        if saved_password:
            password_var.set(saved_password)

        password_entry = ttk.Entry(row1_frame, textvariable=password_var, width=20, show="*")
        password_entry.grid(row=0, column=3, sticky=tk.W, padx=(0, 10))

        # 记住密码选项
        remember_password = tk.BooleanVar(value=bool(saved_password))
        ttk.Checkbutton(row1_frame, text="记住密码", variable=remember_password).grid(row=0, column=4, sticky=tk.W)

        # 第二行：收件人信息
        row2_frame = ttk.Frame(config_frame)
        row2_frame.grid(row=1, column=0, columnspan=4, sticky=tk.W+tk.E, pady=(0, 10))

        ttk.Label(row2_frame, text="监控收件人:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        recipient_count_label = ttk.Label(row2_frame, text=f"{len(recipient_list)} 个收件人",
                                         font=('Microsoft YaHei UI', 9, 'bold'), foreground='blue')
        recipient_count_label.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        if recipient_list:
            preview_text = ", ".join(recipient_list[:3])
            if len(recipient_list) > 3:
                preview_text += f" ... (还有{len(recipient_list)-3}个)"
            ttk.Label(row2_frame, text=preview_text, foreground='gray').grid(row=0, column=2, sticky=tk.W)

        # 第三行：监控选项
        row3_frame = ttk.Frame(config_frame)
        row3_frame.grid(row=2, column=0, columnspan=4, sticky=tk.W+tk.E, pady=(0, 10))

        ttk.Label(row3_frame, text="检查间隔:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        interval_var = tk.StringVar(value="5")
        interval_combo = ttk.Combobox(row3_frame, textvariable=interval_var, width=8, values=["2", "5", "10", "15", "30"])
        interval_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 5))
        ttk.Label(row3_frame, text="分钟").grid(row=0, column=2, sticky=tk.W, padx=(0, 20))

        ttk.Label(row3_frame, text="监控时长:").grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        duration_var = tk.StringVar(value="2")
        duration_combo = ttk.Combobox(row3_frame, textvariable=duration_var, width=8, values=["1", "2", "4", "6", "12", "24"])
        duration_combo.grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        ttk.Label(row3_frame, text="小时").grid(row=0, column=5, sticky=tk.W, padx=(0, 20))

        # 自动启动选项
        auto_start_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(row3_frame, text="发送后自动启动监控", variable=auto_start_var).grid(row=0, column=6, sticky=tk.W)

        # 保存设置按钮
        ttk.Button(row3_frame, text="💾 保存设置",
                  command=lambda: self._save_monitor_settings(interval_var.get(), duration_var.get())).grid(row=0, column=7, sticky=tk.W, padx=(10, 0))

        # 操作按钮
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0))

        def save_password_if_needed():
            if remember_password.get() and password_var.get():
                # 确保保存的格式正确
                self.auth_codes[sender_email] = {
                    'auth_code': password_var.get(),
                    'add_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                self.save_auth_codes()

        ttk.Button(button_frame, text="🔍 检查当前回复",
                  command=lambda: [save_password_if_needed(),
                                 self._check_current_replies(sender_email, password_var.get(), recipient_list, monitor_window)]).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="📊 查看历史分析",
                  command=lambda: self._show_reply_analysis(sender_email, monitor_window)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="🚀 开始智能监控",
                  command=lambda: [save_password_if_needed(),
                                 self._start_smart_monitoring(sender_email, password_var.get(), recipient_list,
                                                             int(interval_var.get()), int(duration_var.get()), monitor_window)]).pack(side=tk.LEFT, padx=5)

        # 如果没有收件人，显示提示
        if not recipient_list:
            ttk.Label(button_frame, text="⚠️ 请先在主界面填写收件人", foreground='orange').pack(side=tk.LEFT, padx=20)

        # 结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="监控结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 创建笔记本控件
        notebook = ttk.Notebook(result_frame)
        notebook.pack(fill=tk.BOTH, expand=True)

        # 实时监控标签页
        monitor_frame = ttk.Frame(notebook)
        notebook.add(monitor_frame, text="📡 实时监控")

        self.monitor_text = scrolledtext.ScrolledText(monitor_frame, height=15, font=('Consolas', 9))
        self.monitor_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 分析报告标签页
        analysis_frame = ttk.Frame(notebook)
        notebook.add(analysis_frame, text="📊 分析报告")

        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, height=15, font=('Consolas', 9))
        self.analysis_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 有效收件人标签页
        valid_frame = ttk.Frame(notebook)
        notebook.add(valid_frame, text="✅ 有效收件人")

        self.valid_text = scrolledtext.ScrolledText(valid_frame, height=15, font=('Consolas', 9))
        self.valid_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 无效收件人标签页
        invalid_frame = ttk.Frame(notebook)
        notebook.add(invalid_frame, text="❌ 无效收件人")

        self.invalid_text = scrolledtext.ScrolledText(invalid_frame, height=15, font=('Consolas', 9))
        self.invalid_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 关闭按钮
        ttk.Button(main_frame, text="关闭", command=monitor_window.destroy).pack(pady=(10, 0))

        self.log_message("✅ 自动回复监控窗口已打开")

    def _check_current_replies(self, sender_email, password, recipient_list, window):
        """检查当前的自动回复"""
        if not password:
            messagebox.showwarning("提示", "请输入IMAP密码（通常与SMTP授权码相同）")
            return

        try:
            self.monitor_text.delete(1.0, tk.END)
            self.monitor_text.insert(tk.END, "🔍 正在检查自动回复...\n")
            self.monitor_text.insert(tk.END, f"📧 监控收件人: {len(recipient_list)} 个\n")
            window.update()

            # 创建邮件接收器 - 确保使用正确的授权码
            from email_receiver import EmailReceiver
            # 如果没有提供密码，尝试从保存的授权码中获取
            if not password:
                auth_info = self.auth_codes.get(sender_email, {})
                if isinstance(auth_info, dict):
                    password = auth_info.get('auth_code', '')
                else:
                    password = auth_info if isinstance(auth_info, str) else ''
            receiver = EmailReceiver(sender_email, password)

            # 测试连接
            self.monitor_text.insert(tk.END, "📡 测试IMAP连接...\n")
            window.update()

            if not receiver.test_connection():
                self.monitor_text.insert(tk.END, "❌ IMAP连接失败，请检查密码\n")
                return

            self.monitor_text.insert(tk.END, "✅ IMAP连接成功\n")
            window.update()

            # 检查最近24小时的回复
            self.monitor_text.insert(tk.END, "🔍 检查最近24小时的自动回复...\n")
            window.update()

            replies = receiver.check_recent_replies(hours=24)

            # 筛选出目标收件人的回复
            target_replies = []
            if recipient_list:
                recipient_set = set(recipient_list)
                target_replies = [reply for reply in replies if reply['recipient_email'] in recipient_set]
            else:
                target_replies = replies

            if target_replies:
                self.monitor_text.insert(tk.END, f"📬 发现 {len(target_replies)} 个目标收件人的自动回复:\n")
                for reply in target_replies:
                    status_icon = "✅" if reply['reply_type'] == 'auto_reply' else "❌"
                    self.monitor_text.insert(tk.END, f"  {status_icon} {reply['recipient_email']}: {reply['reply_type']}\n")
                    self.monitor_text.insert(tk.END, f"    时间: {reply['reply_time']}\n")
                    self.monitor_text.insert(tk.END, f"    主题: {reply['subject'][:50]}...\n\n")
            else:
                self.monitor_text.insert(tk.END, "📭 目标收件人暂无自动回复\n")

            # 显示所有回复（如果有其他回复）
            other_replies = [reply for reply in replies if reply not in target_replies]
            if other_replies:
                self.monitor_text.insert(tk.END, f"📮 其他收件人的回复 ({len(other_replies)} 个):\n")
                for reply in other_replies[:5]:  # 只显示前5个
                    self.monitor_text.insert(tk.END, f"  - {reply['recipient_email']}: {reply['reply_type']}\n")
                if len(other_replies) > 5:
                    self.monitor_text.insert(tk.END, f"  ... 还有 {len(other_replies)-5} 个\n")
                self.monitor_text.insert(tk.END, "\n")

            # 分析目标收件人状态
            if recipient_list:
                self._analyze_target_recipients(recipient_list, target_replies, window)

            # 更新分析报告
            self._update_reply_analysis(sender_email, receiver)

            self.monitor_text.insert(tk.END, "✅ 检查完成\n")

        except Exception as e:
            self.monitor_text.insert(tk.END, f"❌ 检查失败: {str(e)}\n")
            self.log_message(f"❌ 检查自动回复失败: {str(e)}")

    def _analyze_target_recipients(self, recipient_list, replies, window):
        """分析目标收件人状态"""
        try:
            replied_recipients = set(reply['recipient_email'] for reply in replies)
            no_reply_recipients = set(recipient_list) - replied_recipients

            # 统计回复类型
            auto_reply_count = len([r for r in replies if r['reply_type'] == 'auto_reply'])
            bounce_count = len([r for r in replies if r['reply_type'] == 'bounce'])

            self.monitor_text.insert(tk.END, "📊 目标收件人状态分析:\n")
            self.monitor_text.insert(tk.END, f"  ✅ 有自动回复: {auto_reply_count} 个 (邮件成功送达)\n")
            self.monitor_text.insert(tk.END, f"  ❌ 退信/无效: {bounce_count} 个 (邮箱有问题)\n")
            self.monitor_text.insert(tk.END, f"  ❓ 无回复: {len(no_reply_recipients)} 个 (可能进入垃圾箱或未设置自动回复)\n\n")

            if no_reply_recipients:
                self.monitor_text.insert(tk.END, "⚠️ 无回复的收件人:\n")
                for recipient in list(no_reply_recipients)[:10]:  # 只显示前10个
                    self.monitor_text.insert(tk.END, f"  - {recipient}\n")
                if len(no_reply_recipients) > 10:
                    self.monitor_text.insert(tk.END, f"  ... 还有 {len(no_reply_recipients)-10} 个\n")
                self.monitor_text.insert(tk.END, "\n")

        except Exception as e:
            self.monitor_text.insert(tk.END, f"❌ 分析失败: {str(e)}\n")

    def _show_reply_analysis(self, sender_email, window):
        """显示回复分析"""
        try:
            from email_receiver import EmailReceiver
            receiver = EmailReceiver(sender_email, "dummy")  # 只用于数据库查询，不需要密码

            analysis = receiver.get_recipient_analysis(sender_email)

            self.analysis_text.delete(1.0, tk.END)

            if not analysis:
                self.analysis_text.insert(tk.END, "📭 暂无分析数据\n")
                return

            # 显示统计信息
            self.analysis_text.insert(tk.END, "📊 收件人状态统计\n")
            self.analysis_text.insert(tk.END, "=" * 40 + "\n")
            self.analysis_text.insert(tk.END, f"总收件人数: {analysis.get('total_recipients', 0)}\n")
            self.analysis_text.insert(tk.END, f"活跃收件人: {analysis.get('active_recipients', 0)}\n")
            self.analysis_text.insert(tk.END, f"无效收件人: {analysis.get('invalid_recipients', 0)}\n")
            self.analysis_text.insert(tk.END, f"未知状态: {analysis.get('unknown_recipients', 0)}\n\n")

            # 显示最近回复
            self.analysis_text.insert(tk.END, "📬 最近的自动回复\n")
            self.analysis_text.insert(tk.END, "=" * 40 + "\n")
            for reply in analysis.get('recent_replies', []):
                self.analysis_text.insert(tk.END, f"时间: {reply[2]}\n")
                self.analysis_text.insert(tk.END, f"收件人: {reply[0]}\n")
                self.analysis_text.insert(tk.END, f"类型: {reply[1]}\n")
                self.analysis_text.insert(tk.END, f"主题: {reply[3]}\n")
                self.analysis_text.insert(tk.END, "-" * 30 + "\n")

            # 更新有效和无效收件人列表
            valid_recipients = receiver.get_valid_recipients(sender_email)
            invalid_recipients = receiver.get_invalid_recipients(sender_email)

            self.valid_text.delete(1.0, tk.END)
            self.valid_text.insert(tk.END, f"✅ 有效收件人 ({len(valid_recipients)} 个)\n")
            self.valid_text.insert(tk.END, "=" * 40 + "\n")
            for recipient in valid_recipients:
                self.valid_text.insert(tk.END, f"{recipient}\n")

            self.invalid_text.delete(1.0, tk.END)
            self.invalid_text.insert(tk.END, f"❌ 无效收件人 ({len(invalid_recipients)} 个)\n")
            self.invalid_text.insert(tk.END, "=" * 40 + "\n")
            for recipient in invalid_recipients:
                self.invalid_text.insert(tk.END, f"{recipient}\n")

        except Exception as e:
            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, f"❌ 获取分析数据失败: {str(e)}\n")

    def _update_reply_analysis(self, sender_email, receiver):
        """更新回复分析显示"""
        try:
            analysis = receiver.get_recipient_analysis(sender_email)

            self.analysis_text.delete(1.0, tk.END)
            self.analysis_text.insert(tk.END, "📊 实时分析报告\n")
            self.analysis_text.insert(tk.END, "=" * 40 + "\n")
            self.analysis_text.insert(tk.END, f"总收件人数: {analysis.get('total_recipients', 0)}\n")
            self.analysis_text.insert(tk.END, f"活跃收件人: {analysis.get('active_recipients', 0)}\n")
            self.analysis_text.insert(tk.END, f"无效收件人: {analysis.get('invalid_recipients', 0)}\n")
            self.analysis_text.insert(tk.END, f"未知状态: {analysis.get('unknown_recipients', 0)}\n\n")

            # 更新有效和无效收件人列表
            valid_recipients = receiver.get_valid_recipients(sender_email)
            invalid_recipients = receiver.get_invalid_recipients(sender_email)

            self.valid_text.delete(1.0, tk.END)
            self.valid_text.insert(tk.END, f"✅ 有效收件人 ({len(valid_recipients)} 个)\n")
            self.valid_text.insert(tk.END, "=" * 40 + "\n")
            for recipient in valid_recipients:
                self.valid_text.insert(tk.END, f"{recipient}\n")

            self.invalid_text.delete(1.0, tk.END)
            self.invalid_text.insert(tk.END, f"❌ 无效收件人 ({len(invalid_recipients)} 个)\n")
            self.invalid_text.insert(tk.END, "=" * 40 + "\n")
            for recipient in invalid_recipients:
                self.invalid_text.insert(tk.END, f"{recipient}\n")

        except Exception as e:
            self.analysis_text.insert(tk.END, f"❌ 更新分析失败: {str(e)}\n")

    def _start_smart_monitoring(self, sender_email, password, recipient_list, interval_minutes, duration_hours, window):
        """开始智能监控（针对特定收件人）"""
        if not password:
            messagebox.showwarning("提示", "请输入IMAP密码")
            return

        if not recipient_list:
            messagebox.showwarning("提示", "没有要监控的收件人，请先在主界面填写收件人")
            return

        try:
            self.monitor_text.delete(1.0, tk.END)
            self.monitor_text.insert(tk.END, f"🚀 开始智能监控自动回复...\n")
            self.monitor_text.insert(tk.END, f"📧 监控收件人: {len(recipient_list)} 个\n")
            self.monitor_text.insert(tk.END, f"⏰ 检查间隔: {interval_minutes} 分钟\n")
            self.monitor_text.insert(tk.END, f"🕐 监控时长: {duration_hours} 小时\n\n")
            window.update()

            # 在后台线程中执行监控
            def smart_monitor_thread():
                try:
                    from email_receiver import EmailReceiver
                    receiver = EmailReceiver(sender_email, password)

                    max_checks = (duration_hours * 60) // interval_minutes
                    check_interval = interval_minutes * 60  # 转换为秒

                    recipient_set = set(recipient_list)
                    found_replies = set()  # 已发现回复的收件人

                    for i in range(max_checks):
                        try:
                            window.after(0, lambda i=i, total=max_checks:
                                        self.monitor_text.insert(tk.END, f"🔍 第 {i+1}/{total} 次检查 ({datetime.datetime.now().strftime('%H:%M:%S')})...\n"))
                            window.after(0, lambda: window.update())

                            # 检查最近的回复
                            replies = receiver.check_recent_replies(hours=2)

                            # 筛选目标收件人的新回复
                            new_target_replies = []
                            for reply in replies:
                                if (reply['recipient_email'] in recipient_set and
                                    reply['recipient_email'] not in found_replies):
                                    new_target_replies.append(reply)
                                    found_replies.add(reply['recipient_email'])

                            if new_target_replies:
                                window.after(0, lambda count=len(new_target_replies):
                                            self.monitor_text.insert(tk.END, f"📬 发现 {count} 个目标收件人的新回复!\n"))
                                for reply in new_target_replies:
                                    status_icon = "✅" if reply['reply_type'] == 'auto_reply' else "❌"
                                    window.after(0, lambda r=reply, icon=status_icon:
                                                self.monitor_text.insert(tk.END, f"  {icon} {r['recipient_email']}: {r['reply_type']}\n"))
                            else:
                                window.after(0, lambda: self.monitor_text.insert(tk.END, "📭 暂无新的目标回复\n"))

                            # 显示进度
                            remaining_recipients = len(recipient_set - found_replies)
                            window.after(0, lambda remaining=remaining_recipients, found=len(found_replies), total=len(recipient_list):
                                        self.monitor_text.insert(tk.END, f"📊 进度: {found}/{total} 个收件人已回复，{remaining} 个未回复\n"))

                            # 更新分析
                            window.after(0, lambda: self._update_reply_analysis(sender_email, receiver))

                            # 如果所有收件人都已回复，提前结束
                            if remaining_recipients == 0:
                                window.after(0, lambda: self.monitor_text.insert(tk.END, "🎉 所有收件人都已回复，监控提前完成!\n"))
                                break

                            if i < max_checks - 1:
                                window.after(0, lambda m=interval_minutes:
                                            self.monitor_text.insert(tk.END, f"⏰ 等待 {m} 分钟后继续监控...\n\n"))
                                time.sleep(check_interval)

                        except Exception as e:
                            window.after(0, lambda err=str(e): self.monitor_text.insert(tk.END, f"❌ 检查出错: {err}\n"))
                            time.sleep(60)

                    # 最终总结
                    final_remaining = len(recipient_set - found_replies)
                    window.after(0, lambda found=len(found_replies), total=len(recipient_list), remaining=final_remaining:
                                self.monitor_text.insert(tk.END, f"\n✅ 监控完成! 总结:\n"))
                    window.after(0, lambda found=len(found_replies), total=len(recipient_list):
                                self.monitor_text.insert(tk.END, f"  📬 收到回复: {found}/{total} 个收件人\n"))
                    window.after(0, lambda remaining=final_remaining:
                                self.monitor_text.insert(tk.END, f"  ❓ 无回复: {remaining} 个收件人 (可能进入垃圾箱)\n"))

                except Exception as e:
                    window.after(0, lambda err=str(e): self.monitor_text.insert(tk.END, f"❌ 监控失败: {err}\n"))

            import threading
            threading.Thread(target=smart_monitor_thread, daemon=True).start()

        except Exception as e:
            self.monitor_text.insert(tk.END, f"❌ 启动监控失败: {str(e)}\n")

    def auto_start_reply_monitoring(self, sender_email, recipient_list):
        """发送邮件后自动启动回复监控"""
        try:
            # 检查是否有保存的密码
            auth_info = self.auth_codes.get(sender_email, {})
            if isinstance(auth_info, dict):
                saved_password = auth_info.get('auth_code', '')
            else:
                saved_password = auth_info if isinstance(auth_info, str) else ''

            if not saved_password:
                self.log_message("⚠️ 未找到保存的IMAP密码，无法自动启动回复监控")
                return

            if not recipient_list:
                self.log_message("⚠️ 没有收件人，跳过自动回复监控")
                return

            self.log_message(f"🚀 自动启动回复监控，监控 {len(recipient_list)} 个收件人...")

            # 创建后台监控
            def auto_monitor_thread():
                try:
                    import time
                    from email_receiver import EmailReceiver

                    # 等待5分钟后开始监控（给邮件发送一些时间）
                    time.sleep(300)

                    receiver = EmailReceiver(sender_email, saved_password)
                    recipient_set = set(recipient_list)
                    found_replies = set()

                    # 监控2小时，每10分钟检查一次
                    for i in range(12):  # 2小时 = 12次 * 10分钟
                        try:
                            replies = receiver.check_recent_replies(hours=1)

                            # 筛选目标收件人的新回复
                            new_target_replies = []
                            for reply in replies:
                                if (reply['recipient_email'] in recipient_set and
                                    reply['recipient_email'] not in found_replies):
                                    new_target_replies.append(reply)
                                    found_replies.add(reply['recipient_email'])

                            if new_target_replies:
                                self.root.after(0, lambda count=len(new_target_replies):
                                               self.log_message(f"📬 自动监控发现 {count} 个新回复"))
                                for reply in new_target_replies:
                                    status = "送达" if reply['reply_type'] == 'auto_reply' else "退信"
                                    self.root.after(0, lambda r=reply['recipient_email'], s=status:
                                                   self.log_message(f"  - {r}: {s}"))

                                    # 🆘 自动更新QQ应急状态
                                    if reply['reply_type'] == 'auto_reply':
                                        self.root.after(0, lambda email=reply['recipient_email'], content=reply.get('content', ''):
                                                       self._update_qq_sending_result_with_reply(sender_email, email, True, content))

                            # 如果所有收件人都已回复，提前结束
                            remaining = len(recipient_set - found_replies)
                            if remaining == 0:
                                self.root.after(0, lambda: self.log_message("🎉 所有收件人都已回复，自动监控完成"))
                                break

                            # 等待10分钟
                            if i < 11:
                                time.sleep(600)  # 10分钟

                        except Exception as e:
                            self.root.after(0, lambda err=str(e): self.log_message(f"❌ 自动监控出错: {err}"))
                            time.sleep(600)

                    # 最终报告
                    final_found = len(found_replies)
                    final_remaining = len(recipient_set - found_replies)
                    self.root.after(0, lambda found=final_found, total=len(recipient_list), remaining=final_remaining:
                                   self.log_message(f"📊 自动监控完成: {found}/{total} 个收件人已回复，{remaining} 个无回复"))

                except Exception as e:
                    self.root.after(0, lambda err=str(e): self.log_message(f"❌ 自动监控失败: {err}"))

            import threading
            threading.Thread(target=auto_monitor_thread, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ 启动自动监控失败: {str(e)}")

    def _save_monitor_settings(self, interval, duration):
        """保存监控设置"""
        try:
            settings = {
                'check_interval': interval,
                'monitor_duration': duration,
                'save_time': datetime.datetime.now().isoformat()
            }

            with open('monitor_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            self.log_message(f"✅ 监控设置已保存: 间隔{interval}分钟, 时长{duration}小时")
            messagebox.showinfo("保存成功", f"监控设置已保存\n检查间隔: {interval}分钟\n监控时长: {duration}小时")

        except Exception as e:
            error_msg = f"保存监控设置失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("保存失败", error_msg)

    def _load_monitor_settings(self):
        """加载监控设置"""
        try:
            if os.path.exists('monitor_settings.json'):
                with open('monitor_settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                return settings
        except Exception as e:
            self.log_message(f"⚠️ 加载监控设置失败: {str(e)}")

        # 返回默认设置
        return {
            'check_interval': '10',
            'monitor_duration': '2'
        }

    def open_quality_manager(self):
        """打开收件人质量管理器"""
        try:
            self.log_message("📊 打开收件人质量管理器...")
            self._show_quality_manager_window()

        except Exception as e:
            error_msg = f"打开质量管理器失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _show_quality_manager_window(self):
        """显示质量管理器窗口"""
        # 创建质量管理器窗口
        quality_window = tk.Toplevel(self.root)
        quality_window.title("📊 收件人质量数据库管理")
        quality_window.geometry("1400x900")
        quality_window.transient(self.root)
        quality_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(quality_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="📊 收件人质量数据库管理系统",
                               font=('Microsoft YaHei UI', 16, 'bold'))
        title_label.pack(pady=(0, 15))

        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 1. 质量分析标签页
        analysis_frame = ttk.Frame(notebook)
        notebook.add(analysis_frame, text="📈 质量分析")
        self._create_analysis_tab(analysis_frame)

        # 2. 智能批次标签页
        batch_frame = ttk.Frame(notebook)
        notebook.add(batch_frame, text="📦 智能批次")
        self._create_batch_tab(batch_frame)

        # 3. 收件人管理标签页
        recipients_frame = ttk.Frame(notebook)
        notebook.add(recipients_frame, text="👥 收件人管理")
        self._create_recipients_tab(recipients_frame)

        # 4. 数据导入标签页
        import_frame = ttk.Frame(notebook)
        notebook.add(import_frame, text="📥 数据导入")
        self._create_import_tab(import_frame)

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="🔄 刷新数据",
                  command=lambda: self._refresh_quality_data(notebook)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="📊 导出报告",
                  command=self._export_quality_report).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="🧹 清理无效",
                  command=self._cleanup_invalid_recipients).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="关闭",
                  command=quality_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 初始化数据
        self._refresh_quality_data(notebook)

        self.log_message("✅ 质量管理器窗口已打开")

    def _create_analysis_tab(self, parent):
        """创建质量分析标签页"""
        # 概览框架
        overview_frame = ttk.LabelFrame(parent, text="📊 质量概览", padding="10")
        overview_frame.pack(fill=tk.X, pady=(0, 10))

        self.overview_text = tk.Text(overview_frame, height=8, font=('Consolas', 10))
        self.overview_text.pack(fill=tk.X)

        # 分布图框架
        distribution_frame = ttk.LabelFrame(parent, text="📈 质量分布", padding="10")
        distribution_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.distribution_text = scrolledtext.ScrolledText(distribution_frame, height=15, font=('Consolas', 9))
        self.distribution_text.pack(fill=tk.BOTH, expand=True)

        # 建议框架
        recommendations_frame = ttk.LabelFrame(parent, text="💡 优化建议", padding="10")
        recommendations_frame.pack(fill=tk.X)

        self.recommendations_text = tk.Text(recommendations_frame, height=6, font=('Microsoft YaHei UI', 10))
        self.recommendations_text.pack(fill=tk.X)

    def _create_batch_tab(self, parent):
        """创建智能批次标签页"""
        # 批次创建框架
        create_frame = ttk.LabelFrame(parent, text="🆕 创建智能批次", padding="10")
        create_frame.pack(fill=tk.X, pady=(0, 10))

        # 第一行：批次名称和策略
        row1 = ttk.Frame(create_frame)
        row1.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(row1, text="批次名称:").pack(side=tk.LEFT, padx=(0, 5))
        self.batch_name_var = tk.StringVar(value=f"智能批次_{datetime.datetime.now().strftime('%m%d_%H%M')}")
        ttk.Entry(row1, textvariable=self.batch_name_var, width=20).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(row1, text="分批策略:").pack(side=tk.LEFT, padx=(0, 5))
        self.batch_strategy_var = tk.StringVar(value="quality_balanced")
        strategy_combo = ttk.Combobox(row1, textvariable=self.batch_strategy_var, width=15, state="readonly")
        strategy_combo['values'] = [
            "quality_balanced",    # 质量平衡
            "quality_descending",  # 质量递减
            "domain_distributed"   # 域名分布
        ]
        strategy_combo.pack(side=tk.LEFT)

        # 第二行：参数设置
        row2 = ttk.Frame(create_frame)
        row2.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(row2, text="质量阈值:").pack(side=tk.LEFT, padx=(0, 5))
        self.quality_threshold_var = tk.StringVar(value="60")
        ttk.Entry(row2, textvariable=self.quality_threshold_var, width=8).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(row2, text="批次大小:").pack(side=tk.LEFT, padx=(0, 5))
        self.batch_size_var = tk.StringVar(value="100")
        ttk.Entry(row2, textvariable=self.batch_size_var, width=8).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(row2, text="总收件人数:").pack(side=tk.LEFT, padx=(0, 5))
        self.total_recipients_var = tk.StringVar(value="")
        ttk.Entry(row2, textvariable=self.total_recipients_var, width=8).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Button(row2, text="🚀 创建批次",
                  command=self._create_smart_batches).pack(side=tk.LEFT, padx=20)

        # 批次列表框架
        list_frame = ttk.LabelFrame(parent, text="📦 现有批次", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        columns = ('批次ID', '批次名称', '策略', '收件人数', '平均质量', '创建时间')
        self.batch_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)

        for col in columns:
            self.batch_tree.heading(col, text=col)
            self.batch_tree.column(col, width=120)

        # 滚动条
        batch_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.batch_tree.yview)
        self.batch_tree.configure(yscrollcommand=batch_scrollbar.set)

        self.batch_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        batch_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 批次操作按钮
        batch_btn_frame = ttk.Frame(list_frame)
        batch_btn_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(batch_btn_frame, text="📋 查看详情",
                  command=self._view_batch_details).pack(side=tk.LEFT, padx=5)

        ttk.Button(batch_btn_frame, text="📤 导入到主系统",
                  command=self._import_batch_to_main).pack(side=tk.LEFT, padx=5)

        ttk.Button(batch_btn_frame, text="🗑️ 删除批次",
                  command=self._delete_batch).pack(side=tk.LEFT, padx=5)

    def _create_recipients_tab(self, parent):
        """创建收件人管理标签页"""
        # 筛选框架
        filter_frame = ttk.LabelFrame(parent, text="🔍 筛选条件", padding="10")
        filter_frame.pack(fill=tk.X, pady=(0, 10))

        # 筛选条件
        filter_row = ttk.Frame(filter_frame)
        filter_row.pack(fill=tk.X)

        ttk.Label(filter_row, text="质量状态:").pack(side=tk.LEFT, padx=(0, 5))
        self.status_filter_var = tk.StringVar(value="all")
        status_combo = ttk.Combobox(filter_row, textvariable=self.status_filter_var, width=12, state="readonly")
        status_combo['values'] = ["all", "excellent", "good", "fair", "poor", "invalid"]
        status_combo.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(filter_row, text="最低质量分:").pack(side=tk.LEFT, padx=(0, 5))
        self.min_quality_var = tk.StringVar(value="0")
        ttk.Entry(filter_row, textvariable=self.min_quality_var, width=8).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Button(filter_row, text="🔍 筛选",
                  command=self._filter_recipients).pack(side=tk.LEFT, padx=10)

        # 收件人列表框架
        recipients_list_frame = ttk.LabelFrame(parent, text="👥 收件人列表", padding="10")
        recipients_list_frame.pack(fill=tk.BOTH, expand=True)

        # 创建表格
        recipient_columns = ('邮箱', '质量分', '状态', '发送数', '回复数', '回复率', '最后回复')
        self.recipients_tree = ttk.Treeview(recipients_list_frame, columns=recipient_columns, show='headings', height=15)

        for col in recipient_columns:
            self.recipients_tree.heading(col, text=col)
            if col == '邮箱':
                self.recipients_tree.column(col, width=200)
            else:
                self.recipients_tree.column(col, width=100)

        # 滚动条
        recipients_scrollbar = ttk.Scrollbar(recipients_list_frame, orient=tk.VERTICAL, command=self.recipients_tree.yview)
        self.recipients_tree.configure(yscrollcommand=recipients_scrollbar.set)

        self.recipients_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        recipients_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 操作按钮
        recipients_btn_frame = ttk.Frame(recipients_list_frame)
        recipients_btn_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(recipients_btn_frame, text="📤 导入选中",
                  command=self._import_selected_recipients).pack(side=tk.LEFT, padx=5)

        ttk.Button(recipients_btn_frame, text="📊 查看详情",
                  command=self._view_recipient_details).pack(side=tk.LEFT, padx=5)

        ttk.Button(recipients_btn_frame, text="🏷️ 添加标签",
                  command=self._add_recipient_tags).pack(side=tk.LEFT, padx=5)

    def _create_import_tab(self, parent):
        """创建数据导入标签页"""
        # 导入选项框架
        import_options_frame = ttk.LabelFrame(parent, text="📥 导入选项", padding="10")
        import_options_frame.pack(fill=tk.X, pady=(0, 10))

        # 导入历史数据
        history_frame = ttk.Frame(import_options_frame)
        history_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(history_frame, text="从历史记录导入:").pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(history_frame, text="天数:").pack(side=tk.LEFT, padx=(0, 5))
        self.import_days_var = tk.StringVar(value="30")
        ttk.Entry(history_frame, textvariable=self.import_days_var, width=8).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(history_frame, text="发件人:").pack(side=tk.LEFT, padx=(0, 5))
        self.import_sender_var = tk.StringVar()
        ttk.Entry(history_frame, textvariable=self.import_sender_var, width=25).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(history_frame, text="📥 导入历史",
                  command=self._import_from_history).pack(side=tk.LEFT, padx=10)

        # 导入文件
        file_frame = ttk.Frame(import_options_frame)
        file_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(file_frame, text="从文件导入:").pack(side=tk.LEFT, padx=(0, 10))

        self.import_file_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.import_file_var, width=40).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(file_frame, text="📁 选择文件",
                  command=self._select_import_file).pack(side=tk.LEFT, padx=5)

        ttk.Button(file_frame, text="📥 导入文件",
                  command=self._import_from_file).pack(side=tk.LEFT, padx=5)

        # 导入进度和结果
        progress_frame = ttk.LabelFrame(parent, text="📊 导入进度", padding="10")
        progress_frame.pack(fill=tk.BOTH, expand=True)

        self.import_progress_text = scrolledtext.ScrolledText(progress_frame, height=20, font=('Consolas', 9))
        self.import_progress_text.pack(fill=tk.BOTH, expand=True)

    def _refresh_quality_data(self, notebook):
        """刷新质量数据"""
        try:
            from recipient_quality_manager import RecipientQualityManager

            self.quality_manager = RecipientQualityManager()

            # 刷新分析数据
            self._refresh_analysis_data()

            # 刷新批次数据
            self._refresh_batch_data()

            # 刷新收件人数据
            self._refresh_recipients_data()

            self.log_message("✅ 质量数据刷新完成")

        except Exception as e:
            self.log_message(f"❌ 刷新质量数据失败: {str(e)}")

    def _refresh_analysis_data(self):
        """刷新分析数据"""
        try:
            analytics = self.quality_manager.get_quality_analytics()
            recommendations = self.quality_manager.get_recommendations()

            # 更新概览
            overview = analytics.get('overview', {})
            overview_text = f"""📊 质量数据库概览
{'='*40}
总收件人数: {overview.get('total_recipients', 0):,}
平均质量评分: {overview.get('avg_quality_score', 0):.2f}/100
平均回复率: {overview.get('avg_response_rate', 0):.2f}%
平均退信率: {overview.get('avg_bounce_rate', 0):.2f}%
总发送邮件: {overview.get('total_emails_sent', 0):,}
总收到回复: {overview.get('total_replies_received', 0):,}
"""

            self.overview_text.delete(1.0, tk.END)
            self.overview_text.insert(1.0, overview_text)

            # 更新分布
            distribution = analytics.get('quality_distribution', {})
            distribution_text = f"""📈 质量分布详情
{'='*50}

🌟 优秀 (80-100分): {distribution.get('excellent', 0):,} 个
✅ 良好 (60-79分):  {distribution.get('good', 0):,} 个
⚠️ 一般 (40-59分):  {distribution.get('fair', 0):,} 个
❌ 较差 (20-39分):  {distribution.get('poor', 0):,} 个
🚫 无效 (0-19分):   {distribution.get('invalid', 0):,} 个

🌐 主要域名分析:
{'-'*30}
"""

            for domain_info in analytics.get('top_domains', []):
                distribution_text += f"{domain_info['domain']:<20} {domain_info['count']:>6} 个  质量 {domain_info['avg_quality']:>6.2f}\n"

            distribution_text += f"\n📈 最近趋势 (30天):\n{'-'*30}\n"
            for trend in analytics.get('recent_trends', [])[:10]:
                distribution_text += f"{trend['date']} 发送:{trend['sent']:>4} 送达:{trend['delivered']:>4} 回复:{trend['replied']:>3} 回复率:{trend['response_rate']:>5.1f}%\n"

            self.distribution_text.delete(1.0, tk.END)
            self.distribution_text.insert(1.0, distribution_text)

            # 更新建议
            recommendations_text = "💡 智能优化建议\n" + "="*30 + "\n\n"

            if recommendations:
                for i, rec in enumerate(recommendations, 1):
                    priority_icon = "🔴" if rec['priority'] == 'high' else "🟡" if rec['priority'] == 'medium' else "🟢"
                    recommendations_text += f"{priority_icon} {i}. {rec['title']}\n"
                    recommendations_text += f"   {rec['description']}\n"
                    recommendations_text += f"   预期效果: {rec['impact']}\n\n"
            else:
                recommendations_text += "🎉 您的收件人质量数据库状态良好，暂无优化建议！"

            self.recommendations_text.delete(1.0, tk.END)
            self.recommendations_text.insert(1.0, recommendations_text)

        except Exception as e:
            self.log_message(f"❌ 刷新分析数据失败: {str(e)}")

    def _refresh_batch_data(self):
        """刷新批次数据"""
        try:
            # 清空现有数据
            for item in self.batch_tree.get_children():
                self.batch_tree.delete(item)

            # 获取批次数据
            batches = self.quality_manager.get_all_batches()

            for batch in batches:
                self.batch_tree.insert('', 'end', values=(
                    batch['batch_id'],
                    batch['batch_name'],
                    batch['batch_type'],
                    batch['actual_count'],
                    f"{batch['quality_threshold']:.1f}",
                    batch['created_at'][:16]  # 只显示日期和时间
                ))

        except Exception as e:
            self.log_message(f"❌ 刷新批次数据失败: {str(e)}")

    def _refresh_recipients_data(self):
        """刷新收件人数据"""
        try:
            # 清空现有数据
            for item in self.recipients_tree.get_children():
                self.recipients_tree.delete(item)

            # 获取收件人数据
            recipients = self.quality_manager.get_quality_recipients(limit=1000)

            for recipient in recipients:
                self.recipients_tree.insert('', 'end', values=(
                    recipient.email,
                    f"{recipient.quality_score:.1f}",
                    recipient.status,
                    recipient.total_sent,
                    recipient.total_replies,
                    f"{recipient.response_rate*100:.1f}%",
                    recipient.last_reply_time[:16] if recipient.last_reply_time else "无"
                ))

        except Exception as e:
            self.log_message(f"❌ 刷新收件人数据失败: {str(e)}")

    def _create_smart_batches(self):
        """创建智能批次"""
        try:
            batch_name = self.batch_name_var.get().strip()
            strategy = self.batch_strategy_var.get()
            quality_threshold = float(self.quality_threshold_var.get())
            batch_size = int(self.batch_size_var.get())
            total_recipients = self.total_recipients_var.get().strip()

            if not batch_name:
                messagebox.showwarning("提示", "请输入批次名称")
                return

            total_count = int(total_recipients) if total_recipients else None

            self.log_message(f"🚀 开始创建智能批次: {batch_name}")

            # 创建批次
            batch_info = self.quality_manager.create_smart_batches(
                batch_name=batch_name,
                total_recipients=total_count,
                quality_threshold=quality_threshold,
                max_batch_size=batch_size,
                strategy=strategy
            )

            if batch_info:
                self.log_message(f"✅ 成功创建 {len(batch_info)} 个批次")

                # 显示创建结果
                result_msg = f"成功创建 {len(batch_info)} 个智能批次：\n\n"
                for batch in batch_info:
                    result_msg += f"• {batch['batch_name']}: {batch['recipient_count']} 个收件人，平均质量 {batch['avg_quality_score']}\n"

                messagebox.showinfo("批次创建成功", result_msg)

                # 刷新批次数据
                self._refresh_batch_data()
            else:
                messagebox.showwarning("创建失败", "没有找到符合条件的收件人")

        except ValueError as e:
            messagebox.showerror("参数错误", "请检查输入的数值参数")
        except Exception as e:
            error_msg = f"创建智能批次失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("创建失败", error_msg)

    def _import_batch_to_main(self):
        """导入批次到主系统"""
        try:
            selected = self.batch_tree.selection()
            if not selected:
                messagebox.showwarning("提示", "请先选择一个批次")
                return

            # 获取选中的批次
            item = self.batch_tree.item(selected[0])
            batch_id = item['values'][0]
            batch_name = item['values'][1]

            # 获取批次收件人
            recipients = self.quality_manager.get_batch_recipients(batch_id)

            if not recipients:
                messagebox.showwarning("提示", "该批次没有收件人")
                return

            # 导入到主系统收件人框
            current_recipients = self.recipient_emails.get(1.0, tk.END).strip()

            if current_recipients:
                # 如果已有收件人，询问是否替换
                result = messagebox.askyesnocancel(
                    "导入确认",
                    f"当前收件人框已有内容，是否：\n\n"
                    f"是 - 替换为批次收件人 ({len(recipients)} 个)\n"
                    f"否 - 追加到现有收件人后面\n"
                    f"取消 - 取消导入"
                )

                if result is None:  # 取消
                    return
                elif result:  # 替换
                    self.recipient_emails.delete(1.0, tk.END)
                    self.recipient_emails.insert(1.0, '\n'.join(recipients))
                else:  # 追加
                    self.recipient_emails.insert(tk.END, '\n' + '\n'.join(recipients))
            else:
                # 直接导入
                self.recipient_emails.delete(1.0, tk.END)
                self.recipient_emails.insert(1.0, '\n'.join(recipients))

            self.log_message(f"✅ 已导入批次 '{batch_name}' 的 {len(recipients)} 个收件人到主系统")
            messagebox.showinfo("导入成功", f"已导入 {len(recipients)} 个收件人到主系统")

        except Exception as e:
            error_msg = f"导入批次失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导入失败", error_msg)

    def _import_from_history(self):
        """从历史记录导入"""
        try:
            days = int(self.import_days_var.get())
            sender = self.import_sender_var.get().strip()

            if not sender:
                sender = self.sender_email.get().strip()
                if not sender:
                    messagebox.showwarning("提示", "请输入发件人邮箱")
                    return

            self.import_progress_text.delete(1.0, tk.END)
            self.import_progress_text.insert(tk.END, f"🚀 开始从历史记录导入数据...\n")
            self.import_progress_text.insert(tk.END, f"📧 发件人: {sender}\n")
            self.import_progress_text.insert(tk.END, f"📅 时间范围: 最近 {days} 天\n\n")

            # 在后台线程中执行导入
            def import_thread():
                try:
                    imported_count = self.quality_manager.import_recipients_from_history(sender, days)

                    self.root.after(0, lambda: self.import_progress_text.insert(tk.END, f"✅ 导入完成！共导入 {imported_count} 条记录\n"))
                    self.root.after(0, lambda: self._refresh_quality_data(None))
                    self.root.after(0, lambda: self.log_message(f"✅ 从历史记录导入了 {imported_count} 条数据"))

                except Exception as e:
                    self.root.after(0, lambda err=str(e): self.import_progress_text.insert(tk.END, f"❌ 导入失败: {err}\n"))

            import threading
            threading.Thread(target=import_thread, daemon=True).start()

        except ValueError:
            messagebox.showerror("参数错误", "请输入有效的天数")
        except Exception as e:
            error_msg = f"导入历史数据失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导入失败", error_msg)

    def _export_quality_report(self):
        """导出质量报告"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"quality_report_{timestamp}.txt"

            self.quality_manager.export_quality_report(filepath)

            self.log_message(f"✅ 质量报告已导出到: {filepath}")
            messagebox.showinfo("导出成功", f"质量报告已导出到:\n{filepath}")

        except Exception as e:
            error_msg = f"导出质量报告失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导出失败", error_msg)

    def _cleanup_invalid_recipients(self):
        """清理无效收件人"""
        try:
            result = messagebox.askyesno(
                "确认清理",
                "确定要清理所有无效收件人吗？\n\n"
                "这将标记退信率高的收件人为已清理状态，\n"
                "不会删除历史记录。"
            )

            if result:
                cleaned_count = self.quality_manager.cleanup_invalid_recipients()

                self.log_message(f"✅ 清理了 {cleaned_count} 个无效收件人")
                messagebox.showinfo("清理完成", f"已清理 {cleaned_count} 个无效收件人")

                # 刷新数据
                self._refresh_quality_data(None)

        except Exception as e:
            error_msg = f"清理无效收件人失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("清理失败", error_msg)

    def _filter_recipients(self):
        """筛选收件人"""
        try:
            status_filter = self.status_filter_var.get()
            min_quality = float(self.min_quality_var.get())

            # 清空现有数据
            for item in self.recipients_tree.get_children():
                self.recipients_tree.delete(item)

            # 构建筛选条件
            status_list = None if status_filter == "all" else [status_filter]

            # 获取筛选后的收件人
            recipients = self.quality_manager.get_quality_recipients(
                min_quality_score=min_quality,
                status_filter=status_list,
                limit=1000
            )

            # 显示结果
            for recipient in recipients:
                self.recipients_tree.insert('', 'end', values=(
                    recipient.email,
                    f"{recipient.quality_score:.1f}",
                    recipient.status,
                    recipient.total_sent,
                    recipient.total_replies,
                    f"{recipient.response_rate*100:.1f}%",
                    recipient.last_reply_time[:16] if recipient.last_reply_time else "无"
                ))

            self.log_message(f"🔍 筛选完成，找到 {len(recipients)} 个符合条件的收件人")

        except ValueError:
            messagebox.showerror("参数错误", "请输入有效的质量分数")
        except Exception as e:
            error_msg = f"筛选收件人失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("筛选失败", error_msg)

    def _import_selected_recipients(self):
        """导入选中的收件人"""
        try:
            selected_items = self.recipients_tree.selection()
            if not selected_items:
                messagebox.showwarning("提示", "请先选择要导入的收件人")
                return

            # 获取选中的收件人邮箱
            selected_emails = []
            for item in selected_items:
                values = self.recipients_tree.item(item)['values']
                selected_emails.append(values[0])  # 邮箱地址

            # 导入到主系统
            current_recipients = self.recipient_emails.get(1.0, tk.END).strip()

            if current_recipients:
                # 追加到现有收件人
                self.recipient_emails.insert(tk.END, '\n' + '\n'.join(selected_emails))
            else:
                # 直接导入
                self.recipient_emails.delete(1.0, tk.END)
                self.recipient_emails.insert(1.0, '\n'.join(selected_emails))

            self.log_message(f"✅ 已导入 {len(selected_emails)} 个选中的收件人到主系统")
            messagebox.showinfo("导入成功", f"已导入 {len(selected_emails)} 个收件人到主系统")

        except Exception as e:
            error_msg = f"导入选中收件人失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导入失败", error_msg)

    def _update_quality_database_after_send(self, sender_email, sent_recipients):
        """发送邮件后更新质量数据库"""
        try:
            from recipient_quality_manager import RecipientQualityManager

            quality_manager = RecipientQualityManager()

            # 获取邮件内容
            subject = self.subject.get()
            body = self.body.get(1.0, tk.END).strip()

            # 生成活动ID
            campaign_id = f"campaign_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # 更新每个收件人的质量数据
            for recipient in sent_recipients:
                quality_manager.update_recipient_quality(
                    email=recipient,
                    sender_email=sender_email,
                    subject=subject,
                    body=body,
                    success=True,  # 发送成功
                    campaign_id=campaign_id
                )

            self.log_message(f"📊 已更新 {len(sent_recipients)} 个收件人的质量数据")

        except Exception as e:
            self.log_message(f"❌ 更新质量数据库失败: {str(e)}")

    def _view_batch_details(self):
        """查看批次详情"""
        try:
            selected = self.batch_tree.selection()
            if not selected:
                messagebox.showwarning("提示", "请先选择一个批次")
                return

            # 获取选中的批次
            item = self.batch_tree.item(selected[0])
            batch_id = item['values'][0]
            batch_name = item['values'][1]

            # 获取批次收件人
            recipients = self.quality_manager.get_batch_recipients(batch_id)

            # 创建详情窗口
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"批次详情 - {batch_name}")
            detail_window.geometry("800x600")
            detail_window.transient(self.root)

            # 详情内容
            detail_frame = ttk.Frame(detail_window, padding="10")
            detail_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(detail_frame, text=f"批次名称: {batch_name}",
                     font=('Microsoft YaHei UI', 12, 'bold')).pack(pady=(0, 10))

            ttk.Label(detail_frame, text=f"收件人数量: {len(recipients)}").pack(pady=(0, 5))

            # 收件人列表
            recipients_text = scrolledtext.ScrolledText(detail_frame, height=25, font=('Consolas', 10))
            recipients_text.pack(fill=tk.BOTH, expand=True, pady=(10, 10))

            recipients_text.insert(tk.END, f"📦 批次收件人列表 ({len(recipients)} 个)\n")
            recipients_text.insert(tk.END, "="*50 + "\n\n")

            for i, recipient in enumerate(recipients, 1):
                recipients_text.insert(tk.END, f"{i:3d}. {recipient}\n")

            # 操作按钮
            btn_frame = ttk.Frame(detail_frame)
            btn_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(btn_frame, text="📤 导入到主系统",
                      command=lambda: self._import_batch_recipients_from_detail(recipients, detail_window)).pack(side=tk.LEFT, padx=5)

            ttk.Button(btn_frame, text="📋 复制列表",
                      command=lambda: self._copy_recipients_to_clipboard(recipients)).pack(side=tk.LEFT, padx=5)

            ttk.Button(btn_frame, text="关闭",
                      command=detail_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            error_msg = f"查看批次详情失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("查看失败", error_msg)

    def _import_batch_recipients_from_detail(self, recipients, detail_window):
        """从详情窗口导入批次收件人"""
        try:
            # 导入到主系统收件人框
            current_recipients = self.recipient_emails.get(1.0, tk.END).strip()

            if current_recipients:
                result = messagebox.askyesnocancel(
                    "导入确认",
                    f"是否替换当前收件人列表？\n\n"
                    f"是 - 替换 ({len(recipients)} 个收件人)\n"
                    f"否 - 追加到现有列表\n"
                    f"取消 - 取消导入"
                )

                if result is None:
                    return
                elif result:
                    self.recipient_emails.delete(1.0, tk.END)
                    self.recipient_emails.insert(1.0, '\n'.join(recipients))
                else:
                    self.recipient_emails.insert(tk.END, '\n' + '\n'.join(recipients))
            else:
                self.recipient_emails.delete(1.0, tk.END)
                self.recipient_emails.insert(1.0, '\n'.join(recipients))

            self.log_message(f"✅ 已导入 {len(recipients)} 个收件人到主系统")
            messagebox.showinfo("导入成功", f"已导入 {len(recipients)} 个收件人到主系统")
            detail_window.destroy()

        except Exception as e:
            error_msg = f"导入收件人失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导入失败", error_msg)

    def _copy_recipients_to_clipboard(self, recipients):
        """复制收件人列表到剪贴板"""
        try:
            recipients_text = '\n'.join(recipients)
            self.root.clipboard_clear()
            self.root.clipboard_append(recipients_text)
            self.log_message(f"✅ 已复制 {len(recipients)} 个收件人到剪贴板")
            messagebox.showinfo("复制成功", f"已复制 {len(recipients)} 个收件人到剪贴板")

        except Exception as e:
            error_msg = f"复制到剪贴板失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("复制失败", error_msg)

    def _delete_batch(self):
        """删除选中的批次"""
        try:
            selected = self.batch_tree.selection()
            if not selected:
                messagebox.showwarning("提示", "请先选择要删除的批次")
                return

            # 获取选中的批次信息
            item = self.batch_tree.item(selected[0])
            batch_id = item['values'][0]
            batch_name = item['values'][1]

            # 确认删除
            result = messagebox.askyesno(
                "确认删除",
                f"确定要删除批次 '{batch_name}' 吗？\n\n"
                f"批次ID: {batch_id}\n"
                f"此操作不可撤销。"
            )

            if result:
                # 删除批次
                success = self.quality_manager.delete_batch(batch_id)

                if success:
                    self.log_message(f"✅ 已删除批次: {batch_name}")
                    messagebox.showinfo("删除成功", f"批次 '{batch_name}' 已删除")

                    # 刷新批次数据
                    self._refresh_batch_data()
                else:
                    messagebox.showerror("删除失败", "删除批次时发生错误")

        except Exception as e:
            error_msg = f"删除批次失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("删除失败", error_msg)

    def _view_batch_details(self):
        """查看批次详情"""
        try:
            selected = self.batch_tree.selection()
            if not selected:
                messagebox.showwarning("提示", "请先选择一个批次")
                return

            # 获取选中的批次
            item = self.batch_tree.item(selected[0])
            batch_id = item['values'][0]
            batch_name = item['values'][1]

            # 获取批次收件人
            recipients = self.quality_manager.get_batch_recipients(batch_id)

            # 创建详情窗口
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"批次详情 - {batch_name}")
            detail_window.geometry("800x600")
            detail_window.transient(self.root)

            # 详情内容
            detail_frame = ttk.Frame(detail_window, padding="10")
            detail_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(detail_frame, text=f"批次名称: {batch_name}",
                     font=('Microsoft YaHei UI', 12, 'bold')).pack(pady=(0, 10))

            ttk.Label(detail_frame, text=f"收件人数量: {len(recipients)}").pack(pady=(0, 5))

            # 收件人列表
            recipients_text = scrolledtext.ScrolledText(detail_frame, height=25, font=('Consolas', 10))
            recipients_text.pack(fill=tk.BOTH, expand=True, pady=(10, 10))

            recipients_text.insert(tk.END, f"📦 批次收件人列表 ({len(recipients)} 个)\n")
            recipients_text.insert(tk.END, "="*50 + "\n\n")

            for i, recipient in enumerate(recipients, 1):
                recipients_text.insert(tk.END, f"{i:3d}. {recipient}\n")

            # 操作按钮
            btn_frame = ttk.Frame(detail_frame)
            btn_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(btn_frame, text="📤 导入到主系统",
                      command=lambda: self._import_batch_recipients_from_detail(recipients, detail_window)).pack(side=tk.LEFT, padx=5)

            ttk.Button(btn_frame, text="📋 复制列表",
                      command=lambda: self._copy_recipients_to_clipboard(recipients)).pack(side=tk.LEFT, padx=5)

            ttk.Button(btn_frame, text="关闭",
                      command=detail_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            error_msg = f"查看批次详情失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("查看失败", error_msg)

    def _add_recipient_tags(self):
        """添加收件人标签"""
        try:
            selected_items = self.recipients_tree.selection()
            if not selected_items:
                messagebox.showwarning("提示", "请先选择要添加标签的收件人")
                return

            # 获取选中的收件人邮箱
            selected_emails = []
            for item in selected_items:
                values = self.recipients_tree.item(item)['values']
                selected_emails.append(values[0])  # 邮箱地址

            # 创建标签输入对话框
            tag_window = tk.Toplevel(self.root)
            tag_window.title("添加收件人标签")
            tag_window.geometry("400x200")
            tag_window.transient(self.root)
            tag_window.grab_set()

            main_frame = ttk.Frame(tag_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(main_frame, text=f"为 {len(selected_emails)} 个收件人添加标签",
                     font=('Microsoft YaHei UI', 12, 'bold')).pack(pady=(0, 15))

            ttk.Label(main_frame, text="标签名称:").pack(anchor=tk.W)
            tag_var = tk.StringVar()
            tag_entry = ttk.Entry(main_frame, textvariable=tag_var, width=30)
            tag_entry.pack(fill=tk.X, pady=(5, 10))
            tag_entry.focus()

            ttk.Label(main_frame, text="标签描述:").pack(anchor=tk.W)
            desc_var = tk.StringVar()
            desc_entry = ttk.Entry(main_frame, textvariable=desc_var, width=30)
            desc_entry.pack(fill=tk.X, pady=(5, 15))

            def save_tags():
                tag_name = tag_var.get().strip()
                tag_desc = desc_var.get().strip()

                if not tag_name:
                    messagebox.showwarning("提示", "请输入标签名称")
                    return

                try:
                    # 这里可以添加实际的标签保存逻辑
                    self.log_message(f"✅ 为 {len(selected_emails)} 个收件人添加标签: {tag_name}")
                    messagebox.showinfo("添加成功", f"已为 {len(selected_emails)} 个收件人添加标签: {tag_name}")
                    tag_window.destroy()

                except Exception as e:
                    messagebox.showerror("添加失败", f"添加标签失败: {str(e)}")

            # 按钮
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(btn_frame, text="保存", command=save_tags).pack(side=tk.LEFT, padx=5)
            ttk.Button(btn_frame, text="取消", command=tag_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            error_msg = f"添加收件人标签失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("添加失败", error_msg)

    def _view_recipient_details(self):
        """查看收件人详情"""
        try:
            selected_items = self.recipients_tree.selection()
            if not selected_items:
                messagebox.showwarning("提示", "请先选择一个收件人")
                return

            # 获取选中的收件人
            item = selected_items[0]
            values = self.recipients_tree.item(item)['values']
            email = values[0]

            # 创建详情窗口
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"收件人详情 - {email}")
            detail_window.geometry("600x500")
            detail_window.transient(self.root)

            # 详情内容
            detail_frame = ttk.Frame(detail_window, padding="10")
            detail_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(detail_frame, text=f"收件人详情",
                     font=('Microsoft YaHei UI', 14, 'bold')).pack(pady=(0, 15))

            # 基本信息
            info_text = f"""📧 邮箱地址: {values[0]}
📊 质量评分: {values[1]}
🏷️ 状态: {values[2]}
📤 发送数: {values[3]}
📬 回复数: {values[4]}
📈 回复率: {values[5]}
⏰ 最后回复: {values[6]}
"""

            info_label = ttk.Label(detail_frame, text=info_text, font=('Consolas', 10))
            info_label.pack(anchor=tk.W, pady=(0, 15))

            # 历史记录
            ttk.Label(detail_frame, text="📋 发送历史记录",
                     font=('Microsoft YaHei UI', 12, 'bold')).pack(anchor=tk.W, pady=(0, 5))

            history_text = scrolledtext.ScrolledText(detail_frame, height=15, font=('Consolas', 9))
            history_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            # 这里可以添加获取历史记录的逻辑
            history_text.insert(tk.END, "📭 暂无详细历史记录\n")
            history_text.insert(tk.END, "可以在这里显示该收件人的发送历史、回复记录等信息")

            # 关闭按钮
            ttk.Button(detail_frame, text="关闭", command=detail_window.destroy).pack(pady=(10, 0))

        except Exception as e:
            error_msg = f"查看收件人详情失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("查看失败", error_msg)

    def _select_import_file(self):
        """选择导入文件"""
        try:
            file_path = filedialog.askopenfilename(
                title="选择要导入的文件",
                filetypes=[
                    ("文本文件", "*.txt"),
                    ("CSV文件", "*.csv"),
                    ("所有文件", "*.*")
                ]
            )

            if file_path:
                self.import_file_var.set(file_path)
                self.log_message(f"📁 已选择导入文件: {file_path}")

        except Exception as e:
            error_msg = f"选择导入文件失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("选择失败", error_msg)

    def _import_from_file(self):
        """从文件导入收件人"""
        try:
            file_path = self.import_file_var.get().strip()
            if not file_path:
                messagebox.showwarning("提示", "请先选择要导入的文件")
                return

            if not os.path.exists(file_path):
                messagebox.showerror("错误", "选择的文件不存在")
                return

            self.import_progress_text.delete(1.0, tk.END)
            self.import_progress_text.insert(tk.END, f"📁 开始从文件导入数据...\n")
            self.import_progress_text.insert(tk.END, f"文件路径: {file_path}\n\n")

            # 在后台线程中执行导入
            def import_thread():
                try:
                    imported_count = 0

                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()

                    # 简单的邮箱提取逻辑
                    import re
                    email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'

                    for line_num, line in enumerate(lines, 1):
                        emails = re.findall(email_pattern, line.strip())
                        for email in emails:
                            # 这里可以添加实际的导入逻辑
                            imported_count += 1

                            if imported_count % 10 == 0:
                                self.root.after(0, lambda count=imported_count:
                                    self.import_progress_text.insert(tk.END, f"已处理 {count} 个邮箱...\n"))

                    self.root.after(0, lambda: self.import_progress_text.insert(tk.END, f"✅ 导入完成！共导入 {imported_count} 个邮箱\n"))
                    self.root.after(0, lambda: self._refresh_quality_data(None))
                    self.root.after(0, lambda: self.log_message(f"✅ 从文件导入了 {imported_count} 个邮箱"))

                except Exception as e:
                    self.root.after(0, lambda err=str(e): self.import_progress_text.insert(tk.END, f"❌ 导入失败: {err}\n"))

            import threading
            threading.Thread(target=import_thread, daemon=True).start()

        except Exception as e:
            error_msg = f"从文件导入失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导入失败", error_msg)

    def open_anti_spam_manager(self):
        """打开反垃圾邮件管理器"""
        try:
            sender_email = self.sender_email.get().strip()
            if not sender_email:
                messagebox.showwarning("提示", "请先填写发件人邮箱")
                return

            self.log_message("🛡️ 打开反垃圾邮件管理器...")
            self._show_anti_spam_window(sender_email)

        except Exception as e:
            error_msg = f"打开反垃圾邮件管理器失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _show_anti_spam_window(self, sender_email):
        """显示反垃圾邮件管理窗口"""
        # 创建反垃圾邮件窗口
        spam_window = tk.Toplevel(self.root)
        spam_window.title("🛡️ 反垃圾邮件管理")
        spam_window.geometry("1200x800")
        spam_window.transient(self.root)
        spam_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(spam_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="🛡️ 反垃圾邮件管理系统",
                               font=('Microsoft YaHei UI', 16, 'bold'))
        title_label.pack(pady=(0, 15))

        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 1. 发送控制标签页
        control_frame = ttk.Frame(notebook)
        notebook.add(control_frame, text="🎛️ 发送控制")
        self._create_sending_control_tab(control_frame, sender_email)

        # 2. 风险监控标签页
        monitor_frame = ttk.Frame(notebook)
        notebook.add(monitor_frame, text="📊 风险监控")
        self._create_risk_monitor_tab(monitor_frame, sender_email)

        # 3. 发送分析标签页
        analytics_frame = ttk.Frame(notebook)
        notebook.add(analytics_frame, text="📈 发送分析")
        self._create_sending_analytics_tab(analytics_frame, sender_email)

        # 4. 策略配置标签页
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="⚙️ 策略配置")
        self._create_strategy_config_tab(config_frame, sender_email)

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="🔄 刷新数据",
                  command=lambda: self._refresh_anti_spam_data(notebook, sender_email)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="🔍 检测风险",
                  command=lambda: self._manual_risk_detection(sender_email)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="📊 导出报告",
                  command=lambda: self._export_anti_spam_report(sender_email)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="关闭",
                  command=spam_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 初始化反垃圾邮件管理器并刷新数据
        self._init_anti_spam_manager(sender_email)
        self._refresh_anti_spam_data(notebook, sender_email)

        self.log_message("✅ 反垃圾邮件管理器窗口已打开")

    def _init_anti_spam_manager(self, sender_email):
        """初始化反垃圾邮件管理器"""
        try:
            from anti_spam_manager import AntiSpamManager
            self.anti_spam_manager = AntiSpamManager()

            # 初始化发件人配置
            self.anti_spam_manager.initialize_sender(sender_email, 'moderate')

        except Exception as e:
            self.log_message(f"❌ 初始化反垃圾邮件管理器失败: {str(e)}")

    def _create_sending_control_tab(self, parent, sender_email):
        """创建发送控制标签页"""
        # 当前状态框架
        status_frame = ttk.LabelFrame(parent, text="📊 当前发送状态", padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        self.spam_status_text = tk.Text(status_frame, height=8, font=('Consolas', 10))
        self.spam_status_text.pack(fill=tk.X)

        # 发送权限检查框架
        permission_frame = ttk.LabelFrame(parent, text="🔍 发送权限检查", padding="10")
        permission_frame.pack(fill=tk.X, pady=(0, 10))

        # 收件人数量输入
        input_frame = ttk.Frame(permission_frame)
        input_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(input_frame, text="计划发送数量:").pack(side=tk.LEFT, padx=(0, 5))
        self.planned_send_count = tk.StringVar(value="100")
        ttk.Entry(input_frame, textvariable=self.planned_send_count, width=10).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Button(input_frame, text="🔍 检查权限",
                  command=lambda: self._check_sending_permission(sender_email)).pack(side=tk.LEFT, padx=10)

        # 权限检查结果
        self.permission_result_text = scrolledtext.ScrolledText(permission_frame, height=12, font=('Consolas', 9))
        self.permission_result_text.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # 智能建议框架
        suggestions_frame = ttk.LabelFrame(parent, text="💡 智能建议", padding="10")
        suggestions_frame.pack(fill=tk.BOTH, expand=True)

        self.suggestions_text = tk.Text(suggestions_frame, height=8, font=('Microsoft YaHei UI', 10))
        self.suggestions_text.pack(fill=tk.BOTH, expand=True)

    def _create_risk_monitor_tab(self, parent, sender_email):
        """创建风险监控标签页"""
        # 风险检测结果框架
        detection_frame = ttk.LabelFrame(parent, text="🚨 风险检测结果", padding="10")
        detection_frame.pack(fill=tk.X, pady=(0, 10))

        self.risk_detection_text = scrolledtext.ScrolledText(detection_frame, height=10, font=('Consolas', 9))
        self.risk_detection_text.pack(fill=tk.BOTH, expand=True)

        # 实时监控框架
        monitor_frame = ttk.LabelFrame(parent, text="📡 实时监控", padding="10")
        monitor_frame.pack(fill=tk.BOTH, expand=True)

        # 监控指标
        metrics_frame = ttk.Frame(monitor_frame)
        metrics_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建监控指标显示
        self.delivery_rate_var = tk.StringVar(value="0%")
        self.bounce_rate_var = tk.StringVar(value="0%")
        self.reply_rate_var = tk.StringVar(value="0%")
        self.risk_level_var = tk.StringVar(value="未知")

        ttk.Label(metrics_frame, text="送达率:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Label(metrics_frame, textvariable=self.delivery_rate_var, font=('Arial', 12, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(metrics_frame, text="退信率:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        ttk.Label(metrics_frame, textvariable=self.bounce_rate_var, font=('Arial', 12, 'bold')).grid(row=0, column=3, sticky=tk.W, padx=(0, 20))

        ttk.Label(metrics_frame, text="回复率:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Label(metrics_frame, textvariable=self.reply_rate_var, font=('Arial', 12, 'bold')).grid(row=1, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(metrics_frame, text="风险等级:").grid(row=1, column=2, sticky=tk.W, padx=(0, 5))
        ttk.Label(metrics_frame, textvariable=self.risk_level_var, font=('Arial', 12, 'bold')).grid(row=1, column=3, sticky=tk.W)

        # 监控日志
        self.monitor_log_text = scrolledtext.ScrolledText(monitor_frame, height=15, font=('Consolas', 9))
        self.monitor_log_text.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

    def _create_sending_analytics_tab(self, parent, sender_email):
        """创建发送分析标签页"""
        # 时间范围选择
        range_frame = ttk.Frame(parent)
        range_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(range_frame, text="分析时间范围:").pack(side=tk.LEFT, padx=(0, 5))
        self.analytics_days_var = tk.StringVar(value="7")
        days_combo = ttk.Combobox(range_frame, textvariable=self.analytics_days_var, width=8, values=["1", "3", "7", "14", "30"])
        days_combo.pack(side=tk.LEFT, padx=(0, 5))
        ttk.Label(range_frame, text="天").pack(side=tk.LEFT, padx=(0, 20))

        ttk.Button(range_frame, text="📊 生成分析",
                  command=lambda: self._generate_sending_analytics(sender_email)).pack(side=tk.LEFT, padx=10)

        # 分析结果显示
        self.analytics_text = scrolledtext.ScrolledText(parent, height=25, font=('Consolas', 9))
        self.analytics_text.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

    def _create_strategy_config_tab(self, parent, sender_email):
        """创建策略配置标签页"""
        # 发送模式选择
        pattern_frame = ttk.LabelFrame(parent, text="📋 发送模式配置", padding="10")
        pattern_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建内部框架来分离布局
        pattern_config_frame = ttk.Frame(pattern_frame)
        pattern_config_frame.pack(fill=tk.X)

        ttk.Label(pattern_config_frame, text="发送模式:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.sending_pattern_var = tk.StringVar(value="moderate")
        pattern_combo = ttk.Combobox(pattern_config_frame, textvariable=self.sending_pattern_var, width=15, state="readonly")
        pattern_combo['values'] = ["conservative", "moderate", "aggressive"]
        pattern_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Button(pattern_config_frame, text="💾 保存配置",
                  command=lambda: self._save_sending_pattern(sender_email)).grid(row=0, column=2, sticky=tk.E, padx=(20, 0))

        # 模式说明
        description_frame = ttk.LabelFrame(parent, text="📖 模式说明", padding="10")
        description_frame.pack(fill=tk.BOTH, expand=True)

        description_text = """🛡️ 发送模式说明

🐌 保守模式 (Conservative):
• 初始速率: 10封/小时，最大50封/小时
• 预热期: 7天
• 批次大小: 5封
• 发送间隔: 5-15分钟
• 每日限制: 200封
• 适用: 新邮箱、高风险内容

⚖️ 适中模式 (Moderate):
• 初始速率: 20封/小时，最大100封/小时
• 预热期: 5天
• 批次大小: 10封
• 发送间隔: 3-10分钟
• 每日限制: 500封
• 适用: 一般邮件营销

🚀 积极模式 (Aggressive):
• 初始速率: 30封/小时，最大200封/小时
• 预热期: 3天
• 批次大小: 15封
• 发送间隔: 2-5分钟
• 每日限制: 1000封
• 适用: 已预热邮箱、紧急发送

💡 建议:
• 新邮箱建议使用保守模式
• 根据发送效果逐步调整
• 监控回复率变化及时调整策略
"""

        description_display = scrolledtext.ScrolledText(description_frame, height=20, font=('Microsoft YaHei UI', 9))
        description_display.pack(fill=tk.BOTH, expand=True)
        description_display.insert(tk.END, description_text)
        description_display.config(state='disabled')

    def _check_sending_permission(self, sender_email):
        """检查发送权限"""
        try:
            recipient_count = int(self.planned_send_count.get())

            permission = self.anti_spam_manager.check_sending_permission(sender_email, recipient_count)

            self.permission_result_text.delete(1.0, tk.END)

            if permission.get('error'):
                self.permission_result_text.insert(tk.END, f"❌ 检查失败: {permission['error']}\n")
                return

            # 显示权限检查结果
            can_send = permission.get('can_send', False)
            status_icon = "✅" if can_send else "❌"

            result_text = f"{status_icon} 发送权限检查结果\n"
            result_text += "=" * 40 + "\n\n"

            result_text += f"📧 计划发送: {recipient_count} 封邮件\n"
            result_text += f"🎯 发送权限: {'允许' if can_send else '受限'}\n\n"

            result_text += f"📊 当前限制:\n"
            result_text += f"  • 今日剩余额度: {permission.get('daily_remaining', 0)} 封\n"
            result_text += f"  • 当前小时剩余: {permission.get('hourly_remaining', 0)} 封\n"
            result_text += f"  • 当前发送速率: {permission.get('current_rate', 0)} 封/小时\n"
            result_text += f"  • 声誉评分: {permission.get('reputation_score', 0):.1f}/100\n\n"

            result_text += f"🚨 垃圾邮件风险: {permission.get('spam_risk', 0):.1%}\n"
            result_text += f"🔥 预热状态: {'已完成' if permission.get('is_warmed_up') else '进行中'}\n\n"

            result_text += f"💡 发送建议:\n"
            result_text += f"  • 建议批次大小: {permission.get('suggested_batch_size', 0)} 封\n"
            result_text += f"  • 建议发送间隔: {permission.get('suggested_interval', 0)} 秒\n\n"

            result_text += f"📋 系统建议:\n"
            for rec in permission.get('recommendations', []):
                result_text += f"  {rec}\n"

            self.permission_result_text.insert(tk.END, result_text)

            # 更新建议文本
            self._update_suggestions_text(permission.get('recommendations', []))

        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的发送数量")
        except Exception as e:
            error_msg = f"检查发送权限失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.permission_result_text.delete(1.0, tk.END)
            self.permission_result_text.insert(tk.END, f"❌ {error_msg}")

    def _update_suggestions_text(self, recommendations):
        """更新建议文本"""
        self.suggestions_text.delete(1.0, tk.END)

        suggestions_text = "💡 智能发送建议\n"
        suggestions_text += "=" * 30 + "\n\n"

        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                suggestions_text += f"{i}. {rec}\n\n"
        else:
            suggestions_text += "🎉 当前发送条件良好，可以正常发送！"

        self.suggestions_text.insert(tk.END, suggestions_text)

    def _manual_risk_detection(self, sender_email):
        """手动风险检测"""
        try:
            self.risk_detection_text.delete(1.0, tk.END)
            self.risk_detection_text.insert(tk.END, "🔍 正在进行风险检测...\n\n")

            # 执行风险检测
            detection_result = self.anti_spam_manager.detect_spam_pattern(sender_email)

            self.risk_detection_text.delete(1.0, tk.END)

            if detection_result.get('error'):
                self.risk_detection_text.insert(tk.END, f"❌ 检测失败: {detection_result['error']}\n")
                return

            # 显示检测结果
            spam_detected = detection_result.get('spam_detected', False)
            risk_level = detection_result.get('risk_level', 'unknown')

            status_icon = "🚨" if spam_detected else "✅"
            result_text = f"{status_icon} 垃圾邮件风险检测结果\n"
            result_text += "=" * 50 + "\n\n"

            result_text += f"🎯 检测状态: {'发现风险' if spam_detected else '正常'}\n"
            result_text += f"⚠️ 风险等级: {risk_level.upper()}\n\n"

            detections = detection_result.get('detections', [])
            if detections:
                result_text += f"🔍 检测到的问题 ({len(detections)} 项):\n"
                result_text += "-" * 30 + "\n"

                for i, detection in enumerate(detections, 1):
                    severity_icon = "🔴" if detection['severity'] == 'high' else "🟡" if detection['severity'] == 'medium' else "🟢"
                    result_text += f"{severity_icon} {i}. {detection['type']}\n"
                    result_text += f"   描述: {detection['description']}\n"
                    result_text += f"   阈值: {detection['threshold']}\n"
                    result_text += f"   实际值: {detection['actual']:.3f}\n\n"
            else:
                result_text += "✅ 未检测到异常模式\n\n"

            recommendations = detection_result.get('recommendations', [])
            if recommendations:
                result_text += f"💡 处理建议:\n"
                result_text += "-" * 20 + "\n"
                for rec in recommendations:
                    result_text += f"• {rec}\n"

            self.risk_detection_text.insert(tk.END, result_text)

            # 更新风险等级显示
            self.risk_level_var.set(risk_level.upper())

            self.log_message(f"🔍 风险检测完成 - 风险等级: {risk_level}")

        except Exception as e:
            error_msg = f"风险检测失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.risk_detection_text.delete(1.0, tk.END)
            self.risk_detection_text.insert(tk.END, f"❌ {error_msg}")

    def _generate_sending_analytics(self, sender_email):
        """生成发送分析"""
        try:
            days = int(self.analytics_days_var.get())

            self.analytics_text.delete(1.0, tk.END)
            self.analytics_text.insert(tk.END, f"📊 正在生成最近 {days} 天的发送分析...\n\n")

            # 获取分析数据
            analytics = self.anti_spam_manager.get_sending_analytics(sender_email, days)

            self.analytics_text.delete(1.0, tk.END)

            if not analytics:
                self.analytics_text.insert(tk.END, "📭 暂无发送数据")
                return

            # 显示分析结果
            analytics_text = f"📊 发送分析报告 (最近 {days} 天)\n"
            analytics_text += "=" * 60 + "\n\n"

            # 总体统计
            analytics_text += f"📈 总体统计:\n"
            analytics_text += f"  📧 总发送量: {analytics.get('total_sent', 0):,} 封\n"
            analytics_text += f"  ✅ 成功送达: {analytics.get('total_delivered', 0):,} 封\n"
            analytics_text += f"  ❌ 退信数量: {analytics.get('total_bounced', 0):,} 封\n"
            analytics_text += f"  📬 收到回复: {analytics.get('total_replied', 0):,} 封\n\n"

            # 关键指标
            analytics_text += f"📊 关键指标:\n"
            analytics_text += f"  📈 送达率: {analytics.get('delivery_rate', 0):.2f}%\n"
            analytics_text += f"  📉 退信率: {analytics.get('bounce_rate', 0):.2f}%\n"
            analytics_text += f"  💬 回复率: {analytics.get('reply_rate', 0):.2f}%\n"
            analytics_text += f"  🚨 垃圾邮件检测: {analytics.get('spam_detections', 0)} 次\n\n"

            # 每日详情
            daily_stats = analytics.get('daily_stats', [])
            if daily_stats:
                analytics_text += f"📅 每日发送详情:\n"
                analytics_text += "-" * 50 + "\n"
                analytics_text += f"{'日期':<12} {'发送':<8} {'送达':<8} {'退信':<8} {'回复':<8} {'送达率':<8} {'回复率':<8}\n"
                analytics_text += "-" * 50 + "\n"

                for stat in daily_stats:
                    analytics_text += f"{stat['date']:<12} {stat['sent']:<8} {stat['delivered']:<8} {stat['bounced']:<8} {stat['replied']:<8} {stat['delivery_rate']:<7.1f}% {stat['reply_rate']:<7.1f}%\n"

            # 最近检测记录
            recent_detections = analytics.get('recent_detections', [])
            if recent_detections:
                analytics_text += f"\n🚨 最近的风险检测:\n"
                analytics_text += "-" * 30 + "\n"
                for detection in recent_detections:
                    analytics_text += f"时间: {detection['time'][:16]}\n"
                    analytics_text += f"类型: {detection['type']}\n"
                    analytics_text += f"数值: {detection['value']:.3f}\n"
                    analytics_text += "-" * 20 + "\n"

            self.analytics_text.insert(tk.END, analytics_text)

            # 更新监控指标
            self.delivery_rate_var.set(f"{analytics.get('delivery_rate', 0):.1f}%")
            self.bounce_rate_var.set(f"{analytics.get('bounce_rate', 0):.1f}%")
            self.reply_rate_var.set(f"{analytics.get('reply_rate', 0):.1f}%")

            self.log_message(f"📊 发送分析生成完成 - {days} 天数据")

        except ValueError:
            messagebox.showerror("输入错误", "请选择有效的天数")
        except Exception as e:
            error_msg = f"生成发送分析失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.analytics_text.delete(1.0, tk.END)
            self.analytics_text.insert(tk.END, f"❌ {error_msg}")

    def _save_sending_pattern(self, sender_email):
        """保存发送模式"""
        try:
            pattern = self.sending_pattern_var.get()

            # 重新初始化发件人配置
            success = self.anti_spam_manager.initialize_sender(sender_email, pattern)

            if success:
                self.log_message(f"✅ 发送模式已更新为: {pattern}")
                messagebox.showinfo("保存成功", f"发送模式已更新为: {pattern}")

                # 刷新状态显示
                self._refresh_anti_spam_data(None, sender_email)
            else:
                messagebox.showerror("保存失败", "更新发送模式失败")

        except Exception as e:
            error_msg = f"保存发送模式失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("保存失败", error_msg)

    def _refresh_anti_spam_data(self, notebook, sender_email):
        """刷新反垃圾邮件数据"""
        try:
            # 更新状态显示
            self._update_spam_status_display(sender_email)

            # 自动检查发送权限
            if hasattr(self, 'planned_send_count'):
                self._check_sending_permission(sender_email)

            # 自动生成分析
            if hasattr(self, 'analytics_text'):
                self._generate_sending_analytics(sender_email)

            self.log_message("✅ 反垃圾邮件数据刷新完成")

        except Exception as e:
            self.log_message(f"❌ 刷新反垃圾邮件数据失败: {str(e)}")

    def _update_spam_status_display(self, sender_email):
        """更新垃圾邮件状态显示"""
        try:
            # 获取当前状态
            permission = self.anti_spam_manager.check_sending_permission(sender_email, 1)

            status_text = f"🛡️ 反垃圾邮件状态 - {sender_email}\n"
            status_text += "=" * 50 + "\n\n"

            if permission.get('error'):
                status_text += f"❌ 状态获取失败: {permission['error']}\n"
            else:
                status_text += f"📊 当前发送速率: {permission.get('current_rate', 0)} 封/小时\n"
                status_text += f"🎯 声誉评分: {permission.get('reputation_score', 0):.1f}/100\n"
                status_text += f"🚨 垃圾邮件风险: {permission.get('spam_risk', 0):.1%}\n"
                status_text += f"🔥 预热状态: {'已完成' if permission.get('is_warmed_up') else '进行中'}\n"
                status_text += f"📅 今日剩余额度: {permission.get('daily_remaining', 0)} 封\n"
                status_text += f"⏰ 当前小时剩余: {permission.get('hourly_remaining', 0)} 封\n"

            if hasattr(self, 'spam_status_text'):
                self.spam_status_text.delete(1.0, tk.END)
                self.spam_status_text.insert(1.0, status_text)

        except Exception as e:
            self.log_message(f"❌ 更新状态显示失败: {str(e)}")

    def _export_anti_spam_report(self, sender_email):
        """导出反垃圾邮件报告"""
        try:
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filepath = f"anti_spam_report_{sender_email.replace('@', '_')}_{timestamp}.txt"

            # 获取分析数据
            analytics = self.anti_spam_manager.get_sending_analytics(sender_email, 30)
            permission = self.anti_spam_manager.check_sending_permission(sender_email, 100)
            detection = self.anti_spam_manager.detect_spam_pattern(sender_email)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("🛡️ 反垃圾邮件分析报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"发件人: {sender_email}\n\n")

                # 当前状态
                f.write("📊 当前状态\n")
                f.write("-" * 30 + "\n")
                if not permission.get('error'):
                    f.write(f"发送速率: {permission.get('current_rate', 0)} 封/小时\n")
                    f.write(f"声誉评分: {permission.get('reputation_score', 0):.1f}/100\n")
                    f.write(f"垃圾邮件风险: {permission.get('spam_risk', 0):.1%}\n")
                    f.write(f"预热状态: {'已完成' if permission.get('is_warmed_up') else '进行中'}\n\n")

                # 发送统计
                if analytics:
                    f.write("📈 发送统计 (最近30天)\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"总发送量: {analytics.get('total_sent', 0):,} 封\n")
                    f.write(f"送达率: {analytics.get('delivery_rate', 0):.2f}%\n")
                    f.write(f"退信率: {analytics.get('bounce_rate', 0):.2f}%\n")
                    f.write(f"回复率: {analytics.get('reply_rate', 0):.2f}%\n")
                    f.write(f"垃圾邮件检测: {analytics.get('spam_detections', 0)} 次\n\n")

                # 风险检测
                if not detection.get('error'):
                    f.write("🚨 风险检测结果\n")
                    f.write("-" * 30 + "\n")
                    f.write(f"风险状态: {'发现风险' if detection.get('spam_detected') else '正常'}\n")
                    f.write(f"风险等级: {detection.get('risk_level', 'unknown').upper()}\n")

                    detections = detection.get('detections', [])
                    if detections:
                        f.write(f"检测到问题: {len(detections)} 项\n")
                        for det in detections:
                            f.write(f"  - {det['type']}: {det['description']}\n")
                    f.write("\n")

                # 建议
                recommendations = permission.get('recommendations', [])
                if recommendations:
                    f.write("💡 优化建议\n")
                    f.write("-" * 30 + "\n")
                    for rec in recommendations:
                        f.write(f"• {rec}\n")

            self.log_message(f"✅ 反垃圾邮件报告已导出到: {filepath}")
            messagebox.showinfo("导出成功", f"报告已导出到:\n{filepath}")

        except Exception as e:
            error_msg = f"导出报告失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("导出失败", error_msg)

    def _update_anti_spam_stats_after_send(self, sender_email, sent_recipients):
        """发送邮件后更新反垃圾邮件统计"""
        try:
            from anti_spam_manager import AntiSpamManager

            anti_spam_manager = AntiSpamManager()

            # 初始化发件人（如果还没有）
            anti_spam_manager.initialize_sender(sender_email, 'moderate')

            # 记录每个收件人的发送结果
            for recipient in sent_recipients:
                anti_spam_manager.record_sending_result(
                    sender_email=sender_email,
                    recipient_email=recipient,
                    success=True,  # 发送成功
                    bounced=False,  # 暂时假设没有退信
                    replied=False   # 暂时假设没有回复
                )

            self.log_message(f"🛡️ 已更新 {len(sent_recipients)} 个收件人的反垃圾邮件统计")

        except Exception as e:
            self.log_message(f"❌ 更新反垃圾邮件统计失败: {str(e)}")

    def _auto_check_qq_emergency_after_send(self, sender_email, sent_recipients):
        """发送邮件后自动检测QQ应急状态"""
        try:
            from qq_email_anti_spam import QQEmailAntiSpamManager

            qq_manager = QQEmailAntiSpamManager()

            # 初始化QQ邮箱（如果还没有）
            account_type = 'vip' if 'vip' in sender_email.lower() else 'normal'
            qq_manager.initialize_qq_sender(sender_email, account_type)

            # 记录发送结果（暂时假设发送成功，无自动回复）
            for i, recipient in enumerate(sent_recipients):
                subject = self.subject.get()

                # 记录发送结果
                qq_manager.record_qq_sending_result(
                    sender_email=sender_email,
                    recipient_email=recipient,
                    subject=subject,
                    has_auto_reply=False,  # 发送时暂时假设无回复
                    reply_content="",
                    batch_id=f"auto_batch_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    sequence_number=i + 1
                )

            # 检查应急状态
            emergency_status = qq_manager._check_emergency_status(sender_email)

            if emergency_status.get('should_trigger', False):
                consecutive_no_reply = emergency_status.get('consecutive_no_reply', 0)

                # 自动触发应急模式
                self.log_message(f"🚨 检测到连续 {consecutive_no_reply} 封邮件无回复，自动激活QQ应急模式")

                # 激活应急模式
                qq_manager._activate_emergency_mode(sender_email, emergency_status)

                # 显示应急通知
                self._show_emergency_notification(sender_email, consecutive_no_reply)

                # 如果用户开启了自动回复监控，暂停发送
                if hasattr(self, 'auto_start_reply_monitoring') and self.auto_start_reply_monitoring.get():
                    self.log_message("⏸️ 应急模式激活，建议暂停后续发送")

            self.log_message(f"🆘 QQ应急检测完成 - 连续无回复: {emergency_status.get('consecutive_no_reply', 0)} 封")

        except Exception as e:
            self.log_message(f"❌ QQ应急检测失败: {str(e)}")

    def _show_emergency_notification(self, sender_email, consecutive_no_reply):
        """显示应急模式通知"""
        try:
            # 创建应急通知窗口
            emergency_window = tk.Toplevel(self.root)
            emergency_window.title("🚨 QQ邮箱应急模式激活")
            emergency_window.geometry("500x400")
            emergency_window.transient(self.root)
            emergency_window.grab_set()

            # 设置窗口置顶
            emergency_window.attributes('-topmost', True)

            main_frame = ttk.Frame(emergency_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = ttk.Label(main_frame, text="🚨 应急模式已激活",
                                   font=('Microsoft YaHei UI', 16, 'bold'))
            title_label.pack(pady=(0, 20))

            # 警告信息
            warning_text = f"""⚠️ 检测到邮件发送异常

📧 发件人: {sender_email}
🔢 连续无回复: {consecutive_no_reply} 封邮件
🚨 状态: 已自动激活应急模式

📋 应急措施已启动:
• 暂停发送30分钟
• 降低发送频率
• 启动恢复策略
• 建议更换邮件内容

💡 建议操作:
1. 检查邮件内容是否包含敏感词
2. 更换邮件主题模式
3. 等待应急恢复完成
4. 发送测试邮件验证恢复
"""

            warning_label = ttk.Label(main_frame, text=warning_text,
                                     font=('Microsoft YaHei UI', 10),
                                     justify=tk.LEFT)
            warning_label.pack(pady=(0, 20))

            # 按钮框架
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            def open_qq_emergency():
                emergency_window.destroy()
                self.open_qq_emergency_manager()

            def close_notification():
                emergency_window.destroy()
                self.log_message("📋 应急通知已关闭，应急模式仍在运行")

            ttk.Button(button_frame, text="🆘 打开应急管理器",
                      command=open_qq_emergency).pack(side=tk.LEFT, padx=5)

            ttk.Button(button_frame, text="📋 我知道了",
                      command=close_notification).pack(side=tk.RIGHT, padx=5)

            # 自动关闭定时器（30秒后）
            def auto_close():
                if emergency_window.winfo_exists():
                    emergency_window.destroy()
                    self.log_message("📋 应急通知自动关闭")

            emergency_window.after(30000, auto_close)  # 30秒后自动关闭

        except Exception as e:
            self.log_message(f"❌ 显示应急通知失败: {str(e)}")

    def _update_qq_sending_result_with_reply(self, sender_email, recipient_email, has_reply, reply_content=""):
        """更新QQ邮箱发送结果（当收到回复时调用）"""
        try:
            from qq_email_anti_spam import QQEmailAntiSpamManager

            qq_manager = QQEmailAntiSpamManager()

            # 更新发送结果
            subject = self.subject.get()
            qq_manager.record_qq_sending_result(
                sender_email=sender_email,
                recipient_email=recipient_email,
                subject=subject,
                has_auto_reply=has_reply,
                reply_content=reply_content,
                batch_id=f"reply_update_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}",
                sequence_number=1
            )

            # 检查是否可以退出应急模式
            emergency_status = qq_manager._check_emergency_status(sender_email)

            if emergency_status.get('is_active', False) and has_reply:
                # 如果在应急模式且收到回复，检查是否可以恢复
                qq_manager._check_emergency_recovery(sender_email)
                self.log_message(f"📬 收到回复，检查应急恢复状态: {recipient_email}")

        except Exception as e:
            self.log_message(f"❌ 更新QQ发送结果失败: {str(e)}")

    def check_anti_spam_before_send(self, sender_email, recipient_count):
        """发送前检查反垃圾邮件限制"""
        try:
            from anti_spam_manager import AntiSpamManager

            anti_spam_manager = AntiSpamManager()

            # 检查发送权限
            permission = anti_spam_manager.check_sending_permission(sender_email, recipient_count)

            if permission.get('error'):
                return {
                    'can_send': False,
                    'reason': f"检查权限失败: {permission['error']}",
                    'recommendations': []
                }

            can_send = permission.get('can_send', False)

            if not can_send:
                reasons = []
                if permission.get('daily_remaining', 0) <= 0:
                    reasons.append("今日发送量已达上限")
                if permission.get('hourly_remaining', 0) <= 0:
                    reasons.append("当前小时发送量已达上限")

                return {
                    'can_send': False,
                    'reason': "发送受限: " + ", ".join(reasons),
                    'recommendations': permission.get('recommendations', []),
                    'suggested_batch_size': permission.get('suggested_batch_size', 0),
                    'suggested_interval': permission.get('suggested_interval', 300)
                }

            # 检查垃圾邮件风险
            spam_risk = permission.get('spam_risk', 0)
            if spam_risk > 0.7:
                return {
                    'can_send': True,
                    'warning': f"垃圾邮件风险较高 ({spam_risk:.1%})",
                    'recommendations': permission.get('recommendations', []),
                    'suggested_batch_size': permission.get('suggested_batch_size', recipient_count),
                    'suggested_interval': permission.get('suggested_interval', 300)
                }

            return {
                'can_send': True,
                'recommendations': permission.get('recommendations', []),
                'suggested_batch_size': permission.get('suggested_batch_size', recipient_count),
                'suggested_interval': permission.get('suggested_interval', 180)
            }

        except Exception as e:
            self.log_message(f"❌ 反垃圾邮件检查失败: {str(e)}")
            return {
                'can_send': True,  # 检查失败时允许发送，但记录错误
                'warning': f"反垃圾邮件检查失败: {str(e)}",
                'recommendations': []
            }

    def open_qq_emergency_manager(self):
        """打开QQ邮箱应急管理器"""
        try:
            sender_email = self.sender_email.get().strip()
            if not sender_email:
                messagebox.showwarning("提示", "请先填写发件人邮箱")
                return

            # 检查是否为QQ邮箱
            if not any(domain in sender_email.lower() for domain in ['qq.com', 'foxmail.com']):
                result = messagebox.askyesno(
                    "确认",
                    f"检测到您使用的不是QQ邮箱 ({sender_email})\n\n"
                    "QQ应急管理器专为QQ邮箱优化，\n"
                    "是否仍要继续？"
                )
                if not result:
                    return

            self.log_message("🆘 打开QQ邮箱应急管理器...")
            self._show_qq_emergency_window(sender_email)

        except Exception as e:
            error_msg = f"打开QQ应急管理器失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _show_qq_emergency_window(self, sender_email):
        """显示QQ应急管理窗口"""
        # 创建QQ应急管理窗口
        emergency_window = tk.Toplevel(self.root)
        emergency_window.title("🆘 QQ邮箱应急管理")
        emergency_window.geometry("1400x900")
        emergency_window.transient(self.root)
        emergency_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(emergency_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="🆘 QQ邮箱应急管理系统",
                               font=('Microsoft YaHei UI', 16, 'bold'))
        title_label.pack(pady=(0, 15))

        # 创建笔记本控件
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 1. 应急状态监控
        status_frame = ttk.Frame(notebook)
        notebook.add(status_frame, text="🚨 应急状态")
        self._create_emergency_status_tab(status_frame, sender_email)

        # 2. 实时监控
        monitor_frame = ttk.Frame(notebook)
        notebook.add(monitor_frame, text="📡 实时监控")
        self._create_qq_monitor_tab(monitor_frame, sender_email)

        # 3. 应急恢复
        recovery_frame = ttk.Frame(notebook)
        notebook.add(recovery_frame, text="🔧 应急恢复")
        self._create_emergency_recovery_tab(recovery_frame, sender_email)

        # 4. QQ邮箱配置
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="⚙️ QQ配置")
        self._create_qq_config_tab(config_frame, sender_email)

        # 操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(button_frame, text="🔄 刷新状态",
                  command=lambda: self._refresh_qq_emergency_data(notebook, sender_email)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="🧪 测试发送",
                  command=lambda: self._qq_test_send(sender_email)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="🆘 手动应急",
                  command=lambda: self._manual_emergency_activation(sender_email)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="✅ 退出应急",
                  command=lambda: self._manual_emergency_deactivation(sender_email)).pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="关闭",
                  command=emergency_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 初始化QQ应急管理器并刷新数据
        self._init_qq_emergency_manager(sender_email)
        self._refresh_qq_emergency_data(notebook, sender_email)

        self.log_message("✅ QQ应急管理器窗口已打开")

    def _init_qq_emergency_manager(self, sender_email):
        """初始化QQ应急管理器"""
        try:
            from qq_email_anti_spam import QQEmailAntiSpamManager
            self.qq_emergency_manager = QQEmailAntiSpamManager()

            # 检测账户类型
            account_type = 'vip' if 'vip' in sender_email.lower() else 'normal'

            # 初始化QQ邮箱配置
            self.qq_emergency_manager.initialize_qq_sender(sender_email, account_type)

        except Exception as e:
            self.log_message(f"❌ 初始化QQ应急管理器失败: {str(e)}")

    def _create_emergency_status_tab(self, parent, sender_email):
        """创建应急状态标签页"""
        # 当前状态框架
        current_status_frame = ttk.LabelFrame(parent, text="🚨 当前应急状态", padding="10")
        current_status_frame.pack(fill=tk.X, pady=(0, 10))

        self.qq_emergency_status_text = tk.Text(current_status_frame, height=8, font=('Consolas', 10))
        self.qq_emergency_status_text.pack(fill=tk.X)

        # 应急阈值设置
        threshold_frame = ttk.LabelFrame(parent, text="⚙️ 应急阈值设置", padding="10")
        threshold_frame.pack(fill=tk.X, pady=(0, 10))

        threshold_row = ttk.Frame(threshold_frame)
        threshold_row.pack(fill=tk.X)

        ttk.Label(threshold_row, text="连续无回复触发阈值:").pack(side=tk.LEFT, padx=(0, 5))
        self.emergency_threshold_var = tk.StringVar(value="5")
        threshold_spinbox = ttk.Spinbox(threshold_row, from_=3, to=10, width=5, textvariable=self.emergency_threshold_var)
        threshold_spinbox.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(threshold_row, text="封邮件").pack(side=tk.LEFT, padx=(0, 20))

        ttk.Button(threshold_row, text="💾 保存设置",
                  command=lambda: self._save_emergency_threshold(sender_email)).pack(side=tk.LEFT, padx=10)

        # 应急历史记录
        history_frame = ttk.LabelFrame(parent, text="📋 应急历史记录", padding="10")
        history_frame.pack(fill=tk.BOTH, expand=True)

        # 创建历史记录表格
        history_columns = ('触发时间', '触发原因', '恢复时间', '恢复状态')
        self.emergency_history_tree = ttk.Treeview(history_frame, columns=history_columns, show='headings', height=12)

        for col in history_columns:
            self.emergency_history_tree.heading(col, text=col)
            self.emergency_history_tree.column(col, width=150)

        # 滚动条
        history_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.emergency_history_tree.yview)
        self.emergency_history_tree.configure(yscrollcommand=history_scrollbar.set)

        self.emergency_history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def _create_qq_monitor_tab(self, parent, sender_email):
        """创建QQ实时监控标签页"""
        # 实时指标显示
        metrics_frame = ttk.LabelFrame(parent, text="📊 实时指标", padding="10")
        metrics_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建指标显示
        metrics_grid = ttk.Frame(metrics_frame)
        metrics_grid.pack(fill=tk.X)

        # 第一行指标
        self.qq_daily_sent_var = tk.StringVar(value="0")
        self.qq_daily_limit_var = tk.StringVar(value="100")
        self.qq_consecutive_no_reply_var = tk.StringVar(value="0")
        self.qq_emergency_active_var = tk.StringVar(value="否")

        ttk.Label(metrics_grid, text="今日已发送:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Label(metrics_grid, textvariable=self.qq_daily_sent_var, font=('Arial', 12, 'bold')).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(metrics_grid, text="每日限额:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        ttk.Label(metrics_grid, textvariable=self.qq_daily_limit_var, font=('Arial', 12, 'bold')).grid(row=0, column=3, sticky=tk.W, padx=(0, 20))

        # 第二行指标
        ttk.Label(metrics_grid, text="连续无回复:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Label(metrics_grid, textvariable=self.qq_consecutive_no_reply_var, font=('Arial', 12, 'bold')).grid(row=1, column=1, sticky=tk.W, padx=(0, 20))

        ttk.Label(metrics_grid, text="应急状态:").grid(row=1, column=2, sticky=tk.W, padx=(0, 5))
        ttk.Label(metrics_grid, textvariable=self.qq_emergency_active_var, font=('Arial', 12, 'bold')).grid(row=1, column=3, sticky=tk.W)

        # 发送监控日志
        monitor_log_frame = ttk.LabelFrame(parent, text="📡 发送监控日志", padding="10")
        monitor_log_frame.pack(fill=tk.BOTH, expand=True)

        self.qq_monitor_log_text = scrolledtext.ScrolledText(monitor_log_frame, height=20, font=('Consolas', 9))
        self.qq_monitor_log_text.pack(fill=tk.BOTH, expand=True)

        # 自动刷新控制
        refresh_frame = ttk.Frame(monitor_log_frame)
        refresh_frame.pack(fill=tk.X, pady=(10, 0))

        self.auto_refresh_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(refresh_frame, text="自动刷新 (30秒)", variable=self.auto_refresh_var).pack(side=tk.LEFT)

        ttk.Button(refresh_frame, text="🔄 立即刷新",
                  command=lambda: self._refresh_qq_monitor_log(sender_email)).pack(side=tk.RIGHT, padx=5)

    def _create_emergency_recovery_tab(self, parent, sender_email):
        """创建应急恢复标签页"""
        # 恢复策略选择
        strategy_frame = ttk.LabelFrame(parent, text="🔧 恢复策略", padding="10")
        strategy_frame.pack(fill=tk.X, pady=(0, 10))

        # 策略选项
        self.recovery_strategy_var = tk.StringVar(value="comprehensive")

        strategies = [
            ("comprehensive", "综合恢复 - 执行所有恢复步骤"),
            ("subject_change", "主题优化 - 更换邮件主题模式"),
            ("speed_reduce", "降速发送 - 大幅降低发送频率"),
            ("test_send", "测试发送 - 发送测试邮件验证"),
            ("wait_recovery", "等待恢复 - 暂停发送等待自然恢复")
        ]

        for value, text in strategies:
            ttk.Radiobutton(strategy_frame, text=text, variable=self.recovery_strategy_var,
                           value=value).pack(anchor=tk.W, pady=2)

        # 恢复操作按钮
        recovery_btn_frame = ttk.Frame(strategy_frame)
        recovery_btn_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(recovery_btn_frame, text="🚀 执行恢复",
                  command=lambda: self._execute_recovery_strategy(sender_email)).pack(side=tk.LEFT, padx=5)

        ttk.Button(recovery_btn_frame, text="🧪 测试恢复效果",
                  command=lambda: self._test_recovery_effect(sender_email)).pack(side=tk.LEFT, padx=5)

        # 恢复进度显示
        progress_frame = ttk.LabelFrame(parent, text="📈 恢复进度", padding="10")
        progress_frame.pack(fill=tk.BOTH, expand=True)

        self.recovery_progress_text = scrolledtext.ScrolledText(progress_frame, height=15, font=('Consolas', 9))
        self.recovery_progress_text.pack(fill=tk.BOTH, expand=True)

        # 恢复建议
        suggestions_frame = ttk.LabelFrame(parent, text="💡 恢复建议", padding="10")
        suggestions_frame.pack(fill=tk.X)

        self.recovery_suggestions_text = tk.Text(suggestions_frame, height=6, font=('Microsoft YaHei UI', 10))
        self.recovery_suggestions_text.pack(fill=tk.X)

    def _create_qq_config_tab(self, parent, sender_email):
        """创建QQ配置标签页"""
        # 账户类型配置
        account_frame = ttk.LabelFrame(parent, text="👤 账户配置", padding="10")
        account_frame.pack(fill=tk.X, pady=(0, 10))

        account_row = ttk.Frame(account_frame)
        account_row.pack(fill=tk.X)

        ttk.Label(account_row, text="账户类型:").pack(side=tk.LEFT, padx=(0, 5))
        self.qq_account_type_var = tk.StringVar(value="normal")
        account_combo = ttk.Combobox(account_row, textvariable=self.qq_account_type_var, width=15, state="readonly")
        account_combo['values'] = ["normal", "vip"]
        account_combo.pack(side=tk.LEFT, padx=(0, 20))

        ttk.Label(account_row, text="每日限额:").pack(side=tk.LEFT, padx=(0, 5))
        self.qq_daily_limit_config_var = tk.StringVar(value="100")
        ttk.Entry(account_row, textvariable=self.qq_daily_limit_config_var, width=8).pack(side=tk.LEFT, padx=(0, 20))

        ttk.Button(account_row, text="💾 保存配置",
                  command=lambda: self._save_qq_config(sender_email)).pack(side=tk.LEFT, padx=10)

        # QQ邮箱特殊设置
        special_frame = ttk.LabelFrame(parent, text="🎯 QQ邮箱特殊设置", padding="10")
        special_frame.pack(fill=tk.X, pady=(0, 10))

        # 安全发送时间
        time_frame = ttk.Frame(special_frame)
        time_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(time_frame, text="安全发送时间:").pack(side=tk.LEFT, padx=(0, 10))
        safe_hours_text = "9-11点, 14-17点 (QQ邮箱最佳发送时间)"
        ttk.Label(time_frame, text=safe_hours_text, font=('Microsoft YaHei UI', 9)).pack(side=tk.LEFT)

        # 避免关键词
        keywords_frame = ttk.Frame(special_frame)
        keywords_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(keywords_frame, text="避免关键词:").pack(side=tk.LEFT, padx=(0, 10))
        avoid_keywords_text = "营销, 推广, 广告, 优惠, 免费"
        ttk.Label(keywords_frame, text=avoid_keywords_text, font=('Microsoft YaHei UI', 9)).pack(side=tk.LEFT)

        # QQ邮箱使用建议
        tips_frame = ttk.LabelFrame(parent, text="💡 QQ邮箱使用建议", padding="10")
        tips_frame.pack(fill=tk.BOTH, expand=True)

        tips_text = """🎯 QQ邮箱发送优化建议

📧 发送频率控制:
• 普通用户: 每日最多100封，建议每小时不超过20封
• VIP用户: 每日最多500封，建议每小时不超过50封
• 批次大小: 建议每批3-5封邮件

⏰ 最佳发送时间:
• 上午: 9:00-11:00 (工作开始时间)
• 下午: 14:00-17:00 (工作时间)
• 避免: 晚上和周末发送

📝 内容优化:
• 避免营销敏感词汇
• 添加个性化称呼
• 使用自然语言，避免模板化
• 主题简洁明了，不要全大写

🚨 应急机制:
• 连续5封无回复自动触发应急模式
• 应急模式下暂停发送30分钟
• 自动调整发送策略和频率
• 建议更换邮件内容和主题

🔧 恢复策略:
• 发送测试邮件验证恢复
• 逐步恢复正常发送频率
• 监控回复率变化
• 必要时更换发件人邮箱
"""

        tips_display = scrolledtext.ScrolledText(tips_frame, height=20, font=('Microsoft YaHei UI', 9))
        tips_display.pack(fill=tk.BOTH, expand=True)
        tips_display.insert(tk.END, tips_text)
        tips_display.config(state='disabled')

    def _refresh_qq_emergency_data(self, notebook, sender_email):
        """刷新QQ应急数据"""
        try:
            # 获取QQ应急状态
            emergency_status = self.qq_emergency_manager.get_qq_emergency_status(sender_email)

            # 更新应急状态显示
            self._update_qq_emergency_status_display(sender_email, emergency_status)

            # 更新实时指标
            self._update_qq_metrics_display(sender_email, emergency_status)

            # 更新应急历史
            self._update_emergency_history_display(emergency_status.get('emergency_history', []))

            # 更新监控日志
            self._refresh_qq_monitor_log(sender_email)

            self.log_message("✅ QQ应急数据刷新完成")

        except Exception as e:
            self.log_message(f"❌ 刷新QQ应急数据失败: {str(e)}")

    def _update_qq_emergency_status_display(self, sender_email, emergency_status):
        """更新QQ应急状态显示"""
        try:
            emergency_info = emergency_status.get('emergency_info', {})
            daily_stats = emergency_status.get('daily_stats', {})

            status_text = f"🆘 QQ邮箱应急状态 - {sender_email}\n"
            status_text += "=" * 60 + "\n\n"

            # 应急状态
            is_active = emergency_info.get('is_active', False)
            status_icon = "🚨" if is_active else "✅"
            status_text += f"{status_icon} 应急状态: {'激活' if is_active else '正常'}\n"

            if is_active:
                start_time = emergency_info.get('start_time', '')
                status_text += f"🕐 激活时间: {start_time[:16] if start_time else '未知'}\n"

            # 连续无回复统计
            consecutive_no_reply = emergency_info.get('consecutive_no_reply', 0)
            threshold = 5  # 默认阈值

            if consecutive_no_reply >= threshold:
                status_text += f"⚠️ 连续无回复: {consecutive_no_reply} 封 (已达阈值)\n"
            elif consecutive_no_reply >= threshold - 2:
                status_text += f"🟡 连续无回复: {consecutive_no_reply} 封 (接近阈值)\n"
            else:
                status_text += f"✅ 连续无回复: {consecutive_no_reply} 封 (正常)\n"

            # 今日统计
            status_text += f"\n📊 今日发送统计:\n"
            status_text += f"  📧 总发送: {daily_stats.get('total_sent', 0)} 封\n"
            status_text += f"  📬 有回复: {daily_stats.get('total_replies', 0)} 封\n"
            status_text += f"  📭 无回复: {daily_stats.get('no_replies', 0)} 封\n"
            status_text += f"  📈 回复率: {daily_stats.get('reply_rate', 0):.1f}%\n"

            # 应急原因
            if is_active:
                reason = emergency_info.get('reason', '未知')
                status_text += f"\n🚨 触发原因: {reason}\n"

            if hasattr(self, 'qq_emergency_status_text'):
                self.qq_emergency_status_text.delete(1.0, tk.END)
                self.qq_emergency_status_text.insert(1.0, status_text)

        except Exception as e:
            self.log_message(f"❌ 更新应急状态显示失败: {str(e)}")

    def _update_qq_metrics_display(self, sender_email, emergency_status):
        """更新QQ指标显示"""
        try:
            daily_stats = emergency_status.get('daily_stats', {})
            emergency_info = emergency_status.get('emergency_info', {})

            # 更新指标变量
            self.qq_daily_sent_var.set(str(daily_stats.get('total_sent', 0)))
            self.qq_daily_limit_var.set("100")  # 默认限额，可以从配置获取
            self.qq_consecutive_no_reply_var.set(str(emergency_info.get('consecutive_no_reply', 0)))
            self.qq_emergency_active_var.set("是" if emergency_info.get('is_active', False) else "否")

        except Exception as e:
            self.log_message(f"❌ 更新QQ指标显示失败: {str(e)}")

    def _update_emergency_history_display(self, emergency_history):
        """更新应急历史显示"""
        try:
            # 清空现有数据
            for item in self.emergency_history_tree.get_children():
                self.emergency_history_tree.delete(item)

            # 添加历史记录
            for record in emergency_history:
                trigger_time = record.get('trigger_time', '')[:16]
                reason = record.get('reason', '')
                recovery_time = record.get('recovery_time', '')
                recovery_time_display = recovery_time[:16] if recovery_time else "未恢复"
                success = "成功" if record.get('success', False) else "失败"

                self.emergency_history_tree.insert('', 'end', values=(
                    trigger_time, reason, recovery_time_display, success
                ))

        except Exception as e:
            self.log_message(f"❌ 更新应急历史失败: {str(e)}")

    def _refresh_qq_monitor_log(self, sender_email):
        """刷新QQ监控日志"""
        try:
            # 获取最近的发送记录
            import sqlite3

            conn = sqlite3.connect(self.qq_emergency_manager.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT send_time, recipient_email, subject, has_auto_reply, reply_time
                FROM qq_sending_records
                WHERE sender_email = ?
                ORDER BY send_time DESC
                LIMIT 50
            ''', (sender_email,))

            records = cursor.fetchall()
            conn.close()

            # 更新监控日志
            if hasattr(self, 'qq_monitor_log_text'):
                self.qq_monitor_log_text.delete(1.0, tk.END)

                log_text = f"📡 QQ邮箱发送监控日志 - {sender_email}\n"
                log_text += "=" * 80 + "\n\n"

                if records:
                    log_text += f"{'时间':<16} {'收件人':<25} {'主题':<20} {'回复状态':<10} {'回复时间':<16}\n"
                    log_text += "-" * 80 + "\n"

                    for record in records:
                        send_time = record[0][:16] if record[0] else ""
                        recipient = record[1][:25] if record[1] else ""
                        subject = record[2][:20] if record[2] else ""
                        has_reply = "有回复" if record[3] else "无回复"
                        reply_time = record[4][:16] if record[4] else ""

                        log_text += f"{send_time:<16} {recipient:<25} {subject:<20} {has_reply:<10} {reply_time:<16}\n"
                else:
                    log_text += "📭 暂无发送记录\n"

                self.qq_monitor_log_text.insert(tk.END, log_text)
                self.qq_monitor_log_text.see(tk.END)

        except Exception as e:
            self.log_message(f"❌ 刷新监控日志失败: {str(e)}")

    def _save_emergency_threshold(self, sender_email):
        """保存应急阈值"""
        try:
            threshold = int(self.emergency_threshold_var.get())

            # 更新数据库配置
            import sqlite3
            conn = sqlite3.connect(self.qq_emergency_manager.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE qq_email_config
                SET emergency_threshold = ?, updated_at = ?
                WHERE sender_email = ?
            ''', (threshold, datetime.datetime.now().isoformat(), sender_email))

            conn.commit()
            conn.close()

            self.log_message(f"✅ 应急阈值已更新为: {threshold} 封")
            messagebox.showinfo("保存成功", f"应急阈值已设置为 {threshold} 封邮件")

        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的数字")
        except Exception as e:
            error_msg = f"保存应急阈值失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("保存失败", error_msg)

    def _qq_test_send(self, sender_email):
        """QQ测试发送"""
        try:
            # 创建测试发送对话框
            test_window = tk.Toplevel(self.root)
            test_window.title("🧪 QQ邮箱测试发送")
            test_window.geometry("600x400")
            test_window.transient(self.root)
            test_window.grab_set()

            main_frame = ttk.Frame(test_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(main_frame, text="🧪 QQ邮箱测试发送",
                     font=('Microsoft YaHei UI', 14, 'bold')).pack(pady=(0, 15))

            # 测试收件人
            recipient_frame = ttk.Frame(main_frame)
            recipient_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(recipient_frame, text="测试收件人:").pack(side=tk.LEFT, padx=(0, 5))
            test_recipient_var = tk.StringVar(value="<EMAIL>")
            ttk.Entry(recipient_frame, textvariable=test_recipient_var, width=30).pack(side=tk.LEFT, padx=(0, 10))

            # 测试主题
            subject_frame = ttk.Frame(main_frame)
            subject_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(subject_frame, text="测试主题:").pack(side=tk.LEFT, padx=(0, 5))
            test_subject_var = tk.StringVar(value="QQ邮箱测试 - 请回复确认收到")
            ttk.Entry(subject_frame, textvariable=test_subject_var, width=40).pack(side=tk.LEFT)

            # 测试内容
            content_frame = ttk.LabelFrame(main_frame, text="测试内容", padding="5")
            content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            test_content = """这是一封QQ邮箱测试邮件。

如果您收到这封邮件，请回复"收到"确认。

这将帮助我们验证邮件发送功能是否正常。

谢谢您的配合！"""

            test_content_text = tk.Text(content_frame, height=8, font=('Microsoft YaHei UI', 10))
            test_content_text.pack(fill=tk.BOTH, expand=True)
            test_content_text.insert(tk.END, test_content)

            # 按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            def send_test():
                try:
                    recipient = test_recipient_var.get().strip()
                    subject = test_subject_var.get().strip()
                    content = test_content_text.get(1.0, tk.END).strip()

                    if not recipient or not subject:
                        messagebox.showwarning("输入错误", "请填写收件人和主题")
                        return

                    # 这里可以调用实际的发送功能
                    self.log_message(f"🧪 发送测试邮件到: {recipient}")
                    messagebox.showinfo("测试发送", f"测试邮件已发送到: {recipient}\n\n请检查收件箱并观察是否有自动回复。")
                    test_window.destroy()

                except Exception as e:
                    messagebox.showerror("发送失败", f"测试发送失败: {str(e)}")

            ttk.Button(button_frame, text="📧 发送测试", command=send_test).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="取消", command=test_window.destroy).pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            error_msg = f"打开测试发送失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _manual_emergency_activation(self, sender_email):
        """手动激活应急模式"""
        try:
            result = messagebox.askyesno(
                "确认激活应急模式",
                f"确定要手动激活 {sender_email} 的应急模式吗？\n\n"
                "激活后将：\n"
                "• 暂停当前发送\n"
                "• 大幅降低发送频率\n"
                "• 启动恢复策略\n\n"
                "建议只在检测到明显问题时使用。"
            )

            if result:
                # 手动激活应急模式
                emergency_info = {
                    'should_trigger': True,
                    'consecutive_no_reply': 999,  # 标记为手动触发
                    'reason': '手动激活应急模式'
                }

                self.qq_emergency_manager._activate_emergency_mode(sender_email, emergency_info)

                self.log_message(f"🚨 手动激活应急模式: {sender_email}")
                messagebox.showinfo("应急模式激活", "应急模式已激活，系统将自动执行恢复策略。")

                # 刷新显示
                self._refresh_qq_emergency_data(None, sender_email)

        except Exception as e:
            error_msg = f"手动激活应急模式失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("激活失败", error_msg)

    def _manual_emergency_deactivation(self, sender_email):
        """手动退出应急模式"""
        try:
            result = messagebox.askyesno(
                "确认退出应急模式",
                f"确定要手动退出 {sender_email} 的应急模式吗？\n\n"
                "退出后将：\n"
                "• 恢复正常发送频率\n"
                "• 停止应急限制\n\n"
                "请确保问题已经解决。"
            )

            if result:
                # 手动退出应急模式
                self.qq_emergency_manager._deactivate_emergency_mode(sender_email, "手动退出应急模式")

                self.log_message(f"✅ 手动退出应急模式: {sender_email}")
                messagebox.showinfo("应急模式退出", "已退出应急模式，恢复正常发送。")

                # 刷新显示
                self._refresh_qq_emergency_data(None, sender_email)

        except Exception as e:
            error_msg = f"手动退出应急模式失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("退出失败", error_msg)

    def _personalize_content(self, body, sequence):
        """轻微个性化邮件内容以避免被识别为垃圾邮件"""
        if not body.strip():
            return body  # 空正文不做处理

        # 检查用户是否选择添加个性化后缀
        if not self.add_personalization.get():
            return body  # 用户未选择添加个性化，直接返回原始正文

        import datetime

        # 添加时间戳（精确到秒，确保每封邮件都不同）
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 在正文末尾添加轻微的个性化信息（不影响用户体验）
        personalized_body = body

        # 如果正文不为空且用户选择了个性化，添加个性化后缀
        if body.strip():
            # 使用序号和时间戳确保每封邮件都不同
            personalized_body += f"\n\n---\n邮件编号: #{sequence:03d} | 发送时间: {timestamp}"

        return personalized_body

    def _on_key_press(self, event):
        """处理按键事件，支持输入法"""
        # 允许所有按键事件通过，确保输入法正常工作
        # event参数用于接收按键事件信息
        return None

    def _on_click(self, event):
        """处理鼠标点击事件"""
        # 确保文本框获得焦点，支持输入法
        # event参数用于接收鼠标点击事件信息
        self.body.focus_set()
        return None

    def _create_emoji_menu(self):
        """创建Emoji右键菜单"""
        self.emoji_menu = tk.Menu(self.root, tearoff=0)

        # 常用Emoji分类
        emoji_categories = {
            "笑脸表情": ["😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇"],
            "爱心表情": ["❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔"],
            "手势表情": ["👍", "👎", "👌", "🤏", "✌️", "🤞", "🤟", "🤘", "🤙", "👈"],
            "动物表情": ["🐶", "🐱", "🐭", "🐹", "🐰", "🦊", "🐻", "🐼", "🐨", "🐯"],
            "食物表情": ["🍎", "🍊", "🍋", "🍌", "🍉", "🍇", "🍓", "🫐", "🍈", "🍒"]
        }

        for category, emojis in emoji_categories.items():
            category_menu = tk.Menu(self.emoji_menu, tearoff=0)
            for emoji in emojis:
                category_menu.add_command(
                    label=f"{emoji} {emoji}",
                    command=lambda e=emoji: self._insert_emoji(e)
                )
            self.emoji_menu.add_cascade(label=category, menu=category_menu)

        self.emoji_menu.add_separator()
        self.emoji_menu.add_command(label="打开Windows Emoji面板 (Win+.)",
                                   command=self._open_emoji_panel)

        # 绑定右键菜单
        self.body.bind("<Button-3>", self._show_emoji_menu)

    def _insert_emoji(self, emoji):
        """插入Emoji到当前光标位置"""
        try:
            cursor_pos = self.body.index(tk.INSERT)
            self.body.insert(cursor_pos, emoji)
            self.body.focus_set()
        except Exception as e:
            self.log_message(f"插入Emoji失败: {str(e)}")

    def _show_emoji_menu(self, event):
        """显示Emoji右键菜单"""
        try:
            self.emoji_menu.tk_popup(event.x_root, event.y_root)
        except Exception as e:
            self.log_message(f"显示Emoji菜单失败: {str(e)}")
        finally:
            self.emoji_menu.grab_release()

    def _open_emoji_panel(self):
        """打开Windows Emoji面板"""
        try:
            import subprocess
            import os
            if os.name == 'nt':  # Windows系统
                # 使用Windows快捷键打开Emoji面板
                subprocess.run(['powershell', '-Command',
                               'Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait("^{ESC}")'],
                               shell=True)
                self.log_message("已尝试打开Windows Emoji面板，请使用 Win+. 快捷键")
            else:
                self.log_message("当前系统不支持Windows Emoji面板")
        except Exception as e:
            self.log_message(f"打开Emoji面板失败: {str(e)}")
            self.log_message("请手动使用 Win+. 快捷键打开Emoji面板")

    def open_emoji_helper(self):
        """打开Emoji助手"""
        try:
            from emoji_helper import EmojiHelper
            helper = EmojiHelper()
            helper.create_emoji_window(self.body)
            self.log_message("已打开Emoji表情选择器")
        except ImportError:
            self.log_message("Emoji助手模块未找到，请使用 Win+. 快捷键打开系统表情面板")
            messagebox.showwarning("提示", "Emoji助手模块未找到\n请使用 Win+. 快捷键打开系统表情面板")
        except Exception as e:
            self.log_message(f"打开Emoji助手失败: {str(e)}")
            messagebox.showerror("错误", f"打开Emoji助手失败：{str(e)}")

    def load_auth_codes(self):
        """加载保存的授权码"""
        try:
            import json
            if os.path.exists('auth_codes.json'):
                with open('auth_codes.json', 'r', encoding='utf-8') as f:
                    self.auth_codes = json.load(f)
                self.log_message(f"已加载 {len(self.auth_codes)} 个授权码")
            else:
                self.auth_codes = {}
        except Exception as e:
            self.log_message(f"加载授权码失败: {str(e)}")
            self.auth_codes = {}

    def save_auth_codes(self):
        """保存授权码到文件"""
        try:
            import json
            with open('auth_codes.json', 'w', encoding='utf-8') as f:
                json.dump(self.auth_codes, f, ensure_ascii=False, indent=2)
            self.log_message("授权码已保存")
        except Exception as e:
            self.log_message(f"保存授权码失败: {str(e)}")

    def manage_auth_codes(self):
        """管理授权码"""
        auth_window = tk.Toplevel(self.root)
        auth_window.title("授权码管理")
        auth_window.geometry("600x400")
        auth_window.resizable(True, True)

        # 设置为模态窗口
        auth_window.transient(self.root)
        auth_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(auth_window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        auth_window.rowconfigure(0, weight=1)
        auth_window.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        main_frame.columnconfigure(0, weight=1)

        # 标题
        ttk.Label(main_frame, text="授权码管理",
                 font=('Arial', 14, 'bold')).grid(row=0, column=0, pady=(0, 10))

        # 授权码列表
        list_frame = ttk.LabelFrame(main_frame, text="已保存的授权码", padding="5")
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        list_frame.rowconfigure(0, weight=1)
        list_frame.columnconfigure(0, weight=1)

        # 创建Treeview显示授权码
        columns = ('邮箱', '授权码', '添加时间')
        self.auth_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        # 设置列标题
        self.auth_tree.heading('邮箱', text='邮箱地址')
        self.auth_tree.heading('授权码', text='授权码')
        self.auth_tree.heading('添加时间', text='添加时间')

        # 设置列宽
        self.auth_tree.column('邮箱', width=200)
        self.auth_tree.column('授权码', width=200)
        self.auth_tree.column('添加时间', width=150)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.auth_tree.yview)
        self.auth_tree.configure(yscrollcommand=scrollbar.set)

        self.auth_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 刷新授权码列表
        self.refresh_auth_list()

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, pady=10)

        ttk.Button(btn_frame, text="添加授权码",
                  command=self.add_auth_code).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除选中",
                  command=self.delete_auth_code).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="使用选中",
                  command=lambda: self.use_auth_code(auth_window)).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="关闭",
                  command=auth_window.destroy).pack(side=tk.LEFT, padx=5)

    def refresh_auth_list(self):
        """刷新授权码列表"""
        # 清空现有项目
        for item in self.auth_tree.get_children():
            self.auth_tree.delete(item)

        # 添加授权码项目
        for email, info in self.auth_codes.items():
            auth_code = info.get('auth_code', '')
            add_time = info.get('add_time', '未知')
            # 隐藏部分授权码
            masked_code = auth_code[:4] + '*' * 8 + auth_code[-4:] if len(auth_code) > 8 else auth_code
            self.auth_tree.insert('', tk.END, values=(email, masked_code, add_time))

    def add_auth_code(self):
        """添加新的授权码"""
        add_window = tk.Toplevel(self.root)
        add_window.title("添加授权码")
        add_window.geometry("400x200")
        add_window.resizable(False, False)

        # 设置为模态窗口
        add_window.transient(self.root)
        add_window.grab_set()

        # 主框架
        frame = ttk.Frame(add_window, padding="20")
        frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 邮箱地址
        ttk.Label(frame, text="邮箱地址:").grid(row=0, column=0, sticky=tk.W, pady=5)
        email_entry = ttk.Entry(frame, width=30)
        email_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=5)
        email_entry.insert(0, "@qq.com")

        # 授权码
        ttk.Label(frame, text="授权码:").grid(row=1, column=0, sticky=tk.W, pady=5)
        auth_entry = ttk.Entry(frame, width=30, show="*")
        auth_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)

        # 按钮
        btn_frame = ttk.Frame(frame)
        btn_frame.grid(row=2, column=0, columnspan=2, pady=20)

        def save_auth():
            email = email_entry.get().strip()
            auth_code = auth_entry.get().strip()

            if not email or not email.endswith('@qq.com'):
                messagebox.showerror("错误", "请输入有效的QQ邮箱地址")
                return

            if not auth_code:
                messagebox.showerror("错误", "请输入授权码")
                return

            # 保存授权码
            import datetime
            self.auth_codes[email] = {
                'auth_code': auth_code,
                'add_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            self.save_auth_codes()
            self.refresh_auth_list()
            self.log_message(f"已添加授权码: {email}")

            add_window.destroy()
            messagebox.showinfo("成功", "授权码添加成功！")

        ttk.Button(btn_frame, text="保存", command=save_auth).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=add_window.destroy).pack(side=tk.LEFT, padx=5)

    def delete_auth_code(self):
        """删除选中的授权码"""
        selection = self.auth_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的授权码")
            return

        item = self.auth_tree.item(selection[0])
        email = item['values'][0]

        if messagebox.askyesno("确认删除", f"确定要删除 {email} 的授权码吗？"):
            if email in self.auth_codes:
                del self.auth_codes[email]
                self.save_auth_codes()
                self.refresh_auth_list()
                self.log_message(f"已删除授权码: {email}")
                messagebox.showinfo("成功", "授权码删除成功！")

    def use_auth_code(self, auth_window):
        """使用选中的授权码"""
        selection = self.auth_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要使用的授权码")
            return

        item = self.auth_tree.item(selection[0])
        email = item['values'][0]

        # 设置发送者邮箱
        self.sender_email.delete(0, tk.END)
        self.sender_email.insert(0, email)

        # 更新配置文件中的授权码
        if email in self.auth_codes:
            auth_code = self.auth_codes[email]['auth_code']
            # 动态更新SMTP配置
            from config import SMTP_CONFIG
            SMTP_CONFIG['username'] = email
            SMTP_CONFIG['password'] = auth_code

            self.log_message(f"已切换到邮箱: {email}")
            messagebox.showinfo("成功", f"已切换到邮箱: {email}")
            auth_window.destroy()

    def send_complete(self, success, message="", sent_recipients=None, sender_email=None):
        """发送完成"""
        self.progress.stop()
        self.is_sending = False
        self.should_stop = False
        self.should_pause = False
        self.is_paused = False

        # 重置按钮状态
        self.send_button.config(state='normal')
        self.pause_button.config(state='disabled')
        self.stop_button.config(state='disabled')
        self.resume_button.config(state='disabled')

        # 检查是否有未完成的任务来决定是否启用断点继续按钮
        try:
            progress_file = "email_send_progress.json"
            if os.path.exists(progress_file):
                import json
                with open(progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)

                current_email = progress_data.get('current_email_index', 0)
                total_emails = progress_data.get('total_emails', 0)

                if current_email < total_emails:
                    self.continue_button.config(state='normal')
                else:
                    self.continue_button.config(state='disabled')
            else:
                self.continue_button.config(state='disabled')
        except:
            self.continue_button.config(state='disabled')

        # 如果有已发送的邮件，启用撤回按钮
        if self.sent_emails:
            self.recall_button.config(state='normal')

        if success:
            self.status_var.set("邮件发送成功")
            self.log_message("✅ 主系统邮件发送任务完成")

            # 🔧 深度协调系统：触发邮件发送事件
            if self.coordinator and sent_recipients and sender_email:
                try:
                    self.coordinator.data_center.emit_event(SystemEvent.EMAIL_SENT, {
                        'sender_email': sender_email,
                        'recipient_count': len(sent_recipients),
                        'recipients': sent_recipients,
                        'send_time': datetime.datetime.now().isoformat()
                    })
                    self.log_message(f"🔧 深度协调：已触发邮件发送事件 ({len(sent_recipients)} 个收件人)")
                except Exception as e:
                    self.log_message(f"⚠️ 深度协调事件触发失败: {str(e)}")

            # 📊 自动更新质量数据库
            if sent_recipients and sender_email:
                try:
                    self._update_quality_database_after_send(sender_email, sent_recipients)
                except Exception as e:
                    self.log_message(f"⚠️ 更新质量数据库失败: {str(e)}")

            # 🛡️ 自动更新反垃圾邮件统计
            if sent_recipients and sender_email:
                try:
                    self._update_anti_spam_stats_after_send(sender_email, sent_recipients)
                except Exception as e:
                    self.log_message(f"⚠️ 更新反垃圾邮件统计失败: {str(e)}")

            # 🆘 自动检测QQ邮箱应急状态
            if sent_recipients and sender_email and any(domain in sender_email.lower() for domain in ['qq.com', 'foxmail.com']):
                try:
                    self._auto_check_qq_emergency_after_send(sender_email, sent_recipients)
                except Exception as e:
                    self.log_message(f"⚠️ QQ应急检测失败: {str(e)}")

            # 🚀 自动启动回复监控（如果用户启用了该选项）
            if sent_recipients and sender_email and self.auto_start_reply_monitoring.get():
                self.log_message(f"🚀 邮件发送成功，自动启动回复监控 ({len(sent_recipients)} 个收件人)")
                try:
                    self.auto_start_reply_monitoring_func(sender_email, sent_recipients)
                except Exception as e:
                    self.log_message(f"⚠️ 自动启动回复监控失败: {str(e)}")
            elif sent_recipients and sender_email and not self.auto_start_reply_monitoring.get():
                self.log_message(f"💡 提示: 可点击'📬 自动回复监控'按钮手动监控 {len(sent_recipients)} 个收件人的回复状态")

            # 检查是否有待发送的队列任务
            pending_tasks = [task for task in self.email_queue if task['status'] == 'pending']
            if pending_tasks:
                self.log_message(f"🔍 检测到队列中有 {len(pending_tasks)} 个待发送任务")

                # 根据自动队列模式设置决定行为
                if self.auto_queue_mode.get():
                    # 自动模式：完全静默，直接启动队列
                    self.log_message("🤖 自动队列模式已启用，将自动启动队列发送")
                    self.log_message(f"📬 检测到队列中有 {len(pending_tasks)} 个待发送任务")
                    self.log_message("⏰ 3秒后自动开始队列发送...")
                    # 延迟3秒后启动队列发送，给用户一些准备时间
                    self.root.after(3000, self._auto_start_queue)
                else:
                    # 手动模式：询问用户是否启动
                    self.log_message("⚠️ 自动队列模式已禁用，询问用户是否启动队列")
                    auto_start = messagebox.askyesno(
                        "主系统发送完成",
                        f"{message or '主系统邮件发送成功！'}\n\n"
                        f"检测到队列中有 {len(pending_tasks)} 个待发送任务。\n"
                        f"是否立即开始队列发送？\n\n"
                        f"选择'是'：立即开始队列发送\n"
                        f"选择'否'：稍后手动启动\n\n"
                        f"提示：可在队列设置中启用'自动队列模式'来跳过此询问"
                    )

                    if auto_start:
                        self.log_message("🚀 用户选择手动启动队列发送")
                        # 延迟3秒后启动队列发送，给用户一些准备时间
                        self.root.after(3000, self._auto_start_queue)
                    else:
                        self.log_message("⏸️ 用户选择稍后手动启动队列")
                        messagebox.showinfo("提示", "队列任务已准备就绪，您可以随时点击'🚀 开始队列发送'按钮启动")
            else:
                # 没有队列任务时的处理
                if self.auto_queue_mode.get():
                    # 自动模式：只记录日志，不显示弹窗
                    self.log_message("✅ 主系统邮件发送成功！")
                    self.log_message("📭 队列中没有待发送任务")
                else:
                    # 手动模式：显示成功提示
                    messagebox.showinfo("成功", message or "邮件发送成功！")
        else:
            self.status_var.set("邮件发送失败")
            self.log_message("❌ 主系统邮件发送任务失败")
            messagebox.showerror("失败", message or "邮件发送失败，请查看日志获取详细信息")

    def _auto_start_queue(self):
        """自动启动队列发送"""
        try:
            self.log_message("⏰ 3秒准备时间结束，开始自动启动队列发送")
            self.start_queue_sending(auto_mode=True)  # 传递auto_mode=True参数
        except Exception as e:
            self.log_message(f"❌ 自动启动队列发送失败: {str(e)}")
            messagebox.showerror("错误", f"自动启动队列发送失败：{str(e)}")

    def pause_sending(self):
        """暂停发送"""
        if self.is_sending:
            self.should_pause = True
            self.is_paused = True

            # 更新按钮状态
            self.pause_button.config(state='disabled')
            self.resume_button.config(state='normal')

            self.status_var.set("发送已暂停")
            self.log_message("⏸️ 发送已暂停，进度已保存")
            messagebox.showinfo("暂停发送", "发送已暂停，进度已保存。\n点击'恢复发送'可继续。")
        else:
            messagebox.showwarning("提示", "当前没有正在进行的发送任务")

    def resume_sending(self):
        """恢复发送"""
        if self.is_paused:
            self.should_pause = False
            self.is_paused = False

            # 更新按钮状态
            self.pause_button.config(state='normal')
            self.resume_button.config(state='disabled')

            # 如果有批次管理器，恢复发送
            if self.current_batch_manager:
                self.current_batch_manager.resume_sending()

            self.status_var.set("发送已恢复")
            self.log_message("▶️ 发送已恢复")
            messagebox.showinfo("恢复发送", "发送已恢复")
        else:
            messagebox.showwarning("提示", "当前没有暂停的发送任务")

    def continue_from_breakpoint(self):
        """从断点继续发送"""
        try:
            from batch_manager import BatchManager

            # 检查是否有进度文件
            progress_file = "email_send_progress.json"
            if not os.path.exists(progress_file):
                messagebox.showwarning("没有断点", "没有找到可恢复的发送进度")
                return

            import json
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)

            # 显示断点信息
            session_id = progress_data.get('session_id', 'unknown')
            send_mode = progress_data.get('send_mode', 'standard')
            current_email = progress_data.get('current_email_index', 0)
            total_emails = progress_data.get('total_emails', 0)
            success_count = progress_data.get('success_count', 0)
            failed_count = progress_data.get('failed_count', 0)

            info_message = f"""断点信息：

会话ID: {session_id}
发送模式: {send_mode}
总邮件数: {total_emails}
已发送成功: {success_count}
已发送失败: {failed_count}
当前位置: 第{current_email + 1}封
剩余邮件: {total_emails - current_email}

确定要从断点继续发送吗？"""

            if messagebox.askyesno("断点继续", info_message):
                # 设置断点继续标志
                self.resume_from_breakpoint = True
                self.breakpoint_session_id = session_id

                # 设置发送模式
                self.send_mode.set(send_mode)

                # 禁用断点继续按钮
                self.continue_button.config(state='disabled')

                self.log_message(f"🔄 已设置断点继续模式 - 会话: {session_id}")
                messagebox.showinfo("断点继续", "已设置断点继续模式。\n请点击'发送邮件'继续发送。")

        except Exception as e:
            self.log_message(f"❌ 读取断点信息失败: {str(e)}")
            messagebox.showerror("错误", f"读取断点信息失败：{str(e)}")

    def check_pending_tasks(self):
        """检查是否有未完成的发送任务（静默检查，减少弹窗）"""
        try:
            progress_file = "email_send_progress.json"
            if os.path.exists(progress_file):
                import json
                import datetime
                import os

                # 检查文件修改时间，如果是很久之前的文件，可能是测试残留
                file_mtime = os.path.getmtime(progress_file)
                file_time = datetime.datetime.fromtimestamp(file_mtime)
                now = datetime.datetime.now()
                hours_ago = (now - file_time).total_seconds() / 3600

                # 如果文件超过24小时没有修改，认为是测试残留，静默删除
                if hours_ago > 24:
                    self.log_message(f"🗑️ 发现过期的进度文件（{hours_ago:.1f}小时前），已自动清理")
                    os.remove(progress_file)
                    return

                with open(progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)

                session_id = progress_data.get('session_id', 'unknown')
                current_email = progress_data.get('current_email_index', 0)
                total_emails = progress_data.get('total_emails', 0)
                success_count = progress_data.get('success_count', 0)

                if current_email < total_emails:
                    # 有未完成的任务
                    self.continue_button.config(state='normal')
                    remaining = total_emails - current_email

                    # 只在日志中记录，不弹窗
                    self.log_message(f"🔍 发现未完成任务: {remaining} 封邮件待发送")
                    self.log_message(f"💡 提示: 可点击'🔄 断点继续'按钮恢复发送")

                    # 检查是否是测试会话（包含test字样）
                    if 'test' in session_id.lower():
                        self.log_message(f"⚠️ 检测到测试会话，如不需要可忽略")
                        return

                    # 只有在文件很新（1小时内）且不是测试会话时才弹窗询问
                    if hours_ago < 1:
                        message = f"""发现未完成的发送任务：

会话ID: {session_id}
总邮件数: {total_emails}
已发送: {success_count}
剩余: {remaining}

是否要继续发送？"""

                        if messagebox.askyesno("发现未完成任务", message):
                            self.continue_from_breakpoint()
                else:
                    # 任务已完成，删除进度文件
                    os.remove(progress_file)
                    self.log_message("✅ 已清理完成的任务进度文件")

        except Exception as e:
            self.log_message(f"检查未完成任务时出错: {str(e)}")

    def smart_startup_check(self):
        """智能启动检查（智能弹窗）"""
        try:
            # 检查启动配置
            startup_config = self._load_startup_config()

            # 自动清理过期文件
            if startup_config.get("auto_clean_old_progress", True):
                self._auto_clean_old_files(startup_config.get("max_progress_file_age_hours", 24))

            # 智能检查未完成任务
            self._intelligent_check_pending_tasks(startup_config)

        except Exception as e:
            self.log_message(f"智能启动检查失败: {str(e)}")
            # 降级到基本检查
            self.check_pending_tasks()

    def _load_startup_config(self):
        """加载启动配置"""
        try:
            import json
            if os.path.exists("startup_config.json"):
                with open("startup_config.json", "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception as e:
            self.log_message(f"加载启动配置失败: {str(e)}")

        # 返回默认配置
        return {
            "intelligent_popup": True,      # 智能弹窗（默认启用）
            "auto_clean_old_progress": True,
            "max_progress_file_age_hours": 24,
            "popup_for_test_data": False,   # 测试数据不弹窗
            "popup_for_old_tasks": False,   # 旧任务不弹窗
            "popup_threshold_hours": 1      # 1小时内的任务才弹窗
        }

    def _silent_check_pending_tasks(self):
        """静默检查未完成任务（不弹窗）"""
        try:
            progress_file = "email_send_progress.json"
            if os.path.exists(progress_file):
                import json
                with open(progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)

                session_id = progress_data.get('session_id', 'unknown')
                current_email = progress_data.get('current_email_index', 0)
                total_emails = progress_data.get('total_emails', 0)
                success_count = progress_data.get('success_count', 0)

                if current_email < total_emails:
                    # 有未完成的任务，只在日志中记录
                    self.continue_button.config(state='normal')
                    remaining = total_emails - current_email

                    self.log_message(f"🔍 发现未完成任务: {remaining} 封邮件待发送")
                    self.log_message(f"📋 会话ID: {session_id}")
                    self.log_message(f"💡 提示: 可点击'🔄 断点继续'按钮恢复发送")

                    # 在状态栏显示提示
                    self.status_var.set(f"发现 {remaining} 封未完成邮件，可点击断点继续")
                else:
                    # 任务已完成，删除进度文件
                    os.remove(progress_file)
                    self.log_message("✅ 已清理完成的任务进度文件")

        except Exception as e:
            self.log_message(f"静默检查未完成任务时出错: {str(e)}")

    def _auto_clean_old_files(self, max_age_hours):
        """自动清理过期文件"""
        try:
            import datetime

            files_to_check = [
                "email_send_progress.json",
                "temp_progress.json",
                "debug_progress.json"
            ]

            cleaned_count = 0
            for filename in files_to_check:
                if os.path.exists(filename):
                    file_mtime = os.path.getmtime(filename)
                    file_time = datetime.datetime.fromtimestamp(file_mtime)
                    now = datetime.datetime.now()
                    hours_ago = (now - file_time).total_seconds() / 3600

                    if hours_ago > max_age_hours:
                        os.remove(filename)
                        self.log_message(f"🗑️ 已自动清理过期文件: {filename} ({hours_ago:.1f}小时前)")
                        cleaned_count += 1

            if cleaned_count > 0:
                self.log_message(f"✨ 启动清理完成，共清理 {cleaned_count} 个过期文件")

        except Exception as e:
            self.log_message(f"自动清理文件失败: {str(e)}")

    def _intelligent_check_pending_tasks(self, config):
        """智能检查未完成任务（根据重要性决定是否弹窗）"""
        try:
            progress_file = "email_send_progress.json"
            if not os.path.exists(progress_file):
                return

            import json
            import datetime

            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)

            session_id = progress_data.get('session_id', 'unknown')
            current_email = progress_data.get('current_email_index', 0)
            total_emails = progress_data.get('total_emails', 0)
            success_count = progress_data.get('success_count', 0)

            if current_email >= total_emails:
                # 任务已完成，删除进度文件
                os.remove(progress_file)
                self.log_message("✅ 已清理完成的任务进度文件")
                return

            # 有未完成的任务，进行智能判断
            remaining = total_emails - current_email
            self.continue_button.config(state='normal')

            # 基础日志记录
            self.log_message(f"🔍 发现未完成任务: {remaining} 封邮件待发送")
            self.log_message(f"📋 会话ID: {session_id}")

            # 智能判断是否需要弹窗
            should_popup = self._should_popup_for_task(progress_data, config)

            if should_popup:
                # 重要任务，弹窗通知
                message = f"""发现重要的未完成发送任务：

会话ID: {session_id}
总邮件数: {total_emails}
已发送: {success_count}
剩余: {remaining}

是否要继续发送？"""

                self.log_message("🔔 重要任务，弹窗询问用户")
                if messagebox.askyesno("发现重要未完成任务", message):
                    self.continue_from_breakpoint()
            else:
                # 非重要任务，只在状态栏和日志中提示
                self.log_message("💡 提示: 可点击'🔄 断点继续'按钮恢复发送")
                self.status_var.set(f"发现 {remaining} 封未完成邮件，可点击断点继续")

        except Exception as e:
            self.log_message(f"智能检查未完成任务时出错: {str(e)}")

    def _should_popup_for_task(self, progress_data, config):
        """判断任务是否需要弹窗提示"""
        try:
            import datetime
            import os

            session_id = progress_data.get('session_id', 'unknown')

            # 1. 检查是否是测试数据
            if 'test' in session_id.lower():
                if not config.get("popup_for_test_data", False):
                    self.log_message("⚠️ 检测到测试会话，静默处理")
                    return False

            # 2. 检查文件时间
            progress_file = "email_send_progress.json"
            if os.path.exists(progress_file):
                file_mtime = os.path.getmtime(progress_file)
                file_time = datetime.datetime.fromtimestamp(file_mtime)
                now = datetime.datetime.now()
                hours_ago = (now - file_time).total_seconds() / 3600

                threshold_hours = config.get("popup_threshold_hours", 1)
                if hours_ago > threshold_hours:
                    if not config.get("popup_for_old_tasks", False):
                        self.log_message(f"⏰ 任务较旧（{hours_ago:.1f}小时前），静默处理")
                        return False

            # 3. 检查任务规模（小任务可能不重要）
            total_emails = progress_data.get('total_emails', 0)
            if total_emails <= 3:
                self.log_message("📧 小规模任务，静默处理")
                return False

            # 4. 检查是否启用智能弹窗
            if not config.get("intelligent_popup", True):
                return False

            # 通过所有检查，认为是重要任务
            self.log_message("⭐ 检测到重要任务，需要用户确认")
            return True

        except Exception as e:
            self.log_message(f"判断任务重要性时出错: {str(e)}")
            # 出错时默认弹窗，确保重要任务不被遗漏
            return True

    def stop_sending(self):
        """停止发送"""
        if self.is_sending or self.queue_mode:
            self.should_stop = True
            if self.queue_mode:
                self.log_message("⏹️ 正在停止队列发送，请稍候...")
                self.status_var.set("正在停止队列发送...")
                messagebox.showinfo("停止队列发送", "已发出停止指令，将在当前邮件发送完成后停止队列")
            else:
                self.log_message("⏹️ 正在停止发送，请稍候...")
                self.status_var.set("正在停止发送...")
                messagebox.showinfo("停止发送", "已发出停止指令，将在当前邮件发送完成后停止")
        else:
            messagebox.showwarning("提示", "当前没有正在发送的邮件或队列")

    def send_recall_email(self):
        """发送撤回邮件"""
        if not self.sent_emails:
            messagebox.showwarning("提示", "没有已发送的邮件可以撤回")
            return

        # 创建撤回邮件选择窗口
        recall_window = tk.Toplevel(self.root)
        recall_window.title("发送撤回邮件")
        recall_window.geometry("600x500")
        recall_window.transient(self.root)
        recall_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(recall_window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        recall_window.columnconfigure(0, weight=1)
        recall_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 标题
        ttk.Label(main_frame, text="选择要撤回的邮件",
                 font=('Arial', 14, 'bold')).grid(row=0, column=0, pady=(0, 10))

        # 邮件列表框架
        list_frame = ttk.LabelFrame(main_frame, text="已发送的邮件", padding="5")
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # 创建Treeview显示邮件列表
        columns = ('时间', '收件人', '主题', '批次')
        tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 设置列标题
        tree.heading('时间', text='发送时间')
        tree.heading('收件人', text='收件人')
        tree.heading('主题', text='主题')
        tree.heading('批次', text='批次')

        # 设置列宽
        tree.column('时间', width=150)
        tree.column('收件人', width=200)
        tree.column('主题', width=200)
        tree.column('批次', width=80)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 填充邮件数据
        for i, email_record in enumerate(self.sent_emails):
            time_str = email_record['send_time'].strftime('%Y-%m-%d %H:%M:%S')
            tree.insert('', 'end', values=(
                time_str,
                email_record['recipient'],
                email_record['subject'][:30] + '...' if len(email_record['subject']) > 30 else email_record['subject'],
                f"第{email_record['batch_id']}批"
            ))

        # 撤回内容框架
        content_frame = ttk.LabelFrame(main_frame, text="撤回邮件内容", padding="5")
        content_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=10)
        content_frame.columnconfigure(1, weight=1)

        ttk.Label(content_frame, text="撤回主题:").grid(row=0, column=0, sticky=tk.W, pady=2)
        recall_subject = ttk.Entry(content_frame, width=50)
        recall_subject.insert(0, "邮件撤回通知")
        recall_subject.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))

        ttk.Label(content_frame, text="撤回内容:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=2)
        recall_body = scrolledtext.ScrolledText(content_frame, width=50, height=8)
        recall_body.insert(tk.END, """尊敬的收件人：

我之前发送给您的邮件需要撤回，请忽略该邮件内容。

如有任何疑问，请联系我。

谢谢！""")
        recall_body.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=3, column=0, pady=10)

        def send_recall_to_selected():
            """发送撤回邮件给选中的收件人"""
            selected_items = tree.selection()
            if not selected_items:
                messagebox.showwarning("提示", "请选择要撤回的邮件")
                return

            subject = recall_subject.get().strip()
            body = recall_body.get(1.0, tk.END).strip()

            if not subject:
                messagebox.showerror("错误", "请输入撤回邮件主题")
                return

            if not body:
                messagebox.showerror("错误", "请输入撤回邮件内容")
                return

            # 获取选中的邮件
            selected_emails = []
            for item in selected_items:
                values = tree.item(item)['values']
                recipient = values[1]
                selected_emails.append(recipient)

            # 确认发送
            if messagebox.askyesno("确认撤回",
                                 f"确定要向 {len(selected_emails)} 个收件人发送撤回邮件吗？\n\n"
                                 f"收件人：{', '.join(selected_emails[:3])}"
                                 f"{'...' if len(selected_emails) > 3 else ''}"):

                # 发送撤回邮件
                self._send_recall_emails(selected_emails, subject, body)
                recall_window.destroy()

        def send_recall_to_all():
            """发送撤回邮件给所有收件人"""
            subject = recall_subject.get().strip()
            body = recall_body.get(1.0, tk.END).strip()

            if not subject:
                messagebox.showerror("错误", "请输入撤回邮件主题")
                return

            if not body:
                messagebox.showerror("错误", "请输入撤回邮件内容")
                return

            all_emails = [record['recipient'] for record in self.sent_emails]
            unique_emails = list(set(all_emails))  # 去重

            if messagebox.askyesno("确认撤回",
                                 f"确定要向所有 {len(unique_emails)} 个收件人发送撤回邮件吗？"):
                self._send_recall_emails(unique_emails, subject, body)
                recall_window.destroy()

        ttk.Button(btn_frame, text="撤回选中邮件", command=send_recall_to_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="一键撤回全部", command=send_recall_to_all).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="取消", command=recall_window.destroy).pack(side=tk.LEFT, padx=5)

        # 说明标签
        info_label = ttk.Label(main_frame,
                              text="注意：撤回邮件只是发送一封新的通知邮件，无法真正删除已发送的邮件",
                              font=('Arial', 9), foreground='red')
        info_label.grid(row=4, column=0, pady=5)

    def _send_recall_emails(self, recipients, subject, body):
        """实际发送撤回邮件"""
        try:
            sender_email = self.sender_email.get().strip()
            if not sender_email:
                messagebox.showerror("错误", "请先设置发送者邮箱")
                return

            # 获取授权码 - 修复授权码获取逻辑
            auth_info = self.auth_codes.get(sender_email)
            if not auth_info:
                messagebox.showerror("错误", "请先设置邮箱授权码")
                return

            # 提取授权码（支持新旧格式）
            if isinstance(auth_info, dict):
                auth_code = auth_info.get('auth_code', '')
            else:
                auth_code = auth_info  # 兼容旧格式

            if not auth_code:
                messagebox.showerror("错误", "授权码为空，请重新设置")
                return

            from email_sender import EmailSender
            sender = EmailSender(sender_email)
            sender.smtp_config['password'] = auth_code

            success_count = 0
            failed_count = 0

            self.log_message(f"开始发送撤回邮件给 {len(recipients)} 个收件人...")

            for i, email in enumerate(recipients, 1):
                self.log_message(f"正在发送撤回邮件 {i}/{len(recipients)} 给: {email}")

                try:
                    success = sender.send_email(
                        to_emails=[email],
                        subject=subject,
                        body=body
                    )

                    if success:
                        success_count += 1
                        self.log_message(f"✓ 撤回邮件发送成功: {email}")
                    else:
                        failed_count += 1
                        self.log_message(f"✗ 撤回邮件发送失败: {email}")

                except Exception as e:
                    failed_count += 1
                    self.log_message(f"✗ 撤回邮件发送异常: {email} - {str(e)}")

                # 添加延迟
                if i < len(recipients):
                    import time
                    time.sleep(2)  # 撤回邮件使用较短间隔

            # 显示结果
            result_msg = f"撤回邮件发送完成！\n成功: {success_count}\n失败: {failed_count}"
            if success_count > 0:
                messagebox.showinfo("撤回完成", result_msg)
            else:
                messagebox.showerror("撤回失败", result_msg)

        except Exception as e:
            self.log_message(f"发送撤回邮件时出错: {str(e)}")
            messagebox.showerror("错误", f"发送撤回邮件时出错：{str(e)}")

    def show_send_history(self):
        """显示发送历史"""
        if not self.sent_emails:
            messagebox.showinfo("提示", "暂无发送记录")
            return

        # 创建历史记录窗口
        history_window = tk.Toplevel(self.root)
        history_window.title("发送历史记录")
        history_window.geometry("800x600")
        history_window.transient(self.root)

        # 主框架
        main_frame = ttk.Frame(history_window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        history_window.columnconfigure(0, weight=1)
        history_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 标题
        ttk.Label(main_frame, text=f"发送历史记录 (共 {len(self.sent_emails)} 封邮件)",
                 font=('Arial', 14, 'bold')).grid(row=0, column=0, pady=(0, 10))

        # 创建Treeview
        columns = ('序号', '时间', '收件人', '主题', '批次')
        tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=20)

        # 设置列标题和宽度
        tree.heading('序号', text='序号')
        tree.heading('时间', text='发送时间')
        tree.heading('收件人', text='收件人')
        tree.heading('主题', text='主题')
        tree.heading('批次', text='批次')

        tree.column('序号', width=60)
        tree.column('时间', width=150)
        tree.column('收件人', width=200)
        tree.column('主题', width=250)
        tree.column('批次', width=80)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))

        # 填充数据
        for i, email_record in enumerate(self.sent_emails, 1):
            time_str = email_record['send_time'].strftime('%Y-%m-%d %H:%M:%S')
            tree.insert('', 'end', values=(
                i,
                time_str,
                email_record['recipient'],
                email_record['subject'],
                f"第{email_record['batch_id']}批"
            ))

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, columnspan=2, pady=10)

        def export_history():
            """导出发送历史"""
            try:
                import csv
                from tkinter import filedialog

                filename = filedialog.asksaveasfilename(
                    defaultextension=".csv",
                    filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
                    title="保存发送历史"
                )

                if filename:
                    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                        writer = csv.writer(csvfile)
                        writer.writerow(['序号', '发送时间', '收件人', '主题', '批次'])

                        for i, record in enumerate(self.sent_emails, 1):
                            writer.writerow([
                                i,
                                record['send_time'].strftime('%Y-%m-%d %H:%M:%S'),
                                record['recipient'],
                                record['subject'],
                                f"第{record['batch_id']}批"
                            ])

                    messagebox.showinfo("成功", f"发送历史已导出到：{filename}")

            except Exception as e:
                messagebox.showerror("错误", f"导出失败：{str(e)}")

        ttk.Button(btn_frame, text="导出历史", command=export_history).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="关闭", command=history_window.destroy).pack(side=tk.LEFT, padx=5)

    def clear_send_history(self):
        """清空发送记录"""
        if not self.sent_emails:
            messagebox.showinfo("提示", "暂无发送记录")
            return

        if messagebox.askyesno("确认清空", f"确定要清空所有 {len(self.sent_emails)} 条发送记录吗？\n此操作不可恢复！"):
            self.sent_emails.clear()
            self.recall_button.config(state='disabled')
            self.log_message("已清空所有发送记录")
            messagebox.showinfo("成功", "发送记录已清空")

    def open_queue_system(self):
        """打开队列系统窗口"""
        # 使用内置的队列管理窗口
        self.manage_queue()

    def add_to_queue(self):
        """添加当前邮件到发送队列"""
        # 验证当前表单
        sender_email = self.sender_email.get().strip()
        recipient_emails = self.recipient_emails.get(1.0, tk.END).strip()
        subject = self.subject.get().strip()
        body = self.body.get(1.0, tk.END).strip()

        if not sender_email or not sender_email.endswith('@qq.com'):
            messagebox.showerror("错误", "请输入有效的QQ邮箱地址")
            return

        if not recipient_emails:
            messagebox.showerror("错误", "请输入收件人邮箱地址")
            return

        if not subject:
            subject = "来自自动化邮件助手的邮件"

        # 获取附件列表
        attachments = []
        if hasattr(self, 'attachment_files') and self.attachment_files:
            attachments = self.attachment_files.copy()

        # 创建邮件任务
        import datetime
        email_task = {
            'id': len(self.email_queue) + 1,
            'sender_email': sender_email,
            'recipient_emails': recipient_emails,
            'subject': subject,
            'body': body,
            'attachments': attachments,
            'send_mode': self.send_mode.get(),
            'add_personalization': self.add_personalization.get(),
            'created_time': datetime.datetime.now(),
            'status': 'pending'  # pending, sending, completed, failed
        }

        self.email_queue.append(email_task)
        self.update_queue_status()

        # 清空当前表单（可选）
        if messagebox.askyesno("添加成功",
                             f"邮件任务已添加到队列（第{len(self.email_queue)}个）\n\n是否清空当前表单以便添加下一个任务？"):
            self.clear_form()

        self.log_message(f"已添加邮件任务到队列：{subject} -> {len(recipient_emails.split())} 个收件人")

    def update_queue_status(self):
        """更新队列状态显示"""
        queue_count = len(self.email_queue)
        pending_count = len([task for task in self.email_queue if task['status'] == 'pending'])
        completed_count = len([task for task in self.email_queue if task['status'] == 'completed'])
        failed_count = len([task for task in self.email_queue if task['status'] == 'failed'])

        if queue_count == 0:
            self.queue_status_label.config(text="队列: 0 个任务")
            self.start_queue_button.config(state='disabled')
            self.continue_queue_button.config(state='disabled')
        else:
            status_text = f"队列: {queue_count} 个任务"
            if pending_count > 0:
                status_text += f" ({pending_count} 待发送"
                if completed_count > 0:
                    status_text += f", {completed_count} 已完成"
                if failed_count > 0:
                    status_text += f", {failed_count} 失败"
                status_text += ")"
            else:
                status_text += f" (全部完成: {completed_count}, 失败: {failed_count})"

            self.queue_status_label.config(text=status_text)

            # 更新开始队列按钮状态
            if pending_count > 0 and not self.queue_mode:
                self.start_queue_button.config(state='normal')
            else:
                self.start_queue_button.config(state='disabled')

            # 更新断点继续按钮状态
            if pending_count > 0 and not self.queue_mode:
                self.continue_queue_button.config(state='normal')
            else:
                self.continue_queue_button.config(state='disabled')

    def manage_queue(self):
        """队列管理窗口"""
        queue_window = tk.Toplevel(self.root)
        queue_window.title("邮件发送队列管理")
        queue_window.geometry("1000x700")
        queue_window.transient(self.root)

        # 主框架
        main_frame = ttk.Frame(queue_window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        queue_window.columnconfigure(0, weight=1)
        queue_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 标题
        title_text = f"邮件发送队列管理 (共 {len(self.email_queue)} 个任务)"
        ttk.Label(main_frame, text=title_text,
                 font=('Arial', 14, 'bold')).grid(row=0, column=0, pady=(0, 10))

        # 队列列表框架
        list_frame = ttk.LabelFrame(main_frame, text="队列任务列表", padding="5")
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)

        # 创建Treeview
        columns = ('ID', '状态', '发送者', '收件人数量', '主题', '发送模式', '创建时间')
        tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 设置列标题和宽度
        tree.heading('ID', text='ID')
        tree.heading('状态', text='状态')
        tree.heading('发送者', text='发送者')
        tree.heading('收件人数量', text='收件人数')
        tree.heading('主题', text='主题')
        tree.heading('发送模式', text='发送模式')
        tree.heading('创建时间', text='创建时间')

        tree.column('ID', width=50)
        tree.column('状态', width=80)
        tree.column('发送者', width=150)
        tree.column('收件人数量', width=80)
        tree.column('主题', width=200)
        tree.column('发送模式', width=100)
        tree.column('创建时间', width=150)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 填充队列数据
        def refresh_queue_list():
            # 清空现有数据
            for item in tree.get_children():
                tree.delete(item)

            # 重新填充数据
            for task in self.email_queue:
                recipient_count = len([email.strip() for email in
                                     task['recipient_emails'].replace(',', '\n').replace(';', '\n').split('\n')
                                     if email.strip()])

                status_text = {
                    'pending': '待发送',
                    'sending': '发送中',
                    'completed': '已完成',
                    'failed': '失败'
                }.get(task['status'], task['status'])

                mode_text = {
                    'fast': '快速',
                    'standard': '标准',
                    'safe': '安全'
                }.get(task['send_mode'], task['send_mode'])

                tree.insert('', 'end', values=(
                    task['id'],
                    status_text,
                    task['sender_email'],
                    recipient_count,
                    task['subject'][:30] + '...' if len(task['subject']) > 30 else task['subject'],
                    mode_text,
                    task['created_time'].strftime('%Y-%m-%d %H:%M:%S')
                ))

        refresh_queue_list()

        # 详情显示框架
        detail_frame = ttk.LabelFrame(main_frame, text="任务详情", padding="5")
        detail_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=10)
        detail_frame.columnconfigure(1, weight=1)

        # 详情显示控件
        detail_text = scrolledtext.ScrolledText(detail_frame, width=80, height=8, state=tk.DISABLED)
        detail_text.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)

        def show_task_detail(event):
            """显示选中任务的详情"""
            selection = tree.selection()
            if not selection:
                return

            item = tree.item(selection[0])
            task_id = item['values'][0]

            # 找到对应的任务
            task = None
            for t in self.email_queue:
                if t['id'] == task_id:
                    task = t
                    break

            if task:
                detail_text.config(state=tk.NORMAL)
                detail_text.delete(1.0, tk.END)

                detail_info = f"""任务ID: {task['id']}
状态: {task['status']}
发送者: {task['sender_email']}
发送模式: {task['send_mode']}
个性化设置: {'启用' if task['add_personalization'] else '禁用'}
创建时间: {task['created_time'].strftime('%Y-%m-%d %H:%M:%S')}

主题: {task['subject']}

收件人:
{task['recipient_emails']}

邮件正文:
{task['body']}

附件: {len(task['attachments'])} 个文件
{chr(10).join(task['attachments']) if task['attachments'] else '无附件'}
"""
                detail_text.insert(tk.END, detail_info)
                detail_text.config(state=tk.DISABLED)

        tree.bind('<<TreeviewSelect>>', show_task_detail)

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=3, column=0, pady=10)

        def edit_task():
            """编辑选中的任务"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要编辑的任务")
                return

            item = tree.item(selection[0])
            task_id = item['values'][0]

            # 找到对应的任务
            task = None
            task_index = -1
            for i, t in enumerate(self.email_queue):
                if t['id'] == task_id:
                    task = t
                    task_index = i
                    break

            if task and task['status'] == 'pending':
                # 将任务数据填充到主界面
                self.sender_email.delete(0, tk.END)
                self.sender_email.insert(0, task['sender_email'])

                self.recipient_emails.delete(1.0, tk.END)
                self.recipient_emails.insert(tk.END, task['recipient_emails'])

                self.subject.delete(0, tk.END)
                self.subject.insert(0, task['subject'])

                self.body.delete(1.0, tk.END)
                self.body.insert(tk.END, task['body'])

                self.send_mode.set(task['send_mode'])
                self.add_personalization.set(task['add_personalization'])

                # 删除原任务
                self.email_queue.pop(task_index)
                self.update_queue_status()
                refresh_queue_list()

                queue_window.destroy()
                messagebox.showinfo("成功", "任务已加载到主界面，您可以修改后重新添加到队列")
            else:
                messagebox.showwarning("提示", "只能编辑待发送状态的任务")

        def delete_task():
            """删除选中的任务"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要删除的任务")
                return

            item = tree.item(selection[0])
            task_id = item['values'][0]

            if messagebox.askyesno("确认删除", f"确定要删除任务 #{task_id} 吗？"):
                # 删除任务
                self.email_queue = [task for task in self.email_queue if task['id'] != task_id]
                self.update_queue_status()
                refresh_queue_list()

                # 清空详情显示
                detail_text.config(state=tk.NORMAL)
                detail_text.delete(1.0, tk.END)
                detail_text.config(state=tk.DISABLED)

        def clear_completed():
            """清空已完成的任务"""
            completed_count = len([task for task in self.email_queue if task['status'] in ['completed', 'failed']])
            if completed_count == 0:
                messagebox.showinfo("提示", "没有已完成的任务")
                return

            if messagebox.askyesno("确认清空", f"确定要清空 {completed_count} 个已完成的任务吗？"):
                self.email_queue = [task for task in self.email_queue if task['status'] not in ['completed', 'failed']]
                self.update_queue_status()
                refresh_queue_list()

        def add_new_task():
            """添加新任务到队列"""
            self.create_queue_task_editor(queue_window, refresh_queue_list)

        def edit_task_advanced():
            """高级编辑选中的任务"""
            selection = tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择要编辑的任务")
                return

            item = tree.item(selection[0])
            task_id = item['values'][0]

            # 找到对应的任务
            task = None
            for t in self.email_queue:
                if t['id'] == task_id:
                    task = t
                    break

            if task and task['status'] == 'pending':
                self.create_queue_task_editor(queue_window, refresh_queue_list, task)
            else:
                messagebox.showwarning("提示", "只能编辑待发送状态的任务")

        ttk.Button(btn_frame, text="➕ 新建任务", command=add_new_task, style='Accent.TButton').pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="✏️ 高级编辑", command=edit_task_advanced).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="📝 快速编辑", command=edit_task).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🗑️ 删除任务", command=delete_task).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🧹 清空已完成", command=clear_completed).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🔄 刷新列表", command=refresh_queue_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="关闭", command=queue_window.destroy).pack(side=tk.LEFT, padx=5)

    def create_queue_task_editor(self, parent_window, refresh_callback, edit_task=None):
        """创建队列任务编辑器"""
        editor_window = tk.Toplevel(parent_window)
        editor_window.title("队列任务编辑器" if edit_task else "新建队列任务")
        editor_window.geometry("800x700")
        editor_window.transient(parent_window)
        editor_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(editor_window, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        editor_window.columnconfigure(0, weight=1)
        editor_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        title_text = "编辑队列任务" if edit_task else "新建队列任务"
        ttk.Label(main_frame, text=title_text, style='Title.TLabel').grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 发送者邮箱
        ttk.Label(main_frame, text="发送者邮箱:").grid(row=1, column=0, sticky=tk.W, pady=5)
        sender_frame = ttk.Frame(main_frame)
        sender_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        sender_frame.columnconfigure(0, weight=1)

        sender_email = ttk.Entry(sender_frame, width=40)
        sender_email.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))

        def select_sender():
            """选择发送者邮箱"""
            if self.auth_codes:
                emails = list(self.auth_codes.keys())
                if emails:
                    # 创建选择窗口
                    select_window = tk.Toplevel(editor_window)
                    select_window.title("选择发送者邮箱")
                    select_window.geometry("400x300")
                    select_window.transient(editor_window)
                    select_window.grab_set()

                    ttk.Label(select_window, text="选择已配置的邮箱:", font=('Arial', 12, 'bold')).pack(pady=10)

                    listbox = tk.Listbox(select_window, height=8)
                    listbox.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

                    for email in emails:
                        listbox.insert(tk.END, email)

                    def confirm_select():
                        selection = listbox.curselection()
                        if selection:
                            selected_email = emails[selection[0]]
                            sender_email.delete(0, tk.END)
                            sender_email.insert(0, selected_email)
                            select_window.destroy()

                    btn_frame = ttk.Frame(select_window)
                    btn_frame.pack(pady=10)
                    ttk.Button(btn_frame, text="确定", command=confirm_select).pack(side=tk.LEFT, padx=5)
                    ttk.Button(btn_frame, text="取消", command=select_window.destroy).pack(side=tk.LEFT, padx=5)
            else:
                messagebox.showwarning("提示", "请先在主界面配置邮箱授权码")

        ttk.Button(sender_frame, text="选择邮箱", command=select_sender).grid(row=0, column=1)

        # 收件人邮箱
        ttk.Label(main_frame, text="收件人邮箱:").grid(row=2, column=0, sticky=(tk.W, tk.N), pady=5)
        recipient_emails = scrolledtext.ScrolledText(main_frame, width=50, height=4, font=('Arial', 9), wrap=tk.WORD)
        recipient_emails.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5)

        # 提示标签
        ttk.Label(main_frame, text="支持分号(;)、逗号(,)、换行分隔",
                 font=('Arial', 8), foreground='gray').grid(row=2, column=2, sticky=tk.W, padx=(10, 0))

        # 邮件主题
        ttk.Label(main_frame, text="邮件主题:").grid(row=3, column=0, sticky=tk.W, pady=5)
        subject = ttk.Entry(main_frame, width=50)
        subject.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5)

        # 邮件正文
        ttk.Label(main_frame, text="邮件正文:").grid(row=4, column=0, sticky=(tk.W, tk.N), pady=5)
        body = scrolledtext.ScrolledText(main_frame, width=50, height=8,
                                        font=('Microsoft YaHei UI', 10), wrap=tk.WORD)
        body.grid(row=4, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        main_frame.rowconfigure(4, weight=1)

        # 发送模式
        ttk.Label(main_frame, text="发送模式:").grid(row=5, column=0, sticky=tk.W, pady=5)
        send_mode_frame = ttk.Frame(main_frame)
        send_mode_frame.grid(row=5, column=1, sticky=tk.W, pady=5)

        send_mode = tk.StringVar(value="standard")
        ttk.Radiobutton(send_mode_frame, text="快速(30-60秒)", variable=send_mode, value="fast").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(send_mode_frame, text="标准(1-2分钟)", variable=send_mode, value="standard").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(send_mode_frame, text="安全(3-5分钟)", variable=send_mode, value="safe").pack(side=tk.LEFT, padx=5)

        # 个性化设置
        add_personalization = tk.BooleanVar(value=False)
        ttk.Checkbutton(main_frame, text="添加邮件编号和时间戳",
                       variable=add_personalization).grid(row=6, column=1, sticky=tk.W, pady=5)

        # 附件区域
        attachment_frame = ttk.LabelFrame(main_frame, text="附件", padding="5")
        attachment_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        attachment_frame.columnconfigure(0, weight=1)

        attachment_listbox = tk.Listbox(attachment_frame, height=3)
        attachment_listbox.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        def add_attachment():
            files = filedialog.askopenfilenames(title="选择附件文件")
            for file in files:
                if file not in attachment_listbox.get(0, tk.END):
                    attachment_listbox.insert(tk.END, file)

        def remove_attachment():
            selection = attachment_listbox.curselection()
            if selection:
                attachment_listbox.delete(selection[0])

        def clear_attachments():
            attachment_listbox.delete(0, tk.END)

        btn_frame = ttk.Frame(attachment_frame)
        btn_frame.grid(row=1, column=0, columnspan=3, pady=5)
        ttk.Button(btn_frame, text="添加附件", command=add_attachment).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="删除附件", command=remove_attachment).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="清空附件", command=clear_attachments).pack(side=tk.LEFT, padx=5)

        # 如果是编辑模式，填充现有数据
        if edit_task:
            sender_email.insert(0, edit_task['sender_email'])
            recipient_emails.insert(tk.END, edit_task['recipient_emails'])
            subject.insert(0, edit_task['subject'])
            body.insert(tk.END, edit_task['body'])
            send_mode.set(edit_task['send_mode'])
            add_personalization.set(edit_task['add_personalization'])
            for attachment in edit_task.get('attachments', []):
                attachment_listbox.insert(tk.END, attachment)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=8, column=0, columnspan=3, pady=20)

        def save_task():
            """保存任务"""
            # 验证输入
            sender = sender_email.get().strip()
            recipients = recipient_emails.get(1.0, tk.END).strip()
            subj = subject.get().strip()
            content = body.get(1.0, tk.END).strip()

            if not sender or not sender.endswith('@qq.com'):
                messagebox.showerror("错误", "请输入有效的QQ邮箱地址")
                return

            if not recipients:
                messagebox.showerror("错误", "请输入收件人邮箱地址")
                return

            if not subj:
                subj = "来自自动化邮件助手的邮件"

            # 获取附件列表
            attachments = list(attachment_listbox.get(0, tk.END))

            # 创建或更新任务
            import datetime
            if edit_task:
                # 更新现有任务
                edit_task['sender_email'] = sender
                edit_task['recipient_emails'] = recipients
                edit_task['subject'] = subj
                edit_task['body'] = content
                edit_task['attachments'] = attachments
                edit_task['send_mode'] = send_mode.get()
                edit_task['add_personalization'] = add_personalization.get()
                action_text = "更新"
            else:
                # 创建新任务
                new_task = {
                    'id': len(self.email_queue) + 1,
                    'sender_email': sender,
                    'recipient_emails': recipients,
                    'subject': subj,
                    'body': content,
                    'attachments': attachments,
                    'send_mode': send_mode.get(),
                    'add_personalization': add_personalization.get(),
                    'created_time': datetime.datetime.now(),
                    'status': 'pending'
                }
                self.email_queue.append(new_task)
                action_text = "添加"

            # 更新状态和刷新列表
            self.update_queue_status()
            refresh_callback()

            # 计算收件人数量
            recipient_count = len([email.strip() for email in
                                  recipients.replace(',', '\n').replace(';', '\n').split('\n')
                                  if email.strip()])

            self.log_message(f"✅ 已{action_text}队列任务：{subj} -> {recipient_count} 个收件人")
            editor_window.destroy()
            messagebox.showinfo("成功", f"任务{action_text}成功！")

        ttk.Button(button_frame, text="💾 保存任务", command=save_task, style='Accent.TButton').pack(side=tk.LEFT, padx=10)
        ttk.Button(button_frame, text="取消", command=editor_window.destroy).pack(side=tk.LEFT, padx=10)

    def add_to_queue(self):
        """添加当前表单内容到队列"""
        # 验证输入
        sender_email = self.sender_email.get().strip()
        recipient_emails = self.recipient_emails.get(1.0, tk.END).strip()
        subject = self.subject.get().strip()
        body_raw = self.body.get(1.0, tk.END).strip()

        # 处理占位符文本
        placeholder_text = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
        if body_raw == placeholder_text.strip():
            body = ""
        else:
            body = body_raw

        if not sender_email or not sender_email.endswith('@qq.com'):
            messagebox.showerror("错误", "请输入有效的QQ邮箱地址")
            return

        if not recipient_emails:
            messagebox.showerror("错误", "请输入收件人邮箱地址")
            return

        if not subject:
            subject = "来自自动化邮件助手的邮件"

        # 获取附件列表
        attachments = list(self.attachment_listbox.get(0, tk.END))

        # 创建邮件任务
        import datetime
        email_task = {
            'id': len(self.email_queue) + 1,
            'sender_email': sender_email,
            'recipient_emails': recipient_emails,
            'subject': subject,
            'body': body,
            'attachments': attachments,
            'send_mode': self.send_mode.get(),
            'add_personalization': self.add_personalization.get(),
            'created_time': datetime.datetime.now(),
            'status': 'pending'
        }

        self.email_queue.append(email_task)
        self.update_queue_status()

        # 计算收件人数量 - 正确处理中文分号
        normalized_emails = recipient_emails.replace('；', ';').replace('，', ',')
        recipient_count = len([email.strip() for email in
                              normalized_emails.replace(',', '\n').replace(';', '\n').split('\n')
                              if email.strip()])

        # 清空当前表单（可选）
        if messagebox.askyesno("添加成功",
                             f"邮件任务已添加到队列（第{len(self.email_queue)}个）\n收件人数量: {recipient_count}\n\n是否清空当前表单以便添加下一个任务？"):
            self.clear_form()

        self.log_message(f"✅ 已添加邮件任务到队列：{subject} -> {recipient_count} 个收件人")

    def stop_queue_sending(self):
        """停止队列发送"""
        self.should_stop = True
        self.log_message("⏹️ 正在停止队列发送...")
        self.status_var.set("正在停止队列发送...")
        messagebox.showinfo("停止发送", "已发出停止指令，将在当前邮件发送完成后停止")

    def pause_queue_sending(self):
        """暂停队列发送"""
        if not self.queue_mode:
            messagebox.showwarning("提示", "当前没有正在进行的队列发送任务")
            return

        self.should_pause = True
        self.log_message("⏸️ 正在暂停队列发送...")
        self.status_var.set("正在暂停队列发送...")

        # 更新按钮状态
        self.pause_queue_button.config(state='disabled')
        self.resume_queue_button.config(state='normal')

        messagebox.showinfo("暂停发送", "已发出暂停指令，将在当前邮件发送完成后暂停")

    def resume_queue_sending(self):
        """恢复队列发送"""
        if not self.should_pause and not self.is_paused:
            messagebox.showwarning("提示", "当前没有暂停的队列发送任务")
            return

        self.should_pause = False
        self.is_paused = False
        self.log_message("▶️ 恢复队列发送...")
        self.status_var.set("恢复队列发送...")

        # 更新按钮状态
        self.pause_queue_button.config(state='normal')
        self.resume_queue_button.config(state='disabled')

        messagebox.showinfo("恢复发送", "队列发送已恢复")

    def continue_queue_from_breakpoint(self):
        """从断点继续队列发送"""
        try:
            # 检查是否有未完成的队列任务
            pending_tasks = [task for task in self.email_queue if task['status'] == 'pending']
            paused_tasks = [task for task in self.email_queue if task['status'] == 'sending']

            if not pending_tasks and not paused_tasks:
                messagebox.showinfo("提示", "没有可以继续的队列任务")
                return

            # 将暂停的任务重置为待发送状态
            for task in paused_tasks:
                task['status'] = 'pending'

            total_tasks = len(pending_tasks) + len(paused_tasks)

            if messagebox.askyesno("断点继续",
                                 f"发现 {total_tasks} 个未完成的队列任务，是否从断点继续发送？"):
                self.log_message(f"🔄 从断点继续队列发送：{total_tasks} 个任务")
                self.start_queue_sending(auto_mode=True)  # 自动模式启动，跳过确认

                # 更新按钮状态
                self.continue_queue_button.config(state='disabled')
            else:
                self.log_message("👤 用户取消了断点继续")

        except Exception as e:
            error_msg = f"断点继续失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def clear_queue(self):
        """清空队列"""
        if not self.email_queue:
            messagebox.showinfo("提示", "队列已经是空的")
            return

        if messagebox.askyesno("确认清空", f"确定要清空队列中的 {len(self.email_queue)} 个任务吗？"):
            self.email_queue.clear()
            self.update_queue_status()
            self.log_message("🗑️ 已清空邮件队列")
            messagebox.showinfo("成功", "队列已清空")

    def start_queue_sending(self, auto_mode=False):
        """开始队列发送

        Args:
            auto_mode (bool): 是否为自动模式启动，自动模式下跳过确认对话框
        """
        pending_tasks = [task for task in self.email_queue if task['status'] == 'pending']
        if not pending_tasks:
            if not auto_mode:  # 只有手动模式才显示提示
                messagebox.showinfo("提示", "队列中没有待发送的任务")
            return

        # 计算总邮件数 - 正确处理中文分号
        total_emails = sum(len([email.strip() for email in
                               task['recipient_emails'].replace('；', ';').replace('，', ',').replace(',', '\n').replace(';', '\n').split('\n')
                               if email.strip()]) for task in pending_tasks)

        # 根据模式决定是否需要确认
        if auto_mode:
            # 自动模式：直接开始，只记录日志
            self.log_message(f"🤖 自动队列模式：直接开始发送 {len(pending_tasks)} 个任务，共 {total_emails} 封邮件")
        else:
            # 手动模式：需要用户确认
            confirm_msg = f"""准备开始队列发送：

任务数量: {len(pending_tasks)} 个
总邮件数: {total_emails} 封
预计耗时: 根据发送模式而定

队列发送将自动依次处理所有任务，您可以随时停止。

确定开始吗？"""

            if not messagebox.askyesno("确认队列发送", confirm_msg):
                self.log_message("👤 用户取消了队列发送")
                return

        # 开始队列发送
        self.queue_mode = True
        self.current_queue_index = 0

        # 禁用相关按钮
        self.send_button.config(state='disabled')
        self.start_queue_button.config(state='disabled')

        # 启用队列控制按钮
        self.pause_queue_button.config(state='normal')
        self.stop_queue_button.config(state='normal')
        self.stop_button.config(state='normal')

        self.log_message(f"🚀 开始队列发送：{len(pending_tasks)} 个任务，共 {total_emails} 封邮件")

        # 在新线程中执行队列发送
        import threading
        self.current_send_thread = threading.Thread(target=self._execute_queue_sending)
        self.current_send_thread.daemon = True
        self.current_send_thread.start()

    def _execute_queue_sending(self):
        """执行队列发送（在后台线程中运行）"""
        try:
            pending_tasks = [task for task in self.email_queue if task['status'] == 'pending']

            for task_index, task in enumerate(pending_tasks):
                # 检查是否需要停止
                if self.should_stop:
                    self.root.after(0, lambda: self.log_message("❌ 用户停止了队列发送"))
                    break

                # 检查是否需要暂停
                if self.should_pause:
                    self.is_paused = True
                    self.root.after(0, lambda: self.log_message("⏸️ 队列发送已暂停"))
                    self.root.after(0, lambda: self.status_var.set("队列发送已暂停"))

                    # 等待恢复
                    while self.should_pause and not self.should_stop:
                        import time
                        time.sleep(1)

                    if self.should_stop:
                        self.root.after(0, lambda: self.log_message("❌ 暂停期间用户停止了队列发送"))
                        break

                    self.root.after(0, lambda: self.log_message("▶️ 队列发送已恢复"))

                # 更新任务状态
                task['status'] = 'sending'
                self.root.after(0, lambda t=task: self.log_message(f"📧 开始发送任务 #{t['id']}: {t['subject']}"))

                # 设置当前任务的参数
                self.root.after(0, lambda: self._set_current_task_params(task))

                # 发送当前任务
                success = self._send_single_task(task)

                if success:
                    task['status'] = 'completed'
                    self.root.after(0, lambda t=task: self.log_message(f"✅ 任务 #{t['id']} 发送完成"))
                else:
                    task['status'] = 'failed'
                    self.root.after(0, lambda t=task: self.log_message(f"❌ 任务 #{t['id']} 发送失败"))

                # 更新队列状态
                self.root.after(0, self.update_queue_status)

                # 任务间隔（除了最后一个任务）
                if task_index < len(pending_tasks) - 1 and not self.should_stop:
                    self.root.after(0, lambda: self.log_message("⏳ 等待30秒后开始下一个任务..."))
                    # 分段睡眠，每秒检查一次是否需要停止
                    for i in range(30):
                        if self.should_stop:
                            break
                        import time
                        time.sleep(1)

            # 队列发送完成
            completed_count = len([task for task in self.email_queue if task['status'] == 'completed'])
            failed_count = len([task for task in self.email_queue if task['status'] == 'failed'])

            if self.should_stop:
                result_msg = f"队列发送已停止！\n已完成: {completed_count} 个任务\n失败: {failed_count} 个任务"
            else:
                result_msg = f"队列发送完成！\n成功: {completed_count} 个任务\n失败: {failed_count} 个任务"

            self.root.after(0, lambda: self.log_message(f"🎉 {result_msg}"))
            self.root.after(0, lambda: self._queue_send_complete(result_msg))

        except Exception as e:
            error_msg = f"队列发送出错: {str(e)}"
            self.root.after(0, lambda: self.log_message(f"❌ {error_msg}"))
            self.root.after(0, lambda msg=error_msg: self._queue_send_complete(msg, False))

    def _set_current_task_params(self, task):
        """设置当前任务的参数到界面（用于显示当前进度）"""
        # 这里可以选择是否更新界面显示当前任务信息
        self.status_var.set(f"正在发送任务 #{task['id']}: {task['subject']}")

    def _send_single_task(self, task):
        """发送单个任务"""
        try:
            # 获取授权码 - 修复授权码获取逻辑
            auth_info = self.auth_codes.get(task['sender_email'])
            if not auth_info:
                self.root.after(0, lambda: self.log_message(f"❌ 任务 #{task['id']} 缺少授权码"))
                return False

            # 提取授权码（支持新旧格式）
            if isinstance(auth_info, dict):
                auth_code = auth_info.get('auth_code', '')
            else:
                auth_code = auth_info  # 兼容旧格式

            if not auth_code:
                self.root.after(0, lambda: self.log_message(f"❌ 任务 #{task['id']} 授权码为空"))
                return False

            # 创建邮件发送器
            from email_sender import EmailSender
            sender = EmailSender(task['sender_email'])
            sender.smtp_config['password'] = auth_code

            # 解析收件人列表 - 正确处理中文分号和编码
            recipient_emails = task['recipient_emails']

            # 统一处理中英文分号和逗号
            normalized_emails = recipient_emails.replace('；', ';').replace('，', ',')

            recipient_list = []
            for email in normalized_emails.replace(',', '\n').replace(';', '\n').split('\n'):
                email = email.strip()
                if email and '@' in email:
                    recipient_list.append(email)

            if not recipient_list:
                self.root.after(0, lambda: self.log_message(f"❌ 任务 #{task['id']} 没有有效的收件人"))
                return False

            # 获取发送间隔
            import random
            delay_ranges = {
                "fast": (30, 60),
                "standard": (60, 120),
                "safe": (180, 300)
            }
            delay_range = delay_ranges.get(task['send_mode'], (60, 120))

            success_count = 0
            failed_count = 0

            for i, email in enumerate(recipient_list, 1):
                # 检查是否需要停止
                if self.should_stop:
                    break

                # 检查是否需要暂停
                if self.should_pause:
                    self.is_paused = True
                    self.root.after(0, lambda: self.log_message("⏸️ 任务内暂停"))

                    # 等待恢复
                    while self.should_pause and not self.should_stop:
                        import time
                        time.sleep(1)

                    if self.should_stop:
                        break

                    self.root.after(0, lambda: self.log_message("▶️ 任务内恢复"))

                self.root.after(0, lambda i=i, email=email, total=len(recipient_list), task_id=task['id']:
                               self.log_message(f"📤 任务 #{task_id} 发送第 {i}/{total} 封邮件给: {email}"))

                try:
                    # 个性化内容
                    personalized_body = task['body']
                    if task['add_personalization']:
                        import datetime
                        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        personalized_body += f"\n\n---\n邮件编号: #{i:03d} | 发送时间: {timestamp}"

                    success = sender.send_email(
                        to_emails=[email],
                        subject=task['subject'],
                        body=personalized_body,
                        attachments=task['attachments'] if task['attachments'] else None
                    )

                    if success:
                        success_count += 1
                        # 记录已发送邮件（内存和数据库）
                        import datetime
                        email_record = {
                            'recipient': email,
                            'subject': task['subject'],
                            'body': personalized_body,
                            'send_time': datetime.datetime.now(),
                            'batch_id': task['id']
                        }
                        self.sent_emails.append(email_record)

                        # 同时记录到历史数据库
                        try:
                            self.history_manager.add_email_record(
                                sender_email=task['sender_email'],
                                recipient_email=email,
                                subject=task['subject'],
                                body=personalized_body,
                                success=True,
                                batch_id=task['id'],
                                attachments=task.get('attachments', [])
                            )
                        except Exception as e:
                            self.root.after(0, lambda err=str(e): self.log_message(f"⚠️ 队列记录历史失败: {err}"))

                        self.root.after(0, lambda email=email: self.log_message(f"✅ 发送成功: {email}"))
                    else:
                        failed_count += 1
                        # 记录失败的邮件到数据库
                        try:
                            self.history_manager.add_email_record(
                                sender_email=task['sender_email'],
                                recipient_email=email,
                                subject=task['subject'],
                                body=personalized_body,
                                success=False,
                                batch_id=task['id'],
                                attachments=task.get('attachments', [])
                            )
                        except Exception as e:
                            self.root.after(0, lambda err=str(e): self.log_message(f"⚠️ 记录失败邮件历史失败: {err}"))

                        self.root.after(0, lambda email=email: self.log_message(f"❌ 发送失败: {email}"))

                except Exception as e:
                    failed_count += 1
                    # 记录异常的邮件到数据库
                    try:
                        self.history_manager.add_email_record(
                            sender_email=task['sender_email'],
                            recipient_email=email,
                            subject=task['subject'],
                            body=personalized_body,
                            success=False,
                            batch_id=task['id'],
                            attachments=task.get('attachments', [])
                        )
                    except Exception as db_e:
                        self.root.after(0, lambda err=str(db_e): self.log_message(f"⚠️ 记录异常邮件历史失败: {err}"))

                    self.root.after(0, lambda email=email, error=str(e):
                                   self.log_message(f"❌ 发送异常: {email} - {error}"))

                # 添加延迟
                if i < len(recipient_list) and not self.should_stop:
                    next_delay = random.uniform(delay_range[0], delay_range[1])
                    self.root.after(0, lambda d=next_delay: self.log_message(f"⏱️ 等待 {d:.1f} 秒后发送下一封..."))

                    # 分段睡眠，同时检查暂停和停止
                    sleep_time = 0
                    while sleep_time < next_delay and not self.should_stop:
                        # 检查暂停
                        if self.should_pause:
                            self.is_paused = True
                            self.root.after(0, lambda: self.log_message("⏸️ 延迟期间暂停"))

                            # 等待恢复
                            while self.should_pause and not self.should_stop:
                                import time
                                time.sleep(1)

                            if self.should_stop:
                                break

                            self.root.after(0, lambda: self.log_message("▶️ 延迟期间恢复"))

                        import time
                        time.sleep(0.5)
                        sleep_time += 0.5

            # 任务完成统计
            self.root.after(0, lambda sc=success_count, fc=failed_count, task_id=task['id']:
                           self.log_message(f"📊 任务 #{task_id} 统计：成功 {sc}，失败 {fc}"))

            return success_count > 0

        except Exception as e:
            self.root.after(0, lambda error=str(e), task_id=task['id']:
                           self.log_message(f"❌ 任务 #{task_id} 执行异常: {error}"))
            return False

    def _queue_send_complete(self, message, success=True):
        """队列发送完成"""
        self.queue_mode = False
        self.should_stop = False
        self.should_pause = False
        self.is_paused = False

        # 重置按钮状态
        self.send_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.pause_queue_button.config(state='disabled')
        self.resume_queue_button.config(state='disabled')
        self.stop_queue_button.config(state='disabled')

        # 检查是否有未完成任务，决定是否启用断点继续按钮
        pending_tasks = [task for task in self.email_queue if task['status'] == 'pending']
        if pending_tasks:
            self.continue_queue_button.config(state='normal')
        else:
            self.continue_queue_button.config(state='disabled')

        self.progress.stop()

        # 更新队列状态
        self.update_queue_status()

        # 如果有已发送邮件，启用撤回按钮
        if self.sent_emails:
            self.recall_button.config(state='normal')

        if success:
            self.status_var.set("队列发送完成")
            messagebox.showinfo("队列发送完成", message)
        else:
            self.status_var.set("队列发送失败")
            messagebox.showerror("队列发送失败", message)

    def send_error(self, error):
        """发送出错"""
        self.progress.stop()
        self.send_button.config(state='normal')
        self.status_var.set("发送出错")
        self.log_message(f"发送出错: {error}")
        messagebox.showerror("错误", f"发送邮件时出错：{error}")

def main():
    """主函数"""
    root = tk.Tk()
    EmailSenderGUI(root)
    
    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass
        
    root.mainloop()

if __name__ == "__main__":
    main()

    def open_system_coordinator(self):
        """打开系统协调器"""
        try:
            coordinator_window = tk.Toplevel(self.root)
            coordinator_window.title("🔧 系统协调器")
            coordinator_window.geometry("900x700")
            coordinator_window.transient(self.root)
            
            main_frame = ttk.Frame(coordinator_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(main_frame, text="🔧 邮件系统协调器", 
                                   font=('Microsoft YaHei UI', 16, 'bold'))
            title_label.pack(pady=(0, 20))
            
            # 发件人选择
            sender_frame = ttk.Frame(main_frame)
            sender_frame.pack(fill=tk.X, pady=(0, 10))
            
            ttk.Label(sender_frame, text="发件人邮箱:").pack(side=tk.LEFT, padx=(0, 5))
            sender_var = tk.StringVar(value="<EMAIL>")
            ttk.Entry(sender_frame, textvariable=sender_var, width=30).pack(side=tk.LEFT, padx=(0, 10))
            
            def generate_report():
                try:
                    from system_coordinator import EmailSystemCoordinator
                    coordinator = EmailSystemCoordinator()
                    
                    sender_email = sender_var.get().strip()
                    if not sender_email:
                        messagebox.showwarning("提示", "请输入发件人邮箱")
                        return
                    
                    report = coordinator.generate_system_report(sender_email)
                    
                    if report:
                        # 显示报告
                        report_text.delete(1.0, tk.END)
                        
                        analysis = report['recipient_analysis']
                        report_content = f"""📊 系统协调报告
{'='*60}

📧 发件人: {sender_email}
📊 分析时间: {report['analysis_time'][:19]}

📈 收件人统计:
  • 总收件人: {analysis['total_recipients']}
  • ✅ 活跃收件人: {len(analysis['active_recipients'])} (有回复)
  • ❌ 未回复收件人: {len(analysis['no_reply_recipients'])} (可能进入垃圾箱)
  • 🗑️ 无效收件人: {len(analysis['invalid_recipients'])} (邮箱有问题)
  • ⚠️ 风险等级: {analysis['risk_level'].upper()}

💡 系统建议:
"""
                        for i, recommendation in enumerate(analysis['recommendations'], 1):
                            report_content += f"  {i}. {recommendation}\n"
                        
                        coordination = report['coordination_results']
                        report_content += f"""
🔧 功能协调状态:
  • 🆘 QQ应急系统: {'✅ 已协调' if coordination['qq_emergency'] else '⚪ 无需协调'}
  • 📊 质量管理器: {'✅ 已协调' if coordination['quality_manager'] else '❌ 协调失败'}
  • 🛡️ 反垃圾邮件: {'✅ 已协调' if coordination['anti_spam'] else '❌ 协调失败'}

🎯 系统状态: {report['system_status'].upper()}

📋 详细分析:
"""
                        
                        if analysis['active_recipients']:
                            report_content += f"\n✅ 活跃收件人 ({len(analysis['active_recipients'])} 个):\n"
                            for recipient in analysis['active_recipients'][:10]:  # 显示前10个
                                report_content += f"  • {recipient['email']} (回复 {recipient['reply_count']} 次)\n"
                        
                        if analysis['no_reply_recipients']:
                            report_content += f"\n❌ 未回复收件人 ({len(analysis['no_reply_recipients'])} 个):\n"
                            for recipient in analysis['no_reply_recipients'][:10]:  # 显示前10个
                                report_content += f"  • {recipient['email']} (可能进入垃圾箱或邮箱有问题)\n"
                        
                        if analysis['invalid_recipients']:
                            report_content += f"\n🗑️ 无效收件人 ({len(analysis['invalid_recipients'])} 个):\n"
                            for recipient in analysis['invalid_recipients'][:10]:  # 显示前10个
                                report_content += f"  • {recipient['email']} (退信 {recipient['bounce_count']} 次)\n"
                        
                        report_text.insert(tk.END, report_content)
                        
                        self.log_message(f"✅ 系统协调报告已生成: {sender_email}")
                        
                    else:
                        messagebox.showerror("错误", "生成协调报告失败")
                        
                except Exception as e:
                    error_msg = f"生成协调报告失败: {str(e)}"
                    self.log_message(f"❌ {error_msg}")
                    messagebox.showerror("错误", error_msg)
            
            ttk.Button(sender_frame, text="📊 生成协调报告", command=generate_report).pack(side=tk.LEFT)
            
            # 报告显示区域
            report_text = scrolledtext.ScrolledText(main_frame, height=35, font=('Consolas', 10))
            report_text.pack(fill=tk.BOTH, expand=True, pady=(10, 10))
            
            # 操作按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)
            
            ttk.Button(button_frame, text="🆘 打开QQ应急管理", 
                      command=self.open_qq_emergency_manager).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="📊 打开质量管理器", 
                      command=self.open_quality_manager).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="🛡️ 打开反垃圾邮件", 
                      command=self.open_anti_spam_manager).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="关闭", 
                      command=coordinator_window.destroy).pack(side=tk.RIGHT, padx=5)
            
            # 初始生成报告
            generate_report()
            
        except Exception as e:
            error_msg = f"打开系统协调器失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

if __name__ == "__main__":
    root = tk.Tk()
    app = EmailSenderGUI(root)
    root.mainloop()
