' ================================================================
' 深度调试启动器 v5.0 - 检查Python程序启动情况
' 功能: 等待并检查Python程序是否真正启动
' 日期: 2025-06-12
' ================================================================

Option Explicit

' 声明变量
Dim objShell, objFSO, currentDir, scriptPath, command, result
Dim pythonCmd, errorMsg, logFile, logContent, processObj

' 初始化日志
logContent = "=== 深度调试日志 ===" & vbCrLf
logContent = logContent & "启动时间: " & Now() & vbCrLf & vbCrLf

' 初始化对象
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

logContent = logContent & "当前目录: " & currentDir & vbCrLf & vbCrLf

' 检查文件
scriptPath = currentDir & "\gui_main.py"
If Not objFSO.FileExists(scriptPath) Then
    logContent = logContent & "错误: gui_main.py不存在!" & vbCrLf
    WriteLog
    MsgBox "gui_main.py文件不存在!", vbCritical, "文件错误"
    WScript.Quit 1
End If

' 检测Python
pythonCmd = "python"
result = objShell.Run("python --version", 0, True)
If result <> 0 Then
    pythonCmd = "python3"
    result = objShell.Run("python3 --version", 0, True)
    If result <> 0 Then
        logContent = logContent & "错误: 没有找到Python!" & vbCrLf
        WriteLog
        MsgBox "没有找到Python环境!", vbCritical, "Python错误"
        WScript.Quit 2
    End If
End If

logContent = logContent & "使用Python命令: " & pythonCmd & vbCrLf & vbCrLf

' 方法1: 先测试Python程序语法
logContent = logContent & "=== 方法1: 测试Python程序语法 ===" & vbCrLf
command = "cmd.exe /c ""cd /d """ & currentDir & """ && " & pythonCmd & " -m py_compile gui_main.py 2>&1"""
logContent = logContent & "语法检查命令: " & command & vbCrLf

On Error Resume Next
result = objShell.Run(command, 0, True)
logContent = logContent & "语法检查返回码: " & result & vbCrLf
If result <> 0 Then
    logContent = logContent & "警告: Python程序可能有语法错误!" & vbCrLf
Else
    logContent = logContent & "成功: Python程序语法正确" & vbCrLf
End If
On Error GoTo 0

' 方法2: 使用Exec方法启动并等待
logContent = logContent & vbCrLf & "=== 方法2: 使用Exec方法启动 ===" & vbCrLf
command = "cmd.exe /c ""cd /d """ & currentDir & """ && " & pythonCmd & " gui_main.py 2>&1"""
logContent = logContent & "启动命令: " & command & vbCrLf

On Error Resume Next
Set processObj = objShell.Exec(command)
If Err.Number <> 0 Then
    logContent = logContent & "错误: 无法启动进程 - " & Err.Description & vbCrLf
    WriteLog
    MsgBox "无法启动Python进程!" & vbCrLf & Err.Description, vbCritical, "启动错误"
    WScript.Quit 3
End If
On Error GoTo 0

logContent = logContent & "进程已启动，等待输出..." & vbCrLf

' 等待进程输出
Dim waitCount, output, errorOutput
waitCount = 0
Do While processObj.Status = 0 And waitCount < 30  ' 等待最多30秒
    WScript.Sleep 1000
    waitCount = waitCount + 1
    logContent = logContent & "等待第 " & waitCount & " 秒..." & vbCrLf
    
    ' 检查是否有输出
    If Not processObj.StdOut.AtEndOfStream Then
        output = processObj.StdOut.ReadAll()
        logContent = logContent & "标准输出: " & output & vbCrLf
    End If
    
    If Not processObj.StdErr.AtEndOfStream Then
        errorOutput = processObj.StdErr.ReadAll()
        logContent = logContent & "错误输出: " & errorOutput & vbCrLf
    End If
Loop

' 检查最终状态
If processObj.Status = 0 Then
    logContent = logContent & "进程仍在运行中..." & vbCrLf
    logContent = logContent & "这可能意味着GUI正在正常运行" & vbCrLf
Else
    logContent = logContent & "进程已结束，退出码: " & processObj.ExitCode & vbCrLf
    
    ' 读取剩余输出
    If Not processObj.StdOut.AtEndOfStream Then
        output = processObj.StdOut.ReadAll()
        logContent = logContent & "最终标准输出: " & output & vbCrLf
    End If
    
    If Not processObj.StdErr.AtEndOfStream Then
        errorOutput = processObj.StdErr.ReadAll()
        logContent = logContent & "最终错误输出: " & errorOutput & vbCrLf
    End If
End If

' 方法3: 检查是否有Python进程在运行
logContent = logContent & vbCrLf & "=== 方法3: 检查Python进程 ===" & vbCrLf
On Error Resume Next
Dim wmi, processes, process
Set wmi = GetObject("winmgmts:")
Set processes = wmi.ExecQuery("SELECT * FROM Win32_Process WHERE Name = 'python.exe' OR Name = 'pythonw.exe'")

Dim processCount
processCount = 0
For Each process In processes
    processCount = processCount + 1
    logContent = logContent & "发现Python进程: " & process.Name & " (PID: " & process.ProcessId & ")" & vbCrLf
    If InStr(process.CommandLine, "gui_main.py") > 0 Then
        logContent = logContent & "  -> 这是我们的GUI进程!" & vbCrLf
        logContent = logContent & "  -> 命令行: " & process.CommandLine & vbCrLf
    End If
Next

If processCount = 0 Then
    logContent = logContent & "没有发现Python进程在运行" & vbCrLf
Else
    logContent = logContent & "总共发现 " & processCount & " 个Python进程" & vbCrLf
End If
On Error GoTo 0

' 完成
logContent = logContent & vbCrLf & "=== 调试完成 ===" & vbCrLf
logContent = logContent & "完成时间: " & Now() & vbCrLf

WriteLog

' 显示结果
If processCount > 0 Then
    MsgBox "调试完成!" & vbCrLf & vbCrLf & _
           "发现 " & processCount & " 个Python进程正在运行。" & vbCrLf & _
           "如果GUI没有显示，可能是窗口被隐藏了。" & vbCrLf & vbCrLf & _
           "详细日志: 深度调试日志.txt", vbInformation, "调试结果"
Else
    MsgBox "调试完成!" & vbCrLf & vbCrLf & _
           "没有发现Python进程在运行。" & vbCrLf & _
           "Python程序可能启动失败或立即退出。" & vbCrLf & vbCrLf & _
           "详细日志: 深度调试日志.txt", vbExclamation, "调试结果"
End If

WScript.Quit 0

' 写入日志的子程序
Sub WriteLog()
    On Error Resume Next
    Dim logFilePath, logFileObj
    logFilePath = currentDir & "\深度调试日志.txt"
    
    Set logFileObj = objFSO.CreateTextFile(logFilePath, True)
    If Not logFileObj Is Nothing Then
        logFileObj.Write logContent
        logFileObj.Close
    End If
    On Error GoTo 0
End Sub
