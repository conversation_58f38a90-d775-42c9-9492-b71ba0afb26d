#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库锁定监控脚本
"""

import sqlite3
import time
import threading
import os

class DatabaseMonitor:
    def __init__(self):
        self.db_files = [
            'email_history.db',
            'recipient_quality.db',
            'auto_reply_monitor.db',
            'anti_spam.db',
            'qq_anti_spam.db'
        ]
        self.monitoring = False
        self.monitor_thread = None
    
    def check_database_locks(self):
        """检查数据库锁定状态"""
        locked_dbs = []
        
        for db_file in self.db_files:
            if os.path.exists(db_file):
                try:
                    conn = sqlite3.connect(db_file, timeout=1.0)
                    conn.execute("SELECT 1")
                    conn.close()
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e):
                        locked_dbs.append(db_file)
                except:
                    pass
        
        return locked_dbs
    
    def auto_unlock_database(self, db_file):
        """自动解锁数据库"""
        try:
            # 删除WAL和SHM文件
            wal_file = db_file + '-wal'
            shm_file = db_file + '-shm'
            
            if os.path.exists(wal_file):
                os.remove(wal_file)
            
            if os.path.exists(shm_file):
                os.remove(shm_file)
            
            # 重新连接
            conn = sqlite3.connect(db_file, timeout=5.0)
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA busy_timeout=30000")
            conn.close()
            
            print(f"✅ 自动解锁数据库: {db_file}")
            return True
            
        except Exception as e:
            print(f"❌ 自动解锁失败: {db_file} - {str(e)}")
            return False
    
    def monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                locked_dbs = self.check_database_locks()
                
                if locked_dbs:
                    print(f"🚨 发现锁定的数据库: {locked_dbs}")
                    
                    for db_file in locked_dbs:
                        self.auto_unlock_database(db_file)
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                print(f"❌ 监控循环错误: {str(e)}")
                time.sleep(30)
    
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("✅ 数据库监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("✅ 数据库监控已停止")

if __name__ == "__main__":
    monitor = DatabaseMonitor()
    monitor.start_monitoring()
    
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        monitor.stop_monitoring()
