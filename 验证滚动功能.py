#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证滚动功能是否正常工作
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

def test_scroll_functionality():
    """测试滚动功能"""
    root = tk.Tk()
    root.title("滚动功能验证")
    root.geometry("600x500")
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="滚动功能验证测试", 
                           font=('Microsoft YaHei UI', 16, 'bold'))
    title_label.pack(pady=(0, 10))
    
    # 说明
    info_label = ttk.Label(main_frame, 
                          text="这个测试用来验证ScrolledText组件的滚动功能是否正常工作",
                          font=('Microsoft YaHei UI', 10))
    info_label.pack(pady=(0, 10))
    
    # 创建滚动文本框 - 与完整版相同的设置
    scroll_frame = ttk.LabelFrame(main_frame, text="📋 滚动测试区域", padding="5")
    scroll_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    scroll_text = scrolledtext.ScrolledText(
        scroll_frame,
        width=45,
        height=12,  # 与完整版相同的高度
        font=('Consolas', 9),  # 与完整版相同的字体
        wrap=tk.WORD,
        relief='solid',
        borderwidth=1,
        bg='#1e293b',  # 与完整版相同的背景色
        fg='#e2e8f0',  # 与完整版相同的前景色
        insertbackground='#3b82f6',
        selectbackground='#374151',
        state=tk.NORMAL  # 确保可以滚动
    )
    scroll_text.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
    
    # 添加大量测试内容
    test_content = """📋 滚动功能验证测试
========================================
这是第1行 - 测试滚动功能是否正常工作
这是第2行 - 请使用鼠标滚轮测试向上向下滚动
这是第3行 - 请拖拽右侧滚动条测试
这是第4行 - 请点击滚动条上下箭头测试
这是第5行 - 如果能看到滚动条，说明内容足够多
这是第6行 - 滚动条应该出现在右侧
这是第7行 - 鼠标滚轮应该可以上下滚动
这是第8行 - 拖拽滚动条应该可以快速移动
这是第9行 - 点击滚动条箭头应该逐行滚动
这是第10行 - 滚动应该流畅无卡顿
这是第11行 - 文字应该清晰可见
这是第12行 - 背景色应该是深色
这是第13行 - 前景色应该是浅色
这是第14行 - 字体应该是等宽字体Consolas
这是第15行 - 如果滚动正常，这个测试就成功了
========================================
🖱️ 滚动测试说明：
• 使用鼠标滚轮向上向下滚动
• 拖拽右侧滚动条进行快速滚动
• 点击滚动条上下箭头进行逐行滚动
• 滚动应该流畅，没有卡顿现象
• 内容应该正确显示，字体清晰

💡 如果以上功能都正常，说明滚动功能没有问题
如果滚动不工作，可能的原因：
1. state设置为DISABLED阻止了滚动
2. 内容不够多，没有触发滚动条
3. 高度设置太大，内容都能显示完
4. 鼠标事件被其他组件拦截

🔍 故障排除：
- 确保state=tk.NORMAL
- 确保内容超过显示区域
- 确保height设置合适
- 确保没有其他组件阻挡鼠标事件

这是第30行 - 更多内容用于测试滚动
这是第31行 - 继续添加内容
这是第32行 - 确保有足够内容触发滚动
这是第33行 - 滚动条应该已经出现了
这是第34行 - 请测试各种滚动方式
这是第35行 - 鼠标滚轮滚动
这是第36行 - 拖拽滚动条
这是第37行 - 点击滚动条箭头
这是第38行 - 所有方式都应该工作正常
这是第39行 - 如果能看到这行，说明滚动到底部了
这是第40行 - 测试完成！"""
    
    scroll_text.insert(tk.END, test_content)
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))
    
    def scroll_to_top():
        scroll_text.see(1.0)
    
    def scroll_to_bottom():
        scroll_text.see(tk.END)
    
    def add_more_content():
        scroll_text.insert(tk.END, "\n\n🆕 新增内容：\n")
        for i in range(10):
            scroll_text.insert(tk.END, f"新增第{i+1}行 - 测试动态添加内容后的滚动\n")
        scroll_text.see(tk.END)
    
    def test_disable_enable():
        current_state = scroll_text.cget('state')
        if current_state == tk.NORMAL:
            scroll_text.config(state=tk.DISABLED)
            toggle_btn.config(text="启用滚动")
            status_label.config(text="状态：已禁用 - 滚动应该不工作", foreground="red")
        else:
            scroll_text.config(state=tk.NORMAL)
            toggle_btn.config(text="禁用滚动")
            status_label.config(text="状态：已启用 - 滚动应该正常工作", foreground="green")
    
    ttk.Button(button_frame, text="滚动到顶部", command=scroll_to_top).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="滚动到底部", command=scroll_to_bottom).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="添加更多内容", command=add_more_content).pack(side=tk.LEFT, padx=5)
    toggle_btn = ttk.Button(button_frame, text="禁用滚动", command=test_disable_enable)
    toggle_btn.pack(side=tk.LEFT, padx=5)
    
    # 状态显示
    status_label = ttk.Label(main_frame, 
                            text="状态：已启用 - 滚动应该正常工作", 
                            font=('Microsoft YaHei UI', 10),
                            foreground="green")
    status_label.pack(pady=(10, 0))
    
    # 测试结果
    result_frame = ttk.Frame(main_frame)
    result_frame.pack(fill=tk.X, pady=(10, 0))
    
    def report_success():
        messagebox.showinfo("测试结果", "✅ 滚动功能正常工作！\n\n完整版v3的队列滚动功能应该也能正常工作。")
    
    def report_failure():
        messagebox.showerror("测试结果", "❌ 滚动功能不工作！\n\n可能的原因：\n1. state设置问题\n2. 内容不够多\n3. 高度设置问题\n4. 鼠标事件被拦截")
    
    ttk.Button(result_frame, text="✅ 滚动正常", command=report_success).pack(side=tk.LEFT, padx=5)
    ttk.Button(result_frame, text="❌ 滚动异常", command=report_failure).pack(side=tk.LEFT, padx=5)
    
    # 自动滚动到顶部开始测试
    scroll_text.see(1.0)
    
    root.mainloop()

if __name__ == "__main__":
    test_scroll_functionality()
