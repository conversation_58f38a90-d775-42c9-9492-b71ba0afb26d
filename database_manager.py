#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接管理器 - 解决数据库锁定问题
"""

import sqlite3
import threading
import time
import os
from contextlib import contextmanager

class DatabaseManager:
    """数据库连接管理器，解决并发访问和锁定问题"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.lock = threading.RLock()  # 可重入锁
        self._local = threading.local()
    
    def get_connection(self):
        """获取线程本地的数据库连接"""
        if not hasattr(self._local, 'connection'):
            self._local.connection = sqlite3.connect(
                self.db_path,
                timeout=30.0,  # 30秒超时
                check_same_thread=False
            )
            # 设置WAL模式，提高并发性能
            self._local.connection.execute("PRAGMA journal_mode=WAL")
            self._local.connection.execute("PRAGMA synchronous=NORMAL")
            self._local.connection.execute("PRAGMA cache_size=10000")
            self._local.connection.execute("PRAGMA temp_store=memory")
        
        return self._local.connection
    
    @contextmanager
    def get_cursor(self, retries=3):
        """获取数据库游标的上下文管理器"""
        for attempt in range(retries):
            try:
                with self.lock:
                    conn = self.get_connection()
                    cursor = conn.cursor()
                    try:
                        yield cursor
                        conn.commit()
                        break
                    except Exception as e:
                        conn.rollback()
                        if attempt == retries - 1:
                            raise
                        time.sleep(0.1 * (attempt + 1))  # 递增延迟
                    finally:
                        cursor.close()
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < retries - 1:
                    time.sleep(0.5 * (attempt + 1))  # 递增延迟
                    continue
                raise
    
    def execute_query(self, query, params=None, retries=3):
        """执行查询"""
        for attempt in range(retries):
            try:
                with self.get_cursor() as cursor:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)
                    return cursor.fetchall()
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < retries - 1:
                    time.sleep(0.5 * (attempt + 1))
                    continue
                raise
    
    def execute_update(self, query, params=None, retries=3):
        """执行更新操作"""
        for attempt in range(retries):
            try:
                with self.get_cursor() as cursor:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)
                    return cursor.rowcount
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < retries - 1:
                    time.sleep(0.5 * (attempt + 1))
                    continue
                raise
    
    def execute_many(self, query, params_list, retries=3):
        """批量执行操作"""
        for attempt in range(retries):
            try:
                with self.get_cursor() as cursor:
                    cursor.executemany(query, params_list)
                    return cursor.rowcount
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < retries - 1:
                    time.sleep(0.5 * (attempt + 1))
                    continue
                raise
    
    def close_all_connections(self):
        """关闭所有连接"""
        if hasattr(self._local, 'connection'):
            try:
                self._local.connection.close()
                delattr(self._local, 'connection')
            except:
                pass

# 全局数据库管理器实例
_db_managers = {}
_managers_lock = threading.Lock()

def get_db_manager(db_path):
    """获取数据库管理器实例（单例模式）"""
    with _managers_lock:
        if db_path not in _db_managers:
            _db_managers[db_path] = DatabaseManager(db_path)
        return _db_managers[db_path]

def safe_db_operation(db_path, operation_func, *args, **kwargs):
    """安全的数据库操作包装器"""
    db_manager = get_db_manager(db_path)
    return operation_func(db_manager, *args, **kwargs)

# 测试函数
def test_database_manager():
    """测试数据库管理器"""
    print("🔧 测试数据库管理器")
    print("=" * 50)
    
    test_db = "test_db_manager.db"
    
    try:
        # 创建测试表
        db_manager = get_db_manager(test_db)
        
        with db_manager.get_cursor() as cursor:
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS test_table (
                    id INTEGER PRIMARY KEY,
                    name TEXT,
                    value INTEGER
                )
            """)
        
        print("✅ 测试表创建成功")
        
        # 测试插入
        db_manager.execute_update(
            "INSERT INTO test_table (name, value) VALUES (?, ?)",
            ("test1", 100)
        )
        print("✅ 插入操作成功")
        
        # 测试查询
        results = db_manager.execute_query("SELECT * FROM test_table")
        print(f"✅ 查询操作成功，结果: {results}")
        
        # 测试并发操作
        import threading
        import time
        
        def concurrent_insert(thread_id):
            try:
                for i in range(5):
                    db_manager.execute_update(
                        "INSERT INTO test_table (name, value) VALUES (?, ?)",
                        (f"thread_{thread_id}_{i}", i)
                    )
                    time.sleep(0.01)
                print(f"✅ 线程 {thread_id} 完成")
            except Exception as e:
                print(f"❌ 线程 {thread_id} 失败: {str(e)}")
        
        # 启动多个线程测试并发
        threads = []
        for i in range(3):
            thread = threading.Thread(target=concurrent_insert, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 检查最终结果
        final_results = db_manager.execute_query("SELECT COUNT(*) FROM test_table")
        print(f"✅ 并发测试完成，总记录数: {final_results[0][0]}")
        
        print("🎉 数据库管理器测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
    
    finally:
        # 清理测试文件
        if os.path.exists(test_db):
            os.remove(test_db)

if __name__ == "__main__":
    test_database_manager()
