#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的智能检索和重复检测功能
验证3.0系统功能是否达到2.0系统水平
"""

import tkinter as tk
import sys
import os

def test_smart_search_feature():
    """测试智能检索功能"""
    print("🔍 测试智能检索功能...")
    
    try:
        # 导入RAG搜索引擎
        from rag_search_engine import RAGSearchEngine
        
        # 创建搜索引擎实例
        search_engine = RAGSearchEngine()
        
        # 测试搜索功能
        test_query = "重要通知"
        test_sender = "<EMAIL>"
        
        print(f"📧 测试查询: {test_query}")
        print(f"📤 测试发件人: {test_sender}")
        
        # 执行搜索
        results = search_engine.semantic_search(test_query, test_sender, 10)
        
        print(f"📊 搜索结果: 找到 {len(results)} 个相关结果")
        
        if results:
            for i, result in enumerate(results[:3], 1):  # 只显示前3个结果
                print(f"   {i}. {result.get('recipient_email', '')} - 相关度: {result.get('relevance_score', 0):.3f}")
        
        print("✅ 智能检索功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 智能检索功能测试失败: {str(e)}")
        return False

def test_duplicate_detection_feature():
    """测试重复检测功能"""
    print("\n🔍 测试重复检测功能...")
    
    try:
        # 导入RAG搜索引擎
        from rag_search_engine import RAGSearchEngine
        
        # 创建搜索引擎实例
        rag_search = RAGSearchEngine()
        
        # 测试数据
        test_subject = "重要通知"
        test_body = "这是一封重要的通知邮件，请查收相关文件。"
        test_sender = "<EMAIL>"
        test_recipients = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ]
        
        print(f"📧 测试主题: {test_subject}")
        print(f"📤 测试发件人: {test_sender}")
        print(f"📬 测试收件人: {len(test_recipients)} 个")
        
        # 执行重复检测
        result = rag_search.advanced_duplicate_detection(
            test_subject, test_body, test_recipients, test_sender
        )
        
        # 显示结果
        total = result.get('total_recipients', 0)
        safe = len(result.get('safe_recipients', []))
        exact = len(result.get('exact_matches', []))
        similar = len(result.get('similar_matches', []))
        
        print(f"📊 检测结果:")
        print(f"   总收件人: {total} 个")
        print(f"   安全发送: {safe} 个")
        print(f"   完全重复: {exact} 个")
        print(f"   相似重复: {similar} 个")
        print(f"   建议: {result.get('recommendation', '无')}")
        
        print("✅ 重复检测功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 重复检测功能测试失败: {str(e)}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成...")
    
    try:
        # 导入主GUI
        from gui_complete_v3 import EmailSenderGUI
        
        # 创建测试窗口
        root = tk.Tk()
        app = EmailSenderGUI(root)
        
        print("✅ GUI初始化成功")
        
        # 测试智能检索按钮是否存在
        smart_search_exists = hasattr(app, 'open_smart_search')
        print(f"   智能检索功能: {'存在' if smart_search_exists else '不存在'}")
        
        # 测试重复检测按钮是否存在
        duplicate_check_exists = hasattr(app, 'check_duplicates')
        print(f"   重复检测功能: {'存在' if duplicate_check_exists else '不存在'}")
        
        # 测试重复检测界面是否存在
        duplicate_interface_exists = hasattr(app, 'open_duplicate_detection')
        print(f"   重复检测界面: {'存在' if duplicate_interface_exists else '不存在'}")
        
        # 测试相关方法是否存在
        methods_to_check = [
            '_perform_smart_search',
            '_start_duplicate_detection',
            '_show_duplicate_detection_result'
        ]
        
        for method in methods_to_check:
            exists = hasattr(app, method)
            print(f"   {method}: {'存在' if exists else '不存在'}")
        
        # 关闭测试窗口
        root.after(100, root.quit)
        root.mainloop()
        root.destroy()
        
        print("✅ GUI集成测试通过")
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {str(e)}")
        return False

def test_feature_completeness():
    """测试功能完整性"""
    print("\n📋 测试功能完整性...")
    
    try:
        # 检查关键文件是否存在
        required_files = [
            'rag_search_engine.py',
            'email_history_manager.py',
            'gui_complete_v3.py'
        ]
        
        for file in required_files:
            if os.path.exists(file):
                print(f"   ✅ {file}: 存在")
            else:
                print(f"   ❌ {file}: 不存在")
                return False
        
        # 检查功能模块是否可以导入
        try:
            from rag_search_engine import RAGSearchEngine
            print("   ✅ RAG搜索引擎: 可导入")
        except ImportError as e:
            print(f"   ❌ RAG搜索引擎: 导入失败 - {e}")
            return False
        
        try:
            from email_history_manager import EmailHistoryManager
            print("   ✅ 邮件历史管理器: 可导入")
        except ImportError as e:
            print(f"   ❌ 邮件历史管理器: 导入失败 - {e}")
            return False
        
        print("✅ 功能完整性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 功能完整性测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试修复后的智能检索和重复检测功能")
    print("=" * 60)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("功能完整性", test_feature_completeness()))
    test_results.append(("智能检索", test_smart_search_feature()))
    test_results.append(("重复检测", test_duplicate_detection_feature()))
    test_results.append(("GUI集成", test_gui_integration()))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能测试通过！3.0系统的智能检索和重复检测功能已修复完成")
        print("\n💡 功能说明:")
        print("   • 智能检索: 可以在历史记录中搜索相关邮件")
        print("   • 重复检测: 可以检测即将发送的邮件是否重复")
        print("   • 快速检测: 在快速操作区域添加了重复检测按钮")
        print("   • 完整界面: 提供了完整的检测结果显示和操作")
    else:
        print("⚠️ 部分功能仍需修复，请检查失败的测试项")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    if not success:
        input("\n按回车键退出...")
