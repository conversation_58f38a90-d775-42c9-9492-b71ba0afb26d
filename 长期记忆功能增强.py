#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2.0系统长期记忆功能增强
为所有功能添加数据持久化，解决重启后数据丢失问题
"""

import os
import json
import datetime
import sqlite3
from typing import Dict, List, Any

class LongTermMemoryManager:
    """长期记忆管理器"""
    
    def __init__(self):
        self.config_dir = "user_data"
        self.db_path = os.path.join(self.config_dir, "user_settings.db")
        self._ensure_directories()
        self._init_database()
    
    def _ensure_directories(self):
        """确保目录存在"""
        if not os.path.exists(self.config_dir):
            os.makedirs(self.config_dir)
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 用户设置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                category TEXT NOT NULL,
                key TEXT NOT NULL,
                value TEXT NOT NULL,
                data_type TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(category, key)
            )
        ''')
        
        # 邮件模板表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                subject TEXT NOT NULL,
                body TEXT NOT NULL,
                attachments TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 收件人组表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recipient_groups (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                group_name TEXT NOT NULL UNIQUE,
                recipients TEXT NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 用户偏好设置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_preferences (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                preference_key TEXT NOT NULL UNIQUE,
                preference_value TEXT NOT NULL,
                preference_type TEXT NOT NULL,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def save_setting(self, category: str, key: str, value: Any, data_type: str = "string"):
        """保存设置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        if data_type in ["dict", "list"]:
            # 处理datetime对象的序列化
            value_str = json.dumps(value, default=self._datetime_serializer)
        else:
            value_str = str(value)
        
        cursor.execute('''
            INSERT OR REPLACE INTO user_settings (category, key, value, data_type, updated_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (category, key, value_str, data_type, datetime.datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
    
    def _datetime_serializer(self, obj):
        """datetime对象的JSON序列化器"""
        if isinstance(obj, datetime.datetime):
            return obj.isoformat()
        elif isinstance(obj, datetime.date):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")
    
    def load_setting(self, category: str, key: str, default_value: Any = None):
        """加载设置"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT value, data_type FROM user_settings 
            WHERE category = ? AND key = ?
        ''', (category, key))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            value_str, data_type = result
            if data_type in ["dict", "list"]:
                return json.loads(value_str)
            elif data_type == "bool":
                return value_str.lower() == "true"
            elif data_type == "int":
                return int(value_str)
            elif data_type == "float":
                return float(value_str)
            else:
                return value_str
        
        return default_value
    
    def save_email_template(self, name: str, subject: str, body: str, attachments: List[str] = None):
        """保存邮件模板"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        attachments_str = json.dumps(attachments or [])
        
        cursor.execute('''
            INSERT OR REPLACE INTO email_templates (name, subject, body, attachments, updated_at)
            VALUES (?, ?, ?, ?, ?)
        ''', (name, subject, body, attachments_str, datetime.datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
    
    def load_email_templates(self) -> List[Dict]:
        """加载邮件模板"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT name, subject, body, attachments, created_at, updated_at 
            FROM email_templates ORDER BY updated_at DESC
        ''')
        
        templates = []
        for row in cursor.fetchall():
            templates.append({
                'name': row[0],
                'subject': row[1],
                'body': row[2],
                'attachments': json.loads(row[3]),
                'created_at': row[4],
                'updated_at': row[5]
            })
        
        conn.close()
        return templates
    
    def save_recipient_group(self, group_name: str, recipients: List[str], description: str = ""):
        """保存收件人组"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        recipients_str = json.dumps(recipients)
        
        cursor.execute('''
            INSERT OR REPLACE INTO recipient_groups (group_name, recipients, description, updated_at)
            VALUES (?, ?, ?, ?)
        ''', (group_name, recipients_str, description, datetime.datetime.now().isoformat()))
        
        conn.commit()
        conn.close()
    
    def load_recipient_groups(self) -> List[Dict]:
        """加载收件人组"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT group_name, recipients, description, created_at, updated_at 
            FROM recipient_groups ORDER BY updated_at DESC
        ''')
        
        groups = []
        for row in cursor.fetchall():
            groups.append({
                'group_name': row[0],
                'recipients': json.loads(row[1]),
                'description': row[2],
                'created_at': row[3],
                'updated_at': row[4]
            })
        
        conn.close()
        return groups

def create_gui_memory_patch():
    """创建GUI长期记忆补丁"""
    patch_content = '''# GUI长期记忆功能补丁
# 将以下代码添加到gui_main.py中

def _init_long_term_memory(self):
    """初始化长期记忆功能"""
    try:
        from 长期记忆功能增强 import LongTermMemoryManager
        self.memory_manager = LongTermMemoryManager()
        
        # 恢复用户设置
        self._restore_user_settings()
        
        # 恢复邮件内容
        self._restore_email_content()
        
        # 恢复发送配置
        self._restore_send_config()
        
        self.log_message("🧠 长期记忆功能已初始化")
        
    except Exception as e:
        self.log_message(f"❌ 长期记忆初始化失败: {str(e)}")

def _restore_user_settings(self):
    """恢复用户设置"""
    try:
        # 恢复发送模式
        send_mode = self.memory_manager.load_setting("ui", "send_mode", "standard")
        self.send_mode.set(send_mode)
        
        # 恢复个性化设置
        add_personalization = self.memory_manager.load_setting("ui", "add_personalization", False, "bool")
        self.add_personalization.set(add_personalization)
        
        # 恢复自动回复监控设置
        auto_reply_monitoring = self.memory_manager.load_setting("ui", "auto_reply_monitoring", True, "bool")
        self.auto_reply_monitoring.set(auto_reply_monitoring)
        
        # 恢复自动队列模式
        auto_queue_mode = self.memory_manager.load_setting("ui", "auto_queue_mode", True, "bool")
        self.auto_queue_mode.set(auto_queue_mode)
        
        self.log_message("✅ 用户设置已恢复")
        
    except Exception as e:
        self.log_message(f"❌ 恢复用户设置失败: {str(e)}")

def _restore_email_content(self):
    """恢复邮件内容"""
    try:
        # 恢复发件人邮箱
        sender_email = self.memory_manager.load_setting("email", "sender_email", "@qq.com")
        self.sender_email.delete(0, tk.END)
        self.sender_email.insert(0, sender_email)
        
        # 恢复收件人列表
        recipients = self.memory_manager.load_setting("email", "recipients", "")
        self.recipient_emails.delete(1.0, tk.END)
        self.recipient_emails.insert(1.0, recipients)
        
        # 恢复邮件主题
        subject = self.memory_manager.load_setting("email", "subject", "")
        self.subject.delete(0, tk.END)
        self.subject.insert(0, subject)
        
        # 恢复邮件正文
        body = self.memory_manager.load_setting("email", "body", "")
        self.body.delete(1.0, tk.END)
        self.body.insert(1.0, body)
        
        # 恢复附件列表
        attachments = self.memory_manager.load_setting("email", "attachments", [], "list")
        self.attachment_listbox.delete(0, tk.END)
        for attachment in attachments:
            self.attachment_listbox.insert(tk.END, attachment)
        
        self.log_message("✅ 邮件内容已恢复")
        
    except Exception as e:
        self.log_message(f"❌ 恢复邮件内容失败: {str(e)}")

def _restore_send_config(self):
    """恢复发送配置"""
    try:
        # 恢复邮件队列
        email_queue = self.memory_manager.load_setting("queue", "email_queue", [], "list")
        self.email_queue = email_queue
        self.update_queue_status()
        
        # 恢复授权码
        auth_codes = self.memory_manager.load_setting("auth", "auth_codes", {}, "dict")
        self.auth_codes = auth_codes
        
        self.log_message("✅ 发送配置已恢复")
        
    except Exception as e:
        self.log_message(f"❌ 恢复发送配置失败: {str(e)}")

def _save_user_settings(self):
    """保存用户设置"""
    try:
        if not hasattr(self, 'memory_manager'):
            return
        
        # 保存发送模式
        self.memory_manager.save_setting("ui", "send_mode", self.send_mode.get())
        
        # 保存个性化设置
        self.memory_manager.save_setting("ui", "add_personalization", self.add_personalization.get(), "bool")
        
        # 保存自动回复监控设置
        self.memory_manager.save_setting("ui", "auto_reply_monitoring", self.auto_reply_monitoring.get(), "bool")
        
        # 保存自动队列模式
        self.memory_manager.save_setting("ui", "auto_queue_mode", self.auto_queue_mode.get(), "bool")
        
    except Exception as e:
        self.log_message(f"❌ 保存用户设置失败: {str(e)}")

def _save_email_content(self):
    """保存邮件内容"""
    try:
        if not hasattr(self, 'memory_manager'):
            return
        
        # 保存发件人邮箱
        self.memory_manager.save_setting("email", "sender_email", self.sender_email.get())
        
        # 保存收件人列表
        self.memory_manager.save_setting("email", "recipients", self.recipient_emails.get(1.0, tk.END).strip())
        
        # 保存邮件主题
        self.memory_manager.save_setting("email", "subject", self.subject.get())
        
        # 保存邮件正文
        self.memory_manager.save_setting("email", "body", self.body.get(1.0, tk.END).strip())
        
        # 保存附件列表
        attachments = list(self.attachment_listbox.get(0, tk.END))
        self.memory_manager.save_setting("email", "attachments", attachments, "list")
        
    except Exception as e:
        self.log_message(f"❌ 保存邮件内容失败: {str(e)}")

def _save_send_config(self):
    """保存发送配置"""
    try:
        if not hasattr(self, 'memory_manager'):
            return
        
        # 保存邮件队列
        self.memory_manager.save_setting("queue", "email_queue", self.email_queue, "list")
        
        # 保存授权码
        self.memory_manager.save_setting("auth", "auth_codes", self.auth_codes, "dict")
        
    except Exception as e:
        self.log_message(f"❌ 保存发送配置失败: {str(e)}")

def _auto_save_data(self):
    """自动保存数据"""
    try:
        self._save_user_settings()
        self._save_email_content()
        self._save_send_config()
        
        # 每30秒自动保存一次
        self.root.after(30000, self._auto_save_data)
        
    except Exception as e:
        self.log_message(f"❌ 自动保存失败: {str(e)}")

# 在__init__方法末尾添加:
# self._init_long_term_memory()
# self._auto_save_data()

# 在程序退出时添加:
def on_closing(self):
    """程序退出时保存数据"""
    try:
        self._save_user_settings()
        self._save_email_content()
        self._save_send_config()
        self.log_message("💾 数据已保存")
    except Exception as e:
        self.log_message(f"❌ 退出保存失败: {str(e)}")
    finally:
        self.root.destroy()

# 绑定关闭事件:
# self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
'''
    
    with open('gui_memory_patch.py', 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print("✅ GUI长期记忆补丁已创建: gui_memory_patch.py")

def main():
    """主函数"""
    print("🧠 2.0系统长期记忆功能增强")
    print("="*60)
    
    # 1. 创建长期记忆管理器
    print("📋 创建长期记忆管理器...")
    memory_manager = LongTermMemoryManager()
    print("✅ 长期记忆管理器已创建")
    
    # 2. 创建GUI补丁
    print("🖥️ 创建GUI长期记忆补丁...")
    create_gui_memory_patch()
    
    # 3. 创建配置文件备份
    print("💾 创建配置文件备份...")
    create_config_backup()
    
    # 4. 生成使用说明
    print("📖 生成使用说明...")
    create_usage_guide()
    
    print("\n✅ 长期记忆功能增强完成!")
    print("💡 请按以下步骤操作:")
    print("   1. 将gui_memory_patch.py中的代码添加到gui_main.py")
    print("   2. 重启2.0系统")
    print("   3. 系统将自动保存和恢复所有用户数据")

def create_config_backup():
    """创建配置文件备份"""
    config_files = [
        'all_features_config.json',
        'auth_codes.json',
        'monitor_settings.json',
        'startup_config.json',
        'automation_workflow.json'
    ]
    
    backup_dir = "config_backup"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
    
    for config_file in config_files:
        if os.path.exists(config_file):
            import shutil
            backup_file = os.path.join(backup_dir, f"{config_file}.backup")
            shutil.copy2(config_file, backup_file)
            print(f"   ✅ 已备份: {config_file}")

def create_usage_guide():
    """创建使用说明"""
    guide_content = '''# 🧠 长期记忆功能使用说明

## 📋 功能概述

长期记忆功能为2.0系统添加了完整的数据持久化能力，确保重启后所有用户数据都能恢复。

## 🔧 自动保存的数据

### 用户界面设置
- ✅ 发送模式（标准/快速/安全）
- ✅ 个性化设置（邮件编号和时间戳）
- ✅ 自动回复监控开关
- ✅ 自动队列模式开关

### 邮件内容
- ✅ 发件人邮箱地址
- ✅ 收件人列表
- ✅ 邮件主题
- ✅ 邮件正文
- ✅ 附件列表

### 发送配置
- ✅ 邮件发送队列
- ✅ 授权码管理
- ✅ 全功能模式配置

### 高级功能数据
- ✅ 监控设置
- ✅ 质量数据库
- ✅ 应急管理配置
- ✅ 反垃圾邮件设置

## 🚀 使用方法

1. **自动保存**: 系统每30秒自动保存一次数据
2. **退出保存**: 程序退出时自动保存所有数据
3. **启动恢复**: 程序启动时自动恢复所有数据

## 📁 数据存储位置

- 用户数据: `user_data/user_settings.db`
- 配置备份: `config_backup/`
- 邮件模板: 数据库中的email_templates表
- 收件人组: 数据库中的recipient_groups表

## 💡 使用建议

1. **定期备份**: 建议定期备份user_data目录
2. **模板管理**: 使用邮件模板功能保存常用邮件
3. **收件人组**: 使用收件人组功能管理常用收件人列表
4. **数据迁移**: 可以通过复制user_data目录迁移数据

## 🔧 故障排除

如果数据恢复失败：
1. 检查user_data目录是否存在
2. 检查user_settings.db文件是否损坏
3. 查看日志中的错误信息
4. 从config_backup目录恢复配置文件

## 🎉 新增功能

- 📧 邮件模板管理
- 👥 收件人组管理
- ⚙️ 用户偏好设置
- 🔄 自动数据同步
'''
    
    with open('长期记忆功能说明.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ 使用说明已创建: 长期记忆功能说明.md")

if __name__ == "__main__":
    main()
