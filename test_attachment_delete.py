#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试附件删除功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_attachment_delete_function():
    """测试附件删除功能"""
    print("🔧 测试附件删除功能")
    print("=" * 50)
    
    try:
        # 导入GUI模块
        import gui_main
        print("✅ gui_main模块导入成功")
        
        # 创建测试GUI实例
        root = tk.Tk()
        root.title("附件删除功能测试")
        root.geometry("500x400")
        
        app = gui_main.EmailSenderGUI(root)
        print("✅ EmailSenderGUI实例创建成功")
        
        # 检查附件列表框是否存在
        if hasattr(app, 'attachment_listbox'):
            print("✅ 附件列表框存在")
            
            # 添加一些测试附件
            test_files = [
                "测试文件1.txt",
                "测试文件2.pdf", 
                "测试文件3.docx"
            ]
            
            print("\n📁 添加测试附件:")
            for file in test_files:
                app.attachment_listbox.insert(tk.END, file)
                print(f"  + {file}")
            
            print(f"\n📋 当前附件数量: {app.attachment_listbox.size()}")
            
            # 测试删除功能
            print("\n🗑️ 测试删除功能:")
            
            # 1. 测试没有选中时的删除（应该删除最后一个）
            print("  1. 测试无选中删除（删除最后一个）:")
            before_count = app.attachment_listbox.size()
            app.remove_attachment()
            after_count = app.attachment_listbox.size()
            print(f"     删除前: {before_count}, 删除后: {after_count}")
            
            if after_count == before_count - 1:
                print("     ✅ 无选中删除功能正常")
            else:
                print("     ❌ 无选中删除功能异常")
            
            # 2. 测试选中删除
            print("  2. 测试选中删除:")
            if app.attachment_listbox.size() > 0:
                # 选中第一个
                app.attachment_listbox.selection_set(0)
                selected_file = app.attachment_listbox.get(0)
                print(f"     选中文件: {selected_file}")
                
                before_count = app.attachment_listbox.size()
                app.remove_attachment()
                after_count = app.attachment_listbox.size()
                print(f"     删除前: {before_count}, 删除后: {after_count}")
                
                if after_count == before_count - 1:
                    print("     ✅ 选中删除功能正常")
                else:
                    print("     ❌ 选中删除功能异常")
            
            # 3. 测试清空功能
            print("  3. 测试清空功能:")
            if app.attachment_listbox.size() > 0:
                before_count = app.attachment_listbox.size()
                print(f"     清空前附件数量: {before_count}")
                
                # 模拟用户点击"是"
                import tkinter.messagebox
                original_askyesno = tkinter.messagebox.askyesno
                tkinter.messagebox.askyesno = lambda title, message: True
                
                app.clear_attachments()
                
                # 恢复原函数
                tkinter.messagebox.askyesno = original_askyesno
                
                after_count = app.attachment_listbox.size()
                print(f"     清空后附件数量: {after_count}")
                
                if after_count == 0:
                    print("     ✅ 清空功能正常")
                else:
                    print("     ❌ 清空功能异常")
            
            # 4. 测试空列表删除
            print("  4. 测试空列表删除:")
            app.remove_attachment()  # 应该显示提示信息
            print("     ✅ 空列表删除测试完成")
            
            print(f"\n📊 最终附件数量: {app.attachment_listbox.size()}")
            
        else:
            print("❌ 附件列表框不存在")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def create_interactive_test():
    """创建交互式测试界面"""
    print("\n🧪 创建交互式测试界面")
    print("-" * 50)
    
    try:
        # 创建测试窗口
        test_window = tk.Tk()
        test_window.title("附件删除功能交互测试")
        test_window.geometry("600x500")
        
        main_frame = ttk.Frame(test_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(main_frame, text="📎 附件删除功能测试", 
                 font=('Microsoft YaHei UI', 14, 'bold')).pack(pady=(0, 20))
        
        # 创建附件管理区域
        attachment_frame = ttk.LabelFrame(main_frame, text="附件管理测试", padding="10")
        attachment_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 附件列表
        attachment_listbox = tk.Listbox(attachment_frame, height=6, font=('Microsoft YaHei UI', 9))
        attachment_listbox.pack(fill=tk.X, pady=(0, 10))
        
        # 添加一些测试数据
        test_files = [
            "测试文件1.txt",
            "测试文件2.pdf", 
            "测试文件3.docx",
            "测试文件4.jpg",
            "测试文件5.zip"
        ]
        
        for file in test_files:
            attachment_listbox.insert(tk.END, file)
        
        # 按钮区域
        button_frame = ttk.Frame(attachment_frame)
        button_frame.pack(fill=tk.X)
        
        def add_test_file():
            import random
            file_types = ['.txt', '.pdf', '.docx', '.jpg', '.png', '.zip', '.xlsx']
            filename = f"新文件{random.randint(1, 999)}{random.choice(file_types)}"
            attachment_listbox.insert(tk.END, filename)
            print(f"📁 添加文件: {filename}")
        
        def delete_selected():
            selection = attachment_listbox.curselection()
            if selection:
                filename = attachment_listbox.get(selection[0])
                attachment_listbox.delete(selection[0])
                print(f"🗑️ 删除文件: {filename}")
            else:
                if attachment_listbox.size() > 0:
                    last_index = attachment_listbox.size() - 1
                    filename = attachment_listbox.get(last_index)
                    attachment_listbox.delete(last_index)
                    print(f"🗑️ 删除最后一个文件: {filename}")
                else:
                    print("⚠️ 没有文件可删除")
                    tk.messagebox.showinfo("提示", "没有文件可删除")
        
        def clear_all():
            count = attachment_listbox.size()
            if count == 0:
                print("⚠️ 没有文件需要清空")
                tk.messagebox.showinfo("提示", "没有文件需要清空")
                return
                
            result = tk.messagebox.askyesno("确认清空", f"确定要清空所有 {count} 个文件吗？")
            if result:
                attachment_listbox.delete(0, tk.END)
                print(f"🧹 清空了 {count} 个文件")
                tk.messagebox.showinfo("完成", f"已清空 {count} 个文件")
        
        # 按钮
        ttk.Button(button_frame, text="📁 添加测试文件", command=add_test_file).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🗑️ 删除选中", command=delete_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧹 清空全部", command=clear_all).pack(side=tk.LEFT, padx=5)
        
        # 说明文本
        info_text = """
使用说明：
1. 点击列表中的文件可以选中
2. "删除选中"按钮：如果有选中文件则删除选中的，否则删除最后一个
3. "清空全部"按钮：清空所有文件，会有确认对话框
4. "添加测试文件"按钮：随机添加一个测试文件

测试步骤：
• 先选中一个文件，然后点击"删除选中"
• 不选中任何文件，直接点击"删除选中"
• 点击"清空全部"测试确认对话框
        """
        
        info_label = tk.Text(main_frame, height=12, font=('Microsoft YaHei UI', 9), 
                            wrap=tk.WORD, bg='#f0f0f0')
        info_label.pack(fill=tk.BOTH, expand=True)
        info_label.insert(1.0, info_text)
        info_label.config(state='disabled')
        
        print("✅ 交互式测试界面创建成功")
        print("💡 您可以在界面中测试各种删除功能")
        
        # 运行测试界面
        test_window.mainloop()
        
    except Exception as e:
        print(f"❌ 创建交互式测试界面失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 开始测试附件删除功能...")
    
    # 自动测试
    success = test_attachment_delete_function()
    
    if success:
        print("\n🎉 自动测试通过！")
    else:
        print("\n⚠️ 自动测试失败")
    
    # 交互式测试
    print("\n" + "=" * 50)
    create_interactive_test()
    
    print("\n" + "=" * 50)
    print("测试完成")
