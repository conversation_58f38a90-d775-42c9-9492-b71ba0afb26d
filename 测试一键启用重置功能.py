#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试一键启用和重置全功能模式
"""

import tkinter as tk
import time
from gui_main import EmailSenderGUI

def test_enable_reset_features():
    """测试一键启用和重置功能"""
    print("🚀 测试一键启用和重置全功能模式")
    print("=" * 60)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建GUI实例
        app = EmailSenderGUI(root)
        
        print("✅ GUI创建成功")
        
        # 检查按钮是否存在
        print("\n🔧 检查按钮状态:")
        
        if hasattr(app, 'enable_all_button'):
            print(f"  ✅ 一键启用按钮存在，状态: {app.enable_all_button['state']}")
        else:
            print("  ❌ 一键启用按钮不存在")
            
        if hasattr(app, 'reset_all_button'):
            print(f"  ✅ 重置按钮存在，状态: {app.reset_all_button['state']}")
        else:
            print("  ❌ 重置按钮不存在")
        
        # 检查状态栏按钮
        if hasattr(app, 'quick_enable_button'):
            print("  ✅ 状态栏快速启用按钮存在")
        else:
            print("  ❌ 状态栏快速启用按钮不存在")
        
        # 检查功能方法
        print("\n🔧 检查功能方法:")
        
        enable_methods = [
            'enable_all_features',
            'reset_all_features',
            '_init_all_components',
            '_enable_auto_reply_monitoring',
            '_enable_quality_database',
            '_enable_anti_spam',
            '_enable_qq_emergency',
            '_enable_smart_queue',
            '_enable_deep_coordination',
            '_finalize_all_features'
        ]
        
        for method in enable_methods:
            if hasattr(app, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        # 检查重置方法
        print("\n🔄 检查重置方法:")
        
        reset_methods = [
            '_reset_auto_reply_monitoring',
            '_reset_quality_database',
            '_reset_anti_spam',
            '_reset_qq_emergency',
            '_reset_smart_queue',
            '_reset_deep_coordination',
            '_cleanup_all_features_config',
            '_finalize_reset',
            '_show_reset_completed'
        ]
        
        for method in reset_methods:
            if hasattr(app, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        # 检查状态管理
        print("\n📊 检查状态管理:")
        
        if hasattr(app, 'all_features_enabled'):
            print(f"  ✅ all_features_enabled: {getattr(app, 'all_features_enabled', False)}")
        else:
            print("  ❌ all_features_enabled 属性不存在")
            
        if hasattr(app, 'feature_status'):
            print(f"  ✅ feature_status: {getattr(app, 'feature_status', {})}")
        else:
            print("  ❌ feature_status 属性不存在")
        
        # 模拟测试启用过程
        print("\n🧪 模拟测试启用过程:")
        
        # 设置测试邮箱
        test_email = "<EMAIL>"
        app.sender_email.delete(0, tk.END)
        app.sender_email.insert(0, test_email)
        print(f"  ✅ 设置测试邮箱: {test_email}")
        
        # 测试各个启用步骤
        test_steps = [
            ("初始化系统组件", app._init_all_components),
            ("启用自动回复监控", app._enable_auto_reply_monitoring),
            ("启用质量数据库", app._enable_quality_database),
            ("启用反垃圾邮件", app._enable_anti_spam),
            ("启用QQ应急管理", app._enable_qq_emergency),
            ("启用智能队列", app._enable_smart_queue),
            ("启用深度协调", app._enable_deep_coordination),
            ("完成全功能配置", app._finalize_all_features)
        ]
        
        all_success = True
        
        for step_name, step_func in test_steps:
            try:
                result = step_func(test_email)
                if result['success']:
                    print(f"  ✅ {step_name} - 测试成功")
                else:
                    print(f"  ❌ {step_name} - 测试失败: {result['error']}")
                    all_success = False
            except Exception as e:
                print(f"  ❌ {step_name} - 测试异常: {str(e)}")
                all_success = False
        
        # 检查启用后的状态
        print("\n📈 检查启用后状态:")
        
        if hasattr(app, 'all_features_enabled') and app.all_features_enabled:
            print("  ✅ 全功能模式已启用")
        else:
            print("  ❌ 全功能模式未启用")
        
        if hasattr(app, 'enable_all_button'):
            print(f"  ✅ 一键启用按钮状态: {app.enable_all_button['state']}")
        
        if hasattr(app, 'reset_all_button'):
            print(f"  ✅ 重置按钮状态: {app.reset_all_button['state']}")
        
        # 测试重置过程
        print("\n🔄 模拟测试重置过程:")
        
        reset_steps = [
            ("重置自动回复监控", app._reset_auto_reply_monitoring),
            ("重置质量数据库", app._reset_quality_database),
            ("重置反垃圾邮件", app._reset_anti_spam),
            ("重置QQ应急管理", app._reset_qq_emergency),
            ("重置智能队列", app._reset_smart_queue),
            ("重置深度协调", app._reset_deep_coordination),
            ("清理配置文件", app._cleanup_all_features_config),
            ("完成重置操作", app._finalize_reset)
        ]
        
        for step_name, step_func in reset_steps:
            try:
                result = step_func()
                if result['success']:
                    print(f"  ✅ {step_name} - 测试成功")
                else:
                    print(f"  ❌ {step_name} - 测试失败: {result['error']}")
                    all_success = False
            except Exception as e:
                print(f"  ❌ {step_name} - 测试异常: {str(e)}")
                all_success = False
        
        # 检查重置后的状态
        print("\n📉 检查重置后状态:")
        
        if hasattr(app, 'all_features_enabled') and not app.all_features_enabled:
            print("  ✅ 全功能模式已重置")
        else:
            print("  ❌ 全功能模式未重置")
        
        if hasattr(app, 'enable_all_button'):
            print(f"  ✅ 一键启用按钮状态: {app.enable_all_button['state']}")
        
        if hasattr(app, 'reset_all_button'):
            print(f"  ✅ 重置按钮状态: {app.reset_all_button['state']}")
        
        # 最终结果
        print("\n" + "=" * 60)
        if all_success:
            print("🎉 一键启用和重置功能测试成功！")
            print("✅ 所有功能方法都能正常工作")
            print("✅ 按钮状态切换正常")
            print("✅ 功能状态跟踪正确")
            print("✅ 启用和重置流程完整")
        else:
            print("⚠️ 一键启用和重置功能存在问题")
            print("请检查上述错误信息")
        
        print("\n💡 使用说明:")
        print("1. 启用：点击'🚀 一键启用全功能'按钮")
        print("2. 重置：点击'🔄 重置全功能'按钮")
        print("3. 状态栏也有对应的快速按钮")
        print("4. 启用后一键启用按钮会禁用，重置按钮会启用")
        print("5. 重置后一键启用按钮会启用，重置按钮会禁用")
        
        # 关闭窗口
        root.destroy()
        
        return all_success
        
    except Exception as e:
        print(f"❌ 测试过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enable_reset_features()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成：一键启用和重置功能完全正常！")
        exit(0)
    else:
        print("❌ 测试失败：一键启用和重置功能存在问题")
        exit(1)
