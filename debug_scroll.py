#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试滚动功能
"""

import tkinter as tk
from tkinter import ttk, scrolledtext

def test_scroll():
    root = tk.Tk()
    root.title("滚动功能调试")
    root.geometry("800x600")
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    ttk.Label(main_frame, text="滚动功能调试测试", 
             font=('Microsoft YaHei UI', 16, 'bold')).pack(pady=(0, 10))
    
    # 创建队列列表框架
    queue_frame = ttk.LabelFrame(main_frame, text="📋 队列任务列表", padding="5")
    queue_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    # 创建滚动文本框
    queue_text = scrolledtext.ScrolledText(
        queue_frame,
        width=80,
        height=20,
        font=('Consolas', 10),
        wrap=tk.WORD,
        relief='solid',
        borderwidth=1,
        bg='#1e293b',
        fg='#e2e8f0',
        insertbackground='#3b82f6',
        selectbackground='#374151'
    )
    queue_text.pack(fill=tk.BOTH, expand=True)
    
    # 添加大量测试内容
    test_content = """📋 队列任务列表 (共 20 个任务)
============================================================
⏳ #01 | 重要系统通知 - 服务器维护        | 3人 | 标准 | 10:30:15
📤 #02 | 用户反馈处理 - 功能改进建议      | 5人 | 快速 | 10:31:20
✅ #03 | 月度数据报告 - 业务分析          | 2人 | 安全 | 10:32:25
❌ #04 | 紧急故障通知 - 网络中断          | 1人 | 标准 | 10:33:30
⏳ #05 | 版本发布公告 - 新功能上线        | 4人 | 快速 | 10:34:35
📤 #06 | 培训邀请通知 - 技能提升          | 6人 | 安全 | 10:35:40
✅ #07 | 会议安排通知 - 项目讨论          | 3人 | 标准 | 10:36:45
❌ #08 | 安全警报通知 - 异常访问          | 2人 | 快速 | 10:37:50
⏳ #09 | 客户服务通知 - 满意度调查        | 5人 | 安全 | 10:38:55
📤 #10 | 系统升级通知 - 性能优化          | 1人 | 标准 | 10:40:00
✅ #11 | 备份完成通知 - 数据安全          | 3人 | 快速 | 10:41:05
❌ #12 | 监控报警通知 - 资源使用          | 4人 | 安全 | 10:42:10
⏳ #13 | 审计报告通知 - 合规检查          | 2人 | 标准 | 10:43:15
📤 #14 | 新员工欢迎 - 入职指导            | 6人 | 快速 | 10:44:20
✅ #15 | 项目进度更新 - 里程碑达成        | 3人 | 安全 | 10:45:25
❌ #16 | 系统维护通知 - 定期检查          | 1人 | 标准 | 10:46:30
⏳ #17 | 用户调研通知 - 需求收集          | 5人 | 快速 | 10:47:35
📤 #18 | 产品发布通知 - 市场推广          | 4人 | 安全 | 10:48:40
✅ #19 | 培训完成通知 - 证书颁发          | 2人 | 标准 | 10:49:45
❌ #20 | 年度总结通知 - 绩效评估          | 3人 | 快速 | 10:50:50
============================================================
📊 统计: 待发送 5 | 发送中 4 | 已完成 5 | 失败 6

🖱️ 滚动测试说明：
• 这个文本框应该可以使用鼠标滚轮滚动
• 右侧应该有滚动条可以拖拽
• 内容超过显示区域时会自动显示滚动条
• 可以使用键盘上下箭头键滚动（需要先点击文本框获得焦点）

💡 如果看到这些内容，说明文本框正常工作
🔍 如果无法滚动，可能的原因：
1. 内容不够多，没有超出显示区域
2. 滚动条被禁用
3. 鼠标事件被阻止
4. 文本框状态设置有问题

🧪 测试步骤：
1. 尝试使用鼠标滚轮向上向下滚动
2. 尝试点击并拖拽右侧滚动条
3. 尝试点击滚动条上下箭头
4. 尝试使用键盘方向键（需要先点击文本框）

📝 预期结果：
- 应该能够看到所有20个任务
- 滚动应该流畅无卡顿
- 滚动条应该正确反映当前位置

如果以上功能都正常，说明滚动功能没有问题。
如果有问题，请检查：
- tkinter版本是否支持scrolledtext
- 是否有其他组件阻挡了鼠标事件
- 窗口大小是否足够显示滚动条

这是测试内容的结尾。如果能看到这行文字，说明已经滚动到底部了。"""

    queue_text.insert(tk.END, test_content)
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))
    
    def scroll_to_top():
        queue_text.see(1.0)
    
    def scroll_to_bottom():
        queue_text.see(tk.END)
    
    def add_more_content():
        queue_text.insert(tk.END, "\n\n🆕 新增内容：\n")
        for i in range(5):
            queue_text.insert(tk.END, f"📧 新任务 #{21+i} | 测试邮件 {21+i} | 1人 | 标准 | 11:0{i}:00\n")
        queue_text.see(tk.END)
    
    def clear_content():
        queue_text.delete(1.0, tk.END)
        queue_text.insert(tk.END, "📭 内容已清空\n💡 点击'添加内容'按钮重新添加测试内容")
    
    def toggle_state():
        current_state = queue_text.cget('state')
        if current_state == tk.NORMAL:
            queue_text.config(state=tk.DISABLED)
            state_btn.config(text="启用编辑")
        else:
            queue_text.config(state=tk.NORMAL)
            state_btn.config(text="禁用编辑")
    
    ttk.Button(button_frame, text="滚动到顶部", command=scroll_to_top).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="滚动到底部", command=scroll_to_bottom).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="添加内容", command=add_more_content).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="清空内容", command=clear_content).pack(side=tk.LEFT, padx=5)
    state_btn = ttk.Button(button_frame, text="禁用编辑", command=toggle_state)
    state_btn.pack(side=tk.LEFT, padx=5)
    
    # 状态信息
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(fill=tk.X, pady=(10, 0))
    
    status_label = ttk.Label(status_frame, 
                           text="🔍 请测试滚动功能：使用鼠标滚轮、拖拽滚动条、点击滚动条箭头", 
                           font=('Microsoft YaHei UI', 10))
    status_label.pack()
    
    root.mainloop()

if __name__ == "__main__":
    test_scroll()
