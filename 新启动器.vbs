' ================================================================
' 邮件系统启动器 v3.0
' 功能: 稳定启动邮件系统GUI，避免闪退问题
' 作者: AI助手
' 日期: 2025-06-12
' ================================================================

Option Explicit

' 声明变量
Dim objShell, objFSO, currentDir, scriptPath, command, result
Dim pythonCmd, errorMsg, startupMsg

' 初始化对象
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取当前目录
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 显示启动信息
startupMsg = "邮件系统启动器 v3.0" & vbCrLf & vbCrLf & _
             "正在检查环境..." & vbCrLf & _
             "当前目录: " & currentDir

' 检查主脚本文件
scriptPath = currentDir & "\gui_main.py"
If Not objFSO.FileExists(scriptPath) Then
    errorMsg = "错误: 找不到主程序文件!" & vbCrLf & vbCrLf & _
               "缺少文件: gui_main.py" & vbCrLf & _
               "当前目录: " & currentDir & vbCrLf & vbCrLf & _
               "请确保gui_main.py文件在同一目录中。"
    MsgBox errorMsg, vbCritical + vbOKOnly, "文件缺失"
    WScript.Quit 1
End If

' 检测Python环境
pythonCmd = ""
On Error Resume Next

' 方法1: 测试python命令
Err.Clear
result = objShell.Run("python --version", 0, True)
If Err.Number = 0 And result = 0 Then
    pythonCmd = "python"
End If

' 方法2: 如果python失败，测试python3命令
If pythonCmd = "" Then
    Err.Clear
    result = objShell.Run("python3 --version", 0, True)
    If Err.Number = 0 And result = 0 Then
        pythonCmd = "python3"
    End If
End If

' 方法3: 如果都失败，测试py命令
If pythonCmd = "" Then
    Err.Clear
    result = objShell.Run("py --version", 0, True)
    If Err.Number = 0 And result = 0 Then
        pythonCmd = "py"
    End If
End If

On Error GoTo 0

' 如果没有找到Python，显示错误
If pythonCmd = "" Then
    errorMsg = "错误: 未找到Python环境!" & vbCrLf & vbCrLf & _
               "请安装Python 3.6或更高版本。" & vbCrLf & vbCrLf & _
               "安装后确保Python已添加到系统PATH环境变量。" & vbCrLf & vbCrLf & _
               "下载地址: https://www.python.org/downloads/"
    MsgBox errorMsg, vbCritical + vbOKOnly, "Python环境错误"
    WScript.Quit 2
End If

' 构建启动命令
command = "cmd.exe /c ""cd /d """ & currentDir & """ && " & pythonCmd & " gui_main.py"""

' 显示启动信息
startupMsg = "邮件系统启动中..." & vbCrLf & vbCrLf & _
             "Python命令: " & pythonCmd & vbCrLf & _
             "主程序: gui_main.py" & vbCrLf & vbCrLf & _
             "请稍等片刻..."

' 启动程序
On Error Resume Next
result = objShell.Run(command, 1, False)

If Err.Number <> 0 Then
    errorMsg = "启动失败!" & vbCrLf & vbCrLf & _
               "错误代码: " & Err.Number & vbCrLf & _
               "错误描述: " & Err.Description & vbCrLf & vbCrLf & _
               "请尝试手动运行以下命令:" & vbCrLf & _
               pythonCmd & " gui_main.py"
    MsgBox errorMsg, vbCritical + vbOKOnly, "启动错误"
    WScript.Quit 3
Else
    ' 启动成功，显示提示（可选）
    ' MsgBox "邮件系统启动成功!" & vbCrLf & "图形界面正在加载...", vbInformation + vbOKOnly, "启动成功"
End If

On Error GoTo 0

' 脚本结束
WScript.Quit 0
