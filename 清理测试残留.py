#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理测试残留文件脚本
用于清理开发和测试过程中产生的临时文件
"""

import os
import json
import datetime
import sqlite3

def clean_test_files():
    """清理测试残留文件"""
    print("🧹 开始清理测试残留文件...")
    
    # 要清理的文件列表
    files_to_clean = [
        "email_send_progress.json",
        "test_progress.json", 
        "temp_progress.json",
        "debug_progress.json"
    ]
    
    cleaned_count = 0
    
    # 清理进度文件
    for filename in files_to_clean:
        if os.path.exists(filename):
            try:
                # 检查文件修改时间
                file_mtime = os.path.getmtime(filename)
                file_time = datetime.datetime.fromtimestamp(file_mtime)
                now = datetime.datetime.now()
                hours_ago = (now - file_time).total_seconds() / 3600
                
                # 如果是测试文件或者很久没修改，删除
                if 'test' in filename.lower() or hours_ago > 1:
                    os.remove(filename)
                    print(f"✅ 已删除: {filename} (修改于 {hours_ago:.1f} 小时前)")
                    cleaned_count += 1
                else:
                    print(f"⏭️ 跳过: {filename} (最近修改，可能正在使用)")
                    
            except Exception as e:
                print(f"❌ 删除 {filename} 失败: {str(e)}")
    
    # 清理测试数据库记录
    try:
        if os.path.exists("email_history.db"):
            print("\n🗄️ 清理测试数据库记录...")
            conn = sqlite3.connect("email_history.db")
            cursor = conn.cursor()
            
            # 删除测试邮件记录（包含test字样的）
            cursor.execute("""
                DELETE FROM email_records 
                WHERE sender_email LIKE '%test%' 
                   OR recipient_email LIKE '%test%'
                   OR subject LIKE '%test%'
                   OR subject LIKE '%测试%'
                   OR body LIKE '%test%'
                   OR body LIKE '%测试%'
            """)
            
            test_records_deleted = cursor.rowcount
            
            # 删除过期记录（超过30天的）
            cutoff_date = (datetime.datetime.now() - datetime.timedelta(days=30)).isoformat()
            cursor.execute("DELETE FROM email_records WHERE send_time < ?", (cutoff_date,))
            old_records_deleted = cursor.rowcount
            
            conn.commit()
            conn.close()
            
            if test_records_deleted > 0:
                print(f"✅ 已删除 {test_records_deleted} 条测试邮件记录")
            if old_records_deleted > 0:
                print(f"✅ 已删除 {old_records_deleted} 条过期邮件记录")
                
    except Exception as e:
        print(f"❌ 清理数据库失败: {str(e)}")
    
    # 清理缓存文件
    cache_dirs = ["__pycache__"]
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                import shutil
                shutil.rmtree(cache_dir)
                print(f"✅ 已删除缓存目录: {cache_dir}")
                cleaned_count += 1
            except Exception as e:
                print(f"❌ 删除缓存目录 {cache_dir} 失败: {str(e)}")
    
    print(f"\n🎉 清理完成！共清理了 {cleaned_count} 个文件/目录")
    return cleaned_count

def optimize_startup():
    """优化启动体验"""
    print("\n⚡ 优化启动体验...")
    
    # 创建智能启动配置
    startup_config = {
        "intelligent_popup": True,          # 启用智能弹窗
        "auto_clean_old_progress": True,    # 自动清理过期文件
        "max_progress_file_age_hours": 24,  # 文件过期时间
        "popup_for_test_data": False,       # 测试数据不弹窗
        "popup_for_old_tasks": False,       # 旧任务不弹窗（超过阈值）
        "popup_threshold_hours": 1,         # 弹窗阈值：1小时内的任务
        "popup_min_emails": 3,              # 最小邮件数：少于3封不弹窗
        "show_welcome_message": False,      # 不显示欢迎消息
        "last_cleanup": datetime.datetime.now().isoformat()
    }
    
    try:
        with open("startup_config.json", "w", encoding="utf-8") as f:
            json.dump(startup_config, f, ensure_ascii=False, indent=2)
        print("✅ 已创建启动优化配置")
    except Exception as e:
        print(f"❌ 创建启动配置失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 邮件系统清理工具")
    print("=" * 50)
    
    # 清理测试文件
    cleaned_count = clean_test_files()
    
    # 优化启动
    optimize_startup()
    
    print("\n" + "=" * 50)
    print("✨ 清理完成！现在启动系统应该不会再出现测试弹窗了。")
    print("💡 建议：定期运行此脚本来保持系统整洁。")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
