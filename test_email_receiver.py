#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮件接收器功能
"""

import os
import sys
from email_receiver import EmailReceiver

def test_imap_connection():
    """测试IMAP连接"""
    print("🔧 测试IMAP连接功能")
    print("=" * 50)
    
    # 获取用户输入
    email_address = input("请输入邮箱地址: ").strip()
    password = input("请输入IMAP密码（通常与SMTP授权码相同）: ").strip()
    
    if not email_address or not password:
        print("❌ 邮箱地址和密码不能为空")
        return False
    
    try:
        # 创建邮件接收器
        receiver = EmailReceiver(email_address, password)
        
        # 测试连接
        print("📡 正在测试IMAP连接...")
        if receiver.test_connection():
            print("✅ IMAP连接测试成功！")
            return True, receiver
        else:
            print("❌ IMAP连接测试失败")
            return False, None
            
    except Exception as e:
        print(f"❌ 连接测试失败: {str(e)}")
        return False, None

def test_check_replies(receiver, email_address):
    """测试检查自动回复"""
    print("\n🔍 测试检查自动回复功能")
    print("=" * 50)
    
    try:
        print("📬 检查最近24小时的自动回复...")
        replies = receiver.check_recent_replies(hours=24)
        
        if replies:
            print(f"✅ 发现 {len(replies)} 个自动回复:")
            for i, reply in enumerate(replies, 1):
                print(f"\n【回复 {i}】")
                print(f"收件人: {reply['recipient_email']}")
                print(f"类型: {reply['reply_type']}")
                print(f"时间: {reply['reply_time']}")
                print(f"主题: {reply['subject']}")
                print(f"内容预览: {reply['body'][:100]}...")
        else:
            print("📭 未发现自动回复")
        
        return replies
        
    except Exception as e:
        print(f"❌ 检查自动回复失败: {str(e)}")
        return []

def test_recipient_analysis(receiver, email_address):
    """测试收件人分析"""
    print("\n📊 测试收件人分析功能")
    print("=" * 50)
    
    try:
        analysis = receiver.get_recipient_analysis(email_address)
        
        if analysis:
            print("📈 分析结果:")
            print(f"总收件人数: {analysis.get('total_recipients', 0)}")
            print(f"活跃收件人: {analysis.get('active_recipients', 0)}")
            print(f"无效收件人: {analysis.get('invalid_recipients', 0)}")
            print(f"未知状态: {analysis.get('unknown_recipients', 0)}")
            
            # 显示状态统计
            status_summary = analysis.get('status_summary', {})
            if status_summary:
                print("\n📊 详细状态统计:")
                for status, count in status_summary.items():
                    print(f"  {status}: {count}")
            
            # 显示最近回复
            recent_replies = analysis.get('recent_replies', [])
            if recent_replies:
                print(f"\n📬 最近 {len(recent_replies)} 个回复:")
                for reply in recent_replies[:5]:  # 只显示前5个
                    print(f"  - {reply[0]}: {reply[1]} ({reply[2]})")
            
            # 显示问题收件人
            problematic = analysis.get('problematic_recipients', [])
            if problematic:
                print(f"\n⚠️ 问题收件人 ({len(problematic)} 个):")
                for recipient in problematic[:5]:  # 只显示前5个
                    print(f"  - {recipient[0]}: {recipient[1]} (退信{recipient[2]}次)")
        else:
            print("📭 暂无分析数据")
        
        return analysis
        
    except Exception as e:
        print(f"❌ 收件人分析失败: {str(e)}")
        return {}

def test_valid_invalid_recipients(receiver, email_address):
    """测试获取有效和无效收件人"""
    print("\n✅❌ 测试有效/无效收件人功能")
    print("=" * 50)
    
    try:
        # 获取有效收件人
        valid_recipients = receiver.get_valid_recipients(email_address)
        print(f"✅ 有效收件人 ({len(valid_recipients)} 个):")
        for recipient in valid_recipients[:10]:  # 只显示前10个
            print(f"  - {recipient}")
        
        if len(valid_recipients) > 10:
            print(f"  ... 还有 {len(valid_recipients) - 10} 个")
        
        # 获取无效收件人
        invalid_recipients = receiver.get_invalid_recipients(email_address)
        print(f"\n❌ 无效收件人 ({len(invalid_recipients)} 个):")
        for recipient in invalid_recipients[:10]:  # 只显示前10个
            print(f"  - {recipient}")
        
        if len(invalid_recipients) > 10:
            print(f"  ... 还有 {len(invalid_recipients) - 10} 个")
        
        return valid_recipients, invalid_recipients
        
    except Exception as e:
        print(f"❌ 获取收件人列表失败: {str(e)}")
        return [], []

def test_export_report(receiver, email_address):
    """测试导出报告"""
    print("\n📄 测试导出报告功能")
    print("=" * 50)
    
    try:
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filepath = f"recipient_report_{timestamp}.txt"
        
        print(f"📝 正在导出报告到: {filepath}")
        receiver.export_recipient_report(email_address, filepath)
        
        if os.path.exists(filepath):
            print("✅ 报告导出成功！")
            print(f"📁 文件位置: {os.path.abspath(filepath)}")
            
            # 显示文件大小
            file_size = os.path.getsize(filepath)
            print(f"📏 文件大小: {file_size} 字节")
            
            return filepath
        else:
            print("❌ 报告文件未生成")
            return None
            
    except Exception as e:
        print(f"❌ 导出报告失败: {str(e)}")
        return None

def test_cleanup(receiver):
    """测试清理功能"""
    print("\n🧹 测试清理功能")
    print("=" * 50)
    
    try:
        print("🗑️ 清理30天前的旧记录...")
        receiver.cleanup_old_replies(days=30)
        print("✅ 清理完成")
        
    except Exception as e:
        print(f"❌ 清理失败: {str(e)}")

def main():
    """主测试函数"""
    print("🚀 邮件接收器功能测试")
    print("=" * 60)
    
    # 1. 测试IMAP连接
    success, receiver = test_imap_connection()
    if not success:
        print("\n❌ IMAP连接失败，无法继续测试")
        return
    
    email_address = receiver.email_address
    
    try:
        # 2. 测试检查自动回复
        replies = test_check_replies(receiver, email_address)
        
        # 3. 测试收件人分析
        analysis = test_recipient_analysis(receiver, email_address)
        
        # 4. 测试有效/无效收件人
        valid_recipients, invalid_recipients = test_valid_invalid_recipients(receiver, email_address)
        
        # 5. 测试导出报告
        report_file = test_export_report(receiver, email_address)
        
        # 6. 测试清理功能
        test_cleanup(receiver)
        
        # 总结
        print("\n🎉 测试总结")
        print("=" * 40)
        print(f"✅ IMAP连接: 成功")
        print(f"📬 自动回复: 发现 {len(replies)} 个")
        print(f"📊 分析数据: {'有' if analysis else '无'}")
        print(f"✅ 有效收件人: {len(valid_recipients)} 个")
        print(f"❌ 无效收件人: {len(invalid_recipients)} 个")
        print(f"📄 报告导出: {'成功' if report_file else '失败'}")
        
        if valid_recipients:
            print(f"\n💡 建议: 您有 {len(valid_recipients)} 个有效收件人可以继续发送邮件")
        
        if invalid_recipients:
            print(f"⚠️ 注意: 您有 {len(invalid_recipients)} 个无效收件人，建议从列表中移除")
        
        print("\n🎯 功能验证完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
