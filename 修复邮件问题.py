#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复邮件发送和IMAP连接问题
"""

import json
import os
import sqlite3
import datetime

def fix_auth_codes():
    """修复授权码配置"""
    print("🔧 修复授权码配置")
    print("-" * 30)
    
    try:
        # 确保使用正确的授权码
        correct_auth_codes = {
            "<EMAIL>": {
                "auth_code": "cwnzcpaczwngdgfa",
                "add_time": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
        }
        
        # 如果存在旧的配置，保留其他邮箱的配置
        if os.path.exists('auth_codes.json'):
            try:
                with open('auth_codes.json', 'r', encoding='utf-8') as f:
                    existing_codes = json.load(f)
                
                # 保留其他邮箱的配置，更新目标邮箱
                for email, config in existing_codes.items():
                    if email != "<EMAIL>" and isinstance(config, dict):
                        correct_auth_codes[email] = config
            except:
                pass
        
        # 保存正确的配置
        with open('auth_codes.json', 'w', encoding='utf-8') as f:
            json.dump(correct_auth_codes, f, ensure_ascii=False, indent=2)
        
        print("✅ 授权码配置已修复")
        print(f"✅ <EMAIL> 授权码: cwnzcpaczwngdgfa")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复授权码配置失败: {str(e)}")
        return False

def fix_database_locks():
    """修复数据库锁定问题"""
    print("\n🔧 修复数据库锁定问题")
    print("-" * 30)
    
    try:
        # 检查并修复可能锁定的数据库文件
        db_files = [
            'auto_reply_monitor.db',
            'email_history.db',
            'quality_manager.db',
            'qq_emergency.db'
        ]
        
        for db_file in db_files:
            if os.path.exists(db_file):
                try:
                    # 尝试连接并优化数据库
                    conn = sqlite3.connect(db_file, timeout=30)
                    
                    # 设置WAL模式提高并发性能
                    conn.execute("PRAGMA journal_mode=WAL")
                    conn.execute("PRAGMA synchronous=NORMAL")
                    conn.execute("PRAGMA cache_size=10000")
                    conn.execute("PRAGMA temp_store=memory")
                    
                    # 执行VACUUM清理数据库
                    conn.execute("VACUUM")
                    
                    conn.close()
                    print(f"✅ 数据库 {db_file} 已优化")
                    
                except Exception as e:
                    print(f"⚠️ 数据库 {db_file} 优化失败: {str(e)}")
        
        print("✅ 数据库锁定问题修复完成")
        return True
        
    except Exception as e:
        print(f"❌ 修复数据库锁定问题失败: {str(e)}")
        return False

def create_monitor_settings():
    """创建默认监控设置"""
    print("\n🔧 创建默认监控设置")
    print("-" * 30)
    
    try:
        default_settings = {
            'check_interval': '10',
            'monitor_duration': '2',
            'auto_start': True,
            'save_time': datetime.datetime.now().isoformat()
        }
        
        with open('monitor_settings.json', 'w', encoding='utf-8') as f:
            json.dump(default_settings, f, ensure_ascii=False, indent=2)
        
        print("✅ 默认监控设置已创建")
        print(f"✅ 检查间隔: {default_settings['check_interval']} 分钟")
        print(f"✅ 监控时长: {default_settings['monitor_duration']} 小时")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建监控设置失败: {str(e)}")
        return False

def test_email_connection():
    """测试邮件连接"""
    print("\n🔧 测试邮件连接")
    print("-" * 30)
    
    try:
        import smtplib
        import imaplib
        
        sender_email = "<EMAIL>"
        auth_code = "cwnzcpaczwngdgfa"
        
        # 测试SMTP
        print("📤 测试SMTP连接...")
        try:
            server = smtplib.SMTP('smtp.qq.com', 587)
            server.starttls()
            server.login(sender_email, auth_code)
            server.quit()
            print("✅ SMTP连接成功")
            smtp_ok = True
        except Exception as e:
            print(f"❌ SMTP连接失败: {str(e)}")
            smtp_ok = False
        
        # 测试IMAP
        print("📬 测试IMAP连接...")
        try:
            mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
            mail.login(sender_email, auth_code)
            mail.select('INBOX')
            mail.logout()
            print("✅ IMAP连接成功")
            imap_ok = True
        except Exception as e:
            print(f"❌ IMAP连接失败: {str(e)}")
            imap_ok = False
        
        if smtp_ok and imap_ok:
            print("🎉 邮件连接测试全部通过！")
            return True
        else:
            print("⚠️ 部分邮件连接测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 邮件连接测试失败: {str(e)}")
        return False

def backup_current_config():
    """备份当前配置"""
    print("\n🔧 备份当前配置")
    print("-" * 30)
    
    try:
        backup_dir = f"backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        
        config_files = [
            'auth_codes.json',
            'monitor_settings.json',
            'auto_reply_monitor.db',
            'email_history.db'
        ]
        
        import shutil
        
        for file_name in config_files:
            if os.path.exists(file_name):
                try:
                    shutil.copy2(file_name, os.path.join(backup_dir, file_name))
                    print(f"✅ 已备份: {file_name}")
                except Exception as e:
                    print(f"⚠️ 备份失败: {file_name} - {str(e)}")
        
        print(f"✅ 配置备份完成，备份目录: {backup_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 备份配置失败: {str(e)}")
        return False

def main():
    """主修复函数"""
    print("🔧 邮件系统问题修复工具")
    print("=" * 60)
    
    # 备份当前配置
    backup_current_config()
    
    # 执行修复步骤
    fixes = [
        ("修复授权码配置", fix_auth_codes),
        ("修复数据库锁定", fix_database_locks),
        ("创建监控设置", create_monitor_settings),
        ("测试邮件连接", test_email_connection)
    ]
    
    success_count = 0
    total_count = len(fixes)
    
    for fix_name, fix_func in fixes:
        print(f"\n🔧 执行: {fix_name}")
        if fix_func():
            success_count += 1
            print(f"✅ {fix_name} - 成功")
        else:
            print(f"❌ {fix_name} - 失败")
    
    # 总结
    print(f"\n📊 修复结果总结")
    print("=" * 40)
    print(f"总修复项: {total_count}")
    print(f"成功修复: {success_count}")
    print(f"失败项目: {total_count - success_count}")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("\n🎉 所有问题修复完成！")
        print("✅ 授权码配置正确")
        print("✅ 数据库锁定问题解决")
        print("✅ 监控设置已创建")
        print("✅ 邮件连接测试通过")
        
        print("\n💡 现在可以:")
        print("• 正常发送邮件")
        print("• 使用自动回复监控")
        print("• 保存监控设置")
        print("• QQ应急系统自动触发")
        
    else:
        print(f"\n⚠️ 仍有 {total_count - success_count} 个问题需要手动处理")
        
        if success_count >= 2:  # 至少修复了授权码和数据库问题
            print("\n💡 主要问题已修复，可以尝试:")
            print("• 重启邮件程序")
            print("• 重新尝试发送邮件")
            print("• 检查网络连接")

if __name__ == "__main__":
    main()
