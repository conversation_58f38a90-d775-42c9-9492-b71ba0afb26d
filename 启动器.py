# -*- coding: utf-8 -*-
"""
自动化邮件发送助手 - 启动器
"""

import os
import sys
import subprocess

def clear_screen():
    """清屏"""
    os.system('cls' if os.name == 'nt' else 'clear')

def show_menu():
    """显示菜单"""
    clear_screen()
    print()
    print("=" * 50)
    print("           自动化邮件发送助手")
    print("=" * 50)
    print()
    print("请选择功能:")
    print("1. 发送单封邮件（命令行）")
    print("2. 批量发送邮件（命令行）")
    print("3. 图形界面版本（推荐）")
    print("4. 测试邮件功能")
    print("5. 环境检查与设置")
    print("6. 查看使用说明")
    print("7. 退出")
    print()

def run_command(command):
    """运行命令"""
    try:
        print(f"\n正在执行: {command}")
        print("-" * 40)
        
        # 使用subprocess运行命令
        result = subprocess.run(command, shell=True, check=False)
        
        print("-" * 40)
        if result.returncode == 0:
            print("✓ 执行完成")
        else:
            print("✗ 执行过程中可能出现了问题")
            
    except Exception as e:
        print(f"执行出错: {str(e)}")
    
    print("\n按回车键返回主菜单...")
    input()

def open_file(filename):
    """打开文件"""
    try:
        if os.path.exists(filename):
            if os.name == 'nt':  # Windows
                os.startfile(filename)
            else:  # Linux/Mac
                subprocess.run(['xdg-open', filename])
            print(f"已打开文件: {filename}")
        else:
            print(f"文件不存在: {filename}")
    except Exception as e:
        print(f"打开文件失败: {str(e)}")
    
    print("\n按回车键返回主菜单...")
    input()

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("请输入选项 (1-7): ").strip()
            
            if choice == '1':
                run_command("python main.py")
                
            elif choice == '2':
                run_command("python main.py --batch")
                
            elif choice == '3':
                run_command("python gui_main.py")
                
            elif choice == '4':
                run_command("python test_email.py")
                
            elif choice == '5':
                run_command("python setup.py")
                
            elif choice == '6':
                open_file("README.md")
                
            elif choice == '7':
                print("\n再见！")
                break
                
            else:
                print("\n无效选项，请重新选择")
                print("按回车键继续...")
                input()
                
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"\n发生错误: {str(e)}")
            print("按回车键继续...")
            input()

if __name__ == "__main__":
    main()
