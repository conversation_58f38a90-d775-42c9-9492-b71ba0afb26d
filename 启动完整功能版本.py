#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动完整功能版本的邮件系统v3.0
包含所有2.0系统的功能
"""

import tkinter as tk
import sys
import os

def main():
    """启动完整功能版本的邮件系统"""
    print("🚀 启动完整功能版本的邮件系统v3.0...")
    print("🎯 包含所有2.0系统功能：")
    print("")
    print("📧 核心发送功能：")
    print("   • 邮件发送系统 - 单邮件/批量发送")
    print("   • 队列系统 - 智能队列管理")
    print("   • 发送模式控制 - 快速/标准/安全")
    print("   • 附件管理 - 完整的文件管理")
    print("   • 发送进度跟踪 - 实时状态监控")
    print("")
    print("🔄 撤回功能：")
    print("   • 发送撤回邮件 - 向收件人发送撤回通知")
    print("   • 选择性撤回 - 可选择特定邮件进行撤回")
    print("   • 一键撤回全部 - 向所有收件人发送撤回通知")
    print("   • 发送历史记录 - 查看所有已发送的邮件")
    print("   • 导出发送记录 - 将发送历史导出为CSV文件")
    print("")
    print("📊 数据管理功能：")
    print("   • 收件人质量数据库 - 0-100分智能评分")
    print("   • 邮件历史管理 - 完整发送记录")
    print("   • 自动回复监控 - IMAP实时监控")
    print("   • 智能批次管理 - 自动化批次创建")
    print("   • 统计分析 - 发送效果分析")
    print("")
    print("🛡️ 安全防护功能：")
    print("   • 反垃圾邮件管理 - 发送模式分析")
    print("   • QQ应急系统 - 连续无回复自动激活")
    print("   • 重复检测引擎 - 内容相似度分析")
    print("   • 风险等级评估 - 智能风险控制")
    print("")
    print("🔧 高级功能：")
    print("   • 授权码管理 - 多邮箱管理，密码记忆")
    print("   • 智能检索系统 - 历史邮件搜索")
    print("   • 深度协调系统 - 功能模块智能协调")
    print("   • 调试分析工具 - 相似度分析，发送诊断")
    print("")
    print("🎨 界面特点：")
    print("   • 四栏优化布局 - 更合理的空间利用")
    print("   • 现代化设计 - 美观的用户界面")
    print("   • 功能分类清晰 - 易于使用和管理")
    print("   • 实时状态显示 - 全面的系统监控")
    print("")
    
    try:
        # 导入完整功能版本GUI
        from gui_complete_v3 import EmailSenderGUI
        
        # 创建主窗口
        root = tk.Tk()
        
        # 创建应用实例
        app = EmailSenderGUI(root)
        
        print("✅ 完整功能版本启动成功！")
        print("")
        print("🎯 功能测试指南：")
        print("")
        print("📧 基础功能测试：")
        print("   1. 填写邮件配置信息")
        print("   2. 测试发送邮件功能")
        print("   3. 验证附件管理功能")
        print("   4. 查看操作日志输出")
        print("")
        print("🔄 撤回功能测试：")
        print("   1. 发送邮件后，撤回按钮会激活")
        print("   2. 点击'发送撤回邮件'测试撤回功能")
        print("   3. 测试选择性撤回和一键撤回")
        print("   4. 查看发送记录管理功能")
        print("")
        print("📊 高级功能测试：")
        print("   1. 测试自动回复监控功能")
        print("   2. 测试质量数据库管理")
        print("   3. 测试深度协调系统")
        print("   4. 测试授权码管理功能")
        print("")
        print("🔧 系统特色：")
        print("   • 所有按钮都有响应和反馈")
        print("   • 完整的错误处理机制")
        print("   • 详细的日志记录功能")
        print("   • 友好的用户交互体验")
        print("   • 包含所有2.0系统功能")
        print("")
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 gui_complete_v3.py 文件存在且可正常导入")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查系统环境和依赖")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
