# -*- coding: utf-8 -*-
"""
自动化邮件发送助手 - 使用示例
"""

from email_sender import EmailSender

def example_single_email():
    """示例1: 发送单封邮件"""
    print("示例1: 发送单封邮件")
    print("-" * 30)
    
    # 创建邮件发送器
    sender = EmailSender("<EMAIL>")  # 替换为您的QQ邮箱
    
    # 发送邮件
    success = sender.send_email(
        to_emails=["<EMAIL>"],  # 收件人邮箱
        subject="测试邮件主题",
        body="这是邮件正文内容。\n\n您可以在这里写任何内容。",
        attachments=None  # 无附件
    )
    
    if success:
        print("✓ 邮件发送成功")
    else:
        print("✗ 邮件发送失败")

def example_email_with_attachments():
    """示例2: 发送带附件的邮件"""
    print("\n示例2: 发送带附件的邮件")
    print("-" * 30)
    
    sender = EmailSender("<EMAIL>")
    
    # 发送带附件的邮件
    success = sender.send_email(
        to_emails=["<EMAIL>"],
        subject="带附件的邮件",
        body="请查看附件中的文件。",
        attachments=[
            "document.pdf",      # PDF文件
            "image.jpg",         # 图片文件
            "data.xlsx"          # Excel文件
        ]
    )
    
    if success:
        print("✓ 带附件邮件发送成功")
    else:
        print("✗ 带附件邮件发送失败")

def example_multiple_recipients():
    """示例3: 发送给多个收件人"""
    print("\n示例3: 发送给多个收件人")
    print("-" * 30)
    
    sender = EmailSender("<EMAIL>")
    
    success = sender.send_email(
        to_emails=[
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ],
        subject="群发邮件",
        body="这是一封发送给多个收件人的邮件。",
        attachments=None
    )
    
    if success:
        print("✓ 群发邮件发送成功")
    else:
        print("✗ 群发邮件发送失败")

def example_batch_emails():
    """示例4: 批量发送不同内容的邮件"""
    print("\n示例4: 批量发送不同内容的邮件")
    print("-" * 30)
    
    sender = EmailSender("<EMAIL>")
    
    # 准备邮件列表
    email_list = [
        {
            'to_emails': ['<EMAIL>'],
            'subject': '个人定制邮件 - 用户1',
            'body': '亲爱的用户1，这是为您定制的邮件内容。',
            'attachments': ['report1.pdf']
        },
        {
            'to_emails': ['<EMAIL>'],
            'subject': '个人定制邮件 - 用户2', 
            'body': '亲爱的用户2，这是为您定制的邮件内容。',
            'attachments': ['report2.pdf']
        },
        {
            'to_emails': ['<EMAIL>'],
            'subject': '个人定制邮件 - 用户3',
            'body': '亲爱的用户3，这是为您定制的邮件内容。',
            'attachments': None
        }
    ]
    
    # 批量发送
    results = sender.send_multiple_emails(email_list, delay=3)  # 每封邮件间隔3秒
    
    print(f"批量发送结果:")
    print(f"  成功: {results['success']} 封")
    print(f"  失败: {results['failed']} 封")

def example_business_email():
    """示例5: 商务邮件模板"""
    print("\n示例5: 商务邮件模板")
    print("-" * 30)
    
    sender = EmailSender("<EMAIL>")
    
    # 商务邮件内容
    business_body = """尊敬的客户，

感谢您对我们产品的关注。

附件中包含了您所需的产品资料和报价单，请查收。

如有任何疑问，请随时联系我们。

此致
敬礼！

[您的姓名]
[您的职位]
[公司名称]
[联系电话]
[邮箱地址]"""
    
    success = sender.send_email(
        to_emails=["<EMAIL>"],
        subject="产品资料和报价单 - [公司名称]",
        body=business_body,
        attachments=[
            "product_catalog.pdf",
            "price_list.xlsx"
        ]
    )
    
    if success:
        print("✓ 商务邮件发送成功")
    else:
        print("✗ 商务邮件发送失败")

def example_notification_email():
    """示例6: 通知类邮件"""
    print("\n示例6: 通知类邮件")
    print("-" * 30)
    
    sender = EmailSender("<EMAIL>")
    
    import datetime
    current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    notification_body = f"""系统通知

时间: {current_time}
事件: 数据备份完成
状态: 成功

详细信息请查看附件中的日志文件。

这是一封自动发送的通知邮件，请勿回复。"""
    
    success = sender.send_email(
        to_emails=["<EMAIL>"],
        subject=f"系统通知 - 数据备份完成 ({current_time})",
        body=notification_body,
        attachments=["backup_log.txt"]
    )
    
    if success:
        print("✓ 通知邮件发送成功")
    else:
        print("✗ 通知邮件发送失败")

if __name__ == "__main__":
    print("=" * 50)
    print("    自动化邮件发送助手 - 使用示例")
    print("=" * 50)
    print("\n注意: 这些是代码示例，实际使用时请:")
    print("1. 将 '<EMAIL>' 替换为您的真实QQ邮箱")
    print("2. 将收件人邮箱替换为真实的邮箱地址")
    print("3. 确保附件文件存在于当前目录")
    print("4. 根据需要修改邮件内容")
    
    print("\n以下是各种使用场景的示例代码:")
    
    # 注意: 以下示例仅用于演示，不会实际发送邮件
    # 如需测试，请修改邮箱地址并取消注释
    
    # example_single_email()
    # example_email_with_attachments()  
    # example_multiple_recipients()
    # example_batch_emails()
    # example_business_email()
    # example_notification_email()
    
    print("\n要运行实际的邮件发送，请:")
    print("1. 修改示例中的邮箱地址")
    print("2. 取消相应函数调用的注释")
    print("3. 重新运行此脚本")
