#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QQ邮箱专用反垃圾邮件管理器
包含应急检测和恢复机制
"""

import sqlite3
import datetime
import time
import logging
import json
import threading
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

@dataclass
class QQEmailLimits:
    """QQ邮箱限制配置"""
    # 基于搜索结果的QQ邮箱限制
    daily_limit_normal: int = 100      # 普通用户每日限制
    daily_limit_vip: int = 500         # VIP用户每日限制
    hourly_limit: int = 20             # 每小时建议限制
    batch_size: int = 5                # 建议批次大小
    min_interval: int = 180            # 最小间隔3分钟
    max_interval: int = 600            # 最大间隔10分钟
    emergency_threshold: int = 5       # 应急触发阈值

@dataclass
class EmergencyStatus:
    """应急状态"""
    is_active: bool = False
    trigger_time: str = ""
    trigger_reason: str = ""
    consecutive_no_reply: int = 0
    recovery_attempts: int = 0
    last_recovery_time: str = ""

class QQEmailAntiSpamManager:
    """QQ邮箱专用反垃圾邮件管理器"""
    
    def __init__(self, db_path: str = "qq_anti_spam.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.qq_limits = QQEmailLimits()
        self.emergency_status = EmergencyStatus()
        self._init_database()
        
        # QQ邮箱特殊配置
        self.qq_specific_config = {
            'safe_sending_hours': [9, 10, 11, 14, 15, 16, 17],  # QQ邮箱安全发送时间
            'avoid_keywords': ['营销', '推广', '广告', '优惠', '免费'],  # 避免的关键词
            'emergency_recovery_strategies': [
                'change_subject_pattern',
                'reduce_sending_speed',
                'change_sending_time',
                'add_personalization',
                'wait_and_retry'
            ]
        }
    
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # QQ邮箱发送记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qq_sending_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_email TEXT NOT NULL,
                recipient_email TEXT NOT NULL,
                subject TEXT,
                send_time TEXT NOT NULL,
                has_auto_reply INTEGER DEFAULT 0,
                reply_time TEXT,
                reply_content TEXT,
                batch_id TEXT,
                sequence_number INTEGER,
                is_emergency_send INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 应急状态记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS emergency_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_email TEXT NOT NULL,
                trigger_time TEXT NOT NULL,
                trigger_reason TEXT NOT NULL,
                consecutive_no_reply INTEGER DEFAULT 0,
                recovery_strategy TEXT,
                recovery_time TEXT,
                recovery_success INTEGER DEFAULT 0,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # QQ邮箱配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS qq_email_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sender_email TEXT UNIQUE NOT NULL,
                account_type TEXT DEFAULT 'normal',
                daily_limit INTEGER DEFAULT 100,
                current_daily_sent INTEGER DEFAULT 0,
                last_reset_date TEXT,
                emergency_threshold INTEGER DEFAULT 5,
                is_emergency_active INTEGER DEFAULT 0,
                emergency_start_time TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
        
        self.logger.info("QQ邮箱反垃圾邮件数据库初始化完成")
    
    def initialize_qq_sender(self, sender_email: str, account_type: str = 'normal') -> bool:
        """初始化QQ邮箱发件人"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 确定每日限制
            daily_limit = self.qq_limits.daily_limit_vip if account_type == 'vip' else self.qq_limits.daily_limit_normal
            
            current_time = datetime.datetime.now().isoformat()
            today = datetime.datetime.now().strftime('%Y-%m-%d')
            
            cursor.execute('''
                INSERT OR REPLACE INTO qq_email_config 
                (sender_email, account_type, daily_limit, current_daily_sent, 
                 last_reset_date, emergency_threshold, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (sender_email, account_type, daily_limit, 0, today, 
                  self.qq_limits.emergency_threshold, current_time))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"QQ邮箱 {sender_email} 初始化完成 - {account_type} 账户")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化QQ邮箱发件人失败: {str(e)}")
            return False
    
    def check_qq_sending_permission(self, sender_email: str, recipient_count: int) -> Dict:
        """检查QQ邮箱发送权限"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取QQ邮箱配置
            cursor.execute('''
                SELECT account_type, daily_limit, current_daily_sent, 
                       last_reset_date, is_emergency_active, emergency_start_time
                FROM qq_email_config WHERE sender_email = ?
            ''', (sender_email,))
            
            config = cursor.fetchone()
            if not config:
                # 自动初始化
                self.initialize_qq_sender(sender_email)
                return self.check_qq_sending_permission(sender_email, recipient_count)
            
            account_type, daily_limit, current_sent, last_reset, is_emergency, emergency_start = config
            
            # 检查是否需要重置每日计数
            today = datetime.datetime.now().strftime('%Y-%m-%d')
            if last_reset != today:
                cursor.execute('''
                    UPDATE qq_email_config 
                    SET current_daily_sent = 0, last_reset_date = ?, updated_at = ?
                    WHERE sender_email = ?
                ''', (today, datetime.datetime.now().isoformat(), sender_email))
                current_sent = 0
                conn.commit()
            
            # 检查当前小时发送量
            current_hour = datetime.datetime.now().hour
            hour_start = datetime.datetime.now().replace(minute=0, second=0, microsecond=0).isoformat()
            
            cursor.execute('''
                SELECT COUNT(*) FROM qq_sending_records 
                WHERE sender_email = ? AND send_time >= ?
            ''', (sender_email, hour_start))
            
            hourly_sent = cursor.fetchone()[0] or 0
            
            conn.close()
            
            # 计算剩余额度
            daily_remaining = daily_limit - current_sent
            hourly_remaining = self.qq_limits.hourly_limit - hourly_sent
            
            # 检查应急状态
            emergency_info = self._check_emergency_status(sender_email)
            
            # 生成QQ邮箱专用建议
            recommendations = self._generate_qq_recommendations(
                sender_email, recipient_count, daily_remaining, hourly_remaining, 
                is_emergency, emergency_info
            )
            
            return {
                'can_send': daily_remaining > 0 and hourly_remaining > 0 and not is_emergency,
                'daily_remaining': daily_remaining,
                'hourly_remaining': hourly_remaining,
                'daily_limit': daily_limit,
                'account_type': account_type,
                'is_emergency_active': bool(is_emergency),
                'emergency_info': emergency_info,
                'recommendations': recommendations,
                'suggested_batch_size': min(self.qq_limits.batch_size, hourly_remaining, recipient_count),
                'suggested_interval': self._calculate_qq_interval(is_emergency, emergency_info),
                'safe_sending_hours': self.qq_specific_config['safe_sending_hours']
            }
            
        except Exception as e:
            self.logger.error(f"检查QQ邮箱发送权限失败: {str(e)}")
            return {'can_send': False, 'error': str(e)}
    
    def record_qq_sending_result(self, sender_email: str, recipient_email: str, 
                                subject: str, has_auto_reply: bool = False, 
                                reply_content: str = "", batch_id: str = "", 
                                sequence_number: int = 0) -> bool:
        """记录QQ邮箱发送结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.datetime.now().isoformat()
            reply_time = current_time if has_auto_reply else None
            
            # 记录发送结果
            cursor.execute('''
                INSERT INTO qq_sending_records 
                (sender_email, recipient_email, subject, send_time, has_auto_reply,
                 reply_time, reply_content, batch_id, sequence_number)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (sender_email, recipient_email, subject, current_time, 
                  1 if has_auto_reply else 0, reply_time, reply_content, 
                  batch_id, sequence_number))
            
            # 更新每日发送计数
            cursor.execute('''
                UPDATE qq_email_config 
                SET current_daily_sent = current_daily_sent + 1, updated_at = ?
                WHERE sender_email = ?
            ''', (current_time, sender_email))
            
            conn.commit()
            conn.close()
            
            # 检查是否需要触发应急机制
            self._check_and_trigger_emergency(sender_email, has_auto_reply)
            
            return True
            
        except Exception as e:
            self.logger.error(f"记录QQ邮箱发送结果失败: {str(e)}")
            return False
    
    def _check_emergency_status(self, sender_email: str) -> Dict:
        """检查应急状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取最近的发送记录
            cursor.execute('''
                SELECT has_auto_reply, send_time 
                FROM qq_sending_records 
                WHERE sender_email = ? 
                ORDER BY send_time DESC 
                LIMIT ?
            ''', (sender_email, self.qq_limits.emergency_threshold * 2))
            
            recent_records = cursor.fetchall()
            
            # 检查应急配置
            cursor.execute('''
                SELECT is_emergency_active, emergency_start_time 
                FROM qq_email_config 
                WHERE sender_email = ?
            ''', (sender_email,))
            
            config = cursor.fetchone()
            is_emergency_active = config[0] if config else 0
            emergency_start_time = config[1] if config else None
            
            conn.close()
            
            if len(recent_records) < self.qq_limits.emergency_threshold:
                return {
                    'should_trigger': False,
                    'consecutive_no_reply': 0,
                    'is_active': bool(is_emergency_active),
                    'start_time': emergency_start_time,
                    'reason': '数据不足'
                }
            
            # 检查最近N封邮件的回复情况
            recent_replies = [record[0] for record in recent_records[:self.qq_limits.emergency_threshold]]
            consecutive_no_reply = 0
            
            for has_reply in recent_replies:
                if has_reply:
                    break
                consecutive_no_reply += 1
            
            should_trigger = consecutive_no_reply >= self.qq_limits.emergency_threshold
            
            return {
                'should_trigger': should_trigger,
                'consecutive_no_reply': consecutive_no_reply,
                'is_active': bool(is_emergency_active),
                'start_time': emergency_start_time,
                'reason': f'连续{consecutive_no_reply}封邮件无自动回复' if should_trigger else '正常'
            }
            
        except Exception as e:
            self.logger.error(f"检查应急状态失败: {str(e)}")
            return {'should_trigger': False, 'consecutive_no_reply': 0, 'is_active': False}
    
    def _check_and_trigger_emergency(self, sender_email: str, has_auto_reply: bool):
        """检查并触发应急机制"""
        try:
            emergency_info = self._check_emergency_status(sender_email)
            
            if emergency_info['should_trigger'] and not emergency_info['is_active']:
                # 触发应急机制
                self._activate_emergency_mode(sender_email, emergency_info)
            elif has_auto_reply and emergency_info['is_active']:
                # 收到回复，可能可以退出应急模式
                self._check_emergency_recovery(sender_email)
                
        except Exception as e:
            self.logger.error(f"检查应急触发失败: {str(e)}")
    
    def _activate_emergency_mode(self, sender_email: str, emergency_info: Dict):
        """激活应急模式"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.datetime.now().isoformat()
            
            # 更新配置为应急状态
            cursor.execute('''
                UPDATE qq_email_config 
                SET is_emergency_active = 1, emergency_start_time = ?, updated_at = ?
                WHERE sender_email = ?
            ''', (current_time, current_time, sender_email))
            
            # 记录应急触发
            cursor.execute('''
                INSERT INTO emergency_records 
                (sender_email, trigger_time, trigger_reason, consecutive_no_reply)
                VALUES (?, ?, ?, ?)
            ''', (sender_email, current_time, emergency_info['reason'], 
                  emergency_info['consecutive_no_reply']))
            
            conn.commit()
            conn.close()
            
            self.logger.warning(f"🚨 QQ邮箱 {sender_email} 激活应急模式: {emergency_info['reason']}")
            
            # 启动应急恢复策略
            self._start_emergency_recovery(sender_email)
            
        except Exception as e:
            self.logger.error(f"激活应急模式失败: {str(e)}")
    
    def _start_emergency_recovery(self, sender_email: str):
        """启动应急恢复策略"""
        try:
            recovery_strategies = [
                {
                    'name': 'immediate_pause',
                    'description': '立即暂停发送30分钟',
                    'action': lambda: self._schedule_pause_notification(sender_email, 1800),  # 30分钟
                    'priority': 1
                },
                {
                    'name': 'change_subject_pattern',
                    'description': '建议更换邮件主题模式',
                    'action': self._suggest_subject_change,
                    'priority': 2
                },
                {
                    'name': 'reduce_speed',
                    'description': '降低发送速度到最低',
                    'action': lambda: self._set_emergency_speed(sender_email),
                    'priority': 3
                },
                {
                    'name': 'test_send',
                    'description': '发送测试邮件验证恢复',
                    'action': lambda: self._prepare_test_send(sender_email),
                    'priority': 4
                }
            ]
            
            self.logger.info(f"🔧 启动QQ邮箱应急恢复策略: {sender_email}")
            
            # 执行恢复策略
            for strategy in sorted(recovery_strategies, key=lambda x: x['priority']):
                self.logger.info(f"执行恢复策略: {strategy['description']}")
                try:
                    strategy['action']()
                except Exception as e:
                    self.logger.error(f"恢复策略执行失败: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"启动应急恢复失败: {str(e)}")

    def _schedule_pause_notification(self, sender_email: str, pause_seconds: int):
        """安排暂停通知（不阻塞主线程）"""
        try:
            pause_minutes = pause_seconds // 60
            self.logger.info(f"⏸️ 应急模式激活，建议暂停发送 {pause_minutes} 分钟")

            # 记录暂停开始时间
            pause_start_time = datetime.datetime.now()

            # 在数据库中记录暂停状态
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 先检查表结构，如果没有pause_until字段则添加
            try:
                cursor.execute("ALTER TABLE qq_email_config ADD COLUMN pause_until TEXT")
                conn.commit()
            except sqlite3.OperationalError:
                # 字段已存在，忽略错误
                pass

            cursor.execute('''
                UPDATE qq_email_config
                SET updated_at = ?
                WHERE sender_email = ?
            ''', (pause_start_time.isoformat(), sender_email))

            # 如果没有记录，创建一个
            if cursor.rowcount == 0:
                cursor.execute('''
                    INSERT INTO qq_email_config
                    (sender_email, updated_at)
                    VALUES (?, ?)
                ''', (sender_email, pause_start_time.isoformat()))

            conn.commit()
            conn.close()

            self.logger.info(f"📅 暂停期将于 {pause_start_time + datetime.timedelta(seconds=pause_seconds)} 结束")

        except Exception as e:
            self.logger.error(f"安排暂停通知失败: {str(e)}")

    def _suggest_subject_change(self):
        """建议主题更换"""
        suggestions = [
            "添加个性化前缀，如：[重要] 或 [个人]",
            "避免营销词汇：营销、推广、广告、优惠、免费",
            "使用更自然的语言，避免全大写",
            "添加日期或时间信息",
            "使用问候语开头"
        ]
        
        self.logger.info("💡 主题优化建议:")
        for suggestion in suggestions:
            self.logger.info(f"  • {suggestion}")
    
    def _set_emergency_speed(self, sender_email: str):
        """设置应急发送速度"""
        # 应急模式下的极低速度
        emergency_config = {
            'batch_size': 1,           # 每批1封
            'interval': 1800,          # 间隔30分钟
            'daily_limit': 20,         # 每日最多20封
            'hourly_limit': 2          # 每小时最多2封
        }
        
        self.logger.info(f"⚠️ 设置应急发送速度: {emergency_config}")
        return emergency_config
    
    def _prepare_test_send(self, sender_email: str):
        """建议测试发送策略（不实际发送）"""
        test_suggestions = [
            "💡 建议发送测试邮件验证恢复状态",
            "📧 可以向自己的其他邮箱发送测试邮件",
            "🎯 测试主题建议：'测试邮件 - 请回复确认收到'",
            "⏰ 发送后等待30-60分钟观察是否收到",
            "✅ 如果测试邮件正常收到，可以逐步恢复发送"
        ]

        self.logger.info("🧪 测试发送建议:")
        for suggestion in test_suggestions:
            self.logger.info(f"  • {suggestion}")

        return {'suggestions': test_suggestions}
    
    def _check_emergency_recovery(self, sender_email: str):
        """检查应急恢复"""
        try:
            # 检查最近是否有回复
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 检查最近5封邮件的回复情况
            cursor.execute('''
                SELECT has_auto_reply, send_time
                FROM qq_sending_records
                WHERE sender_email = ?
                ORDER BY send_time DESC
                LIMIT 5
            ''', (sender_email,))

            recent_records = cursor.fetchall()

            if recent_records:
                recent_replies = [row[0] for row in recent_records]
                reply_count = sum(recent_replies)
                total_count = len(recent_replies)

                # 更灵活的恢复条件
                should_recover = False
                recovery_reason = ""

                # 条件1：最近2封邮件都有回复
                if len(recent_replies) >= 2 and all(recent_replies[:2]):
                    should_recover = True
                    recovery_reason = "最近2封邮件都收到回复，系统恢复正常"

                # 条件2：最近5封邮件中有3封或以上有回复
                elif total_count >= 3 and reply_count >= 3:
                    should_recover = True
                    recovery_reason = f"最近{total_count}封邮件中有{reply_count}封收到回复，回复率良好"

                # 条件3：收到任何回复且距离应急触发超过30分钟
                elif reply_count > 0:
                    # 检查应急触发时间
                    cursor.execute('''
                        SELECT trigger_time FROM emergency_records
                        WHERE sender_email = ? AND recovery_time IS NULL
                        ORDER BY trigger_time DESC LIMIT 1
                    ''', (sender_email,))

                    emergency_record = cursor.fetchone()
                    if emergency_record:
                        trigger_time = datetime.datetime.fromisoformat(emergency_record[0])
                        current_time = datetime.datetime.now()
                        time_diff = (current_time - trigger_time).total_seconds()

                        # 如果应急模式已经持续30分钟以上且有回复，可以恢复
                        if time_diff > 1800:  # 30分钟
                            should_recover = True
                            recovery_reason = f"应急模式持续{int(time_diff/60)}分钟后收到回复，系统恢复"

                if should_recover:
                    self._deactivate_emergency_mode(sender_email, recovery_reason)
                    self.logger.info(f"🔄 应急恢复检查: {recovery_reason}")
                else:
                    self.logger.info(f"🔍 应急恢复检查: 最近{total_count}封邮件中{reply_count}封有回复，继续观察")

            conn.close()

        except Exception as e:
            self.logger.error(f"检查应急恢复失败: {str(e)}")
    
    def _deactivate_emergency_mode(self, sender_email: str, reason: str):
        """退出应急模式"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.datetime.now().isoformat()
            
            # 更新配置退出应急状态
            cursor.execute('''
                UPDATE qq_email_config 
                SET is_emergency_active = 0, updated_at = ?
                WHERE sender_email = ?
            ''', (current_time, sender_email))
            
            # 记录恢复
            cursor.execute('''
                UPDATE emergency_records 
                SET recovery_time = ?, recovery_success = 1, notes = ?
                WHERE sender_email = ? AND recovery_time IS NULL
                ORDER BY trigger_time DESC LIMIT 1
            ''', (current_time, reason, sender_email))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"✅ QQ邮箱 {sender_email} 退出应急模式: {reason}")
            
        except Exception as e:
            self.logger.error(f"退出应急模式失败: {str(e)}")
    
    def _generate_qq_recommendations(self, sender_email: str, recipient_count: int,
                                   daily_remaining: int, hourly_remaining: int,
                                   is_emergency: bool, emergency_info: Dict) -> List[str]:
        """生成QQ邮箱专用建议"""
        recommendations = []
        
        current_hour = datetime.datetime.now().hour
        
        # 应急状态建议
        if is_emergency:
            recommendations.extend([
                "🚨 当前处于应急模式，建议暂停发送",
                "📧 更换邮件主题和内容模式",
                "⏰ 等待至少30分钟后再尝试",
                "🧪 先发送测试邮件验证恢复"
            ])
            return recommendations
        
        # 应急预警
        if emergency_info.get('consecutive_no_reply', 0) >= 3:
            recommendations.append(f"⚠️ 已连续{emergency_info['consecutive_no_reply']}封无回复，接近应急阈值")
        
        # 时间建议
        if current_hour not in self.qq_specific_config['safe_sending_hours']:
            recommendations.append("⏰ 建议在9-11点或14-17点发送，效果更好")
        
        # 额度建议
        if daily_remaining <= 10:
            recommendations.append(f"📊 今日剩余额度较少({daily_remaining}封)，建议明天继续")
        
        if hourly_remaining <= 2:
            recommendations.append("⏱️ 当前小时剩余额度较少，建议下个小时继续")
        
        # QQ邮箱特殊建议
        recommendations.extend([
            "📝 避免使用营销敏感词汇",
            "🎯 建议每批发送不超过5封",
            "⏰ 发送间隔保持3-10分钟",
            "📧 添加个性化内容提高回复率"
        ])
        
        return recommendations
    
    def _calculate_qq_interval(self, is_emergency: bool, emergency_info: Dict) -> int:
        """计算QQ邮箱发送间隔"""
        if is_emergency:
            return 1800  # 应急模式30分钟间隔
        
        consecutive_no_reply = emergency_info.get('consecutive_no_reply', 0)
        
        if consecutive_no_reply >= 3:
            return self.qq_limits.max_interval * 2  # 接近应急时加倍间隔
        elif consecutive_no_reply >= 2:
            return self.qq_limits.max_interval      # 有风险时最大间隔
        else:
            return self.qq_limits.min_interval      # 正常最小间隔
    
    def get_qq_emergency_status(self, sender_email: str) -> Dict:
        """获取QQ邮箱应急状态"""
        try:
            emergency_info = self._check_emergency_status(sender_email)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取应急历史
            cursor.execute('''
                SELECT trigger_time, trigger_reason, recovery_time, recovery_success
                FROM emergency_records 
                WHERE sender_email = ? 
                ORDER BY trigger_time DESC 
                LIMIT 5
            ''', (sender_email,))
            
            emergency_history = cursor.fetchall()
            
            # 获取最近发送统计
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_sent,
                    SUM(has_auto_reply) as total_replies,
                    COUNT(*) - SUM(has_auto_reply) as no_replies
                FROM qq_sending_records 
                WHERE sender_email = ? AND send_time >= date('now', '-1 day')
            ''', (sender_email,))
            
            daily_stats = cursor.fetchone()
            
            conn.close()
            
            return {
                'emergency_info': emergency_info,
                'emergency_history': [
                    {
                        'trigger_time': record[0],
                        'reason': record[1],
                        'recovery_time': record[2],
                        'success': bool(record[3])
                    } for record in emergency_history
                ],
                'daily_stats': {
                    'total_sent': daily_stats[0] or 0,
                    'total_replies': daily_stats[1] or 0,
                    'no_replies': daily_stats[2] or 0,
                    'reply_rate': (daily_stats[1] / daily_stats[0] * 100) if daily_stats[0] > 0 else 0
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取应急状态失败: {str(e)}")
            return {}

    def reset_consecutive_no_reply(self, sender_email: str):
        """重置连续无回复计数"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 重置连续无回复计数
            cursor.execute('''
                UPDATE emergency_status
                SET consecutive_no_reply = 0,
                    last_updated = CURRENT_TIMESTAMP
                WHERE sender_email = ?
            ''', (sender_email,))

            # 如果没有记录，创建一个
            if cursor.rowcount == 0:
                cursor.execute('''
                    INSERT INTO emergency_status
                    (sender_email, consecutive_no_reply, is_active, last_updated)
                    VALUES (?, 0, 0, CURRENT_TIMESTAMP)
                ''', (sender_email,))

            conn.commit()
            conn.close()

            # 更新内存中的状态
            self.emergency_status.consecutive_no_reply = 0

            self.logger.info(f"✅ 重置连续无回复计数: {sender_email}")

        except Exception as e:
            self.logger.error(f"重置连续无回复计数失败: {str(e)}")

    def increment_consecutive_no_reply(self, sender_email: str):
        """增加连续无回复计数"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取当前计数
            cursor.execute('''
                SELECT consecutive_no_reply FROM emergency_status
                WHERE sender_email = ?
            ''', (sender_email,))

            result = cursor.fetchone()
            current_count = result[0] if result else 0
            new_count = current_count + 1

            # 更新计数
            if result:
                cursor.execute('''
                    UPDATE emergency_status
                    SET consecutive_no_reply = ?,
                        last_updated = CURRENT_TIMESTAMP
                    WHERE sender_email = ?
                ''', (new_count, sender_email))
            else:
                cursor.execute('''
                    INSERT INTO emergency_status
                    (sender_email, consecutive_no_reply, is_active, last_updated)
                    VALUES (?, ?, 0, CURRENT_TIMESTAMP)
                ''', (sender_email, new_count))

            conn.commit()
            conn.close()

            # 更新内存中的状态
            self.emergency_status.consecutive_no_reply = new_count

            self.logger.info(f"📈 连续无回复计数增加: {sender_email} -> {new_count}")

            # 检查是否需要激活应急模式
            if new_count >= 5:  # 默认阈值为5
                emergency_info = {
                    'should_trigger': True,
                    'consecutive_no_reply': new_count,
                    'reason': f"连续{new_count}封邮件无回复"
                }
                self._activate_emergency_mode(sender_email, emergency_info)

            return new_count

        except Exception as e:
            self.logger.error(f"增加连续无回复计数失败: {str(e)}")
            return 0

    def update_qq_reply_status(self, sender_email: str, recipient_email: str,
                              has_reply: bool, reply_type: str = ""):
        """更新QQ邮箱自动回复状态"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            current_time = datetime.datetime.now().isoformat()

            # 确保reply_type是字符串而不是常量
            if reply_type == "REPLY_RECEIVED":
                reply_type = "自动回复"
            elif not reply_type:
                reply_type = "自动回复" if has_reply else ""

            # 先查找最新的未回复记录
            cursor.execute('''
                SELECT id FROM qq_sending_records
                WHERE sender_email = ? AND recipient_email = ? AND has_auto_reply = 0
                ORDER BY send_time DESC LIMIT 1
            ''', (sender_email, recipient_email))

            record = cursor.fetchone()
            if record:
                # 更新找到的记录
                cursor.execute('''
                    UPDATE qq_sending_records
                    SET has_auto_reply = ?, reply_time = ?, reply_content = ?
                    WHERE id = ?
                ''', (1 if has_reply else 0, current_time if has_reply else None,
                      reply_type, record[0]))
            else:
                # 如果没有找到记录，插入新记录
                cursor.execute('''
                    INSERT INTO qq_sending_records
                    (sender_email, recipient_email, has_auto_reply, reply_time, reply_content,
                     send_time, subject)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (sender_email, recipient_email, 1 if has_reply else 0,
                      current_time if has_reply else None, reply_type, current_time, "自动回复更新"))

            # 检查并触发应急机制
            self._check_and_trigger_emergency(sender_email, has_reply)

            conn.commit()
            conn.close()

            self.logger.info(f"✅ 更新QQ回复状态成功: {recipient_email} -> {has_reply} ({reply_type})")
            return True

        except Exception as e:
            self.logger.error(f"❌ 更新QQ回复状态失败: {str(e)}")
            return False
