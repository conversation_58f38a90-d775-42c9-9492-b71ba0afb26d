#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统集成管理器 - 解决各功能模块相互独立的问题
实现自动回复监控、质量数据库、应急管理等系统的深度集成
"""

import logging
import sqlite3
import datetime
import threading
import time
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

@dataclass
class IntegrationStatus:
    """集成状态"""
    auto_reply_active: bool = False
    quality_db_active: bool = False
    emergency_active: bool = False
    last_sync_time: str = ""
    sync_count: int = 0

class SystemIntegrationManager:
    """系统集成管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.integration_status = IntegrationStatus()
        self.sync_lock = threading.RLock()
        
        # 初始化集成数据库
        self._init_integration_database()
        
        self.logger.info("系统集成管理器初始化完成")
    
    def _init_integration_database(self):
        """初始化集成数据库"""
        try:
            conn = sqlite3.connect('system_integration.db')
            cursor = conn.cursor()
            
            # 创建集成状态表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS integration_status (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_email TEXT NOT NULL,
                    auto_reply_count INTEGER DEFAULT 0,
                    quality_score_avg REAL DEFAULT 0.0,
                    emergency_triggers INTEGER DEFAULT 0,
                    last_sync_time TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建数据同步记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS sync_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_email TEXT NOT NULL,
                    sync_type TEXT NOT NULL,
                    source_system TEXT NOT NULL,
                    target_system TEXT NOT NULL,
                    data_count INTEGER DEFAULT 0,
                    sync_time TEXT NOT NULL,
                    success BOOLEAN DEFAULT 1,
                    error_message TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            self.logger.info("集成数据库初始化完成")
            
        except Exception as e:
            self.logger.error(f"初始化集成数据库失败: {str(e)}")
    
    def sync_auto_reply_to_quality_db(self, sender_email: str) -> Dict:
        """同步自动回复监控数据到质量数据库"""
        try:
            with self.sync_lock:
                self.logger.info(f"开始同步自动回复数据到质量数据库: {sender_email}")
                
                # 导入相关模块
                from email_receiver import EmailReceiver
                from recipient_quality_manager import RecipientQualityManager
                
                # 获取自动回复监控的有效收件人
                receiver = EmailReceiver(sender_email, "")
                valid_recipients = receiver.get_valid_recipients(sender_email)
                
                # 获取无效收件人（退信的）
                invalid_recipients = receiver.get_invalid_recipients(sender_email)
                
                # 初始化质量管理器
                quality_manager = RecipientQualityManager()
                
                sync_count = 0
                
                # 更新有效收件人的质量评分
                for recipient in valid_recipients:
                    quality_manager.update_recipient_quality(
                        email=recipient,
                        sender_email=sender_email,
                        success=True,
                        reply_received=True,
                        reply_type="自动回复"
                    )
                    sync_count += 1
                
                # 更新无效收件人的质量评分
                for recipient in invalid_recipients:
                    quality_manager.update_recipient_quality(
                        email=recipient,
                        sender_email=sender_email,
                        success=False,
                        bounce_reason="退信"
                    )
                    sync_count += 1
                
                # 记录同步结果
                self._record_sync_result(
                    sender_email=sender_email,
                    sync_type="auto_reply_to_quality",
                    source_system="自动回复监控",
                    target_system="质量数据库",
                    data_count=sync_count,
                    success=True
                )
                
                result = {
                    'success': True,
                    'valid_count': len(valid_recipients),
                    'invalid_count': len(invalid_recipients),
                    'total_synced': sync_count,
                    'sync_time': datetime.datetime.now().isoformat()
                }
                
                self.logger.info(f"✅ 自动回复数据同步完成: {sync_count} 个收件人")
                return result
                
        except Exception as e:
            error_msg = f"同步自动回复数据失败: {str(e)}"
            self.logger.error(error_msg)
            
            # 记录同步失败
            self._record_sync_result(
                sender_email=sender_email,
                sync_type="auto_reply_to_quality",
                source_system="自动回复监控",
                target_system="质量数据库",
                data_count=0,
                success=False,
                error_message=str(e)
            )
            
            return {'success': False, 'error': error_msg}
    
    def sync_quality_to_emergency_system(self, sender_email: str) -> Dict:
        """同步质量数据库到应急系统"""
        try:
            with self.sync_lock:
                self.logger.info(f"开始同步质量数据到应急系统: {sender_email}")
                
                # 导入相关模块
                from recipient_quality_manager import RecipientQualityManager
                from qq_email_anti_spam import QQEmailAntiSpamManager
                
                # 获取质量数据
                quality_manager = RecipientQualityManager()
                low_quality_recipients = quality_manager.get_low_quality_recipients(
                    sender_email=sender_email,
                    max_quality_score=30.0
                )
                
                # 初始化应急管理器
                emergency_manager = QQEmailAntiSpamManager()
                
                # 如果低质量收件人过多，触发应急机制
                if len(low_quality_recipients) >= 5:
                    emergency_info = {
                        'should_trigger': True,
                        'consecutive_no_reply': len(low_quality_recipients),
                        'reason': f"检测到{len(low_quality_recipients)}个低质量收件人"
                    }
                    emergency_manager._activate_emergency_mode(sender_email, emergency_info)
                
                # 记录同步结果
                self._record_sync_result(
                    sender_email=sender_email,
                    sync_type="quality_to_emergency",
                    source_system="质量数据库",
                    target_system="应急系统",
                    data_count=len(low_quality_recipients),
                    success=True
                )
                
                result = {
                    'success': True,
                    'low_quality_count': len(low_quality_recipients),
                    'emergency_triggered': len(low_quality_recipients) >= 5,
                    'sync_time': datetime.datetime.now().isoformat()
                }
                
                self.logger.info(f"✅ 质量数据同步到应急系统完成")
                return result
                
        except Exception as e:
            error_msg = f"同步质量数据到应急系统失败: {str(e)}"
            self.logger.error(error_msg)
            
            # 记录同步失败
            self._record_sync_result(
                sender_email=sender_email,
                sync_type="quality_to_emergency",
                source_system="质量数据库",
                target_system="应急系统",
                data_count=0,
                success=False,
                error_message=str(e)
            )
            
            return {'success': False, 'error': error_msg}
    
    def full_system_integration(self, sender_email: str) -> Dict:
        """完整系统集成 - 一键同步所有数据"""
        try:
            self.logger.info(f"开始完整系统集成: {sender_email}")
            
            results = {
                'sender_email': sender_email,
                'start_time': datetime.datetime.now().isoformat(),
                'steps': []
            }
            
            # 步骤1：同步自动回复监控到质量数据库
            step1 = self.sync_auto_reply_to_quality_db(sender_email)
            results['steps'].append({
                'step': 1,
                'name': '自动回复监控 → 质量数据库',
                'result': step1
            })
            
            # 步骤2：同步质量数据库到应急系统
            step2 = self.sync_quality_to_emergency_system(sender_email)
            results['steps'].append({
                'step': 2,
                'name': '质量数据库 → 应急系统',
                'result': step2
            })
            
            # 步骤3：更新系统协调状态
            step3 = self._update_coordination_status(sender_email)
            results['steps'].append({
                'step': 3,
                'name': '更新系统协调状态',
                'result': step3
            })
            
            results['end_time'] = datetime.datetime.now().isoformat()
            results['success'] = all(step['result'].get('success', False) for step in results['steps'])
            
            self.logger.info(f"✅ 完整系统集成完成: {sender_email}")
            return results
            
        except Exception as e:
            error_msg = f"完整系统集成失败: {str(e)}"
            self.logger.error(error_msg)
            return {'success': False, 'error': error_msg}
    
    def _record_sync_result(self, sender_email: str, sync_type: str, 
                           source_system: str, target_system: str, 
                           data_count: int, success: bool, error_message: str = ""):
        """记录同步结果"""
        try:
            conn = sqlite3.connect('system_integration.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO sync_records 
                (sender_email, sync_type, source_system, target_system, 
                 data_count, sync_time, success, error_message)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (sender_email, sync_type, source_system, target_system,
                  data_count, datetime.datetime.now().isoformat(), 
                  success, error_message))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"记录同步结果失败: {str(e)}")
    
    def _update_coordination_status(self, sender_email: str) -> Dict:
        """更新系统协调状态"""
        try:
            # 尝试导入系统协调器，如果不存在则创建简单的协调报告
            try:
                from system_coordinator import EmailSystemCoordinator
                coordinator = EmailSystemCoordinator()
                report = coordinator.coordinate_all_systems(sender_email)
            except ImportError:
                # 如果系统协调器不存在，创建简单的协调报告
                report = {
                    'coordination_time': datetime.datetime.now().isoformat(),
                    'systems_coordinated': ['自动回复监控', '质量数据库', '应急系统'],
                    'coordination_status': 'completed',
                    'message': '系统集成协调完成'
                }

            return {
                'success': True,
                'coordination_report': report,
                'update_time': datetime.datetime.now().isoformat()
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_integration_status(self, sender_email: str) -> Dict:
        """获取集成状态"""
        try:
            conn = sqlite3.connect('system_integration.db')
            cursor = conn.cursor()
            
            # 获取最新的同步记录
            cursor.execute('''
                SELECT sync_type, source_system, target_system, data_count, 
                       sync_time, success, error_message
                FROM sync_records 
                WHERE sender_email = ?
                ORDER BY sync_time DESC LIMIT 10
            ''', (sender_email,))
            
            recent_syncs = cursor.fetchall()
            
            # 统计成功率
            total_syncs = len(recent_syncs)
            successful_syncs = sum(1 for sync in recent_syncs if sync[5])  # success字段
            success_rate = (successful_syncs / total_syncs * 100) if total_syncs > 0 else 0
            
            conn.close()
            
            return {
                'sender_email': sender_email,
                'total_syncs': total_syncs,
                'successful_syncs': successful_syncs,
                'success_rate': success_rate,
                'recent_syncs': [
                    {
                        'sync_type': sync[0],
                        'source_system': sync[1],
                        'target_system': sync[2],
                        'data_count': sync[3],
                        'sync_time': sync[4],
                        'success': bool(sync[5]),
                        'error_message': sync[6] or ""
                    }
                    for sync in recent_syncs
                ],
                'last_check': datetime.datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取集成状态失败: {str(e)}")
            return {'error': str(e)}
