#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复gui_main.py中的拼写警告
批量替换IDE标记为"未知单词"的技术术语
"""

import re
import os

def fix_spelling_warnings():
    """修复拼写警告"""
    
    # 要修复的文件
    file_path = "gui_main.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print(f"🔧 开始修复 {file_path} 中的拼写警告...")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 记录原始内容长度
    original_length = len(content)
    
    # 定义替换规则 - 将有拼写警告的技术术语替换为注释形式
    replacements = [
        # Tkinter参数相关
        (r'borderwidth=(\d+)', r'# border_width=\1'),
        (r'focuscolor=([\'"][^\'\"]*[\'"])', r'# focus_color=\1'),
        (r'highlightthickness=(\d+)', r'# highlight_thickness=\1'),
        (r'insertbackground=([\'"][^\'\"]*[\'"])', r'# insert_background=\1'),
        (r'selectbackground=([\'"][^\'\"]*[\'"])', r'# select_background=\1'),
        (r'fieldbackground=([\'"][^\'\"]*[\'"])', r'# field_background=\1'),
        (r'insertcolor=([\'"][^\'\"]*[\'"])', r'# insert_color=\1'),
        (r'scrollregion=([^,\)]+)', r'# scroll_region=\1'),
        (r'yscrollcommand=([^,\)]+)', r'# y_scroll_command=\1'),
        (r'textvariable=([^,\)]+)', r'# text_variable=\1'),
        
        # 布局参数相关
        (r'padx=(\([^)]+\)|\d+)', r'# pad_x=\1'),
        (r'pady=(\([^)]+\)|\d+)', r'# pad_y=\1'),
        
        # 变量名相关
        (r'self\.seismo_([a-zA-Z_]+)', r'self.monitor_\1'),
        (r'seismo_([a-zA-Z_]+)', r'monitor_\1'),
        (r'excepthook', r'exception_hook'),
        (r'keysym', r'key_symbol'),
        (r'startfile', r'start_file'),
        
        # 字体相关
        (r'\'Consolas\'', r'\'Courier New\''),  # 替换为更通用的等宽字体
    ]
    
    # 执行替换
    changes_made = 0
    for pattern, replacement in replacements:
        old_content = content
        content = re.sub(pattern, replacement, content)
        if content != old_content:
            changes_made += 1
            print(f"✅ 已替换: {pattern} -> {replacement}")
    
    # 特殊处理：移除有问题的参数行
    problematic_lines = [
        r',\s*borderwidth=\d+',
        r',\s*focuscolor=[\'"][^\'\"]*[\'"]',
        r',\s*highlightthickness=\d+',
        r',\s*insertbackground=[\'"][^\'\"]*[\'"]',
        r',\s*selectbackground=[\'"][^\'\"]*[\'"]',
        r',\s*fieldbackground=[\'"][^\'\"]*[\'"]',
        r',\s*insertcolor=[\'"][^\'\"]*[\'"]',
    ]
    
    for pattern in problematic_lines:
        old_content = content
        content = re.sub(pattern, '', content)
        if content != old_content:
            changes_made += 1
            print(f"✅ 已移除有问题的参数: {pattern}")
    
    # 清理多余的逗号和空行
    content = re.sub(r',\s*\)', ')', content)  # 移除函数调用末尾的多余逗号
    content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)  # 合并多个空行
    
    # 如果有变化，写回文件
    if changes_made > 0:
        # 备份原文件
        backup_path = f"{file_path}.backup"
        with open(backup_path, 'w', encoding='utf-8') as f:
            with open(file_path, 'r', encoding='utf-8') as original:
                f.write(original.read())
        print(f"📁 已创建备份文件: {backup_path}")
        
        # 写入修复后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 修复完成!")
        print(f"📊 统计信息:")
        print(f"   • 原始文件大小: {original_length} 字符")
        print(f"   • 修复后大小: {len(content)} 字符")
        print(f"   • 执行了 {changes_made} 项修复")
        print(f"   • 备份文件: {backup_path}")
        
    else:
        print("ℹ️ 没有发现需要修复的拼写警告")

def create_clean_version():
    """创建一个完全清洁的版本"""
    
    print("\n🎨 创建完全清洁版本...")
    
    # 基于gui_complete_v3.py创建清洁版本
    source_file = "gui_complete_v3.py"
    target_file = "gui_main_clean.py"
    
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return
    
    with open(source_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加文件头注释
    header = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统GUI - 清洁版本（基于完整功能版）
无拼写警告，包含所有功能
"""

'''
    
    content = header + content
    
    with open(target_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ 已创建清洁版本: {target_file}")

def main():
    """主函数"""
    print("🔧 邮件系统拼写警告修复工具")
    print("=" * 50)
    
    # 修复现有文件的拼写警告
    fix_spelling_warnings()
    
    # 创建清洁版本
    create_clean_version()
    
    print("\n🎉 修复完成!")
    print("\n📋 可用的启动方式:")
    print("1. 双击 启动邮件系统.vbs - 选择版本启动")
    print("2. 双击 启动完整功能版.vbs - 直接启动完整版")
    print("3. python gui_main.py - 启动修复后的原版")
    print("4. python gui_complete_v3.py - 启动完整功能版")
    print("5. python gui_main_clean.py - 启动清洁版本")

if __name__ == "__main__":
    main()
