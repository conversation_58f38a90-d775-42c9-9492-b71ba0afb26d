#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RecipientQualityManager 完整修复验证工具
修复 "got an unexpected keyword argument 'max_quality_score'" 错误
"""

import sys
import traceback
import datetime

def test_original_error():
    """测试原始错误是否已修复"""
    print("🔍 测试原始错误修复情况")
    print("-" * 40)
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        
        # 这个调用之前会失败，现在应该能兼容处理
        result = quality_manager.get_quality_recipients(max_quality_score=40.0)
        print(f"✅ 原始错误已修复！兼容调用返回 {len(result)} 个收件人")
        return True
        
    except Exception as e:
        print(f"❌ 原始错误仍然存在: {str(e)}")
        return False

def test_correct_usage():
    """测试正确的使用方式"""
    print("\n🎯 测试正确的使用方式")
    print("-" * 40)
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        
        # 测试1: 获取高质量收件人
        high_quality = quality_manager.get_quality_recipients(
            min_quality_score=60.0,
            limit=10
        )
        print(f"✅ 获取高质量收件人: {len(high_quality)} 个")
        
        # 测试2: 获取低质量收件人
        low_quality = quality_manager.get_low_quality_recipients(
            sender_email="<EMAIL>",
            max_quality_score=40.0
        )
        print(f"✅ 获取低质量收件人: {len(low_quality)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 正确使用方式测试失败: {str(e)}")
        return False

def test_auto_cleanup_functionality():
    """测试自动清理功能"""
    print("\n🧹 测试自动清理功能")
    print("-" * 40)
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        sender_email = "<EMAIL>"
        
        # 模拟GUI中的自动清理调用
        print("📞 模拟GUI自动清理调用...")
        
        # 获取低质量收件人
        low_quality_recipients = quality_manager.get_low_quality_recipients(
            sender_email=sender_email,
            max_quality_score=40.0
        )
        
        print(f"📊 找到 {len(low_quality_recipients)} 个低质量收件人")
        
        if low_quality_recipients:
            # 模拟标记过程（只处理前2个作为测试）
            marked_count = 0
            for recipient in low_quality_recipients[:2]:
                try:
                    success = quality_manager.update_recipient_score(
                        email=recipient.email,
                        new_score=0.0,
                        sender_email=sender_email
                    )
                    if success:
                        marked_count += 1
                        print(f"   ✅ 标记 {recipient.email} 为无效")
                except Exception as e:
                    print(f"   ⚠️ 标记 {recipient.email} 失败: {str(e)}")
            
            print(f"🧹 成功标记 {marked_count} 个收件人为无效")
        else:
            print("ℹ️ 没有找到需要清理的低质量收件人")
        
        return True
        
    except Exception as e:
        print(f"❌ 自动清理功能测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_quality_analysis():
    """测试质量分析功能"""
    print("\n📊 测试质量分析功能")
    print("-" * 40)
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        
        # 获取质量分析
        analytics = quality_manager.get_quality_analytics()
        
        if analytics:
            overview = analytics.get('overview', {})
            print(f"📈 总收件人数: {overview.get('total_recipients', 0)}")
            print(f"📈 平均质量分: {overview.get('avg_quality_score', 0):.1f}")
            print(f"📈 平均回复率: {overview.get('avg_response_rate', 0):.1f}%")
            
            distribution = analytics.get('quality_distribution', {})
            print(f"📊 质量分布:")
            for status, count in distribution.items():
                print(f"   {status}: {count} 个")
        
        # 获取优化建议
        recommendations = quality_manager.get_recommendations()
        if recommendations:
            print(f"💡 优化建议 ({len(recommendations)} 条):")
            for rec in recommendations[:3]:  # 只显示前3条
                print(f"   - {rec.get('title', '未知建议')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 质量分析功能测试失败: {str(e)}")
        return False

def test_batch_creation():
    """测试智能批次创建"""
    print("\n📦 测试智能批次创建")
    print("-" * 40)
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        
        # 创建测试批次
        batch_result = quality_manager.create_smart_batches(
            batch_name=f"测试批次_{datetime.datetime.now().strftime('%H%M%S')}",
            total_recipients=10,
            quality_threshold=50.0,
            max_batch_size=5,
            strategy="quality_balanced"
        )
        
        if batch_result.get('success'):
            print(f"✅ 智能批次创建成功:")
            print(f"   📦 批次数量: {batch_result.get('batch_count', 0)}")
            print(f"   👥 总收件人: {batch_result.get('total_recipients', 0)}")
        else:
            print(f"ℹ️ 批次创建结果: {batch_result.get('error', '未知')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能批次创建测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 RecipientQualityManager 完整修复验证")
    print("=" * 60)
    print("本工具将验证以下修复:")
    print("1. ✅ 修复 'max_quality_score' 参数错误")
    print("2. ✅ 确保正确的使用方式正常工作")
    print("3. ✅ 验证自动清理低质量收件人功能")
    print("4. ✅ 验证质量分析功能")
    print("5. ✅ 验证智能批次创建功能")
    print("=" * 60)
    
    tests = [
        ("原始错误修复", test_original_error),
        ("正确使用方式", test_correct_usage),
        ("自动清理功能", test_auto_cleanup_functionality),
        ("质量分析功能", test_quality_analysis),
        ("智能批次创建", test_batch_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} - 通过")
            else:
                print(f"\n❌ {test_name} - 失败")
        except Exception as e:
            print(f"\n❌ {test_name} - 异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！RecipientQualityManager 修复成功！")
        print("\n📋 修复总结:")
        print("✅ 添加了 max_quality_score 兼容性参数")
        print("✅ 自动检测错误调用并转换为正确的方法")
        print("✅ 保持了所有原有功能的正常工作")
        print("✅ 修复了自动清理低质量收件人的错误")
        print("\n💡 现在系统可以正常处理:")
        print("- 有效收件人/安全收件人的识别")
        print("- 无效收件人/低质量收件人的自动清理")
        print("- 智能批次创建和管理")
        print("- 质量分析和优化建议")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
