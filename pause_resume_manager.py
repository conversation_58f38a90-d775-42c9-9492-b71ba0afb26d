# -*- coding: utf-8 -*-
"""
暂停和断点继续管理器
"""

import tkinter as tk
from tkinter import ttk, messagebox
import threading
import datetime
import json
import os
from batch_manager import BatchManager

class PauseResumeManager:
    """暂停和断点继续管理器"""
    
    def __init__(self, parent):
        """
        初始化管理器
        
        Args:
            parent: 父窗口对象
        """
        self.parent = parent
        self.is_paused = False
        self.current_session_id = None
        self.progress_file = "email_send_progress.json"
        
        # 创建控制按钮
        self.create_control_buttons()
        
        # 检查是否有未完成的发送任务
        self.check_pending_tasks()
    
    def create_control_buttons(self):
        """创建控制按钮"""
        # 在主界面添加暂停/恢复按钮
        if hasattr(self.parent, 'button_frame'):
            # 暂停按钮
            self.pause_button = tk.Button(
                self.parent.button_frame,
                text="⏸️ 暂停发送",
                command=self.pause_sending,
                state='disabled',
                bg='#FFA500',
                fg='white',
                font=('Arial', 10, 'bold')
            )
            self.pause_button.pack(side=tk.LEFT, padx=5)
            
            # 恢复按钮
            self.resume_button = tk.Button(
                self.parent.button_frame,
                text="▶️ 恢复发送",
                command=self.resume_sending,
                state='disabled',
                bg='#32CD32',
                fg='white',
                font=('Arial', 10, 'bold')
            )
            self.resume_button.pack(side=tk.LEFT, padx=5)
            
            # 断点继续按钮
            self.continue_button = tk.Button(
                self.parent.button_frame,
                text="🔄 断点继续",
                command=self.continue_from_breakpoint,
                state='disabled',
                bg='#4169E1',
                fg='white',
                font=('Arial', 10, 'bold')
            )
            self.continue_button.pack(side=tk.LEFT, padx=5)
    
    def check_pending_tasks(self):
        """检查是否有未完成的发送任务"""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                
                session_id = progress_data.get('session_id', 'unknown')
                current_email = progress_data.get('current_email_index', 0)
                total_emails = progress_data.get('total_emails', 0)
                success_count = progress_data.get('success_count', 0)
                
                if current_email < total_emails:
                    # 有未完成的任务
                    self.continue_button.config(state='normal')
                    
                    # 显示提示信息
                    remaining = total_emails - current_email
                    message = f"""发现未完成的发送任务：
                    
会话ID: {session_id}
总邮件数: {total_emails}
已发送: {success_count}
剩余: {remaining}

是否要继续发送？"""
                    
                    if messagebox.askyesno("发现未完成任务", message):
                        self.continue_from_breakpoint()
                        
            except Exception as e:
                print(f"检查未完成任务时出错: {str(e)}")
    
    def pause_sending(self):
        """暂停发送"""
        self.is_paused = True
        self.pause_button.config(state='disabled')
        self.resume_button.config(state='normal')
        
        # 通知主程序暂停
        if hasattr(self.parent, 'should_pause'):
            self.parent.should_pause = True
        
        messagebox.showinfo("暂停发送", "发送已暂停，进度已保存。\n点击'恢复发送'可继续。")
    
    def resume_sending(self):
        """恢复发送"""
        self.is_paused = False
        self.pause_button.config(state='normal')
        self.resume_button.config(state='disabled')
        
        # 通知主程序恢复
        if hasattr(self.parent, 'should_pause'):
            self.parent.should_pause = False
        
        # 如果有批次管理器，恢复发送
        if hasattr(self.parent, 'current_batch_manager'):
            self.parent.current_batch_manager.resume_sending()
        
        messagebox.showinfo("恢复发送", "发送已恢复")
    
    def continue_from_breakpoint(self):
        """从断点继续发送"""
        try:
            if not os.path.exists(self.progress_file):
                messagebox.showwarning("没有断点", "没有找到可恢复的发送进度")
                return
            
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            
            # 显示断点信息
            session_id = progress_data.get('session_id', 'unknown')
            send_mode = progress_data.get('send_mode', 'standard')
            current_email = progress_data.get('current_email_index', 0)
            total_emails = progress_data.get('total_emails', 0)
            success_count = progress_data.get('success_count', 0)
            failed_count = progress_data.get('failed_count', 0)
            
            info_message = f"""断点信息：
            
会话ID: {session_id}
发送模式: {send_mode}
总邮件数: {total_emails}
已发送成功: {success_count}
已发送失败: {failed_count}
当前位置: 第{current_email + 1}封
剩余邮件: {total_emails - current_email}

确定要从断点继续发送吗？"""
            
            if messagebox.askyesno("断点继续", info_message):
                # 设置断点继续标志
                self.parent.resume_from_breakpoint = True
                self.parent.breakpoint_session_id = session_id
                
                # 禁用断点继续按钮
                self.continue_button.config(state='disabled')
                
                messagebox.showinfo("断点继续", "已设置断点继续模式。\n请点击'发送邮件'或'开始队列发送'继续。")
                
        except Exception as e:
            messagebox.showerror("错误", f"读取断点信息失败：{str(e)}")
    
    def on_sending_start(self, session_id):
        """发送开始时调用"""
        self.current_session_id = session_id
        self.pause_button.config(state='normal')
        self.continue_button.config(state='disabled')
    
    def on_sending_complete(self):
        """发送完成时调用"""
        self.current_session_id = None
        self.is_paused = False
        self.pause_button.config(state='disabled')
        self.resume_button.config(state='disabled')
        
        # 检查是否还有其他未完成任务
        self.check_pending_tasks()
    
    def on_sending_paused(self):
        """发送暂停时调用"""
        self.pause_button.config(state='disabled')
        self.resume_button.config(state='normal')
    
    def get_pause_callback(self):
        """获取暂停检查回调函数"""
        def pause_callback():
            return self.is_paused or getattr(self.parent, 'should_pause', False)
        return pause_callback
    
    def clear_all_progress(self):
        """清除所有进度文件"""
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                messagebox.showinfo("清除完成", "所有发送进度已清除")
                self.continue_button.config(state='disabled')
        except Exception as e:
            messagebox.showerror("错误", f"清除进度失败：{str(e)}")
    
    def show_progress_info(self):
        """显示当前进度信息"""
        if not os.path.exists(self.progress_file):
            messagebox.showinfo("进度信息", "当前没有保存的发送进度")
            return
        
        try:
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            
            # 格式化显示信息
            info_lines = []
            info_lines.append(f"会话ID: {progress_data.get('session_id', 'unknown')}")
            info_lines.append(f"发送模式: {progress_data.get('send_mode', 'unknown')}")
            info_lines.append(f"总邮件数: {progress_data.get('total_emails', 0)}")
            info_lines.append(f"当前批次: {progress_data.get('current_batch', 0)}")
            info_lines.append(f"总批次数: {progress_data.get('total_batches', 0)}")
            info_lines.append(f"当前邮件: 第{progress_data.get('current_email_index', 0) + 1}封")
            info_lines.append(f"成功发送: {progress_data.get('success_count', 0)}")
            info_lines.append(f"发送失败: {progress_data.get('failed_count', 0)}")
            info_lines.append(f"今日已发: {progress_data.get('today_sent', 0)}")
            
            save_time = progress_data.get('save_time', '')
            if save_time:
                info_lines.append(f"保存时间: {save_time}")
            
            info_message = "当前发送进度：\n\n" + "\n".join(info_lines)
            messagebox.showinfo("进度信息", info_message)
            
        except Exception as e:
            messagebox.showerror("错误", f"读取进度信息失败：{str(e)}")

# 为主程序添加暂停恢复功能的辅助函数
def add_pause_resume_to_main(main_window):
    """为主程序添加暂停恢复功能"""
    # 添加暂停恢复管理器
    main_window.pause_resume_manager = PauseResumeManager(main_window)
    
    # 添加相关属性
    main_window.should_pause = False
    main_window.resume_from_breakpoint = False
    main_window.breakpoint_session_id = None
    main_window.current_batch_manager = None
    
    return main_window.pause_resume_manager
