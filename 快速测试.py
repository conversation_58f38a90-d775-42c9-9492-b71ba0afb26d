# -*- coding: utf-8 -*-
"""
快速测试新的授权码
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from config import SMTP_CONFIG

def test_smtp_auth(sender_email):
    """测试SMTP认证"""
    print(f"测试邮箱: {sender_email}")
    print(f"SMTP服务器: {SMTP_CONFIG['server']}:{SMTP_CONFIG['port']}")
    print(f"授权码: {SMTP_CONFIG['password']}")
    print("-" * 50)
    
    try:
        print("1. 连接SMTP服务器...")
        server = smtplib.SMTP(SMTP_CONFIG['server'], SMTP_CONFIG['port'])
        print("✓ 连接成功")
        
        print("2. 启用TLS加密...")
        server.starttls()
        print("✓ TLS启用成功")
        
        print("3. 进行SMTP认证...")
        server.login(sender_email, SMTP_CONFIG['password'])
        print("✓ 认证成功")
        
        server.quit()
        print("✓ 测试完成，授权码有效！")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ 认证失败: {str(e)}")
        print("可能的原因：")
        print("1. 邮箱地址错误")
        print("2. 授权码错误")
        print("3. 未开启SMTP服务")
        return False
        
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

def test_send_email(sender_email, recipient_email):
    """测试发送邮件"""
    print(f"\n测试发送邮件: {sender_email} -> {recipient_email}")
    print("-" * 50)
    
    try:
        # 创建邮件
        msg = MIMEMultipart()
        msg['From'] = sender_email
        msg['To'] = recipient_email
        msg['Subject'] = "测试邮件"
        
        body = "这是一封测试邮件，验证SMTP功能是否正常。"
        msg.attach(MIMEText(body, 'plain', 'utf-8'))
        
        # 发送邮件
        print("正在发送邮件...")
        server = smtplib.SMTP(SMTP_CONFIG['server'], SMTP_CONFIG['port'])
        server.starttls()
        server.login(sender_email, SMTP_CONFIG['password'])
        
        text = msg.as_string()
        server.sendmail(sender_email, [recipient_email], text)
        server.quit()
        
        print("✓ 邮件发送成功！")
        return True
        
    except Exception as e:
        print(f"❌ 邮件发送失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("QQ邮箱SMTP测试工具")
    print("=" * 50)
    
    # 从用户输入获取邮箱
    sender_email = input("请输入您的QQ邮箱地址: ").strip()
    
    if not sender_email.endswith('@qq.com'):
        print("❌ 请输入有效的QQ邮箱地址")
        exit()
    
    # 测试认证
    if test_smtp_auth(sender_email):
        # 询问是否测试发送
        test_send = input("\n是否测试发送邮件？(y/n): ").strip().lower()
        if test_send == 'y':
            recipient = input("请输入收件人邮箱: ").strip()
            if recipient:
                test_send_email(sender_email, recipient)
    
    input("\n按回车键退出...")
