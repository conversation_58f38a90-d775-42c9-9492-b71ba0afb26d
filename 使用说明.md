# 邮件系统v3.0使用说明

## 🚀 快速启动

### VBS启动脚本（推荐）

1. **一键启动完整版**：
   - 双击 `启动v3完整版.vbs`
   - 自动启动包含所有功能的v3.0版本

2. **选择版本启动**：
   - 双击 `启动邮件系统.vbs`
   - 在弹出菜单中选择要启动的版本

3. **完整功能版启动**：
   - 双击 `启动完整功能版.vbs`
   - 启动功能最全面的版本

### Python直接启动

```bash
# 启动完整功能版（推荐）
python gui_complete_v3.py

# 启动修复后的原版
python gui_main.py

# 启动清洁版本
python gui_main_clean.py

# 启动简化版本
python gui_clean.py
```

## 📋 版本对比

| 版本 | 文件名 | 特点 | 适用场景 |
|------|--------|------|----------|
| **完整功能版** | `gui_complete_v3.py` | 包含所有2.0功能 | 生产使用 |
| **修复原版** | `gui_main.py` | 修复拼写警告 | 兼容性使用 |
| **清洁版本** | `gui_clean.py` | 无警告简化版 | 开发测试 |
| **清洁完整版** | `gui_main_clean.py` | 基于完整版的清洁版 | 推荐使用 |

## 🔄 完整功能版特色功能

### 1. 邮件撤回功能
- **发送撤回邮件**：向收件人发送撤回通知
- **选择性撤回**：选择特定邮件进行撤回
- **一键撤回全部**：向所有收件人发送撤回邮件
- **发送记录管理**：查看、导出发送历史

### 2. 自动回复监控
- **IMAP实时监控**：监控收件箱自动回复
- **收件人状态跟踪**：实时更新收件人回复状态
- **监控报告生成**：生成详细的监控报告
- **智能分析**：分析回复模式和趋势

### 3. 质量数据库管理
- **收件人评分**：0-100分智能评分系统
- **批次管理**：智能分批，避免重复
- **一键导入**：导入到主系统
- **数据分析**：送达率和回复率分析

### 4. 安全防护系统
- **反垃圾邮件**：QQ邮箱专项防护方案
- **应急管理**：5封无回复自动激活应急
- **重复检测**：内容相似度分析
- **风险评估**：实时风险监控

### 5. 深度协调系统
- **功能协调**：各模块智能协调工作
- **状态同步**：实时同步系统状态
- **优化建议**：提供系统优化建议
- **自动调整**：根据情况自动调整参数

## 🎯 使用流程

### 基础使用
1. **启动系统**：双击VBS脚本启动
2. **配置邮箱**：输入发送者邮箱和授权码
3. **编写内容**：填写邮件主题和正文
4. **添加收件人**：输入收件人邮箱列表
5. **发送邮件**：点击发送按钮

### 高级功能使用
1. **启动监控**：发送后启动自动回复监控
2. **使用撤回**：如需撤回，使用撤回功能
3. **质量管理**：使用质量数据库优化收件人
4. **深度协调**：激活深度协调系统
5. **应急管理**：配置应急响应机制

## 🔧 故障排除

### 启动问题
1. **Python未安装**：
   - 下载安装Python 3.8+版本
   - 安装时勾选"Add to PATH"

2. **文件不存在**：
   - 确保所有文件在同一目录
   - 检查文件名是否正确

3. **权限问题**：
   - 以管理员身份运行
   - 检查文件夹权限

### 功能问题
1. **按钮无响应**：
   - 使用完整功能版
   - 查看日志输出

2. **拼写警告**：
   - 使用清洁版本
   - 运行修复脚本

3. **功能缺失**：
   - 使用gui_complete_v3.py
   - 确保版本正确

## 📊 系统要求

### 最低要求
- **操作系统**：Windows 7+
- **Python版本**：3.6+
- **内存**：2GB RAM
- **存储空间**：100MB

### 推荐配置
- **操作系统**：Windows 10+
- **Python版本**：3.8+
- **内存**：4GB RAM
- **存储空间**：500MB

## 🎨 界面说明

### 四栏布局
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│   左侧配置   │  中左操作    │  中右管理    │  右侧高级    │
│             │             │             │             │
│ • 邮件配置   │ • 快速操作   │ • 附件管理   │ • 高级功能   │
│ • 邮件内容   │ • 发送控制   │ • 历史记录   │ • 深度协调   │
│ • 操作日志   │ • 队列管理   │ • 系统监控   │ • 系统状态   │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

### 功能区域说明
- **左侧配置区**：邮件基本配置和内容编辑
- **中左操作区**：发送控制和撤回功能
- **中右管理区**：附件和历史记录管理
- **右侧高级区**：高级功能和系统状态

## 📞 技术支持

### 常见问题
1. **Q: 如何保存授权码？**
   A: 在发送控制区点击"💾 保存授权码"

2. **Q: 如何撤回邮件？**
   A: 发送成功后，撤回按钮会激活，点击即可

3. **Q: 如何启动监控？**
   A: 在高级功能区点击"📡 开始监控"

4. **Q: 如何查看发送记录？**
   A: 在发送控制区点击"📋 查看发送记录"

### 日志文件
- **系统日志**：查看操作日志了解系统状态
- **错误日志**：如遇问题，查看错误信息
- **发送记录**：可导出为CSV文件

## 🎉 总结

邮件系统v3.0完整功能版提供了：
- ✅ **完整的2.0功能**：包含所有原有功能
- ✅ **现代化界面**：四栏优化布局
- ✅ **强大的撤回功能**：多种撤回方式
- ✅ **智能监控系统**：实时监控自动回复
- ✅ **质量数据库**：智能收件人管理
- ✅ **安全防护**：全面的安全机制
- ✅ **深度协调**：系统智能协调

推荐使用 `启动v3完整版.vbs` 一键启动完整功能版本！
