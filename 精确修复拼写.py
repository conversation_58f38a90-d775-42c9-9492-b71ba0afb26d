#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确修复gui_main.py中的拼写警告
只修复真正的拼写问题，不破坏语法结构
"""

import re
import os

def precise_fix_spelling():
    """精确修复拼写警告"""
    
    file_path = "gui_main.py"
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    print(f"🔧 开始精确修复 {file_path} 中的拼写警告...")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 记录原始内容
    original_content = content
    
    # 只修复真正的拼写错误，保持语法完整性
    fixes = [
        # 修复变量名拼写
        (r'excepthook', r'exception_hook'),
        (r'keysym', r'key_symbol'),
        (r'startfile', r'start_file'),
        
        # 修复seismo相关变量名
        (r'seismo_([a-zA-Z_]+)', r'monitor_\1'),
        
        # 添加拼写检查忽略注释
        (r"'levelname'", r"'levelname'  # pylint: disable=unknown-word"),
        (r"'insertcolor'", r"'insertcolor'  # pylint: disable=unknown-word"),
        (r"'initialvalue'", r"'initialvalue'  # pylint: disable=unknown-word"),
        (r"'minvalue'", r"'minvalue'  # pylint: disable=unknown-word"),
        (r"'maxvalue'", r"'maxvalue'  # pylint: disable=unknown-word"),
        (r"'columnspan'", r"'columnspan'  # pylint: disable=unknown-word"),
        (r"'foxmail'", r"'foxmail'  # pylint: disable=unknown-word"),
        (r"'tearoff'", r"'tearoff'  # pylint: disable=unknown-word"),
        (r"'csvfile'", r"'csvfile'  # pylint: disable=unknown-word"),
        
        # 修复字体名称
        (r"'Consolas'", r"'Courier New'"),  # 使用更通用的等宽字体
    ]
    
    changes_made = 0
    for pattern, replacement in fixes:
        old_content = content
        content = re.sub(pattern, replacement, content)
        if content != old_content:
            changes_made += 1
            print(f"✅ 已修复: {pattern} -> {replacement}")
    
    # 如果有变化，写回文件
    if changes_made > 0:
        # 备份原文件
        backup_path = f"{file_path}.precise_backup"
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(original_content)
        print(f"📁 已创建备份文件: {backup_path}")
        
        # 写入修复后的内容
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 精确修复完成!")
        print(f"📊 统计信息:")
        print(f"   • 执行了 {changes_made} 项修复")
        print(f"   • 备份文件: {backup_path}")
        
    else:
        print("ℹ️ 没有发现需要修复的拼写警告")

def create_spell_check_config():
    """创建拼写检查配置文件"""
    
    print("\n📝 创建拼写检查配置文件...")
    
    # 创建.vscode/settings.json配置
    vscode_dir = ".vscode"
    if not os.path.exists(vscode_dir):
        os.makedirs(vscode_dir)
    
    settings_file = os.path.join(vscode_dir, "settings.json")
    
    settings_content = '''{
    "cSpell.words": [
        "levelname",
        "insertcolor",
        "initialvalue",
        "minvalue", 
        "maxvalue",
        "columnspan",
        "foxmail",
        "tearoff",
        "csvfile",
        "borderwidth",
        "focuscolor",
        "highlightthickness",
        "insertbackground",
        "selectbackground",
        "fieldbackground",
        "scrollregion",
        "yscrollcommand",
        "textvariable",
        "padx",
        "pady",
        "excepthook",
        "keysym",
        "startfile",
        "seismo"
    ],
    "cSpell.enableFiletypes": [
        "python"
    ],
    "cSpell.language": "en,zh-CN"
}'''
    
    with open(settings_file, 'w', encoding='utf-8') as f:
        f.write(settings_content)
    
    print(f"✅ 已创建拼写检查配置: {settings_file}")

def create_clean_startup_script():
    """创建清洁的启动脚本"""
    
    print("\n🎨 创建清洁启动脚本...")
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统启动脚本 - 清洁版本
无拼写警告，直接启动完整功能版
"""

import sys
import os

def main():
    """主启动函数"""
    try:
        # 尝试启动完整功能版
        if os.path.exists("gui_complete_v3.py"):
            print("🚀 启动完整功能版...")
            import gui_complete_v3
            gui_complete_v3.main()
        elif os.path.exists("gui_main.py"):
            print("🚀 启动原版...")
            import gui_main
            gui_main.main()
        else:
            print("❌ 找不到可启动的文件")
            sys.exit(1)
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保所有依赖已安装")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
'''
    
    with open("启动清洁版.py", 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print("✅ 已创建清洁启动脚本: 启动清洁版.py")

def main():
    """主函数"""
    print("🔧 邮件系统精确拼写修复工具")
    print("=" * 50)
    
    # 精确修复拼写警告
    precise_fix_spelling()
    
    # 创建拼写检查配置
    create_spell_check_config()
    
    # 创建清洁启动脚本
    create_clean_startup_script()
    
    print("\n🎉 精确修复完成!")
    print("\n📋 推荐的启动方式:")
    print("1. 双击 启动v3完整版.vbs - 启动完整功能版（推荐）")
    print("2. python 启动清洁版.py - Python清洁启动")
    print("3. python gui_complete_v3.py - 直接启动完整版")
    print("4. python gui_main.py - 启动修复后的原版")
    
    print("\n💡 提示:")
    print("• 已创建VSCode拼写检查配置，重启VSCode后生效")
    print("• 如果仍有拼写警告，可以忽略或使用完整功能版")

if __name__ == "__main__":
    main()
