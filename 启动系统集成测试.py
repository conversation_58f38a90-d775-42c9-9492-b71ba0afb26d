#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动系统集成测试 - 验证修复效果
解决系统相互独立不互联的问题
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import datetime
import sys
import os

class SystemIntegrationTestGUI:
    """系统集成测试界面"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_interface()
    
    def setup_window(self):
        """设置窗口"""
        self.root.title("🔗 系统集成测试 - 修复验证")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 设置图标（如果存在）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
    
    def create_interface(self):
        """创建界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔗 系统集成测试中心",
                               font=('Microsoft YaHei UI', 16, 'bold'))
        title_label.pack(pady=(0, 15))
        
        # 问题说明
        problem_frame = ttk.LabelFrame(main_frame, text="❌ 发现的问题", padding="10")
        problem_frame.pack(fill=tk.X, pady=(0, 15))
        
        problem_text = """🔍 系统问题分析：
1. 自动回复监控系统识别的有效收件人无法导入收件人质量数据库
2. QQ应急状态更新出现 'REPLY_RECEIVED' 错误
3. 各功能模块相互独立，缺乏数据共享和系统协调
4. 系统集成度低，用户需要手动在各模块间传递数据"""
        
        ttk.Label(problem_frame, text=problem_text, font=('Microsoft YaHei UI', 9)).pack(anchor=tk.W)
        
        # 解决方案说明
        solution_frame = ttk.LabelFrame(main_frame, text="✅ 解决方案", padding="10")
        solution_frame.pack(fill=tk.X, pady=(0, 15))
        
        solution_text = """🛠️ 实施的修复方案：
1. 创建系统集成管理器，实现各模块间的数据同步
2. 修复QQ应急状态更新中的字符串处理错误
3. 在主界面添加"系统集成"功能，提供一键数据导入
4. 建立自动回复监控→质量数据库→应急系统的数据流"""
        
        ttk.Label(solution_frame, text=solution_text, font=('Microsoft YaHei UI', 9)).pack(anchor=tk.W)
        
        # 测试控制区域
        control_frame = ttk.LabelFrame(main_frame, text="🔧 测试控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 测试邮箱输入
        email_frame = ttk.Frame(control_frame)
        email_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(email_frame, text="测试邮箱:").pack(side=tk.LEFT)
        self.test_email = tk.StringVar(value="<EMAIL>")
        ttk.Entry(email_frame, textvariable=self.test_email, width=30).pack(side=tk.LEFT, padx=(10, 0))
        
        # 测试按钮
        btn_frame = ttk.Frame(control_frame)
        btn_frame.pack(fill=tk.X)
        
        ttk.Button(btn_frame, text="🔄 测试数据同步",
                  command=self.test_data_sync).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🆘 测试QQ应急修复",
                  command=self.test_qq_emergency_fix).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🎯 完整集成测试",
                  command=self.test_full_integration).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="🗑️ 清空日志",
                  command=self.clear_log).pack(side=tk.RIGHT)
        
        # 测试结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="📊 测试结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建结果显示文本框
        self.result_text = scrolledtext.ScrolledText(
            result_frame, height=20, wrap=tk.WORD,
            font=('Consolas', 9)
        )
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 显示欢迎信息
        self.log_message("🔗 系统集成测试中心已启动")
        self.log_message("💡 请选择测试项目开始验证修复效果")
    
    def log_message(self, message: str):
        """记录日志消息"""
        try:
            current_time = datetime.datetime.now().strftime('%H:%M:%S')
            log_line = f"[{current_time}] {message}\n"
            
            self.result_text.insert(tk.END, log_line)
            self.result_text.see(tk.END)
            
        except Exception as e:
            print(f"记录日志失败: {e}")
    
    def test_data_sync(self):
        """测试数据同步功能"""
        def run_test():
            try:
                self.log_message("🔄 开始测试数据同步功能...")
                
                # 导入系统集成管理器
                from system_integration_manager import SystemIntegrationManager
                
                integration_manager = SystemIntegrationManager()
                test_email = self.test_email.get().strip()
                
                self.log_message(f"📧 测试邮箱: {test_email}")
                
                # 测试自动回复监控到质量数据库的同步
                self.log_message("🔄 测试: 自动回复监控 → 质量数据库")
                result = integration_manager.sync_auto_reply_to_quality_db(test_email)
                
                if result.get('success'):
                    self.log_message(f"✅ 同步成功: 有效收件人 {result.get('valid_count', 0)} 个，无效收件人 {result.get('invalid_count', 0)} 个")
                else:
                    self.log_message(f"❌ 同步失败: {result.get('error', '未知错误')}")
                
                self.log_message("✅ 数据同步测试完成")
                
            except Exception as e:
                self.log_message(f"❌ 数据同步测试失败: {str(e)}")
        
        # 在后台线程中运行测试
        threading.Thread(target=run_test, daemon=True).start()
    
    def test_qq_emergency_fix(self):
        """测试QQ应急状态修复"""
        def run_test():
            try:
                self.log_message("🆘 开始测试QQ应急状态修复...")
                
                # 导入QQ应急管理器
                from qq_email_anti_spam import QQEmailAntiSpamManager
                
                qq_manager = QQEmailAntiSpamManager()
                test_email = self.test_email.get().strip()
                test_recipient = "<EMAIL>"
                
                self.log_message(f"📧 测试邮箱: {test_email}")
                self.log_message(f"📧 测试收件人: {test_recipient}")
                
                # 测试修复后的回复状态更新
                self.log_message("🔧 测试修复后的回复状态更新...")
                
                # 测试REPLY_RECEIVED常量处理
                result1 = qq_manager.update_qq_reply_status(
                    sender_email=test_email,
                    recipient_email=test_recipient,
                    has_reply=True,
                    reply_type="REPLY_RECEIVED"  # 这个常量之前会导致错误
                )
                
                if result1:
                    self.log_message("✅ REPLY_RECEIVED常量处理修复成功")
                else:
                    self.log_message("❌ REPLY_RECEIVED常量处理仍有问题")
                
                # 测试正常字符串处理
                result2 = qq_manager.update_qq_reply_status(
                    sender_email=test_email,
                    recipient_email=test_recipient,
                    has_reply=True,
                    reply_type="自动回复"
                )
                
                if result2:
                    self.log_message("✅ 正常字符串处理成功")
                else:
                    self.log_message("❌ 正常字符串处理失败")
                
                self.log_message("✅ QQ应急状态修复测试完成")
                
            except Exception as e:
                self.log_message(f"❌ QQ应急状态修复测试失败: {str(e)}")
        
        # 在后台线程中运行测试
        threading.Thread(target=run_test, daemon=True).start()
    
    def test_full_integration(self):
        """完整集成测试"""
        def run_test():
            try:
                self.log_message("🎯 开始完整集成测试...")
                
                # 导入系统集成管理器
                from system_integration_manager import SystemIntegrationManager
                
                integration_manager = SystemIntegrationManager()
                test_email = self.test_email.get().strip()
                
                self.log_message(f"📧 测试邮箱: {test_email}")
                
                # 执行完整系统集成
                self.log_message("🔄 执行完整系统集成...")
                result = integration_manager.full_system_integration(test_email)
                
                if result.get('success'):
                    self.log_message("✅ 完整集成成功!")
                    self.log_message(f"📊 执行步骤: {len(result.get('steps', []))} 个")
                    
                    for step in result.get('steps', []):
                        step_result = step.get('result', {})
                        status_icon = "✅" if step_result.get('success') else "❌"
                        self.log_message(f"   {step.get('step')}. {step.get('name')} {status_icon}")
                else:
                    self.log_message(f"❌ 完整集成失败: {result.get('error', '未知错误')}")
                
                # 获取集成状态
                self.log_message("📊 获取集成状态...")
                status = integration_manager.get_integration_status(test_email)
                
                if 'error' not in status:
                    self.log_message(f"✅ 集成状态正常: 成功率 {status.get('success_rate', 0):.1f}%")
                else:
                    self.log_message(f"❌ 集成状态异常: {status.get('error', '未知错误')}")
                
                self.log_message("🎉 完整集成测试完成!")
                
                # 显示总结
                self.show_test_summary()
                
            except Exception as e:
                self.log_message(f"❌ 完整集成测试失败: {str(e)}")
        
        # 在后台线程中运行测试
        threading.Thread(target=run_test, daemon=True).start()
    
    def show_test_summary(self):
        """显示测试总结"""
        summary_msg = """🎯 系统集成修复验证完成！

✅ 修复成果：
1. 解决了自动回复监控与质量数据库的数据孤岛问题
2. 修复了QQ应急状态更新中的'REPLY_RECEIVED'错误
3. 实现了各功能模块的深度集成和数据共享
4. 提供了一键系统集成功能，提升用户体验

💡 使用建议：
• 定期执行系统集成，保持数据同步
• 监控集成状态，及时发现和处理异常
• 利用集成功能优化邮件发送策略

🔧 技术改进：
• 建立了统一的数据同步机制
• 增强了系统间的协调能力
• 提供了完整的集成状态监控"""
        
        messagebox.showinfo("测试完成", summary_msg)
    
    def clear_log(self):
        """清空日志"""
        self.result_text.delete(1.0, tk.END)
        self.log_message("🗑️ 日志已清空")

def main():
    """主函数"""
    root = tk.Tk()
    app = SystemIntegrationTestGUI(root)
    
    # 设置窗口居中
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')
    
    root.mainloop()

if __name__ == "__main__":
    main()
