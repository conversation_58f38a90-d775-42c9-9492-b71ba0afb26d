#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 定时发送功能导入问题修复脚本
解决GUI中可能出现的ScheduleManager导入问题
"""

import os
import sys
import importlib

def check_schedule_manager():
    """检查ScheduleManager模块"""
    print("🔍 检查ScheduleManager模块...")
    
    try:
        # 检查文件是否存在
        schedule_file = "schedule_manager.py"
        if not os.path.exists(schedule_file):
            print(f"❌ 文件不存在: {schedule_file}")
            return False
        
        print(f"✅ 文件存在: {schedule_file}")
        
        # 尝试导入
        import schedule_manager
        print("✅ 模块导入成功")
        
        # 重新加载模块
        importlib.reload(schedule_manager)
        print("✅ 模块重新加载成功")
        
        # 检查类
        from schedule_manager import ScheduleManager, ScheduledTask
        print("✅ 类导入成功")
        
        # 创建实例
        manager = ScheduleManager()
        print("✅ 实例创建成功")
        
        # 检查关键方法
        required_methods = [
            'add_scheduled_task',
            'get_all_tasks',
            'get_task_statistics',
            'get_optimal_send_time'
        ]
        
        for method in required_methods:
            if hasattr(manager, method):
                print(f"✅ 方法存在: {method}")
            else:
                print(f"❌ 方法缺失: {method}")
                return False
        
        # 停止调度器
        manager.stop_scheduler()
        print("✅ 调度器已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {str(e)}")
        return False

def fix_import_issues():
    """修复导入问题"""
    print("\n🔧 修复导入问题...")
    
    try:
        # 清理Python缓存
        import sys
        modules_to_remove = [name for name in sys.modules if name.startswith('schedule_manager')]
        for module_name in modules_to_remove:
            del sys.modules[module_name]
        print("✅ Python模块缓存已清理")
        
        # 清理__pycache__
        import shutil
        pycache_dirs = []
        for root, dirs, files in os.walk('.'):
            if '__pycache__' in dirs:
                pycache_dirs.append(os.path.join(root, '__pycache__'))
        
        for pycache_dir in pycache_dirs:
            try:
                shutil.rmtree(pycache_dir)
                print(f"✅ 已删除缓存目录: {pycache_dir}")
            except Exception as e:
                print(f"⚠️ 删除缓存目录失败: {pycache_dir} - {str(e)}")
        
        # 重新导入并测试
        import schedule_manager
        importlib.reload(schedule_manager)
        from schedule_manager import ScheduleManager
        
        manager = ScheduleManager()
        tasks = manager.get_all_tasks()
        stats = manager.get_task_statistics()
        manager.stop_scheduler()
        
        print("✅ 导入问题修复成功")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def create_safe_import_wrapper():
    """创建安全的导入包装器"""
    print("\n📦 创建安全导入包装器...")
    
    wrapper_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ ScheduleManager安全导入包装器
确保在GUI中正确导入和使用ScheduleManager
"""

import importlib
import sys
import os

def get_schedule_manager():
    """安全获取ScheduleManager实例"""
    try:
        # 确保模块路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # 重新加载模块
        if 'schedule_manager' in sys.modules:
            import schedule_manager
            importlib.reload(schedule_manager)
        
        from schedule_manager import ScheduleManager, ScheduledTask
        
        # 创建实例
        manager = ScheduleManager()
        
        # 验证关键方法
        required_methods = [
            'add_scheduled_task',
            'get_all_tasks',
            'get_task_statistics',
            'get_optimal_send_time',
            'cancel_task',
            'delete_task'
        ]
        
        for method in required_methods:
            if not hasattr(manager, method):
                raise AttributeError(f"ScheduleManager缺少方法: {method}")
        
        return manager, ScheduledTask
        
    except Exception as e:
        print(f"❌ 获取ScheduleManager失败: {str(e)}")
        return None, None

def safe_call_method(manager, method_name, *args, **kwargs):
    """安全调用ScheduleManager方法"""
    try:
        if not hasattr(manager, method_name):
            raise AttributeError(f"方法不存在: {method_name}")
        
        method = getattr(manager, method_name)
        return method(*args, **kwargs)
        
    except Exception as e:
        print(f"❌ 调用方法 {method_name} 失败: {str(e)}")
        return None

# 使用示例:
# manager, ScheduledTask = get_schedule_manager()
# if manager:
#     tasks = safe_call_method(manager, 'get_all_tasks')
#     stats = safe_call_method(manager, 'get_task_statistics')
'''
    
    try:
        with open('schedule_manager_wrapper.py', 'w', encoding='utf-8') as f:
            f.write(wrapper_code)
        print("✅ 安全导入包装器已创建: schedule_manager_wrapper.py")
        return True
        
    except Exception as e:
        print(f"❌ 创建包装器失败: {str(e)}")
        return False

def test_wrapper():
    """测试包装器"""
    print("\n🧪 测试安全导入包装器...")
    
    try:
        from schedule_manager_wrapper import get_schedule_manager, safe_call_method
        
        manager, ScheduledTask = get_schedule_manager()
        
        if manager is None:
            print("❌ 包装器测试失败: 无法获取manager")
            return False
        
        print("✅ 包装器获取manager成功")
        
        # 测试方法调用
        tasks = safe_call_method(manager, 'get_all_tasks')
        if tasks is not None:
            print(f"✅ 包装器方法调用成功: get_all_tasks返回 {len(tasks)} 个任务")
        else:
            print("❌ 包装器方法调用失败: get_all_tasks")
            return False
        
        stats = safe_call_method(manager, 'get_task_statistics')
        if stats is not None:
            print(f"✅ 包装器方法调用成功: get_task_statistics返回统计信息")
        else:
            print("❌ 包装器方法调用失败: get_task_statistics")
            return False
        
        # 停止调度器
        safe_call_method(manager, 'stop_scheduler')
        print("✅ 包装器测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 包装器测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 定时发送功能导入问题修复工具")
    print("=" * 50)
    
    # 1. 检查当前状态
    if check_schedule_manager():
        print("✅ ScheduleManager模块状态正常")
    else:
        print("⚠️ ScheduleManager模块存在问题，尝试修复...")
        
        # 2. 修复导入问题
        if fix_import_issues():
            print("✅ 导入问题修复成功")
            
            # 3. 再次检查
            if check_schedule_manager():
                print("✅ 修复后验证成功")
            else:
                print("❌ 修复后验证失败")
        else:
            print("❌ 导入问题修复失败")
    
    # 4. 创建安全包装器
    if create_safe_import_wrapper():
        # 5. 测试包装器
        if test_wrapper():
            print("\n🎉 所有修复和测试完成！")
            print("\n💡 使用建议:")
            print("1. 如果GUI中仍有导入问题，可以使用 schedule_manager_wrapper.py")
            print("2. 在GUI代码中替换导入语句:")
            print("   from schedule_manager_wrapper import get_schedule_manager, safe_call_method")
            print("   manager, ScheduledTask = get_schedule_manager()")
            print("3. 使用 safe_call_method 调用manager的方法")
        else:
            print("❌ 包装器测试失败")
    else:
        print("❌ 包装器创建失败")

if __name__ == "__main__":
    main()
