#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试datetime导入修复
"""

def test_datetime_import():
    """测试datetime导入是否正常"""
    try:
        # 测试导入
        import datetime
        
        # 测试基本功能
        current_time = datetime.datetime.now()
        iso_time = current_time.isoformat()
        
        print("✅ datetime模块导入成功")
        print(f"✅ 当前时间: {current_time}")
        print(f"✅ ISO格式时间: {iso_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ datetime导入测试失败: {str(e)}")
        return False

def test_gui_datetime_usage():
    """测试GUI中datetime的使用"""
    try:
        # 模拟GUI中的datetime使用
        import datetime
        
        # 模拟保存应急阈值的操作
        threshold = 5
        sender_email = "<EMAIL>"
        current_time = datetime.datetime.now().isoformat()
        
        print("✅ GUI中datetime使用测试成功")
        print(f"✅ 阈值: {threshold}")
        print(f"✅ 发件人: {sender_email}")
        print(f"✅ 时间戳: {current_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI datetime使用测试失败: {str(e)}")
        return False

def test_qq_emergency_datetime():
    """测试QQ应急系统中的datetime使用"""
    try:
        # 测试QQ应急系统相关的datetime操作
        import datetime
        
        # 模拟应急系统中的时间操作
        trigger_time = datetime.datetime.now().isoformat()
        emergency_start = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        print("✅ QQ应急系统datetime测试成功")
        print(f"✅ 触发时间: {trigger_time}")
        print(f"✅ 应急开始时间: {emergency_start}")
        
        return True
        
    except Exception as e:
        print(f"❌ QQ应急系统datetime测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 datetime导入修复验证测试")
    print("=" * 50)
    
    tests = [
        ("基本datetime导入", test_datetime_import),
        ("GUI datetime使用", test_gui_datetime_usage),
        ("QQ应急系统datetime", test_qq_emergency_datetime)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 测试: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} - 通过")
        else:
            print(f"❌ {test_name} - 失败")
    
    print(f"\n📊 测试结果")
    print("=" * 30)
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！datetime导入修复成功！")
        print("✅ 现在可以正常使用QQ应急管理器的保存功能了")
    else:
        print(f"\n⚠️ 有 {total - passed} 个测试失败，需要进一步检查")
    
    return passed == total

if __name__ == "__main__":
    main()
