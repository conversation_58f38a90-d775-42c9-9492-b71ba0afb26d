# 自动化邮件发送助手

一个简单易用的Python邮件自动发送工具，专为QQ邮箱优化，支持发送带附件的邮件，并采用最佳实践避免邮件进入垃圾箱。

## 功能特性

- ✅ 支持QQ邮箱SMTP发送
- ✅ 支持多种格式附件（文档、图片、压缩包等）
- ✅ 支持多收件人发送
- ✅ 批量邮件发送功能
- ✅ 智能避免进入垃圾箱
- ✅ 详细的日志记录
- ✅ 友好的命令行界面
- ✅ 发送频率控制
- ✅ 文件大小和类型检查
- ✅ 图形界面版本

## 安装和配置

### 1. 环境要求
- Python 3.6+
- QQ邮箱及SMTP授权码

### 2. 获取QQ邮箱SMTP授权码
1. 登录QQ邮箱网页版
2. 点击"设置" → "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"IMAP/SMTP服务"
5. 按提示获取授权码（已为您配置：vwpboqxircdudgfa）

### 3. 运行程序

#### 方法1: 使用启动脚本（推荐）
```bash
# Windows用户 - 双击运行
双击 "启动.bat" 文件（中文界面）
双击 "start.bat" 文件（英文界面）

# 或在命令行中运行
.\启动.bat     # 中文界面
.\start.bat    # 英文界面

# 注意：在PowerShell中需要使用 .\ 前缀
```

#### 方法2: 直接运行Python脚本
```bash
# 图形界面版本（推荐新手使用）
python gui_main.py

# 命令行版本 - 单邮件发送
python main.py

# 命令行版本 - 批量邮件发送
python main.py --batch

# 功能测试
python test_email.py

# 环境检查与设置
python setup.py
```

## 使用说明

### 图形界面版本（推荐）
1. 运行 `python gui_main.py` 或双击 `start.bat` 选择图形界面
2. 在界面中填写：
   - 发送者邮箱（您的QQ邮箱）
   - 收件人邮箱（多个用逗号分隔）
   - 邮件主题
   - 邮件正文
   - 添加附件（可选）
3. 点击"发送邮件"按钮
4. 支持测试连接功能

### 命令行版本 - 单邮件发送
1. 运行 `python main.py`
2. 按提示输入：
   - 您的QQ邮箱地址
   - 收件人邮箱（多个用逗号分隔）
   - 邮件主题
   - 邮件正文
   - 附件文件路径（可选）
3. 确认信息后发送

### 命令行版本 - 批量邮件发送
1. 运行 `python main.py --batch`
2. 输入发送者邮箱
3. 逐一输入每封邮件的信息
4. 输入 'done' 完成配置
5. 确认后批量发送

## 避免进入垃圾箱的措施

本工具采用以下策略确保邮件正常送达：

1. **规范的邮件头设置**
   - 设置合适的邮件优先级
   - 添加标准的邮件客户端标识
   - 使用正确的字符编码

2. **发送频率控制**
   - 邮件间隔发送（默认2秒）
   - 限制每小时发送数量
   - 避免被识别为垃圾邮件

3. **内容规范**
   - 支持的附件格式检查
   - 文件大小限制（25MB）
   - 合理的邮件结构

## 支持的附件格式

- 文档：.txt, .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx
- 图片：.jpg, .jpeg, .png, .gif
- 压缩包：.zip, .rar, .7z

## 配置文件说明

### config.py
- `SMTP_CONFIG`: SMTP服务器配置
- `DEFAULT_SETTINGS`: 默认设置（字符编码、超时时间等）
- `EMAIL_HEADERS`: 邮件头设置
- `RATE_LIMIT`: 发送频率控制

## 日志文件

程序运行时会生成 `email_sender.log` 日志文件，记录：
- 发送成功/失败信息
- 错误详情
- 附件处理状态

## 常见问题

### Q: 邮件发送失败怎么办？
A: 请检查：
1. QQ邮箱地址是否正确
2. SMTP授权码是否有效
3. 网络连接是否正常
4. 查看日志文件获取详细错误信息

### Q: 收件人收不到邮件？
A: 请提醒收件人：
1. 检查垃圾邮件文件夹
2. 将您的邮箱添加到通讯录
3. 检查邮箱设置是否屏蔽了外部邮件

### Q: 附件太大无法发送？
A: 
1. 单个附件限制25MB
2. 建议压缩大文件
3. 或使用网盘链接方式

## 安全提示

- 请妥善保管您的SMTP授权码
- 不要在公共场所使用本工具
- 定期更换授权码以确保安全

## 技术支持

如遇问题，请查看日志文件或检查：
1. Python版本是否符合要求
2. 网络连接是否正常
3. QQ邮箱设置是否正确

---

**注意**: 请遵守相关法律法规，不要用于发送垃圾邮件或进行其他违法活动。
