#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统功能协调优化 - 让各个功能相互配合，效果更好
"""

import json
import os
import datetime

def create_system_coordinator():
    """创建系统协调器"""
    print("🔧 创建系统协调器")
    print("-" * 30)
    
    coordinator_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统协调器 - 让各个功能相互配合
"""

import json
import datetime
import sqlite3
import threading
import time

class EmailSystemCoordinator:
    """邮件系统协调器"""
    
    def __init__(self):
        self.lock = threading.RLock()
        self.status_cache = {}
        self.last_update = None
    
    def analyze_recipient_status(self, sender_email):
        """分析收件人状态，为各个功能提供协调信息"""
        try:
            with self.lock:
                # 从质量数据库获取收件人状态
                conn = sqlite3.connect('recipient_quality.db', timeout=30.0)
                cursor = conn.cursor()

                # 确保表存在
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS recipient_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        recipient_email TEXT NOT NULL,
                        sender_email TEXT NOT NULL,
                        last_sent_time TEXT,
                        last_reply_time TEXT,
                        reply_count INTEGER DEFAULT 0,
                        bounce_count INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        notes TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(recipient_email, sender_email)
                    )
                ''')

                cursor.execute("""
                    SELECT recipient_email, status, reply_count, bounce_count,
                           last_reply_time, updated_at
                    FROM recipient_status
                    WHERE sender_email = ?
                    ORDER BY last_reply_time DESC
                """, (sender_email,))
                
                recipients = cursor.fetchall()
                conn.close()
                
                # 分析状态
                analysis = {
                    'total_recipients': len(recipients),
                    'active_recipients': [],      # 有回复的收件人
                    'no_reply_recipients': [],    # 未回复的收件人
                    'invalid_recipients': [],     # 无效的收件人
                    'risk_level': 'low',          # 风险等级
                    'recommendations': []         # 建议
                }
                
                for recipient in recipients:
                    email, status, reply_count, bounce_count, last_reply, created = recipient
                    
                    recipient_info = {
                        'email': email,
                        'status': status,
                        'reply_count': reply_count,
                        'bounce_count': bounce_count,
                        'last_reply': last_reply,
                        'created': created
                    }
                    
                    if status == 'active' and reply_count > 0:
                        analysis['active_recipients'].append(recipient_info)
                    elif status == 'invalid' or bounce_count >= 2:
                        analysis['invalid_recipients'].append(recipient_info)
                    else:
                        analysis['no_reply_recipients'].append(recipient_info)
                
                # 计算风险等级
                total = analysis['total_recipients']
                if total > 0:
                    no_reply_rate = len(analysis['no_reply_recipients']) / total
                    invalid_rate = len(analysis['invalid_recipients']) / total
                    
                    if no_reply_rate > 0.8 or invalid_rate > 0.3:
                        analysis['risk_level'] = 'high'
                    elif no_reply_rate > 0.6 or invalid_rate > 0.2:
                        analysis['risk_level'] = 'medium'
                    else:
                        analysis['risk_level'] = 'low'
                
                # 生成建议
                self._generate_recommendations(analysis)
                
                return analysis
                
        except Exception as e:
            print(f"❌ 分析收件人状态失败: {str(e)}")
            return None
    
    def _generate_recommendations(self, analysis):
        """生成系统建议"""
        recommendations = []
        
        no_reply_count = len(analysis['no_reply_recipients'])
        invalid_count = len(analysis['invalid_recipients'])
        active_count = len(analysis['active_recipients'])
        total = analysis['total_recipients']
        
        if total == 0:
            recommendations.append("📭 暂无收件人数据，建议先发送一些邮件建立基础数据")
            analysis['recommendations'] = recommendations
            return
        
        # 基于未回复数量的建议
        if no_reply_count > 5:
            recommendations.append(f"⚠️ 发现 {no_reply_count} 个未回复收件人，可能邮件进入垃圾箱")
            recommendations.append("🛡️ 建议激活QQ应急模式，降低发送频率")
            recommendations.append("📝 建议更换邮件主题和内容模板")
        
        # 基于无效收件人的建议
        if invalid_count > 0:
            recommendations.append(f"🗑️ 发现 {invalid_count} 个无效收件人，建议从发送列表中移除")
            recommendations.append("📊 建议使用质量数据库管理器清理无效邮箱")
        
        # 基于活跃收件人的建议
        if active_count > 0:
            recommendations.append(f"✅ 发现 {active_count} 个活跃收件人，建议优先向他们发送")
            recommendations.append("📈 建议在反垃圾邮件管理器中设置优先发送策略")
        
        # 基于风险等级的建议
        if analysis['risk_level'] == 'high':
            recommendations.append("🚨 风险等级：高 - 建议立即停止发送，检查邮件内容")
            recommendations.append("🆘 建议手动激活QQ应急模式")
        elif analysis['risk_level'] == 'medium':
            recommendations.append("⚠️ 风险等级：中 - 建议降低发送频率，监控回复情况")
            recommendations.append("📊 建议开启自动回复监控")
        else:
            recommendations.append("✅ 风险等级：低 - 当前发送状态良好")
            recommendations.append("📈 建议继续当前发送策略")
        
        analysis['recommendations'] = recommendations
    
    def coordinate_qq_emergency(self, sender_email, analysis):
        """协调QQ应急系统"""
        try:
            if analysis['risk_level'] == 'high':
                # 高风险时自动激活应急模式
                from qq_email_anti_spam import QQEmailAntiSpamManager
                qq_manager = QQEmailAntiSpamManager()
                
                # 检查是否需要激活应急模式
                emergency_status = qq_manager._check_emergency_status(sender_email)
                
                if not emergency_status.get('is_active', False):
                    # 手动激活应急模式
                    qq_manager._activate_emergency_mode(sender_email, {
                        'consecutive_no_reply': len(analysis['no_reply_recipients']),
                        'trigger_reason': 'high_risk_detected'
                    })
                    
                    print(f"🆘 高风险检测，已自动激活QQ应急模式")
                    return True
            
            return False
            
        except Exception as e:
            print(f"❌ 协调QQ应急系统失败: {str(e)}")
            return False
    
    def coordinate_quality_manager(self, sender_email, analysis):
        """协调质量数据库管理器"""
        try:
            # 自动标记无效收件人
            if analysis['invalid_recipients']:
                conn = sqlite3.connect('recipient_quality.db', timeout=30.0)
                cursor = conn.cursor()
                
                for recipient in analysis['invalid_recipients']:
                    cursor.execute("""
                        UPDATE recipient_status 
                        SET status = 'invalid', 
                            updated_at = CURRENT_TIMESTAMP,
                            notes = '系统自动标记：多次退信'
                        WHERE recipient_email = ? AND sender_email = ?
                    """, (recipient['email'], sender_email))
                
                conn.commit()
                conn.close()
                
                print(f"📊 已自动标记 {len(analysis['invalid_recipients'])} 个无效收件人")
            
            return True
            
        except Exception as e:
            print(f"❌ 协调质量管理器失败: {str(e)}")
            return False
    
    def coordinate_anti_spam(self, sender_email, analysis):
        """协调反垃圾邮件管理器"""
        try:
            # 根据风险等级调整发送策略
            strategy_config = {
                'sender_email': sender_email,
                'risk_level': analysis['risk_level'],
                'recommended_pattern': 'conservative' if analysis['risk_level'] == 'high' else 'moderate',
                'max_daily_sends': 50 if analysis['risk_level'] == 'high' else 100,
                'send_interval': 300 if analysis['risk_level'] == 'high' else 180,  # 秒
                'priority_recipients': [r['email'] for r in analysis['active_recipients']],
                'blocked_recipients': [r['email'] for r in analysis['invalid_recipients']],
                'updated_at': datetime.datetime.now().isoformat()
            }
            
            # 保存策略配置
            with open('anti_spam_strategy.json', 'w', encoding='utf-8') as f:
                json.dump(strategy_config, f, ensure_ascii=False, indent=2)
            
            print(f"🛡️ 已更新反垃圾邮件策略：{strategy_config['recommended_pattern']}")
            return True
            
        except Exception as e:
            print(f"❌ 协调反垃圾邮件管理器失败: {str(e)}")
            return False
    
    def generate_system_report(self, sender_email):
        """生成系统协调报告"""
        try:
            analysis = self.analyze_recipient_status(sender_email)
            if not analysis:
                return None
            
            # 协调各个功能
            qq_coordinated = self.coordinate_qq_emergency(sender_email, analysis)
            quality_coordinated = self.coordinate_quality_manager(sender_email, analysis)
            anti_spam_coordinated = self.coordinate_anti_spam(sender_email, analysis)
            
            # 生成报告
            report = {
                'sender_email': sender_email,
                'analysis_time': datetime.datetime.now().isoformat(),
                'recipient_analysis': analysis,
                'coordination_results': {
                    'qq_emergency': qq_coordinated,
                    'quality_manager': quality_coordinated,
                    'anti_spam': anti_spam_coordinated
                },
                'system_status': 'coordinated' if all([quality_coordinated, anti_spam_coordinated]) else 'partial'
            }
            
            # 保存报告
            report_file = f"system_report_{sender_email.replace('@', '_')}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            return report
            
        except Exception as e:
            print(f"❌ 生成系统报告失败: {str(e)}")
            return None

# 使用示例
if __name__ == "__main__":
    coordinator = EmailSystemCoordinator()
    
    # 为指定发件人生成协调报告
    sender_email = "<EMAIL>"
    report = coordinator.generate_system_report(sender_email)
    
    if report:
        print("\\n📊 系统协调报告")
        print("=" * 50)
        
        analysis = report['recipient_analysis']
        print(f"📧 发件人: {sender_email}")
        print(f"📊 总收件人: {analysis['total_recipients']}")
        print(f"✅ 活跃收件人: {len(analysis['active_recipients'])}")
        print(f"❌ 未回复收件人: {len(analysis['no_reply_recipients'])}")
        print(f"🗑️ 无效收件人: {len(analysis['invalid_recipients'])}")
        print(f"⚠️ 风险等级: {analysis['risk_level']}")
        
        print("\\n💡 系统建议:")
        for i, recommendation in enumerate(analysis['recommendations'], 1):
            print(f"  {i}. {recommendation}")
        
        coordination = report['coordination_results']
        print(f"\\n🔧 功能协调状态:")
        print(f"  🆘 QQ应急系统: {'✅ 已协调' if coordination['qq_emergency'] else '⚪ 无需协调'}")
        print(f"  📊 质量管理器: {'✅ 已协调' if coordination['quality_manager'] else '❌ 协调失败'}")
        print(f"  🛡️ 反垃圾邮件: {'✅ 已协调' if coordination['anti_spam'] else '❌ 协调失败'}")
        
        print(f"\\n🎯 系统状态: {report['system_status']}")
'''
    
    try:
        with open('system_coordinator.py', 'w', encoding='utf-8') as f:
            f.write(coordinator_code)
        
        print("✅ 系统协调器已创建: system_coordinator.py")
        return True
        
    except Exception as e:
        print(f"❌ 创建系统协调器失败: {str(e)}")
        return False

def integrate_coordinator_to_gui():
    """将协调器集成到GUI中"""
    print("\n🔧 集成协调器到GUI")
    print("-" * 30)
    
    try:
        # 在GUI中添加系统协调功能
        integration_code = '''
    def open_system_coordinator(self):
        """打开系统协调器"""
        try:
            coordinator_window = tk.Toplevel(self.root)
            coordinator_window.title("🔧 系统协调器")
            coordinator_window.geometry("900x700")
            coordinator_window.transient(self.root)
            
            main_frame = ttk.Frame(coordinator_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(main_frame, text="🔧 邮件系统协调器", 
                                   font=('Microsoft YaHei UI', 16, 'bold'))
            title_label.pack(pady=(0, 20))
            
            # 发件人选择
            sender_frame = ttk.Frame(main_frame)
            sender_frame.pack(fill=tk.X, pady=(0, 10))
            
            ttk.Label(sender_frame, text="发件人邮箱:").pack(side=tk.LEFT, padx=(0, 5))
            sender_var = tk.StringVar(value="<EMAIL>")
            ttk.Entry(sender_frame, textvariable=sender_var, width=30).pack(side=tk.LEFT, padx=(0, 10))
            
            def generate_report():
                try:
                    from system_coordinator import EmailSystemCoordinator
                    coordinator = EmailSystemCoordinator()
                    
                    sender_email = sender_var.get().strip()
                    if not sender_email:
                        messagebox.showwarning("提示", "请输入发件人邮箱")
                        return
                    
                    report = coordinator.generate_system_report(sender_email)
                    
                    if report:
                        # 显示报告
                        report_text.delete(1.0, tk.END)
                        
                        analysis = report['recipient_analysis']
                        report_content = f"""📊 系统协调报告
{'='*60}

📧 发件人: {sender_email}
📊 分析时间: {report['analysis_time'][:19]}

📈 收件人统计:
  • 总收件人: {analysis['total_recipients']}
  • ✅ 活跃收件人: {len(analysis['active_recipients'])} (有回复)
  • ❌ 未回复收件人: {len(analysis['no_reply_recipients'])} (可能进入垃圾箱)
  • 🗑️ 无效收件人: {len(analysis['invalid_recipients'])} (邮箱有问题)
  • ⚠️ 风险等级: {analysis['risk_level'].upper()}

💡 系统建议:
"""
                        for i, recommendation in enumerate(analysis['recommendations'], 1):
                            report_content += f"  {i}. {recommendation}\\n"
                        
                        coordination = report['coordination_results']
                        report_content += f"""
🔧 功能协调状态:
  • 🆘 QQ应急系统: {'✅ 已协调' if coordination['qq_emergency'] else '⚪ 无需协调'}
  • 📊 质量管理器: {'✅ 已协调' if coordination['quality_manager'] else '❌ 协调失败'}
  • 🛡️ 反垃圾邮件: {'✅ 已协调' if coordination['anti_spam'] else '❌ 协调失败'}

🎯 系统状态: {report['system_status'].upper()}

📋 详细分析:
"""
                        
                        if analysis['active_recipients']:
                            report_content += f"\\n✅ 活跃收件人 ({len(analysis['active_recipients'])} 个):\\n"
                            for recipient in analysis['active_recipients'][:10]:  # 显示前10个
                                report_content += f"  • {recipient['email']} (回复 {recipient['reply_count']} 次)\\n"
                        
                        if analysis['no_reply_recipients']:
                            report_content += f"\\n❌ 未回复收件人 ({len(analysis['no_reply_recipients'])} 个):\\n"
                            for recipient in analysis['no_reply_recipients'][:10]:  # 显示前10个
                                report_content += f"  • {recipient['email']} (可能进入垃圾箱或邮箱有问题)\\n"
                        
                        if analysis['invalid_recipients']:
                            report_content += f"\\n🗑️ 无效收件人 ({len(analysis['invalid_recipients'])} 个):\\n"
                            for recipient in analysis['invalid_recipients'][:10]:  # 显示前10个
                                report_content += f"  • {recipient['email']} (退信 {recipient['bounce_count']} 次)\\n"
                        
                        report_text.insert(tk.END, report_content)
                        
                        self.log_message(f"✅ 系统协调报告已生成: {sender_email}")
                        
                    else:
                        messagebox.showerror("错误", "生成协调报告失败")
                        
                except Exception as e:
                    error_msg = f"生成协调报告失败: {str(e)}"
                    self.log_message(f"❌ {error_msg}")
                    messagebox.showerror("错误", error_msg)
            
            ttk.Button(sender_frame, text="📊 生成协调报告", command=generate_report).pack(side=tk.LEFT)
            
            # 报告显示区域
            report_text = scrolledtext.ScrolledText(main_frame, height=35, font=('Consolas', 10))
            report_text.pack(fill=tk.BOTH, expand=True, pady=(10, 10))
            
            # 操作按钮
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)
            
            ttk.Button(button_frame, text="🆘 打开QQ应急管理", 
                      command=self.open_qq_emergency_manager).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="📊 打开质量管理器", 
                      command=self.open_quality_manager).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="🛡️ 打开反垃圾邮件", 
                      command=self.open_anti_spam_manager).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="关闭", 
                      command=coordinator_window.destroy).pack(side=tk.RIGHT, padx=5)
            
            # 初始生成报告
            generate_report()
            
        except Exception as e:
            error_msg = f"打开系统协调器失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
'''
        
        # 将代码添加到GUI文件中
        with open('gui_main.py', 'r', encoding='utf-8') as f:
            gui_content = f.read()
        
        # 在类的末尾添加方法
        if 'def open_system_coordinator(self):' not in gui_content:
            # 找到类的最后一个方法
            insert_pos = gui_content.rfind('    def ')
            if insert_pos != -1:
                # 找到该方法的结束位置
                next_class_pos = gui_content.find('\nclass ', insert_pos)
                if next_class_pos == -1:
                    next_class_pos = len(gui_content)
                
                # 在方法结束后插入新方法
                gui_content = gui_content[:next_class_pos] + integration_code + gui_content[next_class_pos:]
                
                with open('gui_main.py', 'w', encoding='utf-8') as f:
                    f.write(gui_content)
                
                print("✅ 系统协调器已集成到GUI")
                return True
        
        print("⚪ 系统协调器已存在于GUI中")
        return True
        
    except Exception as e:
        print(f"❌ 集成协调器到GUI失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 系统功能协调优化")
    print("=" * 60)
    
    steps = [
        ("创建系统协调器", create_system_coordinator),
        ("集成协调器到GUI", integrate_coordinator_to_gui)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔧 执行: {step_name}")
        if step_func():
            success_count += 1
            print(f"✅ {step_name} - 成功")
        else:
            print(f"❌ {step_name} - 失败")
    
    print(f"\n📊 优化结果")
    print("=" * 30)
    print(f"成功步骤: {success_count}/{len(steps)}")
    
    if success_count == len(steps):
        print("\n🎉 系统功能协调优化完成！")
        print("✅ 系统协调器已创建")
        print("✅ GUI集成已完成")
        
        print("\n💡 现在系统具备:")
        print("• 🔍 智能分析收件人状态")
        print("• 🆘 自动协调QQ应急系统")
        print("• 📊 自动优化质量管理")
        print("• 🛡️ 自动调整反垃圾策略")
        print("• 📋 生成协调报告")
        print("• 💡 提供系统建议")
        
        print("\n🚀 功能协调效果:")
        print("• 未回复收件人 → 自动标记可能进入垃圾箱")
        print("• 高风险状态 → 自动激活QQ应急模式")
        print("• 无效收件人 → 自动从发送列表移除")
        print("• 活跃收件人 → 自动设置优先发送")
        print("• 系统建议 → 指导用户优化策略")
        
    else:
        print(f"\n⚠️ 部分功能优化失败")

if __name__ == "__main__":
    main()
