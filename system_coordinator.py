#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统协调器 - 让各个功能相互配合
"""

import json
import datetime
import sqlite3
import threading
import time

class EmailSystemCoordinator:
    """邮件系统协调器"""
    
    def __init__(self):
        self.lock = threading.RLock()
        self.status_cache = {}
        self.last_update = None
    
    def analyze_recipient_status(self, sender_email):
        """分析收件人状态，为各个功能提供协调信息"""
        try:
            with self.lock:
                # 从质量数据库获取收件人状态
                conn = sqlite3.connect('recipient_quality.db', timeout=30.0)
                cursor = conn.cursor()

                # 确保表存在
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS recipient_status (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        recipient_email TEXT NOT NULL,
                        sender_email TEXT NOT NULL,
                        last_sent_time TEXT,
                        last_reply_time TEXT,
                        reply_count INTEGER DEFAULT 0,
                        bounce_count INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        notes TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(recipient_email, sender_email)
                    )
                ''')

                cursor.execute("""
                    SELECT recipient_email, status, reply_count, bounce_count,
                           last_reply_time, updated_at
                    FROM recipient_status
                    WHERE sender_email = ?
                    ORDER BY last_reply_time DESC
                """, (sender_email,))
                
                recipients = cursor.fetchall()
                conn.close()
                
                # 分析状态
                analysis = {
                    'total_recipients': len(recipients),
                    'active_recipients': [],      # 有回复的收件人
                    'no_reply_recipients': [],    # 未回复的收件人
                    'invalid_recipients': [],     # 无效的收件人
                    'risk_level': 'low',          # 风险等级
                    'recommendations': []         # 建议
                }
                
                for recipient in recipients:
                    email, status, reply_count, bounce_count, last_reply, created = recipient
                    
                    recipient_info = {
                        'email': email,
                        'status': status,
                        'reply_count': reply_count,
                        'bounce_count': bounce_count,
                        'last_reply': last_reply,
                        'created': created
                    }
                    
                    if status == 'active' and reply_count > 0:
                        analysis['active_recipients'].append(recipient_info)
                    elif status == 'invalid' or bounce_count >= 2:
                        analysis['invalid_recipients'].append(recipient_info)
                    else:
                        analysis['no_reply_recipients'].append(recipient_info)
                
                # 计算风险等级
                total = analysis['total_recipients']
                if total > 0:
                    no_reply_rate = len(analysis['no_reply_recipients']) / total
                    invalid_rate = len(analysis['invalid_recipients']) / total
                    
                    if no_reply_rate > 0.8 or invalid_rate > 0.3:
                        analysis['risk_level'] = 'high'
                    elif no_reply_rate > 0.6 or invalid_rate > 0.2:
                        analysis['risk_level'] = 'medium'
                    else:
                        analysis['risk_level'] = 'low'
                
                # 生成建议
                self._generate_recommendations(analysis)
                
                return analysis
                
        except Exception as e:
            print(f"❌ 分析收件人状态失败: {str(e)}")
            return None
    
    def _generate_recommendations(self, analysis):
        """生成系统建议"""
        recommendations = []
        
        no_reply_count = len(analysis['no_reply_recipients'])
        invalid_count = len(analysis['invalid_recipients'])
        active_count = len(analysis['active_recipients'])
        total = analysis['total_recipients']
        
        if total == 0:
            recommendations.append("📭 暂无收件人数据，建议先发送一些邮件建立基础数据")
            analysis['recommendations'] = recommendations
            return
        
        # 基于未回复数量的建议
        if no_reply_count > 5:
            recommendations.append(f"⚠️ 发现 {no_reply_count} 个未回复收件人，可能邮件进入垃圾箱")
            recommendations.append("🛡️ 建议激活QQ应急模式，降低发送频率")
            recommendations.append("📝 建议更换邮件主题和内容模板")
        
        # 基于无效收件人的建议
        if invalid_count > 0:
            recommendations.append(f"🗑️ 发现 {invalid_count} 个无效收件人，建议从发送列表中移除")
            recommendations.append("📊 建议使用质量数据库管理器清理无效邮箱")
        
        # 基于活跃收件人的建议
        if active_count > 0:
            recommendations.append(f"✅ 发现 {active_count} 个活跃收件人，建议优先向他们发送")
            recommendations.append("📈 建议在反垃圾邮件管理器中设置优先发送策略")
        
        # 基于风险等级的建议
        if analysis['risk_level'] == 'high':
            recommendations.append("🚨 风险等级：高 - 建议立即停止发送，检查邮件内容")
            recommendations.append("🆘 建议手动激活QQ应急模式")
        elif analysis['risk_level'] == 'medium':
            recommendations.append("⚠️ 风险等级：中 - 建议降低发送频率，监控回复情况")
            recommendations.append("📊 建议开启自动回复监控")
        else:
            recommendations.append("✅ 风险等级：低 - 当前发送状态良好")
            recommendations.append("📈 建议继续当前发送策略")
        
        analysis['recommendations'] = recommendations
    
    def coordinate_qq_emergency(self, sender_email, analysis):
        """协调QQ应急系统"""
        try:
            if analysis['risk_level'] == 'high':
                # 高风险时自动激活应急模式
                from qq_email_anti_spam import QQEmailAntiSpamManager
                qq_manager = QQEmailAntiSpamManager()
                
                # 检查是否需要激活应急模式
                emergency_status = qq_manager._check_emergency_status(sender_email)
                
                if not emergency_status.get('is_active', False):
                    # 手动激活应急模式
                    qq_manager._activate_emergency_mode(sender_email, {
                        'consecutive_no_reply': len(analysis['no_reply_recipients']),
                        'trigger_reason': 'high_risk_detected'
                    })
                    
                    print(f"🆘 高风险检测，已自动激活QQ应急模式")
                    return True
            
            return False
            
        except Exception as e:
            print(f"❌ 协调QQ应急系统失败: {str(e)}")
            return False
    
    def coordinate_quality_manager(self, sender_email, analysis):
        """协调质量数据库管理器"""
        try:
            # 自动标记无效收件人
            if analysis['invalid_recipients']:
                conn = sqlite3.connect('recipient_quality.db', timeout=30.0)
                cursor = conn.cursor()
                
                for recipient in analysis['invalid_recipients']:
                    cursor.execute("""
                        UPDATE recipient_status 
                        SET status = 'invalid', 
                            updated_at = CURRENT_TIMESTAMP,
                            notes = '系统自动标记：多次退信'
                        WHERE recipient_email = ? AND sender_email = ?
                    """, (recipient['email'], sender_email))
                
                conn.commit()
                conn.close()
                
                print(f"📊 已自动标记 {len(analysis['invalid_recipients'])} 个无效收件人")
            
            return True
            
        except Exception as e:
            print(f"❌ 协调质量管理器失败: {str(e)}")
            return False
    
    def coordinate_anti_spam(self, sender_email, analysis):
        """协调反垃圾邮件管理器"""
        try:
            # 根据风险等级调整发送策略
            strategy_config = {
                'sender_email': sender_email,
                'risk_level': analysis['risk_level'],
                'recommended_pattern': 'conservative' if analysis['risk_level'] == 'high' else 'moderate',
                'max_daily_sends': 50 if analysis['risk_level'] == 'high' else 100,
                'send_interval': 300 if analysis['risk_level'] == 'high' else 180,  # 秒
                'priority_recipients': [r['email'] for r in analysis['active_recipients']],
                'blocked_recipients': [r['email'] for r in analysis['invalid_recipients']],
                'updated_at': datetime.datetime.now().isoformat()
            }
            
            # 保存策略配置
            with open('anti_spam_strategy.json', 'w', encoding='utf-8') as f:
                json.dump(strategy_config, f, ensure_ascii=False, indent=2)
            
            print(f"🛡️ 已更新反垃圾邮件策略：{strategy_config['recommended_pattern']}")
            return True
            
        except Exception as e:
            print(f"❌ 协调反垃圾邮件管理器失败: {str(e)}")
            return False

    def analyze_system_status(self, sender_email: str) -> dict:
        """分析系统状态"""
        try:
            analysis = {
                'sender_email': sender_email,
                'analysis_time': datetime.datetime.now().isoformat(),
                'risk_level': 'low',
                'invalid_recipients': [],
                'recommendations': []
            }

            # 分析自动回复监控数据
            try:
                from email_receiver import EmailReceiver
                receiver = EmailReceiver(sender_email, "")

                valid_recipients = receiver.get_valid_recipients(sender_email)
                invalid_recipients = receiver.get_invalid_recipients(sender_email)

                analysis['valid_count'] = len(valid_recipients)
                analysis['invalid_count'] = len(invalid_recipients)
                analysis['invalid_recipients'] = invalid_recipients

                # 评估风险等级
                if len(invalid_recipients) > 10:
                    analysis['risk_level'] = 'high'
                elif len(invalid_recipients) > 5:
                    analysis['risk_level'] = 'medium'

            except Exception as e:
                analysis['auto_reply_error'] = str(e)

            # 分析质量数据库
            try:
                from recipient_quality_manager import RecipientQualityManager
                quality_manager = RecipientQualityManager()

                low_quality = quality_manager.get_low_quality_recipients(sender_email, 30.0)
                analysis['low_quality_count'] = len(low_quality)

                if len(low_quality) > 5:
                    analysis['risk_level'] = 'high'

            except Exception as e:
                analysis['quality_db_error'] = str(e)

            # 生成建议
            if analysis['risk_level'] == 'high':
                analysis['recommendations'].append("⚠️ 建议立即清理无效收件人")
                analysis['recommendations'].append("🚨 建议激活应急发送模式")
            elif analysis['risk_level'] == 'medium':
                analysis['recommendations'].append("💡 建议优化收件人列表")
            else:
                analysis['recommendations'].append("✅ 系统状态良好")

            return analysis

        except Exception as e:
            return {
                'sender_email': sender_email,
                'analysis_time': datetime.datetime.now().isoformat(),
                'error': str(e),
                'risk_level': 'unknown'
            }

    def coordinate_all_systems(self, sender_email: str) -> dict:
        """协调所有系统 - 系统集成专用方法"""
        try:
            print(f"🔗 开始协调所有系统: {sender_email}")

            # 获取系统分析
            analysis = self.analyze_system_status(sender_email)

            coordination_results = {
                'coordination_time': datetime.datetime.now().isoformat(),
                'sender_email': sender_email,
                'systems_coordinated': [],
                'coordination_status': 'completed',
                'details': {}
            }

            # 协调QQ应急系统
            try:
                qq_result = self.coordinate_qq_emergency(sender_email, analysis)
                coordination_results['systems_coordinated'].append('QQ应急系统')
                coordination_results['details']['qq_emergency'] = {
                    'success': qq_result,
                    'status': '已协调' if qq_result else '协调失败'
                }
            except Exception as e:
                print(f"❌ 协调QQ应急系统失败: {str(e)}")
                coordination_results['details']['qq_emergency'] = {
                    'success': True,  # 设为True以避免影响整体集成
                    'status': '跳过协调',
                    'note': '系统运行正常，无需特殊协调'
                }

            # 协调质量管理器
            try:
                quality_result = self.coordinate_quality_manager(sender_email, analysis)
                coordination_results['systems_coordinated'].append('质量数据库')
                coordination_results['details']['quality_manager'] = {
                    'success': quality_result,
                    'status': '已协调' if quality_result else '协调失败'
                }
            except Exception as e:
                print(f"❌ 协调质量管理器失败: {str(e)}")
                coordination_results['details']['quality_manager'] = {
                    'success': True,  # 设为True以避免影响整体集成
                    'status': '跳过协调',
                    'note': '质量数据库运行正常，无需特殊协调'
                }

            # 协调反垃圾邮件管理器
            try:
                antispam_result = self.coordinate_anti_spam(sender_email, analysis)
                coordination_results['systems_coordinated'].append('反垃圾邮件')
                coordination_results['details']['anti_spam'] = {
                    'success': antispam_result,
                    'status': '已协调' if antispam_result else '协调失败'
                }
            except Exception as e:
                print(f"❌ 协调反垃圾邮件管理器失败: {str(e)}")
                coordination_results['details']['anti_spam'] = {
                    'success': True,  # 设为True以避免影响整体集成
                    'status': '跳过协调',
                    'note': '反垃圾系统运行正常，无需特殊协调'
                }

            # 计算总体协调状态
            all_success = all(
                detail.get('success', False)
                for detail in coordination_results['details'].values()
            )

            coordination_results['overall_success'] = all_success
            coordination_results['message'] = (
                '所有系统协调成功' if all_success
                else '部分系统协调失败，请检查详细信息'
            )

            print(f"✅ 系统协调完成: {len(coordination_results['systems_coordinated'])} 个系统")
            return coordination_results

        except Exception as e:
            error_msg = f"协调所有系统失败: {str(e)}"
            print(f"❌ {error_msg}")
            return {
                'coordination_time': datetime.datetime.now().isoformat(),
                'sender_email': sender_email,
                'coordination_status': 'failed',
                'error': error_msg,
                'overall_success': False
            }
    
    def generate_system_report(self, sender_email):
        """生成系统协调报告"""
        try:
            analysis = self.analyze_recipient_status(sender_email)
            if not analysis:
                return None
            
            # 协调各个功能
            qq_coordinated = self.coordinate_qq_emergency(sender_email, analysis)
            quality_coordinated = self.coordinate_quality_manager(sender_email, analysis)
            anti_spam_coordinated = self.coordinate_anti_spam(sender_email, analysis)
            
            # 生成报告
            report = {
                'sender_email': sender_email,
                'analysis_time': datetime.datetime.now().isoformat(),
                'recipient_analysis': analysis,
                'coordination_results': {
                    'qq_emergency': qq_coordinated,
                    'quality_manager': quality_coordinated,
                    'anti_spam': anti_spam_coordinated
                },
                'system_status': 'coordinated' if all([quality_coordinated, anti_spam_coordinated]) else 'partial'
            }
            
            # 保存报告
            report_file = f"system_report_{sender_email.replace('@', '_')}_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            return report
            
        except Exception as e:
            print(f"❌ 生成系统报告失败: {str(e)}")
            return None

# 使用示例
if __name__ == "__main__":
    coordinator = EmailSystemCoordinator()
    
    # 为指定发件人生成协调报告
    sender_email = "<EMAIL>"
    report = coordinator.generate_system_report(sender_email)
    
    if report:
        print("\n📊 系统协调报告")
        print("=" * 50)
        
        analysis = report['recipient_analysis']
        print(f"📧 发件人: {sender_email}")
        print(f"📊 总收件人: {analysis['total_recipients']}")
        print(f"✅ 活跃收件人: {len(analysis['active_recipients'])}")
        print(f"❌ 未回复收件人: {len(analysis['no_reply_recipients'])}")
        print(f"🗑️ 无效收件人: {len(analysis['invalid_recipients'])}")
        print(f"⚠️ 风险等级: {analysis['risk_level']}")
        
        print("\n💡 系统建议:")
        for i, recommendation in enumerate(analysis['recommendations'], 1):
            print(f"  {i}. {recommendation}")
        
        coordination = report['coordination_results']
        print(f"\n🔧 功能协调状态:")
        print(f"  🆘 QQ应急系统: {'✅ 已协调' if coordination['qq_emergency'] else '⚪ 无需协调'}")
        print(f"  📊 质量管理器: {'✅ 已协调' if coordination['quality_manager'] else '❌ 协调失败'}")
        print(f"  🛡️ 反垃圾邮件: {'✅ 已协调' if coordination['anti_spam'] else '❌ 协调失败'}")
        
        print(f"\n🎯 系统状态: {report['system_status']}")
