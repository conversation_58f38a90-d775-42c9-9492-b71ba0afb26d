# GUI长期记忆功能补丁
# 将以下代码添加到gui_main.py中

def _init_long_term_memory(self):
    """初始化长期记忆功能"""
    try:
        from 长期记忆功能增强 import LongTermMemoryManager
        self.memory_manager = LongTermMemoryManager()
        
        # 恢复用户设置
        self._restore_user_settings()
        
        # 恢复邮件内容
        self._restore_email_content()
        
        # 恢复发送配置
        self._restore_send_config()
        
        self.log_message("🧠 长期记忆功能已初始化")
        
    except Exception as e:
        self.log_message(f"❌ 长期记忆初始化失败: {str(e)}")

def _restore_user_settings(self):
    """恢复用户设置"""
    try:
        # 恢复发送模式
        send_mode = self.memory_manager.load_setting("ui", "send_mode", "standard")
        self.send_mode.set(send_mode)
        
        # 恢复个性化设置
        add_personalization = self.memory_manager.load_setting("ui", "add_personalization", False, "bool")
        self.add_personalization.set(add_personalization)
        
        # 恢复自动回复监控设置
        auto_reply_monitoring = self.memory_manager.load_setting("ui", "auto_reply_monitoring", True, "bool")
        self.auto_reply_monitoring.set(auto_reply_monitoring)
        
        # 恢复自动队列模式
        auto_queue_mode = self.memory_manager.load_setting("ui", "auto_queue_mode", True, "bool")
        self.auto_queue_mode.set(auto_queue_mode)
        
        self.log_message("✅ 用户设置已恢复")
        
    except Exception as e:
        self.log_message(f"❌ 恢复用户设置失败: {str(e)}")

def _restore_email_content(self):
    """恢复邮件内容"""
    try:
        # 恢复发件人邮箱
        sender_email = self.memory_manager.load_setting("email", "sender_email", "@qq.com")
        self.sender_email.delete(0, tk.END)
        self.sender_email.insert(0, sender_email)
        
        # 恢复收件人列表
        recipients = self.memory_manager.load_setting("email", "recipients", "")
        self.recipient_emails.delete(1.0, tk.END)
        self.recipient_emails.insert(1.0, recipients)
        
        # 恢复邮件主题
        subject = self.memory_manager.load_setting("email", "subject", "")
        self.subject.delete(0, tk.END)
        self.subject.insert(0, subject)
        
        # 恢复邮件正文
        body = self.memory_manager.load_setting("email", "body", "")
        self.body.delete(1.0, tk.END)
        self.body.insert(1.0, body)
        
        # 恢复附件列表
        attachments = self.memory_manager.load_setting("email", "attachments", [], "list")
        self.attachment_listbox.delete(0, tk.END)
        for attachment in attachments:
            self.attachment_listbox.insert(tk.END, attachment)
        
        self.log_message("✅ 邮件内容已恢复")
        
    except Exception as e:
        self.log_message(f"❌ 恢复邮件内容失败: {str(e)}")

def _restore_send_config(self):
    """恢复发送配置"""
    try:
        # 恢复邮件队列
        email_queue = self.memory_manager.load_setting("queue", "email_queue", [], "list")
        self.email_queue = email_queue
        self.update_queue_status()
        
        # 恢复授权码
        auth_codes = self.memory_manager.load_setting("auth", "auth_codes", {}, "dict")
        self.auth_codes = auth_codes
        
        self.log_message("✅ 发送配置已恢复")
        
    except Exception as e:
        self.log_message(f"❌ 恢复发送配置失败: {str(e)}")

def _save_user_settings(self):
    """保存用户设置"""
    try:
        if not hasattr(self, 'memory_manager'):
            return
        
        # 保存发送模式
        self.memory_manager.save_setting("ui", "send_mode", self.send_mode.get())
        
        # 保存个性化设置
        self.memory_manager.save_setting("ui", "add_personalization", self.add_personalization.get(), "bool")
        
        # 保存自动回复监控设置
        self.memory_manager.save_setting("ui", "auto_reply_monitoring", self.auto_reply_monitoring.get(), "bool")
        
        # 保存自动队列模式
        self.memory_manager.save_setting("ui", "auto_queue_mode", self.auto_queue_mode.get(), "bool")
        
    except Exception as e:
        self.log_message(f"❌ 保存用户设置失败: {str(e)}")

def _save_email_content(self):
    """保存邮件内容"""
    try:
        if not hasattr(self, 'memory_manager'):
            return
        
        # 保存发件人邮箱
        self.memory_manager.save_setting("email", "sender_email", self.sender_email.get())
        
        # 保存收件人列表
        self.memory_manager.save_setting("email", "recipients", self.recipient_emails.get(1.0, tk.END).strip())
        
        # 保存邮件主题
        self.memory_manager.save_setting("email", "subject", self.subject.get())
        
        # 保存邮件正文
        self.memory_manager.save_setting("email", "body", self.body.get(1.0, tk.END).strip())
        
        # 保存附件列表
        attachments = list(self.attachment_listbox.get(0, tk.END))
        self.memory_manager.save_setting("email", "attachments", attachments, "list")
        
    except Exception as e:
        self.log_message(f"❌ 保存邮件内容失败: {str(e)}")

def _save_send_config(self):
    """保存发送配置"""
    try:
        if not hasattr(self, 'memory_manager'):
            return
        
        # 保存邮件队列
        self.memory_manager.save_setting("queue", "email_queue", self.email_queue, "list")
        
        # 保存授权码
        self.memory_manager.save_setting("auth", "auth_codes", self.auth_codes, "dict")
        
    except Exception as e:
        self.log_message(f"❌ 保存发送配置失败: {str(e)}")

def _auto_save_data(self):
    """自动保存数据"""
    try:
        self._save_user_settings()
        self._save_email_content()
        self._save_send_config()
        
        # 每30秒自动保存一次
        self.root.after(30000, self._auto_save_data)
        
    except Exception as e:
        self.log_message(f"❌ 自动保存失败: {str(e)}")

# 在__init__方法末尾添加:
# self._init_long_term_memory()
# self._auto_save_data()

# 在程序退出时添加:
def on_closing(self):
    """程序退出时保存数据"""
    try:
        self._save_user_settings()
        self._save_email_content()
        self._save_send_config()
        self.log_message("💾 数据已保存")
    except Exception as e:
        self.log_message(f"❌ 退出保存失败: {str(e)}")
    finally:
        self.root.destroy()

# 绑定关闭事件:
# self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
