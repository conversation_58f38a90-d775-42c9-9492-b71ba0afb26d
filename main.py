# -*- coding: utf-8 -*-
"""
自动化邮件发送助手 - 主程序
"""

import os
import sys
from typing import List
from email_sender import EmailSender

def get_user_input():
    """获取用户输入"""
    print("=" * 50)
    print("    自动化邮件发送助手")
    print("=" * 50)
    
    # 获取发送者邮箱
    sender_email = input("请输入您的QQ邮箱地址: ").strip()
    if not sender_email.endswith('@qq.com'):
        print("警告: 请确保使用QQ邮箱地址")
    
    # 获取收件人邮箱
    print("\n请输入收件人邮箱地址（多个邮箱用逗号分隔）:")
    to_emails_str = input().strip()
    to_emails = [email.strip() for email in to_emails_str.split(',') if email.strip()]
    
    if not to_emails:
        print("错误: 请至少输入一个收件人邮箱地址")
        return None
    
    # 获取邮件主题
    subject = input("\n请输入邮件主题: ").strip()
    if not subject:
        subject = "来自自动化邮件助手的邮件"
    
    # 获取邮件正文
    print("\n请输入邮件正文（输入完成后按回车）:")
    body = input().strip()
    if not body:
        body = "这是一封通过自动化邮件助手发送的邮件。"
    
    # 获取附件文件
    print("\n请输入附件文件路径（多个文件用逗号分隔，不添加附件直接按回车）:")
    attachments_str = input().strip()
    attachments = []
    
    if attachments_str:
        attachment_paths = [path.strip() for path in attachments_str.split(',') if path.strip()]
        for path in attachment_paths:
            if os.path.exists(path):
                attachments.append(path)
                print(f"✓ 找到附件: {os.path.basename(path)}")
            else:
                print(f"✗ 附件文件不存在: {path}")
    
    return {
        'sender_email': sender_email,
        'to_emails': to_emails,
        'subject': subject,
        'body': body,
        'attachments': attachments if attachments else None
    }

def confirm_send(email_info):
    """确认发送信息"""
    print("\n" + "=" * 50)
    print("    邮件发送确认")
    print("=" * 50)
    print(f"发送者: {email_info['sender_email']}")
    print(f"收件人: {', '.join(email_info['to_emails'])}")
    print(f"主题: {email_info['subject']}")
    print(f"正文: {email_info['body'][:100]}{'...' if len(email_info['body']) > 100 else ''}")
    
    if email_info['attachments']:
        print("附件:")
        for attachment in email_info['attachments']:
            file_size = os.path.getsize(attachment) / 1024 / 1024  # MB
            print(f"  - {os.path.basename(attachment)} ({file_size:.2f} MB)")
    else:
        print("附件: 无")
    
    print("\n确认发送吗？(y/n): ", end="")
    return input().strip().lower() in ['y', 'yes', '是']

def main():
    """主函数"""
    try:
        # 获取用户输入
        email_info = get_user_input()
        if not email_info:
            return
        
        # 确认发送
        if not confirm_send(email_info):
            print("已取消发送")
            return
        
        # 创建邮件发送器
        sender = EmailSender(email_info['sender_email'])
        
        # 发送邮件
        print("\n正在发送邮件...")
        success = sender.send_email(
            email_info['to_emails'],
            email_info['subject'],
            email_info['body'],
            email_info['attachments']
        )
        
        if success:
            print("\n✓ 邮件发送成功！")
            print("\n提示:")
            print("- 如果收件人没有收到邮件，请检查垃圾邮件文件夹")
            print("- 建议收件人将您的邮箱地址添加到通讯录中")
        else:
            print("\n✗ 邮件发送失败，请查看日志文件 email_sender.log 获取详细信息")
    
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {str(e)}")

def batch_mode():
    """批量发送模式"""
    print("=" * 50)
    print("    批量邮件发送模式")
    print("=" * 50)
    
    sender_email = input("请输入您的QQ邮箱地址: ").strip()
    if not sender_email.endswith('@qq.com'):
        print("警告: 请确保使用QQ邮箱地址")
    
    sender = EmailSender(sender_email)
    email_list = []
    
    print("\n请输入邮件信息（输入 'done' 完成输入）:")
    
    count = 1
    while True:
        print(f"\n--- 第 {count} 封邮件 ---")
        
        to_emails_str = input("收件人邮箱（多个用逗号分隔）: ").strip()
        if to_emails_str.lower() == 'done':
            break
            
        to_emails = [email.strip() for email in to_emails_str.split(',') if email.strip()]
        if not to_emails:
            print("请输入有效的收件人邮箱")
            continue
            
        subject = input("邮件主题: ").strip()
        body = input("邮件正文: ").strip()
        
        attachments_str = input("附件路径（多个用逗号分隔，无附件直接回车）: ").strip()
        attachments = []
        if attachments_str:
            attachment_paths = [path.strip() for path in attachments_str.split(',') if path.strip()]
            for path in attachment_paths:
                if os.path.exists(path):
                    attachments.append(path)
        
        email_list.append({
            'to_emails': to_emails,
            'subject': subject or f"批量邮件 #{count}",
            'body': body or "这是一封批量发送的邮件。",
            'attachments': attachments if attachments else None
        })
        
        count += 1
    
    if not email_list:
        print("没有要发送的邮件")
        return
    
    print(f"\n准备发送 {len(email_list)} 封邮件，确认吗？(y/n): ", end="")
    if input().strip().lower() not in ['y', 'yes', '是']:
        print("已取消发送")
        return
    
    print("\n开始批量发送...")
    results = sender.send_multiple_emails(email_list)
    
    print(f"\n批量发送完成!")
    print(f"成功: {results['success']} 封")
    print(f"失败: {results['failed']} 封")
    
    if results['errors']:
        print("\n错误详情:")
        for error in results['errors']:
            print(f"  - {error}")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == '--batch':
        batch_mode()
    else:
        main()
