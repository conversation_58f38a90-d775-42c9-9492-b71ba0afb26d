# -*- coding: utf-8 -*-
"""
🚀 2.0系统优化布局版启动器
确保所有功能和按钮在界面上可见的优化版本

📋 优化内容：
• 三栏布局优化：35% + 32% + 33%
• 左侧：邮件配置 + 内容 + 日志（紧凑版）
• 中间：快速操作 + 队列管理 + 附件管理（确保可见）
• 右侧：高级功能（滚动支持）+ 系统状态
• 所有按钮和功能确保在界面上可见
• 紧凑的控件布局和间距优化
"""

import tkinter as tk
import sys
import os

def main():
    """启动2.0系统优化布局版"""
    print("=" * 60)
    print("🚀 启动2.0系统优化布局版")
    print("=" * 60)
    print("📋 优化特性：")
    print("   ✅ 三栏布局优化 (35% + 32% + 33%)")
    print("   ✅ 左侧：邮件配置 + 内容 + 日志（紧凑版）")
    print("   ✅ 中间：快速操作 + 队列管理 + 附件管理")
    print("   ✅ 右侧：高级功能（滚动支持）+ 系统状态")
    print("   ✅ 所有功能和按钮确保在界面上可见")
    print("   ✅ 紧凑的控件布局和间距优化")
    print("=" * 60)
    
    try:
        # 导入优化后的GUI
        from gui_main import EmailSenderGUI
        
        # 创建主窗口
        root = tk.Tk()
        
        # 设置窗口属性
        root.title("📧 智能邮件系统 v2.0 - 优化布局版")
        root.geometry("1600x1000")
        root.configure(bg='#f8fafc')
        
        # 窗口居中
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - 1600) // 2
        y = (screen_height - 1000) // 2
        root.geometry(f"1600x1000+{x}+{y}")
        
        # 设置最小尺寸
        root.minsize(1400, 900)
        
        # 创建应用实例
        app = EmailSenderGUI(root)
        
        print("✅ 2.0系统优化布局版启动成功！")
        print("🎯 布局说明：")
        print("   • 左侧35%：邮件配置、内容编辑、日志（紧凑版）")
        print("   • 中间32%：快速操作、队列管理、附件管理（确保可见）")
        print("   • 右侧33%：高级功能（滚动支持）、系统状态")
        print("   • 所有功能和按钮都在界面上可见")
        print("   • 优化的控件尺寸和间距")
        print("=" * 60)
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 gui_main.py 文件存在且可正常导入")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动错误: {e}")
        print("请检查系统环境和依赖")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
