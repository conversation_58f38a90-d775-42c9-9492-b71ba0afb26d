# -*- coding: utf-8 -*-
"""
测试修复后的邮件发送器
"""

from email_sender import EmailSender

def test_fixed_sender():
    """测试修复后的邮件发送器"""
    print("测试修复后的邮件发送器")
    print("=" * 50)
    
    sender_email = "<EMAIL>"
    
    # 测试邮箱列表
    test_emails = [
        "<EMAIL>",
        "<EMAIL>", 
        "<EMAIL>"  # 这个应该失败
    ]
    
    print(f"发送者: {sender_email}")
    print(f"测试邮箱: {test_emails}")
    
    confirm = input("\n确认开始测试吗？(y/n): ").strip().lower()
    if confirm != 'y':
        return
    
    # 创建邮件发送器
    sender = EmailSender(sender_email)
    
    # 逐个测试
    for i, recipient in enumerate(test_emails, 1):
        print(f"\n{'='*50}")
        print(f"测试 {i}/{len(test_emails)}: {recipient}")
        print(f"{'='*50}")
        
        try:
            success = sender.send_email(
                to_emails=[recipient],
                subject="修复后的测试邮件",
                body="这是修复后的邮件发送器发送的测试邮件。\n\n如果您收到这封邮件，说明修复成功！",
                attachments=None  # 先不带附件测试
            )
            
            if success:
                print(f"✅ 发送成功: {recipient}")
            else:
                print(f"❌ 发送失败: {recipient}")
                
        except Exception as e:
            print(f"❌ 发送异常: {recipient} - {str(e)}")
        
        if i < len(test_emails):
            print("\n等待3秒后发送下一封...")
            import time
            time.sleep(3)
    
    print(f"\n{'='*50}")
    print("测试完成！")
    print(f"{'='*50}")

def test_with_attachment():
    """测试带附件的发送"""
    print("\n" + "=" * 50)
    print("测试带附件的邮件发送")
    print("=" * 50)
    
    sender_email = "<EMAIL>"
    recipient = "<EMAIL>"  # 使用确认有效的邮箱
    
    import os
    attachment_file = "弹幕说他是反派，但他在救我狗命。.docx"
    
    if not os.path.exists(attachment_file):
        print(f"❌ 附件文件不存在: {attachment_file}")
        return
    
    print(f"发送者: {sender_email}")
    print(f"收件人: {recipient}")
    print(f"附件: {attachment_file}")
    
    confirm = input("\n确认发送带附件的测试邮件吗？(y/n): ").strip().lower()
    if confirm != 'y':
        return
    
    try:
        sender = EmailSender(sender_email)
        
        success = sender.send_email(
            to_emails=[recipient],
            subject="带附件的测试邮件",
            body="这是一封带附件的测试邮件。\n\n附件是一个Word文档。",
            attachments=[attachment_file]
        )
        
        if success:
            print("✅ 带附件的邮件发送成功！")
        else:
            print("❌ 带附件的邮件发送失败")
            
    except Exception as e:
        print(f"❌ 发送异常: {str(e)}")

def main():
    """主函数"""
    print("修复后的邮件发送器测试")
    print("=" * 50)
    
    while True:
        print("\n请选择测试类型:")
        print("1. 测试基本发送功能")
        print("2. 测试带附件发送")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            test_fixed_sender()
        elif choice == '2':
            test_with_attachment()
        elif choice == '3':
            print("测试结束")
            break
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n程序出错: {str(e)}")
    
    input("\n按回车键退出...")
