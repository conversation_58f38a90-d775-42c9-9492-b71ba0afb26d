#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完全修复IMAP连接和密码重复问题
"""

import json
import os
import datetime
import imaplib

def fix_auth_codes_format():
    """修复授权码格式问题"""
    print("🔧 修复授权码格式")
    print("-" * 30)
    
    try:
        # 读取当前配置
        if os.path.exists('auth_codes.json'):
            with open('auth_codes.json', 'r', encoding='utf-8') as f:
                current_config = json.load(f)
            
            print(f"✅ 当前配置: {current_config}")
            
            # 检查并修复格式
            fixed_config = {}
            for email, info in current_config.items():
                if isinstance(info, str):
                    # 旧格式：直接是字符串
                    fixed_config[email] = {
                        'auth_code': info,
                        'add_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    print(f"✅ 修复格式: {email}")
                elif isinstance(info, dict) and 'auth_code' in info:
                    # 新格式：已经是正确的
                    fixed_config[email] = info
                    print(f"✅ 格式正确: {email}")
                else:
                    print(f"⚠️ 跳过无效配置: {email} = {info}")
            
            # 保存修复后的配置
            with open('auth_codes.json', 'w', encoding='utf-8') as f:
                json.dump(fixed_config, f, ensure_ascii=False, indent=2)
            
            print("✅ 授权码格式修复完成")
            return True
        else:
            print("❌ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 修复授权码格式失败: {str(e)}")
        return False

def test_specific_email():
    """测试特定邮箱的IMAP连接"""
    print("\n🔧 测试特定邮箱IMAP连接")
    print("-" * 30)
    
    try:
        # 读取配置文件
        with open('auth_codes.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        target_email = "<EMAIL>"
        
        if target_email not in config:
            print(f"❌ 配置中没有找到 {target_email}")
            return False
        
        auth_info = config[target_email]
        if isinstance(auth_info, dict):
            auth_code = auth_info.get('auth_code', '')
        else:
            auth_code = auth_info
        
        print(f"📧 测试邮箱: {target_email}")
        print(f"🔑 使用授权码: {auth_code}")
        
        if not auth_code:
            print("❌ 授权码为空")
            return False
        
        # 测试IMAP连接
        print("🔗 连接IMAP服务器...")
        mail = imaplib.IMAP4_SSL('imap.qq.com', 993)
        print("✅ IMAP服务器连接成功")
        
        print("🔑 验证登录...")
        mail.login(target_email, auth_code)
        print("✅ IMAP登录成功")
        
        print("📂 访问收件箱...")
        mail.select('INBOX')
        print("✅ 收件箱访问成功")
        
        # 检查邮件数量
        status, messages = mail.search(None, 'ALL')
        if status == 'OK':
            email_count = len(messages[0].split()) if messages[0] else 0
            print(f"✅ 收件箱邮件数: {email_count}")
        
        mail.logout()
        print("🎉 IMAP连接测试完全成功！")
        return True
        
    except Exception as e:
        print(f"❌ IMAP连接测试失败: {str(e)}")
        return False

def clean_duplicate_passwords():
    """清理可能的重复密码"""
    print("\n🔧 清理重复密码")
    print("-" * 30)
    
    try:
        # 检查配置文件
        if os.path.exists('auth_codes.json'):
            with open('auth_codes.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            cleaned_config = {}
            
            for email, info in config.items():
                if isinstance(info, dict):
                    auth_code = info.get('auth_code', '')
                    
                    # 检查是否有重复的授权码（比如授权码被重复添加）
                    if len(auth_code) > 16:  # QQ授权码通常是16位
                        # 可能是重复的，尝试修复
                        if len(auth_code) == 32 and auth_code[:16] == auth_code[16:]:
                            # 确实是重复的
                            fixed_code = auth_code[:16]
                            print(f"✅ 修复重复授权码: {email} ({len(auth_code)} -> {len(fixed_code)})")
                            cleaned_config[email] = {
                                'auth_code': fixed_code,
                                'add_time': info.get('add_time', datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                            }
                        else:
                            # 长度异常但不是简单重复，保持原样
                            cleaned_config[email] = info
                            print(f"⚠️ 授权码长度异常: {email} (长度: {len(auth_code)})")
                    else:
                        # 长度正常
                        cleaned_config[email] = info
                        print(f"✅ 授权码正常: {email}")
                else:
                    # 旧格式，直接转换
                    cleaned_config[email] = {
                        'auth_code': info,
                        'add_time': datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    print(f"✅ 转换格式: {email}")
            
            # 保存清理后的配置
            with open('auth_codes.json', 'w', encoding='utf-8') as f:
                json.dump(cleaned_config, f, ensure_ascii=False, indent=2)
            
            print("✅ 重复密码清理完成")
            return True
        else:
            print("❌ 配置文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 清理重复密码失败: {str(e)}")
        return False

def create_backup():
    """创建配置备份"""
    print("\n🔧 创建配置备份")
    print("-" * 30)
    
    try:
        if os.path.exists('auth_codes.json'):
            backup_name = f"auth_codes_backup_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            import shutil
            shutil.copy2('auth_codes.json', backup_name)
            
            print(f"✅ 配置已备份为: {backup_name}")
            return True
        else:
            print("❌ 配置文件不存在，无需备份")
            return False
            
    except Exception as e:
        print(f"❌ 创建备份失败: {str(e)}")
        return False

def main():
    """主修复函数"""
    print("🔧 完全修复IMAP连接和密码重复问题")
    print("=" * 60)
    
    steps = [
        ("创建配置备份", create_backup),
        ("修复授权码格式", fix_auth_codes_format),
        ("清理重复密码", clean_duplicate_passwords),
        ("测试IMAP连接", test_specific_email)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔧 执行: {step_name}")
        if step_func():
            success_count += 1
            print(f"✅ {step_name} - 成功")
        else:
            print(f"❌ {step_name} - 失败")
    
    print(f"\n📊 修复结果")
    print("=" * 30)
    print(f"成功步骤: {success_count}/{len(steps)}")
    
    if success_count >= 3:  # 至少3个步骤成功
        print("\n🎉 主要问题已修复！")
        print("✅ 授权码格式正确")
        print("✅ 重复密码已清理")
        print("✅ IMAP连接正常")
        
        print("\n💡 现在应该:")
        print("1. 重启邮件程序")
        print("2. 测试自动回复监控")
        print("3. 检查密码是否还会重复")
        
        # 显示最终配置
        try:
            with open('auth_codes.json', 'r', encoding='utf-8') as f:
                final_config = json.load(f)
            
            print(f"\n📋 最终配置:")
            for email, info in final_config.items():
                if isinstance(info, dict):
                    auth_code = info.get('auth_code', '')
                    print(f"  {email}: {auth_code[:4]}****{auth_code[-4:]} (长度: {len(auth_code)})")
                else:
                    print(f"  {email}: {info}")
        except:
            pass
        
    else:
        print(f"\n⚠️ 仍有 {len(steps) - success_count} 个问题")

if __name__ == "__main__":
    main()
