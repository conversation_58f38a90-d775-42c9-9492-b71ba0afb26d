#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统集成器 - 错误处理版
将智能错误处理与应急恢复系统集成到2.0系统中
确保系统具备完善的错误处理、应急机制和恢复机制
"""

import os
import sys
import json
import time
import logging
import threading
from datetime import datetime
from pathlib import Path

# 导入错误处理系统
try:
    from 智能错误处理与应急恢复系统 import IntelligentErrorHandler
except ImportError:
    print("⚠️ 无法导入智能错误处理系统，将使用基础错误处理")
    IntelligentErrorHandler = None

class SystemIntegratorWithErrorHandling:
    """带错误处理的系统集成器"""
    
    def __init__(self):
        self.setup_logging()
        self.error_handler = None
        self.integration_status = {}
        self.monitoring_active = False
        self.setup_error_handling()
        
    def setup_logging(self):
        """设置日志"""
        os.makedirs('logs', exist_ok=True)
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/system_integrator.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_error_handling(self):
        """设置错误处理"""
        try:
            if IntelligentErrorHandler:
                self.error_handler = IntelligentErrorHandler()
                print("✅ 智能错误处理器已加载")
                self.logger.info("智能错误处理器已加载")
            else:
                print("⚠️ 使用基础错误处理")
                self.setup_basic_error_handling()
        except Exception as e:
            print(f"❌ 错误处理器设置失败: {str(e)}")
            self.setup_basic_error_handling()
    
    def setup_basic_error_handling(self):
        """设置基础错误处理"""
        def basic_exception_handler(exc_type, exc_value, exc_traceback):
            error_msg = f"系统错误: {exc_type.__name__}: {str(exc_value)}"
            print(f"🚨 {error_msg}")
            self.logger.error(error_msg)
            
            # 基础恢复尝试
            self.basic_recovery_attempt()
        
        sys.excepthook = basic_exception_handler
    
    def basic_recovery_attempt(self):
        """基础恢复尝试"""
        try:
            print("🔧 尝试基础恢复...")
            
            # 检查关键文件
            if not os.path.exists('gui_main.py'):
                print("❌ 关键文件缺失，无法恢复")
                return False
            
            # 检查依赖
            try:
                import jieba
                import psutil
            except ImportError as e:
                print(f"❌ 依赖缺失: {str(e)}")
                return False
            
            print("✅ 基础恢复检查通过")
            return True
            
        except Exception as e:
            print(f"❌ 基础恢复失败: {str(e)}")
            return False
    
    def integrate_error_handling_into_system(self):
        """将错误处理集成到系统中"""
        try:
            print("🔧 集成错误处理到2.0系统...")
            
            integration_steps = [
                ("检查系统状态", self.check_system_status),
                ("安装错误处理器", self.install_error_handler),
                ("集成监控机制", self.integrate_monitoring),
                ("设置应急机制", self.setup_emergency_mechanisms),
                ("配置恢复机制", self.configure_recovery_mechanisms),
                ("启动保护服务", self.start_protection_services),
                ("验证集成效果", self.verify_integration)
            ]
            
            for step_name, step_func in integration_steps:
                try:
                    print(f"  🔧 {step_name}...")
                    success = step_func()
                    
                    if success:
                        print(f"    ✅ {step_name}成功")
                        self.integration_status[step_name] = True
                    else:
                        print(f"    ❌ {step_name}失败")
                        self.integration_status[step_name] = False
                        
                except Exception as e:
                    print(f"    💥 {step_name}异常: {str(e)}")
                    self.integration_status[step_name] = False
                    self.logger.error(f"{step_name}异常: {str(e)}")
            
            # 评估集成结果
            success_count = sum(1 for v in self.integration_status.values() if v)
            total_count = len(self.integration_status)
            success_rate = success_count / total_count
            
            if success_rate >= 0.8:
                print(f"✅ 错误处理集成成功 ({success_count}/{total_count})")
                self.save_integration_status(True)
                return True
            else:
                print(f"⚠️ 错误处理集成部分成功 ({success_count}/{total_count})")
                self.save_integration_status(False)
                return False
                
        except Exception as e:
            print(f"❌ 错误处理集成失败: {str(e)}")
            self.logger.error(f"错误处理集成失败: {str(e)}")
            return False
    
    def check_system_status(self):
        """检查系统状态"""
        try:
            # 检查关键文件
            critical_files = [
                'gui_main.py', 'email_sender.py', 'email_history_manager.py'
            ]
            
            for file_path in critical_files:
                if not os.path.exists(file_path):
                    print(f"    ❌ 缺少关键文件: {file_path}")
                    return False
            
            # 检查Python环境
            if not sys.executable:
                print("    ❌ Python环境异常")
                return False
            
            # 检查依赖包
            required_packages = ['jieba', 'psutil']
            for package in required_packages:
                try:
                    __import__(package)
                except ImportError:
                    print(f"    ❌ 缺少依赖包: {package}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"系统状态检查失败: {str(e)}")
            return False
    
    def install_error_handler(self):
        """安装错误处理器"""
        try:
            if self.error_handler:
                # 启动持续监控
                self.error_handler.start_continuous_monitoring()
                
                # 创建错误处理器标志
                with open('temp/error_handler_installed.flag', 'w') as f:
                    f.write(datetime.now().isoformat())
                
                return True
            else:
                print("    ⚠️ 智能错误处理器不可用，使用基础处理")
                return True
                
        except Exception as e:
            self.logger.error(f"安装错误处理器失败: {str(e)}")
            return False
    
    def integrate_monitoring(self):
        """集成监控机制"""
        try:
            # 创建监控配置
            monitoring_config = {
                'enabled': True,
                'check_interval': 60,  # 秒
                'health_check_interval': 300,  # 5分钟
                'auto_repair': True,
                'emergency_mode': True,
                'monitoring_items': [
                    'file_integrity',
                    'dependency_status',
                    'database_health',
                    'system_resources',
                    'syntax_errors'
                ]
            }
            
            os.makedirs('user_data', exist_ok=True)
            with open('user_data/monitoring_config.json', 'w', encoding='utf-8') as f:
                json.dump(monitoring_config, f, ensure_ascii=False, indent=2)
            
            # 启动监控线程
            if not self.monitoring_active:
                self.monitoring_active = True
                monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
                monitor_thread.start()
            
            return True
            
        except Exception as e:
            self.logger.error(f"集成监控机制失败: {str(e)}")
            return False
    
    def setup_emergency_mechanisms(self):
        """设置应急机制"""
        try:
            # 创建应急响应配置
            emergency_config = {
                'auto_emergency_mode': True,
                'emergency_triggers': [
                    'critical_error',
                    'system_crash',
                    'file_corruption',
                    'dependency_failure'
                ],
                'emergency_actions': [
                    'save_state',
                    'create_backup',
                    'stop_non_critical_processes',
                    'free_resources',
                    'start_minimal_mode'
                ],
                'recovery_attempts': 3,
                'safe_mode_threshold': 5
            }
            
            with open('user_data/emergency_config.json', 'w', encoding='utf-8') as f:
                json.dump(emergency_config, f, ensure_ascii=False, indent=2)
            
            # 创建应急脚本
            self.create_emergency_scripts()
            
            return True
            
        except Exception as e:
            self.logger.error(f"设置应急机制失败: {str(e)}")
            return False
    
    def configure_recovery_mechanisms(self):
        """配置恢复机制"""
        try:
            # 创建恢复配置
            recovery_config = {
                'auto_recovery': True,
                'recovery_strategies': {
                    'syntax_error': 'auto_fix_syntax',
                    'import_error': 'install_dependencies',
                    'file_missing': 'restore_from_backup',
                    'database_error': 'rebuild_database',
                    'permission_error': 'fix_permissions'
                },
                'backup_retention': {
                    'daily_backups': 7,
                    'weekly_backups': 4,
                    'monthly_backups': 3
                },
                'recovery_verification': True
            }
            
            with open('user_data/recovery_config.json', 'w', encoding='utf-8') as f:
                json.dump(recovery_config, f, ensure_ascii=False, indent=2)
            
            # 创建恢复脚本
            self.create_recovery_scripts()
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置恢复机制失败: {str(e)}")
            return False
    
    def start_protection_services(self):
        """启动保护服务"""
        try:
            # 创建保护服务状态文件
            protection_status = {
                'error_handler': self.error_handler is not None,
                'monitoring': self.monitoring_active,
                'emergency_ready': os.path.exists('user_data/emergency_config.json'),
                'recovery_ready': os.path.exists('user_data/recovery_config.json'),
                'started_at': datetime.now().isoformat()
            }
            
            with open('temp/protection_status.json', 'w', encoding='utf-8') as f:
                json.dump(protection_status, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            self.logger.error(f"启动保护服务失败: {str(e)}")
            return False
    
    def verify_integration(self):
        """验证集成效果"""
        try:
            verification_results = {}
            
            # 验证错误处理器
            verification_results['error_handler'] = self.error_handler is not None
            
            # 验证监控
            verification_results['monitoring'] = self.monitoring_active
            
            # 验证配置文件
            config_files = [
                'user_data/monitoring_config.json',
                'user_data/emergency_config.json',
                'user_data/recovery_config.json'
            ]
            
            verification_results['config_files'] = all(
                os.path.exists(f) for f in config_files
            )
            
            # 验证保护服务
            verification_results['protection_services'] = os.path.exists(
                'temp/protection_status.json'
            )
            
            # 计算验证成功率
            success_count = sum(1 for v in verification_results.values() if v)
            total_count = len(verification_results)
            
            return success_count == total_count
            
        except Exception as e:
            self.logger.error(f"验证集成效果失败: {str(e)}")
            return False
    
    def create_emergency_scripts(self):
        """创建应急脚本"""
        try:
            os.makedirs('scripts', exist_ok=True)
            
            # 应急启动脚本
            emergency_script = '''@echo off
echo 🚨 应急模式启动
echo 正在执行应急恢复...
python "智能错误处理与应急恢复系统.py" --emergency-test
echo 应急模式完成
pause
'''
            
            with open('scripts/emergency_mode.bat', 'w', encoding='utf-8') as f:
                f.write(emergency_script)
            
            return True
            
        except Exception as e:
            self.logger.error(f"创建应急脚本失败: {str(e)}")
            return False
    
    def create_recovery_scripts(self):
        """创建恢复脚本"""
        try:
            # 系统恢复脚本
            recovery_script = '''@echo off
echo 🔄 系统恢复模式
echo 正在执行系统恢复...
python "系统稳定性终极保障方案.py" --repair
python "智能错误处理与应急恢复系统.py" --health-check
echo 系统恢复完成
pause
'''
            
            with open('scripts/system_recovery.bat', 'w', encoding='utf-8') as f:
                f.write(recovery_script)
            
            return True
            
        except Exception as e:
            self.logger.error(f"创建恢复脚本失败: {str(e)}")
            return False
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 执行基础监控
                self.basic_system_check()
                
                # 等待下次检查
                time.sleep(60)
                
            except Exception as e:
                self.logger.error(f"监控循环异常: {str(e)}")
                time.sleep(60)
    
    def basic_system_check(self):
        """基础系统检查"""
        try:
            # 检查关键文件
            critical_files = ['gui_main.py', 'email_sender.py']
            for file_path in critical_files:
                if not os.path.exists(file_path):
                    self.logger.warning(f"关键文件缺失: {file_path}")
            
            # 检查磁盘空间
            import psutil
            disk_usage = psutil.disk_usage('.')
            free_space_mb = disk_usage.free / (1024 * 1024)
            
            if free_space_mb < 100:
                self.logger.warning(f"磁盘空间不足: {free_space_mb:.1f}MB")
            
        except Exception as e:
            self.logger.error(f"基础系统检查失败: {str(e)}")
    
    def save_integration_status(self, success):
        """保存集成状态"""
        try:
            status = {
                'integration_successful': success,
                'integration_time': datetime.now().isoformat(),
                'integration_details': self.integration_status,
                'error_handler_available': self.error_handler is not None,
                'monitoring_active': self.monitoring_active
            }
            
            os.makedirs('logs', exist_ok=True)
            with open('logs/integration_status.json', 'w', encoding='utf-8') as f:
                json.dump(status, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存集成状态失败: {str(e)}")

def main():
    """主函数"""
    print("🔧 系统集成器 - 错误处理版")
    print("=" * 50)
    
    integrator = SystemIntegratorWithErrorHandling()
    
    # 执行集成
    success = integrator.integrate_error_handling_into_system()
    
    if success:
        print("\n🎉 错误处理系统集成成功！")
        print("✅ 2.0系统现在具备完善的错误处理、应急机制和恢复机制")
        print("🛡️ 系统稳定性和可靠性得到显著提升")
    else:
        print("\n⚠️ 错误处理系统集成部分成功")
        print("💡 建议检查日志文件了解详细信息")
    
    print("\n📊 集成状态:")
    for step, status in integrator.integration_status.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {step}")

if __name__ == "__main__":
    main()
