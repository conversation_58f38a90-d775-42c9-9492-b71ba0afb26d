#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试QQ应急恢复机制
验证收到自动回复后应急模式是否正确解除
"""

import datetime
import time

def test_emergency_recovery():
    """测试应急恢复机制"""
    print("🆘 测试QQ应急恢复机制")
    print("="*50)
    
    try:
        from qq_email_anti_spam import QQEmailAntiSpamManager
        
        # 创建QQ应急管理器
        qq_manager = QQEmailAntiSpamManager()
        test_email = "<EMAIL>"
        test_recipient = "<EMAIL>"
        
        print(f"📧 测试邮箱: {test_email}")
        print(f"📧 测试收件人: {test_recipient}")
        
        # 1. 检查当前应急状态
        print("\n🔍 步骤1: 检查当前应急状态")
        current_status = qq_manager.get_qq_emergency_status(test_email)
        
        if current_status and current_status.get('emergency_info', {}).get('is_active'):
            print("⚠️ 当前处于应急模式")
            print(f"   连续无回复: {current_status['emergency_info'].get('consecutive_no_reply', 0)} 封")
        else:
            print("✅ 当前未处于应急模式")
            
            # 如果不在应急模式，先模拟触发应急模式
            print("\n🔧 模拟触发应急模式...")
            for i in range(5):
                qq_manager.increment_consecutive_no_reply(test_email)
                print(f"   模拟第{i+1}封无回复邮件")
            
            print("🚨 应急模式已触发")
        
        # 2. 模拟收到自动回复
        print("\n📬 步骤2: 模拟收到自动回复")
        result = qq_manager.update_qq_reply_status(
            sender_email=test_email,
            recipient_email=test_recipient,
            has_reply=True,
            reply_type="自动回复"
        )
        
        if result:
            print("✅ 自动回复状态更新成功")
        else:
            print("❌ 自动回复状态更新失败")
        
        # 3. 检查应急恢复情况
        print("\n🔄 步骤3: 检查应急恢复情况")
        time.sleep(1)  # 等待一秒让系统处理
        
        updated_status = qq_manager.get_qq_emergency_status(test_email)
        
        if updated_status:
            emergency_info = updated_status.get('emergency_info', {})
            is_active = emergency_info.get('is_active', False)
            
            if is_active:
                print("⚠️ 应急模式仍然激活")
                print("💡 原因: 需要更多回复或等待更长时间")
                
                # 模拟再收到一封回复
                print("\n📬 模拟再收到一封自动回复...")
                qq_manager.update_qq_reply_status(
                    sender_email=test_email,
                    recipient_email="<EMAIL>",
                    has_reply=True,
                    reply_type="自动回复"
                )
                
                # 再次检查
                time.sleep(1)
                final_status = qq_manager.get_qq_emergency_status(test_email)
                final_emergency = final_status.get('emergency_info', {})
                
                if final_emergency.get('is_active', False):
                    print("⚠️ 应急模式仍然激活")
                    print("💡 可能需要手动解除或等待更长时间")
                else:
                    print("✅ 应急模式已自动解除！")
                    
            else:
                print("✅ 应急模式已自动解除！")
                
                # 显示恢复历史
                history = updated_status.get('emergency_history', [])
                if history:
                    latest = history[0]
                    if latest.get('recovery_time'):
                        print(f"🕐 恢复时间: {latest['recovery_time']}")
                        print(f"📝 恢复原因: 收到自动回复")
        
        # 4. 显示详细统计
        print("\n📊 步骤4: 显示详细统计")
        if updated_status:
            daily_stats = updated_status.get('daily_stats', {})
            print(f"今日发送: {daily_stats.get('total_sent', 0)} 封")
            print(f"收到回复: {daily_stats.get('total_replies', 0)} 封")
            print(f"回复率: {daily_stats.get('reply_rate', 0):.1f}%")
            
            # 显示应急历史
            history = updated_status.get('emergency_history', [])
            if history:
                print(f"\n📋 应急历史 (最近{len(history)}次):")
                for i, record in enumerate(history, 1):
                    trigger_time = record.get('trigger_time', '')[:19] if record.get('trigger_time') else '未知'
                    recovery_time = record.get('recovery_time', '')[:19] if record.get('recovery_time') else '未恢复'
                    success = '✅' if record.get('success') else '❌'
                    print(f"   {i}. 触发: {trigger_time} | 恢复: {recovery_time} {success}")
        
        print("\n" + "="*50)
        print("🎯 测试完成!")
        
        # 5. 给出使用建议
        print("\n💡 应急恢复机制说明:")
        print("• 触发条件: 连续5封邮件无回复")
        print("• 恢复条件 (满足任一即可):")
        print("  - 最近2封邮件都收到回复")
        print("  - 最近5封邮件中3封以上有回复")
        print("  - 应急模式持续30分钟后收到任何回复")
        print("• 建议: 收到自动回复后，应急模式会自动评估是否解除")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def manual_deactivate_emergency():
    """手动解除应急模式"""
    print("\n🔧 手动解除应急模式")
    print("-"*30)
    
    try:
        from qq_email_anti_spam import QQEmailAntiSpamManager
        
        qq_manager = QQEmailAntiSpamManager()
        test_email = "<EMAIL>"
        
        # 手动解除应急模式
        qq_manager._deactivate_emergency_mode(test_email, "手动解除 - 收到自动回复确认系统正常")
        
        print("✅ 应急模式已手动解除")
        
        # 重置连续无回复计数
        qq_manager.reset_consecutive_no_reply(test_email)
        
        print("✅ 连续无回复计数已重置")
        
        # 验证状态
        status = qq_manager.get_qq_emergency_status(test_email)
        if status:
            is_active = status.get('emergency_info', {}).get('is_active', False)
            if is_active:
                print("⚠️ 应急模式仍然激活，可能需要检查数据库")
            else:
                print("✅ 确认应急模式已解除")
        
    except Exception as e:
        print(f"❌ 手动解除失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 QQ应急恢复机制测试")
    print("="*60)
    
    # 运行自动测试
    test_emergency_recovery()
    
    # 询问是否需要手动解除
    print("\n" + "="*60)
    user_input = input("是否需要手动解除应急模式? (y/n): ").lower().strip()
    
    if user_input in ['y', 'yes', '是', '需要']:
        manual_deactivate_emergency()
    
    print("\n🎉 测试程序结束")
