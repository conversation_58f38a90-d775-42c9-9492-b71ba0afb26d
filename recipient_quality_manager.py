#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
收件人质量数据库管理器
用于建立和维护高质量的收件人数据库，支持批次管理和智能分析
"""

import sqlite3
import datetime
import logging
import json
import math
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass

@dataclass
class RecipientQuality:
    """收件人质量评分"""
    email: str
    quality_score: float  # 0-100分
    status: str  # excellent, good, fair, poor, invalid
    total_sent: int
    total_replies: int
    total_bounces: int
    last_reply_time: Optional[str]
    last_sent_time: Optional[str]
    response_rate: float
    bounce_rate: float
    engagement_score: float

class RecipientQualityManager:
    """收件人质量数据库管理器"""
    
    def __init__(self, db_path: str = "recipient_quality.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self._init_database()
        
    def _init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 收件人质量主表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS recipient_quality (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT NOT NULL,
                sender_email TEXT NOT NULL,
                quality_score REAL DEFAULT 50.0,
                status TEXT DEFAULT 'unknown',
                total_sent INTEGER DEFAULT 0,
                total_replies INTEGER DEFAULT 0,
                total_bounces INTEGER DEFAULT 0,
                total_opens INTEGER DEFAULT 0,
                total_clicks INTEGER DEFAULT 0,
                first_contact_date TEXT,
                last_reply_time TEXT,
                last_sent_time TEXT,
                last_bounce_time TEXT,
                response_rate REAL DEFAULT 0.0,
                bounce_rate REAL DEFAULT 0.0,
                engagement_score REAL DEFAULT 0.0,
                domain TEXT,
                tags TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(email, sender_email)
            )
        ''')
        
        # 发送历史表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS send_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                recipient_email TEXT NOT NULL,
                sender_email TEXT NOT NULL,
                subject TEXT,
                body_hash TEXT,
                send_time TEXT NOT NULL,
                campaign_id TEXT,
                batch_id TEXT,
                success INTEGER DEFAULT 1,
                bounce_reason TEXT,
                reply_received INTEGER DEFAULT 0,
                reply_time TEXT,
                reply_type TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (recipient_email) REFERENCES recipient_quality (email)
            )
        ''')
        
        # 批次管理表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS batch_management (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_name TEXT NOT NULL,
                batch_type TEXT DEFAULT 'quality_based',
                total_recipients INTEGER DEFAULT 0,
                quality_threshold REAL DEFAULT 60.0,
                max_batch_size INTEGER DEFAULT 100,
                created_by TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used_at TIMESTAMP,
                status TEXT DEFAULT 'active'
            )
        ''')
        
        # 批次收件人关联表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS batch_recipients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_id INTEGER NOT NULL,
                recipient_email TEXT NOT NULL,
                batch_position INTEGER,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (batch_id) REFERENCES batch_management (id),
                FOREIGN KEY (recipient_email) REFERENCES recipient_quality (email),
                UNIQUE(batch_id, recipient_email)
            )
        ''')
        
        # 营销效果分析表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS campaign_analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                campaign_id TEXT NOT NULL,
                campaign_name TEXT,
                total_sent INTEGER DEFAULT 0,
                total_delivered INTEGER DEFAULT 0,
                total_bounced INTEGER DEFAULT 0,
                total_replies INTEGER DEFAULT 0,
                total_opens INTEGER DEFAULT 0,
                total_clicks INTEGER DEFAULT 0,
                delivery_rate REAL DEFAULT 0.0,
                response_rate REAL DEFAULT 0.0,
                bounce_rate REAL DEFAULT 0.0,
                engagement_rate REAL DEFAULT 0.0,
                start_time TEXT,
                end_time TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引
        indexes = [
            'CREATE INDEX IF NOT EXISTS idx_recipient_email ON recipient_quality(email)',
            'CREATE INDEX IF NOT EXISTS idx_quality_score ON recipient_quality(quality_score)',
            'CREATE INDEX IF NOT EXISTS idx_status ON recipient_quality(status)',
            'CREATE INDEX IF NOT EXISTS idx_send_history_recipient ON send_history(recipient_email)',
            'CREATE INDEX IF NOT EXISTS idx_send_history_time ON send_history(send_time)',
            'CREATE INDEX IF NOT EXISTS idx_batch_recipients_batch ON batch_recipients(batch_id)',
            'CREATE INDEX IF NOT EXISTS idx_campaign_analytics_id ON campaign_analytics(campaign_id)'
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        conn.commit()
        conn.close()
        
        self.logger.info("收件人质量数据库初始化完成")
    
    def update_recipient_quality(self, email: str, sender_email: str, 
                               subject: str = "", body: str = "", 
                               success: bool = True, bounce_reason: str = "",
                               reply_received: bool = False, reply_type: str = "",
                               campaign_id: str = "", batch_id: str = ""):
        """更新收件人质量数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            current_time = datetime.datetime.now().isoformat()
            domain = email.split('@')[1] if '@' in email else ''
            body_hash = str(hash(body)) if body else ''
            
            # 1. 更新或创建收件人质量记录
            cursor.execute('''
                INSERT OR IGNORE INTO recipient_quality
                (email, sender_email, domain, first_contact_date, created_at)
                VALUES (?, ?, ?, ?, ?)
            ''', (email, sender_email, domain, current_time, current_time))
            
            # 2. 添加发送历史记录
            cursor.execute('''
                INSERT INTO send_history 
                (recipient_email, sender_email, subject, body_hash, send_time, 
                 campaign_id, batch_id, success, bounce_reason, reply_received, 
                 reply_time, reply_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (email, sender_email, subject, body_hash, current_time,
                  campaign_id, batch_id, 1 if success else 0, bounce_reason,
                  1 if reply_received else 0, 
                  current_time if reply_received else None, reply_type))
            
            # 3. 重新计算质量评分
            self._recalculate_quality_score(cursor, email, current_time)
            
            conn.commit()
            conn.close()
            
            self.logger.debug(f"更新收件人质量: {email} - 成功: {success}, 回复: {reply_received}")
            
        except Exception as e:
            self.logger.error(f"更新收件人质量失败: {str(e)}")
    
    def _recalculate_quality_score(self, cursor, email: str, current_time: str):
        """重新计算质量评分"""
        try:
            # 获取历史统计数据
            cursor.execute('''
                SELECT 
                    COUNT(*) as total_sent,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as delivered,
                    SUM(CASE WHEN success = 0 THEN 1 ELSE 0 END) as bounced,
                    SUM(CASE WHEN reply_received = 1 THEN 1 ELSE 0 END) as replied,
                    MAX(CASE WHEN reply_received = 1 THEN reply_time END) as last_reply,
                    MAX(send_time) as last_sent
                FROM send_history 
                WHERE recipient_email = ?
            ''', (email,))
            
            stats = cursor.fetchone()
            if not stats or stats[0] == 0:
                return
            
            total_sent, delivered, bounced, replied, last_reply, last_sent = stats
            
            # 计算各项指标
            delivery_rate = delivered / total_sent if total_sent > 0 else 0
            response_rate = replied / delivered if delivered > 0 else 0
            bounce_rate = bounced / total_sent if total_sent > 0 else 0
            
            # 计算时间衰减因子（最近的活动权重更高）
            time_factor = self._calculate_time_factor(last_reply, last_sent, current_time)
            
            # 计算质量评分 (0-100分)
            quality_score = self._calculate_quality_score(
                delivery_rate, response_rate, bounce_rate, time_factor, total_sent
            )
            
            # 确定状态
            status = self._determine_status(quality_score, bounce_rate, response_rate)
            
            # 计算参与度评分
            engagement_score = (response_rate * 0.7 + delivery_rate * 0.3) * time_factor * 100
            
            # 更新收件人质量记录
            cursor.execute('''
                UPDATE recipient_quality
                SET quality_score = ?, status = ?, total_sent = ?, total_replies = ?,
                    total_bounces = ?, response_rate = ?, bounce_rate = ?,
                    engagement_score = ?, last_reply_time = ?, last_sent_time = ?,
                    updated_at = ?
                WHERE email = ? AND sender_email = (
                    SELECT sender_email FROM send_history
                    WHERE recipient_email = ?
                    ORDER BY send_time DESC LIMIT 1
                )
            ''', (quality_score, status, total_sent, replied, bounced,
                  response_rate, bounce_rate, engagement_score, last_reply,
                  last_sent, current_time, email, email))
            
        except Exception as e:
            self.logger.error(f"重新计算质量评分失败: {str(e)}")
    
    def _calculate_time_factor(self, last_reply: str, last_sent: str, current_time: str) -> float:
        """计算时间衰减因子"""
        try:
            current = datetime.datetime.fromisoformat(current_time)
            
            # 使用最近的活动时间
            last_activity = last_reply if last_reply else last_sent
            if not last_activity:
                return 0.5  # 默认值
            
            last_activity_dt = datetime.datetime.fromisoformat(last_activity)
            days_ago = (current - last_activity_dt).days
            
            # 时间衰减函数：最近30天内权重最高，之后逐渐衰减
            if days_ago <= 30:
                return 1.0
            elif days_ago <= 90:
                return 0.8
            elif days_ago <= 180:
                return 0.6
            elif days_ago <= 365:
                return 0.4
            else:
                return 0.2
                
        except Exception:
            return 0.5
    
    def _calculate_quality_score(self, delivery_rate: float, response_rate: float, 
                                bounce_rate: float, time_factor: float, total_sent: int) -> float:
        """计算综合质量评分"""
        # 基础分数权重
        delivery_weight = 0.3
        response_weight = 0.4
        bounce_penalty_weight = 0.2
        volume_bonus_weight = 0.1
        
        # 计算各项得分
        delivery_score = delivery_rate * 100 * delivery_weight
        response_score = response_rate * 100 * response_weight
        bounce_penalty = bounce_rate * 100 * bounce_penalty_weight
        
        # 发送量奖励（鼓励有足够样本量的评估）
        volume_bonus = min(total_sent / 10, 1.0) * 10 * volume_bonus_weight
        
        # 综合评分
        base_score = delivery_score + response_score - bounce_penalty + volume_bonus
        
        # 应用时间衰减
        final_score = base_score * time_factor
        
        # 确保分数在0-100范围内
        return max(0, min(100, final_score))
    
    def _determine_status(self, quality_score: float, bounce_rate: float, response_rate: float) -> str:
        """确定收件人状态"""
        if bounce_rate >= 0.5:  # 50%以上退信率
            return 'invalid'
        elif quality_score >= 80:
            return 'excellent'
        elif quality_score >= 60:
            return 'good'
        elif quality_score >= 40:
            return 'fair'
        elif quality_score >= 20:
            return 'poor'
        else:
            return 'invalid'
    
    def get_quality_recipients(self, min_quality_score: float = 60.0,
                             status_filter: List[str] = None,
                             limit: int = None,
                             max_quality_score: float = None) -> List[RecipientQuality]:
        """获取高质量收件人列表

        Args:
            min_quality_score: 最低质量分数 (默认60.0)
            status_filter: 状态筛选列表
            limit: 限制返回数量
            max_quality_score: 最高质量分数 (兼容性参数，如果提供则忽略min_quality_score)

        Returns:
            符合条件的收件人列表
        """
        try:
            # 兼容性处理：如果传入了max_quality_score，说明调用者想要低质量收件人
            if max_quality_score is not None:
                self.logger.warning(f"检测到错误调用：get_quality_recipients收到max_quality_score参数，"
                                  f"自动转换为get_low_quality_recipients调用")
                return self.get_low_quality_recipients(
                    sender_email=None,  # 这里需要调用者提供sender_email，但为了兼容性暂时设为None
                    max_quality_score=max_quality_score
                )

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 构建查询条件
            where_conditions = ['quality_score >= ?']
            params = [min_quality_score]

            if status_filter:
                placeholders = ','.join(['?' for _ in status_filter])
                where_conditions.append(f'status IN ({placeholders})')
                params.extend(status_filter)

            where_clause = ' AND '.join(where_conditions)

            # 构建查询
            query = f'''
                SELECT email, quality_score, status, total_sent, total_replies,
                       total_bounces, last_reply_time, last_sent_time,
                       response_rate, bounce_rate, engagement_score
                FROM recipient_quality
                WHERE {where_clause}
                ORDER BY quality_score DESC, engagement_score DESC
            '''

            if limit:
                query += f' LIMIT {limit}'

            cursor.execute(query, params)
            results = cursor.fetchall()

            recipients = []
            for row in results:
                recipients.append(RecipientQuality(
                    email=row[0],
                    quality_score=row[1],
                    status=row[2],
                    total_sent=row[3],
                    total_replies=row[4],
                    total_bounces=row[5],
                    last_reply_time=row[6],
                    last_sent_time=row[7],
                    response_rate=row[8],
                    bounce_rate=row[9],
                    engagement_score=row[10]
                ))

            conn.close()
            return recipients

        except Exception as e:
            self.logger.error(f"获取高质量收件人失败: {str(e)}")
            return []

    def create_smart_batches(self, batch_name: str, total_recipients: int = None,
                           quality_threshold: float = 60.0, max_batch_size: int = 100,
                           strategy: str = 'quality_balanced') -> Dict:
        """创建智能批次"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取符合条件的收件人
            recipients = self.get_quality_recipients(
                min_quality_score=quality_threshold,
                status_filter=['excellent', 'good', 'fair'],
                limit=total_recipients
            )

            if not recipients:
                self.logger.warning("没有找到符合条件的收件人")
                return {
                    'success': False,
                    'error': '没有找到符合条件的收件人',
                    'batch_count': 0,
                    'total_recipients': 0
                }

            # 根据策略创建批次
            batches = self._create_batches_by_strategy(recipients, max_batch_size, strategy)

            # 保存批次到数据库
            batch_ids = []
            for i, batch in enumerate(batches):
                # 创建批次记录
                cursor.execute('''
                    INSERT INTO batch_management
                    (batch_name, batch_type, total_recipients, quality_threshold,
                     max_batch_size, created_at, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (f"{batch_name}_批次{i+1}", strategy, len(batch),
                      quality_threshold, max_batch_size,
                      datetime.datetime.now().isoformat(), 'active'))

                batch_id = cursor.lastrowid
                batch_ids.append(batch_id)

                # 添加批次收件人
                for pos, recipient in enumerate(batch):
                    cursor.execute('''
                        INSERT INTO batch_recipients
                        (batch_id, recipient_email, batch_position)
                        VALUES (?, ?, ?)
                    ''', (batch_id, recipient.email, pos + 1))

            conn.commit()
            conn.close()

            self.logger.info(f"创建了 {len(batches)} 个智能批次，总计 {len(recipients)} 个收件人")

            # 返回批次信息
            batch_info = []
            for i, (batch_id, batch) in enumerate(zip(batch_ids, batches)):
                avg_quality = sum(r.quality_score for r in batch) / len(batch)
                batch_info.append({
                    'batch_id': batch_id,
                    'batch_name': f"{batch_name}_批次{i+1}",
                    'recipient_count': len(batch),
                    'avg_quality_score': round(avg_quality, 2),
                    'recipients': [r.email for r in batch]
                })

            return {
                'success': True,
                'batch_count': len(batches),
                'total_recipients': len(recipients),
                'batches': batch_info
            }

        except Exception as e:
            self.logger.error(f"创建智能批次失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'batch_count': 0,
                'total_recipients': 0
            }

    def _create_batches_by_strategy(self, recipients: List[RecipientQuality],
                                  max_batch_size: int, strategy: str) -> List[List[RecipientQuality]]:
        """根据策略创建批次"""
        if strategy == 'quality_balanced':
            return self._create_quality_balanced_batches(recipients, max_batch_size)
        elif strategy == 'quality_descending':
            return self._create_quality_descending_batches(recipients, max_batch_size)
        elif strategy == 'domain_distributed':
            return self._create_domain_distributed_batches(recipients, max_batch_size)
        else:
            return self._create_simple_batches(recipients, max_batch_size)

    def _create_quality_balanced_batches(self, recipients: List[RecipientQuality],
                                       max_batch_size: int) -> List[List[RecipientQuality]]:
        """创建质量平衡的批次（每个批次都有高中低质量的收件人）"""
        # 按质量分组
        excellent = [r for r in recipients if r.quality_score >= 80]
        good = [r for r in recipients if 60 <= r.quality_score < 80]
        fair = [r for r in recipients if 40 <= r.quality_score < 60]

        total_batches = math.ceil(len(recipients) / max_batch_size)
        batches = [[] for _ in range(total_batches)]

        # 平均分配各质量等级的收件人
        for group in [excellent, good, fair]:
            for i, recipient in enumerate(group):
                batch_index = i % total_batches
                if len(batches[batch_index]) < max_batch_size:
                    batches[batch_index].append(recipient)

        return [batch for batch in batches if batch]

    def _create_quality_descending_batches(self, recipients: List[RecipientQuality],
                                         max_batch_size: int) -> List[List[RecipientQuality]]:
        """创建质量递减的批次（优先发送给高质量收件人）"""
        # 按质量评分排序
        sorted_recipients = sorted(recipients, key=lambda r: r.quality_score, reverse=True)
        return self._create_simple_batches(sorted_recipients, max_batch_size)

    def _create_domain_distributed_batches(self, recipients: List[RecipientQuality],
                                         max_batch_size: int) -> List[List[RecipientQuality]]:
        """创建域名分布的批次（避免同一批次中同一域名过多）"""
        from collections import defaultdict

        # 按域名分组
        domain_groups = defaultdict(list)
        for recipient in recipients:
            domain = recipient.email.split('@')[1] if '@' in recipient.email else 'unknown'
            domain_groups[domain].append(recipient)

        total_batches = math.ceil(len(recipients) / max_batch_size)
        batches = [[] for _ in range(total_batches)]

        # 轮流分配各域名的收件人
        for domain, domain_recipients in domain_groups.items():
            for i, recipient in enumerate(domain_recipients):
                batch_index = i % total_batches
                if len(batches[batch_index]) < max_batch_size:
                    batches[batch_index].append(recipient)

        return [batch for batch in batches if batch]

    def _create_simple_batches(self, recipients: List[RecipientQuality],
                             max_batch_size: int) -> List[List[RecipientQuality]]:
        """创建简单批次（按顺序分组）"""
        batches = []
        for i in range(0, len(recipients), max_batch_size):
            batch = recipients[i:i + max_batch_size]
            batches.append(batch)
        return batches

    def get_batch_recipients(self, batch_id: int) -> List[str]:
        """获取批次中的收件人列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT recipient_email
                FROM batch_recipients
                WHERE batch_id = ?
                ORDER BY batch_position
            ''', (batch_id,))

            recipients = [row[0] for row in cursor.fetchall()]
            conn.close()

            return recipients

        except Exception as e:
            self.logger.error(f"获取批次收件人失败: {str(e)}")
            return []

    def get_all_batches(self) -> List[Dict]:
        """获取所有批次信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT bm.id, bm.batch_name, bm.batch_type, bm.total_recipients,
                       bm.quality_threshold, bm.created_at, bm.status,
                       COUNT(br.recipient_email) as actual_count
                FROM batch_management bm
                LEFT JOIN batch_recipients br ON bm.id = br.batch_id
                WHERE bm.status = 'active'
                GROUP BY bm.id
                ORDER BY bm.created_at DESC
            ''', ())

            batches = []
            for row in cursor.fetchall():
                batches.append({
                    'batch_id': row[0],
                    'batch_name': row[1],
                    'batch_type': row[2],
                    'total_recipients': row[3],
                    'actual_count': row[7],
                    'quality_threshold': row[4],
                    'created_at': row[5],
                    'status': row[6]
                })

            conn.close()
            return batches

        except Exception as e:
            self.logger.error(f"获取批次列表失败: {str(e)}")
            return []

    def delete_batch(self, batch_id: int) -> bool:
        """删除指定批次"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 删除批次收件人关联
            cursor.execute('DELETE FROM batch_recipients WHERE batch_id = ?', (batch_id,))

            # 删除批次记录
            cursor.execute('DELETE FROM batch_management WHERE id = ?', (batch_id,))

            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()

            if deleted_count > 0:
                self.logger.info(f"成功删除批次: {batch_id}")
                return True
            else:
                self.logger.warning(f"批次不存在: {batch_id}")
                return False

        except Exception as e:
            self.logger.error(f"删除批次失败: {str(e)}")
            return False

    def delete_batches(self, batch_ids: List[int]) -> int:
        """批量删除批次"""
        try:
            if not batch_ids:
                return 0

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            deleted_count = 0

            for batch_id in batch_ids:
                try:
                    # 删除批次收件人关联
                    cursor.execute('DELETE FROM batch_recipients WHERE batch_id = ?', (batch_id,))

                    # 删除批次记录
                    cursor.execute('DELETE FROM batch_management WHERE id = ?', (batch_id,))

                    if cursor.rowcount > 0:
                        deleted_count += 1

                except Exception as e:
                    self.logger.error(f"删除批次 {batch_id} 失败: {str(e)}")
                    continue

            conn.commit()
            conn.close()

            self.logger.info(f"批量删除批次完成，成功删除 {deleted_count} 个批次")
            return deleted_count

        except Exception as e:
            self.logger.error(f"批量删除批次失败: {str(e)}")
            return 0

    def delete_all_batches(self) -> bool:
        """删除所有批次"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 删除所有批次收件人关联
            cursor.execute('DELETE FROM batch_recipients')

            # 删除所有批次记录
            cursor.execute('DELETE FROM batch_management')

            conn.commit()
            conn.close()

            self.logger.info("成功删除所有批次")
            return True

        except Exception as e:
            self.logger.error(f"删除所有批次失败: {str(e)}")
            return False

    def get_low_quality_recipients(self, sender_email: str = None, max_quality_score: float = 30.0) -> List[RecipientQuality]:
        """获取低质量收件人列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if sender_email:
                cursor.execute('''
                    SELECT email, quality_score, status, total_sent, total_replies,
                           total_bounces, last_reply_time, last_sent_time, response_rate, bounce_rate, engagement_score
                    FROM recipient_quality
                    WHERE sender_email = ? AND quality_score <= ?
                    ORDER BY quality_score ASC
                ''', (sender_email, max_quality_score))
            else:
                cursor.execute('''
                    SELECT email, quality_score, status, total_sent, total_replies,
                           total_bounces, last_reply_time, last_sent_time, response_rate, bounce_rate, engagement_score
                    FROM recipient_quality
                    WHERE quality_score <= ?
                    ORDER BY quality_score ASC
                ''', (max_quality_score,))

            results = cursor.fetchall()
            conn.close()

            recipients = []
            for row in results:
                recipient = RecipientQuality(
                    email=row[0],
                    quality_score=row[1],
                    status=row[2],
                    total_sent=row[3],
                    total_replies=row[4],
                    total_bounces=row[5],
                    last_reply_time=row[6],
                    last_sent_time=row[7],
                    response_rate=row[8],
                    bounce_rate=row[9],
                    engagement_score=row[10]
                )
                recipients.append(recipient)

            self.logger.info(f"获取到 {len(recipients)} 个低质量收件人（评分≤{max_quality_score}）")
            return recipients

        except Exception as e:
            self.logger.error(f"获取低质量收件人失败: {str(e)}")
            return []

    def get_high_quality_recipients(self, sender_email: str = None, min_score: float = 70.0) -> List[RecipientQuality]:
        """获取高质量收件人列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if sender_email:
                cursor.execute('''
                    SELECT email, quality_score, status, total_sent, total_replies,
                           total_bounces, last_reply_time, last_sent_time, response_rate, bounce_rate, engagement_score
                    FROM recipient_quality
                    WHERE sender_email = ? AND quality_score >= ?
                    ORDER BY quality_score DESC
                ''', (sender_email, min_score))
            else:
                cursor.execute('''
                    SELECT email, quality_score, status, total_sent, total_replies,
                           total_bounces, last_reply_time, last_sent_time, response_rate, bounce_rate, engagement_score
                    FROM recipient_quality
                    WHERE quality_score >= ?
                    ORDER BY quality_score DESC
                ''', (min_score,))

            results = cursor.fetchall()
            conn.close()

            recipients = []
            for row in results:
                recipient = RecipientQuality(
                    email=row[0],
                    quality_score=row[1],
                    status=row[2],
                    total_sent=row[3],
                    total_replies=row[4],
                    total_bounces=row[5],
                    last_reply_time=row[6],
                    last_sent_time=row[7],
                    response_rate=row[8],
                    bounce_rate=row[9],
                    engagement_score=row[10]
                )
                recipients.append(recipient)

            self.logger.info(f"获取到 {len(recipients)} 个高质量收件人（评分≥{min_score}）")
            return recipients

        except Exception as e:
            self.logger.error(f"获取高质量收件人失败: {str(e)}")
            return []

    def import_recipients_from_history(self, sender_email: str = None,
                                     days_back: int = 30) -> int:
        """从历史记录导入收件人到质量数据库"""
        try:
            from email_history_manager import EmailHistoryManager
            from email_receiver import EmailReceiver

            # 获取历史邮件记录
            history_manager = EmailHistoryManager()

            conn = sqlite3.connect(history_manager.db_path)
            cursor = conn.cursor()

            # 构建查询条件
            where_conditions = []
            params = []

            if sender_email:
                where_conditions.append("sender_email = ?")
                params.append(sender_email)

            if days_back:
                cutoff_date = (datetime.datetime.now() - datetime.timedelta(days=days_back)).isoformat()
                where_conditions.append("send_time >= ?")
                params.append(cutoff_date)

            where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

            cursor.execute(f'''
                SELECT sender_email, recipient_email, subject, body, send_time, success
                FROM email_records
                WHERE {where_clause}
                ORDER BY send_time DESC
            ''', params)

            records = cursor.fetchall()
            conn.close()

            # 获取自动回复数据
            receiver = EmailReceiver("dummy", "dummy")

            imported_count = 0
            for record in records:
                sender, recipient, subject, body, send_time, success = record

                # 检查是否有自动回复
                reply_received = False
                reply_type = ""

                try:
                    reply_conn = sqlite3.connect(receiver.db_path)
                    reply_cursor = reply_conn.cursor()

                    reply_cursor.execute('''
                        SELECT reply_type FROM auto_replies
                        WHERE recipient_email = ? AND sender_email = ?
                        ORDER BY reply_time DESC LIMIT 1
                    ''', (recipient, sender))

                    reply_result = reply_cursor.fetchone()
                    if reply_result:
                        reply_received = True
                        reply_type = reply_result[0]

                    reply_conn.close()

                except Exception:
                    pass

                # 更新质量数据库
                self.update_recipient_quality(
                    email=recipient,
                    sender_email=sender,
                    subject=subject,
                    body=body,
                    success=bool(success),
                    reply_received=reply_received,
                    reply_type=reply_type
                )

                imported_count += 1

            self.logger.info(f"从历史记录导入了 {imported_count} 条收件人数据")
            return imported_count

        except Exception as e:
            self.logger.error(f"导入历史收件人失败: {str(e)}")
            return 0

    def get_quality_analytics(self) -> Dict:
        """获取质量分析报告"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 总体统计
            cursor.execute('''
                SELECT
                    COUNT(*) as total_recipients,
                    AVG(quality_score) as avg_quality,
                    COUNT(CASE WHEN status = 'excellent' THEN 1 END) as excellent_count,
                    COUNT(CASE WHEN status = 'good' THEN 1 END) as good_count,
                    COUNT(CASE WHEN status = 'fair' THEN 1 END) as fair_count,
                    COUNT(CASE WHEN status = 'poor' THEN 1 END) as poor_count,
                    COUNT(CASE WHEN status = 'invalid' THEN 1 END) as invalid_count,
                    AVG(response_rate) as avg_response_rate,
                    AVG(bounce_rate) as avg_bounce_rate,
                    SUM(total_sent) as total_emails_sent,
                    SUM(total_replies) as total_replies_received
                FROM recipient_quality
            ''')

            stats = cursor.fetchone()

            # 域名分析
            cursor.execute('''
                SELECT domain, COUNT(*) as count, AVG(quality_score) as avg_quality
                FROM recipient_quality
                WHERE domain IS NOT NULL
                GROUP BY domain
                ORDER BY count DESC
                LIMIT 10
            ''')

            domain_stats = cursor.fetchall()

            # 时间趋势分析（最近30天）
            cursor.execute('''
                SELECT
                    DATE(send_time) as send_date,
                    COUNT(*) as emails_sent,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as delivered,
                    SUM(CASE WHEN reply_received = 1 THEN 1 ELSE 0 END) as replied
                FROM send_history
                WHERE send_time >= date('now', '-30 days')
                GROUP BY DATE(send_time)
                ORDER BY send_date DESC
            ''')

            trend_data = cursor.fetchall()

            conn.close()

            # 构建分析报告
            analytics = {
                'overview': {
                    'total_recipients': stats[0] or 0,
                    'avg_quality_score': round(stats[1] or 0, 2),
                    'avg_response_rate': round((stats[7] or 0) * 100, 2),
                    'avg_bounce_rate': round((stats[8] or 0) * 100, 2),
                    'total_emails_sent': stats[9] or 0,
                    'total_replies_received': stats[10] or 0
                },
                'quality_distribution': {
                    'excellent': stats[2] or 0,
                    'good': stats[3] or 0,
                    'fair': stats[4] or 0,
                    'poor': stats[5] or 0,
                    'invalid': stats[6] or 0
                },
                'top_domains': [
                    {
                        'domain': row[0],
                        'count': row[1],
                        'avg_quality': round(row[2], 2)
                    } for row in domain_stats
                ],
                'recent_trends': [
                    {
                        'date': row[0],
                        'sent': row[1],
                        'delivered': row[2],
                        'replied': row[3],
                        'delivery_rate': round((row[2] / row[1] * 100) if row[1] > 0 else 0, 2),
                        'response_rate': round((row[3] / row[2] * 100) if row[2] > 0 else 0, 2)
                    } for row in trend_data
                ]
            }

            return analytics

        except Exception as e:
            self.logger.error(f"获取质量分析失败: {str(e)}")
            return {}

    def get_recommendations(self) -> List[Dict]:
        """获取优化建议"""
        try:
            analytics = self.get_quality_analytics()
            recommendations = []

            overview = analytics.get('overview', {})
            distribution = analytics.get('quality_distribution', {})

            total_recipients = overview.get('total_recipients', 0)
            avg_quality = overview.get('avg_quality_score', 0)
            avg_response_rate = overview.get('avg_response_rate', 0)
            invalid_count = distribution.get('invalid', 0)

            # 建议1：清理无效收件人
            if invalid_count > 0:
                invalid_percentage = (invalid_count / total_recipients * 100) if total_recipients > 0 else 0
                recommendations.append({
                    'type': 'cleanup',
                    'priority': 'high' if invalid_percentage > 10 else 'medium',
                    'title': '清理无效收件人',
                    'description': f'发现 {invalid_count} 个无效收件人（{invalid_percentage:.1f}%），建议从发送列表中移除',
                    'action': 'remove_invalid_recipients',
                    'impact': f'可提高送达率约 {invalid_percentage:.1f}%'
                })

            # 建议2：提升整体质量
            if avg_quality < 60:
                recommendations.append({
                    'type': 'quality_improvement',
                    'priority': 'high',
                    'title': '提升收件人质量',
                    'description': f'平均质量评分为 {avg_quality:.1f}，低于推荐标准（60分）',
                    'action': 'focus_on_quality_recipients',
                    'impact': '专注于高质量收件人可提高整体营销效果'
                })

            # 建议3：改善回复率
            if avg_response_rate < 5:
                recommendations.append({
                    'type': 'engagement',
                    'priority': 'medium',
                    'title': '改善邮件内容',
                    'description': f'平均回复率为 {avg_response_rate:.1f}%，建议优化邮件内容和发送时间',
                    'action': 'improve_content_strategy',
                    'impact': '优化内容可显著提高用户参与度'
                })

            # 建议4：批次优化
            excellent_count = distribution.get('excellent', 0)
            good_count = distribution.get('good', 0)

            if excellent_count + good_count > 50:
                recommendations.append({
                    'type': 'batch_optimization',
                    'priority': 'medium',
                    'title': '创建高质量批次',
                    'description': f'您有 {excellent_count + good_count} 个高质量收件人，建议创建专门的批次',
                    'action': 'create_quality_batches',
                    'impact': '分批发送可提高管理效率和发送效果'
                })

            return recommendations

        except Exception as e:
            self.logger.error(f"获取优化建议失败: {str(e)}")
            return []

    def export_quality_report(self, filepath: str):
        """导出质量报告"""
        try:
            analytics = self.get_quality_analytics()
            recommendations = self.get_recommendations()

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("📊 收件人质量分析报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                # 概览
                overview = analytics.get('overview', {})
                f.write("📈 总体概览\n")
                f.write("-" * 30 + "\n")
                f.write(f"总收件人数: {overview.get('total_recipients', 0)}\n")
                f.write(f"平均质量评分: {overview.get('avg_quality_score', 0):.2f}/100\n")
                f.write(f"平均回复率: {overview.get('avg_response_rate', 0):.2f}%\n")
                f.write(f"平均退信率: {overview.get('avg_bounce_rate', 0):.2f}%\n")
                f.write(f"总发送邮件数: {overview.get('total_emails_sent', 0)}\n")
                f.write(f"总收到回复数: {overview.get('total_replies_received', 0)}\n\n")

                # 质量分布
                distribution = analytics.get('quality_distribution', {})
                f.write("📊 质量分布\n")
                f.write("-" * 30 + "\n")
                f.write(f"优秀 (80-100分): {distribution.get('excellent', 0)} 个\n")
                f.write(f"良好 (60-79分): {distribution.get('good', 0)} 个\n")
                f.write(f"一般 (40-59分): {distribution.get('fair', 0)} 个\n")
                f.write(f"较差 (20-39分): {distribution.get('poor', 0)} 个\n")
                f.write(f"无效 (0-19分): {distribution.get('invalid', 0)} 个\n\n")

                # 域名分析
                f.write("🌐 主要域名分析\n")
                f.write("-" * 30 + "\n")
                for domain_info in analytics.get('top_domains', []):
                    f.write(f"{domain_info['domain']}: {domain_info['count']} 个收件人, "
                           f"平均质量 {domain_info['avg_quality']:.2f}\n")
                f.write("\n")

                # 优化建议
                f.write("💡 优化建议\n")
                f.write("-" * 30 + "\n")
                for i, rec in enumerate(recommendations, 1):
                    f.write(f"{i}. {rec['title']} ({rec['priority']}优先级)\n")
                    f.write(f"   描述: {rec['description']}\n")
                    f.write(f"   预期效果: {rec['impact']}\n\n")

            self.logger.info(f"质量报告已导出到: {filepath}")

        except Exception as e:
            self.logger.error(f"导出质量报告失败: {str(e)}")

    def cleanup_invalid_recipients(self) -> int:
        """清理无效收件人"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取无效收件人
            cursor.execute('''
                SELECT email FROM recipient_quality
                WHERE status = 'invalid' OR bounce_rate >= 0.5
            ''')

            invalid_emails = [row[0] for row in cursor.fetchall()]

            if not invalid_emails:
                return 0

            # 标记为已清理（不直接删除，保留历史记录）
            placeholders = ','.join(['?' for _ in invalid_emails])
            cursor.execute(f'''
                UPDATE recipient_quality
                SET status = 'cleaned', updated_at = ?
                WHERE email IN ({placeholders})
            ''', [datetime.datetime.now().isoformat()] + invalid_emails)

            conn.commit()
            conn.close()

            self.logger.info(f"清理了 {len(invalid_emails)} 个无效收件人")
            return len(invalid_emails)

        except Exception as e:
            self.logger.error(f"清理无效收件人失败: {str(e)}")
            return 0

    def remove_recipient(self, email: str, sender_email: str = None) -> bool:
        """删除指定收件人"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if sender_email:
                # 删除特定发件人的收件人记录
                cursor.execute('''
                    DELETE FROM recipient_quality
                    WHERE email = ? AND sender_email = ?
                ''', (email, sender_email))

                cursor.execute('''
                    DELETE FROM send_history
                    WHERE recipient_email = ? AND sender_email = ?
                ''', (email, sender_email))
            else:
                # 删除所有相关记录
                cursor.execute('''
                    DELETE FROM recipient_quality
                    WHERE email = ?
                ''', (email,))

                cursor.execute('''
                    DELETE FROM send_history
                    WHERE recipient_email = ?
                ''', (email,))

                # 删除批次关联
                cursor.execute('''
                    DELETE FROM batch_recipients
                    WHERE recipient_email = ?
                ''', (email,))

            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()

            if deleted_count > 0:
                self.logger.info(f"成功删除收件人: {email}")
                return True
            else:
                self.logger.warning(f"收件人不存在: {email}")
                return False

        except Exception as e:
            self.logger.error(f"删除收件人失败: {str(e)}")
            return False

    def remove_recipients_batch(self, emails: List[str], sender_email: str = None) -> int:
        """批量删除收件人"""
        try:
            if not emails:
                return 0

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            deleted_count = 0

            for email in emails:
                try:
                    if sender_email:
                        # 删除特定发件人的收件人记录
                        cursor.execute('''
                            DELETE FROM recipient_quality
                            WHERE email = ? AND sender_email = ?
                        ''', (email, sender_email))

                        cursor.execute('''
                            DELETE FROM send_history
                            WHERE recipient_email = ? AND sender_email = ?
                        ''', (email, sender_email))
                    else:
                        # 删除所有相关记录
                        cursor.execute('''
                            DELETE FROM recipient_quality
                            WHERE email = ?
                        ''', (email,))

                        cursor.execute('''
                            DELETE FROM send_history
                            WHERE recipient_email = ?
                        ''', (email,))

                        # 删除批次关联
                        cursor.execute('''
                            DELETE FROM batch_recipients
                            WHERE recipient_email = ?
                        ''', (email,))

                    if cursor.rowcount > 0:
                        deleted_count += 1

                except Exception as e:
                    self.logger.error(f"删除收件人 {email} 失败: {str(e)}")
                    continue

            conn.commit()
            conn.close()

            self.logger.info(f"批量删除完成: 成功删除 {deleted_count}/{len(emails)} 个收件人")
            return deleted_count

        except Exception as e:
            self.logger.error(f"批量删除收件人失败: {str(e)}")
            return 0

    def clear_all_recipients(self, sender_email: str = None) -> bool:
        """清空所有收件人数据"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if sender_email:
                # 只清空特定发件人的数据
                cursor.execute('''
                    DELETE FROM recipient_quality
                    WHERE sender_email = ?
                ''', (sender_email,))

                cursor.execute('''
                    DELETE FROM send_history
                    WHERE sender_email = ?
                ''', (sender_email,))
            else:
                # 清空所有数据
                cursor.execute('DELETE FROM recipient_quality')
                cursor.execute('DELETE FROM send_history')
                cursor.execute('DELETE FROM batch_recipients')
                cursor.execute('DELETE FROM batch_management')
                cursor.execute('DELETE FROM campaign_analytics')

            conn.commit()
            conn.close()

            self.logger.info(f"成功清空收件人数据{'（发件人: ' + sender_email + '）' if sender_email else ''}")
            return True

        except Exception as e:
            self.logger.error(f"清空收件人数据失败: {str(e)}")
            return False

    def add_recipient(self, email: str, sender_email: str = None, initial_score: float = 50.0, source: str = "手动添加") -> bool:
        """添加新收件人"""
        try:
            if not sender_email:
                # 如果没有指定发件人，使用默认值
                sender_email = "<EMAIL>"

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            current_time = datetime.datetime.now().isoformat()
            domain = email.split('@')[1] if '@' in email else 'unknown'

            # 确定状态
            if initial_score >= 80:
                status = 'excellent'
            elif initial_score >= 60:
                status = 'good'
            elif initial_score >= 40:
                status = 'fair'
            elif initial_score >= 20:
                status = 'poor'
            else:
                status = 'invalid'

            cursor.execute('''
                INSERT OR REPLACE INTO recipient_quality
                (email, sender_email, quality_score, status, domain,
                 first_contact_date, created_at, updated_at, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (email, sender_email, initial_score, status, domain,
                  current_time, current_time, current_time, f"来源: {source}"))

            conn.commit()
            conn.close()

            self.logger.info(f"成功添加收件人: {email} (评分: {initial_score}, 来源: {source})")
            return True

        except Exception as e:
            self.logger.error(f"添加收件人失败: {str(e)}")
            return False

    def update_recipient_score(self, email: str, new_score: float, sender_email: str = None) -> bool:
        """更新收件人评分"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 确定新状态
            if new_score >= 80:
                status = 'excellent'
            elif new_score >= 60:
                status = 'good'
            elif new_score >= 40:
                status = 'fair'
            elif new_score >= 20:
                status = 'poor'
            else:
                status = 'invalid'

            current_time = datetime.datetime.now().isoformat()

            if sender_email:
                cursor.execute('''
                    UPDATE recipient_quality
                    SET quality_score = ?, status = ?, updated_at = ?
                    WHERE email = ? AND sender_email = ?
                ''', (new_score, status, current_time, email, sender_email))
            else:
                cursor.execute('''
                    UPDATE recipient_quality
                    SET quality_score = ?, status = ?, updated_at = ?
                    WHERE email = ?
                ''', (new_score, status, current_time, email))

            updated_count = cursor.rowcount
            conn.commit()
            conn.close()

            if updated_count > 0:
                self.logger.info(f"成功更新收件人评分: {email} -> {new_score} ({status})")
                return True
            else:
                self.logger.warning(f"收件人不存在，无法更新: {email}")
                return False

        except Exception as e:
            self.logger.error(f"更新收件人评分失败: {str(e)}")
            return False

    def get_recipient_quality(self, email: str, sender_email: str = None) -> Optional[Dict]:
        """获取单个收件人的质量信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            if sender_email:
                cursor.execute('''
                    SELECT email, quality_score, status, total_sent, total_replies,
                           total_bounces, last_reply_time, last_sent_time,
                           response_rate, bounce_rate, engagement_score, domain,
                           created_at, updated_at, notes
                    FROM recipient_quality
                    WHERE email = ? AND sender_email = ?
                ''', (email, sender_email))
            else:
                cursor.execute('''
                    SELECT email, quality_score, status, total_sent, total_replies,
                           total_bounces, last_reply_time, last_sent_time,
                           response_rate, bounce_rate, engagement_score, domain,
                           created_at, updated_at, notes
                    FROM recipient_quality
                    WHERE email = ?
                    ORDER BY updated_at DESC
                    LIMIT 1
                ''', (email,))

            result = cursor.fetchone()
            conn.close()

            if result:
                return {
                    'email': result[0],
                    'quality_score': result[1],
                    'status': result[2],
                    'total_sent': result[3],
                    'total_replies': result[4],
                    'total_bounces': result[5],
                    'last_reply_time': result[6],
                    'last_sent_time': result[7],
                    'response_rate': result[8],
                    'bounce_rate': result[9],
                    'engagement_score': result[10],
                    'domain': result[11],
                    'created_at': result[12],
                    'updated_at': result[13],
                    'notes': result[14]
                }
            else:
                return None

        except Exception as e:
            self.logger.error(f"获取收件人质量信息失败: {str(e)}")
            return None
