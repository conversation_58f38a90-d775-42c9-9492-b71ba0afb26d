#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进版本的邮件系统
修复了附件布局和功能响应问题
"""

import tkinter as tk
import sys
import os

def main():
    """启动改进版本的邮件系统"""
    print("🔧 启动改进版本的邮件系统...")
    print("🎯 改进内容：")
    print("   • 修复附件布局问题")
    print("   • 增强功能响应")
    print("   • 优化用户体验")
    print("   • 添加错误处理")
    
    try:
        # 导入改进版本GUI
        from gui_clean import EmailSenderGUI
        
        # 创建主窗口
        root = tk.Tk()
        
        # 创建应用实例
        app = EmailSenderGUI(root)
        
        print("✅ 改进版本启动成功！")
        print("📐 布局改进：")
        print("   • 左侧：邮件配置、内容编辑、操作日志")
        print("   • 中间：快速操作、队列管理、高级工具")
        print("   • 右侧：附件管理、系统监控、状态显示")
        print("")
        print("🔧 功能改进：")
        print("   • 附件管理移到右侧，布局更合理")
        print("   • 所有按钮都有响应和反馈")
        print("   • 增加了输入验证和错误处理")
        print("   • 优化了用户交互体验")
        print("")
        print("🎯 测试建议：")
        print("   • 测试所有按钮的响应")
        print("   • 尝试添加/删除附件")
        print("   • 验证邮箱地址功能")
        print("   • 查看日志输出")
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 gui_clean.py 文件存在且可正常导入")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查系统环境和依赖")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
