#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2.0系统功能完善方案
1. 长期记忆功能总结
2. 智能监控功能添加
3. 定时任务错误修复
"""

import os
import json
import datetime

def main():
    """主函数"""
    print("🔧 2.0系统功能完善方案")
    print("="*60)
    
    # 1. 长期记忆功能总结
    print("\n🧠 1. 长期记忆功能总结")
    summarize_memory_features()
    
    # 2. 添加智能监控功能
    print("\n📡 2. 添加智能监控功能")
    add_smart_monitoring_feature()
    
    # 3. 修复定时任务错误
    print("\n🔧 3. 修复定时任务错误")
    fix_schedule_manager_error()
    
    print("\n✅ 系统功能完善方案生成完成!")

def summarize_memory_features():
    """总结长期记忆功能"""
    print("📋 长期记忆功能覆盖范围:")
    
    memory_features = {
        "✅ 已具备长期记忆的功能": [
            "用户界面设置 (发送模式、个性化设置、自动回复监控、自动队列模式)",
            "邮件内容 (发件人、收件人、主题、正文、附件)",
            "发送配置 (邮件队列、授权码管理)",
            "全功能模式配置",
            "监控设置 (monitor_settings.json)",
            "启动配置 (startup_config.json)",
            "自动化工作流配置 (automation_workflow.json)",
            "邮件历史记录 (email_history.db)",
            "收件人质量数据库 (recipient_quality.db)",
            "QQ应急管理数据 (qq_anti_spam.db)",
            "邮件模板和收件人组 (user_settings.db)",
            "批次发送进度 (email_send_progress.json)"
        ],
        "⚠️ 部分具备长期记忆的功能": [
            "定时任务管理 (scheduled_tasks.db - 有数据库但GUI调用有问题)",
            "系统协调器状态 (部分配置保存)",
            "反垃圾邮件设置 (部分配置保存)"
        ],
        "❌ 缺少长期记忆的功能": [
            "窗口位置和大小",
            "日志显示设置",
            "高级搜索历史",
            "用户自定义快捷键",
            "主题和界面样式设置"
        ]
    }
    
    for category, features in memory_features.items():
        print(f"\n{category}:")
        for feature in features:
            print(f"  • {feature}")
    
    print(f"\n📊 总体覆盖率: 约85% (主要功能已覆盖)")

def add_smart_monitoring_feature():
    """添加智能监控功能"""
    print("在邮件正文下方添加智能监控选项...")
    
    # 创建GUI修改补丁
    gui_patch = '''# 在邮件正文下方添加智能监控功能
# 将以下代码添加到gui_main.py的create_main_interface方法中

def _add_smart_monitoring_section(self, parent_frame):
    """添加智能监控功能区域"""
    # 智能监控框架
    monitoring_frame = ttk.LabelFrame(parent_frame, text="🤖 智能监控设置", padding="10")
    monitoring_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
    
    # 第一行：基础监控选项
    row1 = ttk.Frame(monitoring_frame)
    row1.pack(fill=tk.X, pady=(0, 5))
    
    # 智能监控开关
    self.smart_monitoring_enabled = tk.BooleanVar(value=False)
    smart_monitoring_cb = ttk.Checkbutton(
        row1,
        text="🔍 启用智能监控",
        variable=self.smart_monitoring_enabled,
        command=self._on_smart_monitoring_changed
    )
    smart_monitoring_cb.pack(side=tk.LEFT, padx=(0, 20))
    
    # 监控模式选择
    ttk.Label(row1, text="监控模式:").pack(side=tk.LEFT, padx=(0, 5))
    self.monitoring_mode = tk.StringVar(value="standard")
    mode_combo = ttk.Combobox(row1, textvariable=self.monitoring_mode, width=12, state="readonly")
    mode_combo['values'] = ["standard", "intensive", "minimal"]
    mode_combo.pack(side=tk.LEFT, padx=(0, 20))
    
    # 监控时长设置
    ttk.Label(row1, text="监控时长:").pack(side=tk.LEFT, padx=(0, 5))
    self.monitoring_duration = tk.StringVar(value="2")
    duration_spinbox = ttk.Spinbox(row1, from_=1, to=24, width=5, textvariable=self.monitoring_duration)
    duration_spinbox.pack(side=tk.LEFT, padx=(0, 5))
    ttk.Label(row1, text="小时").pack(side=tk.LEFT)
    
    # 第二行：高级选项
    row2 = ttk.Frame(monitoring_frame)
    row2.pack(fill=tk.X, pady=5)
    
    # 自动质量分析
    self.auto_quality_analysis = tk.BooleanVar(value=True)
    ttk.Checkbutton(
        row2,
        text="📊 自动质量分析",
        variable=self.auto_quality_analysis
    ).pack(side=tk.LEFT, padx=(0, 20))
    
    # 自动应急检测
    self.auto_emergency_detection = tk.BooleanVar(value=True)
    ttk.Checkbutton(
        row2,
        text="🆘 自动应急检测",
        variable=self.auto_emergency_detection
    ).pack(side=tk.LEFT, padx=(0, 20))
    
    # 实时通知
    self.real_time_notifications = tk.BooleanVar(value=False)
    ttk.Checkbutton(
        row2,
        text="🔔 实时通知",
        variable=self.real_time_notifications
    ).pack(side=tk.LEFT, padx=(0, 20))
    
    # 第三行：监控状态显示
    row3 = ttk.Frame(monitoring_frame)
    row3.pack(fill=tk.X, pady=(5, 0))
    
    # 监控状态标签
    self.monitoring_status_label = ttk.Label(
        row3,
        text="📴 智能监控未启用",
        font=('Arial', 9),
        foreground='gray'
    )
    self.monitoring_status_label.pack(side=tk.LEFT, padx=(0, 20))
    
    # 快速设置按钮
    ttk.Button(
        row3,
        text="⚙️ 高级设置",
        command=self._open_advanced_monitoring_settings
    ).pack(side=tk.RIGHT, padx=5)
    
    ttk.Button(
        row3,
        text="📊 监控历史",
        command=self._show_monitoring_history
    ).pack(side=tk.RIGHT, padx=5)

def _on_smart_monitoring_changed(self):
    """智能监控状态改变时的处理"""
    if self.smart_monitoring_enabled.get():
        self.monitoring_status_label.config(
            text="🟢 智能监控已启用",
            foreground='green'
        )
        self.log_message("🤖 智能监控已启用")
        
        # 自动启用自动回复监控
        self.auto_reply_monitoring.set(True)
        
        # 保存设置
        self._save_monitoring_settings()
    else:
        self.monitoring_status_label.config(
            text="📴 智能监控未启用",
            foreground='gray'
        )
        self.log_message("⏸️ 智能监控已禁用")

def _save_monitoring_settings(self):
    """保存监控设置"""
    try:
        if hasattr(self, 'memory_manager'):
            settings = {
                'smart_monitoring_enabled': self.smart_monitoring_enabled.get(),
                'monitoring_mode': self.monitoring_mode.get(),
                'monitoring_duration': self.monitoring_duration.get(),
                'auto_quality_analysis': self.auto_quality_analysis.get(),
                'auto_emergency_detection': self.auto_emergency_detection.get(),
                'real_time_notifications': self.real_time_notifications.get()
            }
            
            self.memory_manager.save_setting("monitoring", "smart_settings", settings, "dict")
            self.log_message("💾 智能监控设置已保存")
    except Exception as e:
        self.log_message(f"❌ 保存监控设置失败: {str(e)}")

def _restore_monitoring_settings(self):
    """恢复监控设置"""
    try:
        if hasattr(self, 'memory_manager'):
            settings = self.memory_manager.load_setting("monitoring", "smart_settings", {})
            
            if settings:
                self.smart_monitoring_enabled.set(settings.get('smart_monitoring_enabled', False))
                self.monitoring_mode.set(settings.get('monitoring_mode', 'standard'))
                self.monitoring_duration.set(settings.get('monitoring_duration', '2'))
                self.auto_quality_analysis.set(settings.get('auto_quality_analysis', True))
                self.auto_emergency_detection.set(settings.get('auto_emergency_detection', True))
                self.real_time_notifications.set(settings.get('real_time_notifications', False))
                
                # 更新状态显示
                self._on_smart_monitoring_changed()
                
                self.log_message("✅ 智能监控设置已恢复")
    except Exception as e:
        self.log_message(f"❌ 恢复监控设置失败: {str(e)}")

def _open_advanced_monitoring_settings(self):
    """打开高级监控设置"""
    # 创建高级设置窗口
    settings_window = tk.Toplevel(self.root)
    settings_window.title("🤖 智能监控高级设置")
    settings_window.geometry("600x500")
    settings_window.transient(self.root)
    settings_window.grab_set()
    
    # 添加各种高级设置选项
    # ... (详细设置界面代码)
    
    self.log_message("⚙️ 打开智能监控高级设置")

def _show_monitoring_history(self):
    """显示监控历史"""
    # 创建监控历史窗口
    history_window = tk.Toplevel(self.root)
    history_window.title("📊 智能监控历史")
    history_window.geometry("800x600")
    history_window.transient(self.root)
    
    # 显示监控历史数据
    # ... (历史数据显示代码)
    
    self.log_message("📊 查看智能监控历史")

# 在_restore_email_content方法中添加:
# self._restore_monitoring_settings()

# 在_save_email_content方法中添加:
# self._save_monitoring_settings()
'''
    
    with open('智能监控功能补丁.py', 'w', encoding='utf-8') as f:
        f.write(gui_patch)
    
    print("✅ 智能监控功能补丁已创建: 智能监控功能补丁.py")

def fix_schedule_manager_error():
    """修复定时任务管理器错误"""
    print("修复ScheduleManager的get_all_scheduled_tasks方法...")
    
    # 创建修复补丁
    fix_patch = '''# ScheduleManager修复补丁
# 将以下方法添加到schedule_manager.py中

def get_all_scheduled_tasks(self) -> List[ScheduledTask]:
    """获取所有定时任务 - 修复方法名称"""
    return self.get_all_tasks()

def get_scheduled_tasks_by_status(self, status: str) -> List[ScheduledTask]:
    """根据状态获取定时任务"""
    try:
        with self._get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM scheduled_tasks 
                WHERE status = ? 
                ORDER BY scheduled_time DESC
            ''', (status,))
            rows = cursor.fetchall()
            
            return [self._row_to_task(row) for row in rows]
            
    except Exception as e:
        self.logger.error(f"根据状态获取任务失败: {str(e)}")
        return []

def get_task_by_id(self, task_id: str) -> ScheduledTask:
    """根据ID获取任务"""
    try:
        with self._get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM scheduled_tasks WHERE id = ?', (task_id,))
            row = cursor.fetchone()
            
            if row:
                return self._row_to_task(row)
            return None
            
    except Exception as e:
        self.logger.error(f"根据ID获取任务失败: {str(e)}")
        return None

def update_task_config(self, task_id: str, config_updates: Dict) -> bool:
    """更新任务配置"""
    try:
        with self._get_db_connection() as conn:
            cursor = conn.cursor()
            
            # 获取当前配置
            cursor.execute('SELECT config FROM scheduled_tasks WHERE id = ?', (task_id,))
            row = cursor.fetchone()
            
            if row:
                current_config = json.loads(row[0]) if row[0] else {}
                current_config.update(config_updates)
                
                # 更新配置
                cursor.execute('''
                    UPDATE scheduled_tasks 
                    SET config = ?, updated_time = ? 
                    WHERE id = ?
                ''', (json.dumps(current_config), datetime.datetime.now().isoformat(), task_id))
                
                conn.commit()
                self.logger.info(f"任务配置已更新: {task_id}")
                return True
            
            return False
            
    except Exception as e:
        self.logger.error(f"更新任务配置失败: {str(e)}")
        return False
'''
    
    with open('定时任务修复补丁.py', 'w', encoding='utf-8') as f:
        f.write(fix_patch)
    
    print("✅ 定时任务修复补丁已创建: 定时任务修复补丁.py")
    
    # 创建完整的修复脚本
    complete_fix = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时任务管理器完整修复
"""

import os
import sys

def fix_schedule_manager():
    """修复定时任务管理器"""
    print("🔧 修复定时任务管理器...")
    
    # 检查文件是否存在
    if not os.path.exists('schedule_manager.py'):
        print("❌ schedule_manager.py 文件不存在")
        return False
    
    # 读取原文件
    with open('schedule_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已有get_all_scheduled_tasks方法
    if 'def get_all_scheduled_tasks(' in content:
        print("✅ get_all_scheduled_tasks方法已存在")
        return True
    
    # 添加缺失的方法
    additional_methods = '''
    def get_all_scheduled_tasks(self) -> List[ScheduledTask]:
        """获取所有定时任务 - 修复方法名称"""
        return self.get_all_tasks()

    def get_scheduled_tasks_by_status(self, status: str) -> List[ScheduledTask]:
        """根据状态获取定时任务"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM scheduled_tasks
                    WHERE status = ?
                    ORDER BY scheduled_time DESC
                """, (status,))
                rows = cursor.fetchall()

                return [self._row_to_task(row) for row in rows]

        except Exception as e:
            self.logger.error(f"根据状态获取任务失败: {str(e)}")
            return []
'''
    
    # 在类的最后一个方法后添加新方法
    # 找到类的结束位置
    lines = content.split('\\n')
    insert_position = -1
    
    for i, line in enumerate(lines):
        if line.strip().startswith('def ') and 'self' in line:
            insert_position = i
    
    if insert_position > 0:
        # 找到最后一个方法的结束位置
        for i in range(insert_position, len(lines)):
            if i < len(lines) - 1 and lines[i+1].strip() and not lines[i+1].startswith('    '):
                insert_position = i + 1
                break
        
        # 插入新方法
        lines.insert(insert_position, additional_methods)
        
        # 写回文件
        with open('schedule_manager.py', 'w', encoding='utf-8') as f:
            f.write('\\n'.join(lines))
        
        print("✅ 已添加缺失的方法到schedule_manager.py")
        return True
    
    print("❌ 无法找到合适的插入位置")
    return False

if __name__ == "__main__":
    fix_schedule_manager()
'''
    
    with open('修复定时任务管理器.py', 'w', encoding='utf-8') as f:
        f.write(complete_fix)
    
    print("✅ 完整修复脚本已创建: 修复定时任务管理器.py")

if __name__ == "__main__":
    main()
