自动化邮件发送助手 - 新功能使用说明
====================================

🎉 新增功能
-----------

### 1. ✅ 空正文支持
**功能说明：** 现在可以发送空正文的邮件，不再强制使用默认内容

**使用方法：**
- 在"邮件正文"框中不输入任何内容
- 系统会显示"邮件正文为空"的日志
- 邮件将以空正文发送

**适用场景：**
- 只需要发送附件的邮件
- 简单的通知邮件
- 用户有特殊需求的场景

### 2. ✅ 多授权码管理
**功能说明：** 可以保存和管理多个QQ邮箱的授权码，快速切换发送者

**主要特性：**
- 安全存储多个邮箱的授权码
- 一键切换发送者邮箱
- 授权码加密显示
- 添加时间记录

🔧 授权码管理使用指南
---------------------

### 第一步：添加授权码
1. 点击发送者邮箱旁边的"授权码管理"按钮
2. 在授权码管理窗口中点击"添加授权码"
3. 输入邮箱地址（如：<EMAIL>）
4. 输入对应的SMTP授权码
5. 点击"保存"

### 第二步：使用授权码
1. 在授权码管理窗口中选择要使用的邮箱
2. 点击"使用选中"按钮
3. 系统会自动：
   - 设置发送者邮箱地址
   - 更新SMTP配置
   - 切换到选中的邮箱

### 第三步：管理授权码
- **删除授权码：** 选中后点击"删除选中"
- **查看授权码：** 列表中显示部分隐藏的授权码
- **自动保存：** 所有授权码自动保存到本地文件

📊 授权码管理界面说明
---------------------

### 授权码列表显示
```
邮箱地址              授权码              添加时间
<EMAIL>         abcd********efgh    2025-01-11 10:30:15
<EMAIL>         wxyz********1234    2025-01-11 11:45:22
```

### 按钮功能
- **添加授权码：** 添加新的邮箱和授权码
- **删除选中：** 删除选中的授权码记录
- **使用选中：** 切换到选中的邮箱发送
- **关闭：** 关闭授权码管理窗口

🔒 安全特性
-----------

### 授权码保护
- **本地存储：** 授权码保存在本地文件 `auth_codes.json`
- **部分隐藏：** 界面中只显示前4位和后4位
- **加密传输：** SMTP连接使用TLS加密

### 数据安全
- **文件权限：** 建议设置授权码文件为只读
- **定期更新：** 建议定期更换QQ邮箱授权码
- **备份建议：** 可以备份 `auth_codes.json` 文件

💡 使用技巧
-----------

### 多账户管理
1. **工作邮箱：** 添加工作用的QQ邮箱
2. **个人邮箱：** 添加个人QQ邮箱
3. **备用邮箱：** 添加备用QQ邮箱
4. **快速切换：** 根据需要快速切换发送者

### 批量发送策略
1. **分账户发送：** 使用不同邮箱分批发送
2. **负载均衡：** 避免单个邮箱发送过多
3. **风险分散：** 降低被限制的风险

### 空正文应用
1. **纯附件邮件：** 只发送文档，不需要正文
2. **简洁通知：** 主题已经说明一切
3. **特殊格式：** 某些场景下的特殊需求

🚀 实际使用流程
---------------

### 场景1：多账户轮换发送
```
步骤1：添加3个QQ邮箱的授权码
步骤2：发送第1批邮件（使用邮箱A）
步骤3：切换到邮箱B，发送第2批
步骤4：切换到邮箱C，发送第3批
```

### 场景2：空正文附件邮件
```
步骤1：不在正文框输入任何内容
步骤2：添加需要发送的附件
步骤3：设置邮件主题
步骤4：正常发送邮件
```

### 场景3：快速切换发送者
```
步骤1：点击"授权码管理"
步骤2：选择要使用的邮箱
步骤3：点击"使用选中"
步骤4：开始发送邮件
```

⚠️ 注意事项
-----------

### 授权码管理
- **授权码有效性：** 确保添加的授权码是最新有效的
- **邮箱状态：** 确保邮箱SMTP服务已开启
- **网络环境：** 不同网络环境可能影响连接

### 空正文邮件
- **收件人理解：** 确保收件人能理解空正文邮件
- **垃圾邮件风险：** 空正文可能增加被识别为垃圾邮件的风险
- **主题重要性：** 空正文时邮件主题更加重要

### 多账户使用
- **发送限制：** 每个QQ邮箱都有日发送量限制
- **时间间隔：** 建议在不同邮箱间切换时稍作间隔
- **内容一致性：** 使用不同邮箱发送时注意内容的一致性

🔧 故障排除
-----------

### 授权码问题
**问题：** 添加授权码后仍然发送失败
**解决：**
1. 确认授权码是否正确
2. 检查邮箱SMTP服务状态
3. 重新获取授权码

### 切换邮箱问题
**问题：** 切换邮箱后发送失败
**解决：**
1. 检查新邮箱的授权码
2. 确认邮箱地址格式正确
3. 测试SMTP连接

### 空正文问题
**问题：** 空正文邮件被拒收
**解决：**
1. 添加简单的正文内容
2. 确保邮件主题清晰
3. 检查附件是否正常

📞 技术支持
-----------

### 日志查看
- 所有操作都有详细日志记录
- 可以通过日志分析问题原因
- 支持保存日志到文件

### 配置文件
- `auth_codes.json` - 授权码存储文件
- `config.py` - 系统配置文件
- `email_sender.log` - 详细日志文件

====================================
享受更强大、更灵活的邮件发送体验！
