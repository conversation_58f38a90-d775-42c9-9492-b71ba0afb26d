#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能弹窗系统测试脚本
用于验证不同场景下的弹窗行为
"""

import json
import datetime
import os

def create_test_scenarios():
    """创建不同的测试场景"""
    print("🧪 创建智能弹窗测试场景...")
    
    scenarios = [
        {
            "name": "重要任务（应该弹窗）",
            "file": "email_send_progress_important.json",
            "data": {
                "session_id": "important_batch_20250612_001",
                "total_emails": 50,
                "current_email_index": 30,
                "success_count": 28,
                "failed_count": 2,
                "start_time": datetime.datetime.now().isoformat(),
                "last_update": datetime.datetime.now().isoformat()
            }
        },
        {
            "name": "测试任务（应该静默）",
            "file": "email_send_progress_test.json", 
            "data": {
                "session_id": "test_session_20250612_001",
                "total_emails": 10,
                "current_email_index": 5,
                "success_count": 4,
                "failed_count": 1,
                "start_time": datetime.datetime.now().isoformat(),
                "last_update": datetime.datetime.now().isoformat()
            }
        },
        {
            "name": "小任务（应该静默）",
            "file": "email_send_progress_small.json",
            "data": {
                "session_id": "small_batch_20250612_001", 
                "total_emails": 2,
                "current_email_index": 1,
                "success_count": 1,
                "failed_count": 0,
                "start_time": datetime.datetime.now().isoformat(),
                "last_update": datetime.datetime.now().isoformat()
            }
        },
        {
            "name": "旧任务（应该静默）",
            "file": "email_send_progress_old.json",
            "data": {
                "session_id": "old_batch_20250611_001",
                "total_emails": 20,
                "current_email_index": 10,
                "success_count": 9,
                "failed_count": 1,
                "start_time": (datetime.datetime.now() - datetime.timedelta(hours=5)).isoformat(),
                "last_update": (datetime.datetime.now() - datetime.timedelta(hours=5)).isoformat()
            }
        }
    ]
    
    for scenario in scenarios:
        try:
            with open(scenario["file"], "w", encoding="utf-8") as f:
                json.dump(scenario["data"], f, ensure_ascii=False, indent=2)
            print(f"✅ 创建场景: {scenario['name']}")
        except Exception as e:
            print(f"❌ 创建场景失败: {scenario['name']} - {str(e)}")
    
    return scenarios

def test_scenario(scenario_file, scenario_name):
    """测试单个场景"""
    print(f"\n🔍 测试场景: {scenario_name}")
    
    # 将测试文件重命名为标准文件名
    if os.path.exists(scenario_file):
        if os.path.exists("email_send_progress.json"):
            os.remove("email_send_progress.json")
        os.rename(scenario_file, "email_send_progress.json")
        print(f"📁 已设置测试文件: {scenario_file}")
        
        print("💡 现在启动GUI程序观察弹窗行为...")
        print("   - 重要任务应该弹窗询问")
        print("   - 测试/小/旧任务应该静默处理")
        print("   - 查看日志了解判断过程")
        
        input("按回车键继续下一个测试...")
        
        # 清理测试文件
        if os.path.exists("email_send_progress.json"):
            os.remove("email_send_progress.json")
            print("🗑️ 已清理测试文件")
    else:
        print(f"❌ 测试文件不存在: {scenario_file}")

def interactive_test():
    """交互式测试"""
    print("🎮 智能弹窗系统交互式测试")
    print("=" * 50)
    
    scenarios = create_test_scenarios()
    
    while True:
        print("\n请选择测试场景:")
        print("1. 重要任务（应该弹窗）")
        print("2. 测试任务（应该静默）") 
        print("3. 小任务（应该静默）")
        print("4. 旧任务（应该静默）")
        print("5. 查看配置文件")
        print("6. 修改配置文件")
        print("7. 清理所有测试文件")
        print("8. 退出")
        
        choice = input("\n请输入选项 (1-8): ").strip()
        
        if choice == "1":
            test_scenario("email_send_progress_important.json", "重要任务")
        elif choice == "2":
            test_scenario("email_send_progress_test.json", "测试任务")
        elif choice == "3":
            test_scenario("email_send_progress_small.json", "小任务")
        elif choice == "4":
            test_scenario("email_send_progress_old.json", "旧任务")
        elif choice == "5":
            show_config()
        elif choice == "6":
            modify_config()
        elif choice == "7":
            cleanup_test_files()
        elif choice == "8":
            cleanup_test_files()
            print("👋 测试结束")
            break
        else:
            print("❌ 无效选项，请重新选择")

def show_config():
    """显示当前配置"""
    try:
        if os.path.exists("startup_config.json"):
            with open("startup_config.json", "r", encoding="utf-8") as f:
                config = json.load(f)
            print("\n📋 当前配置:")
            for key, value in config.items():
                print(f"  {key}: {value}")
        else:
            print("❌ 配置文件不存在")
    except Exception as e:
        print(f"❌ 读取配置失败: {str(e)}")

def modify_config():
    """修改配置"""
    try:
        config_file = "startup_config.json"
        if os.path.exists(config_file):
            with open(config_file, "r", encoding="utf-8") as f:
                config = json.load(f)
        else:
            config = {}
        
        print("\n🔧 配置修改选项:")
        print("1. 修改时间阈值 (popup_threshold_hours)")
        print("2. 修改最小邮件数 (popup_min_emails)")
        print("3. 启用/禁用测试数据弹窗 (popup_for_test_data)")
        print("4. 启用/禁用智能弹窗 (intelligent_popup)")
        print("5. 返回")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            hours = float(input("请输入时间阈值（小时）: "))
            config["popup_threshold_hours"] = hours
        elif choice == "2":
            emails = int(input("请输入最小邮件数: "))
            config["popup_min_emails"] = emails
        elif choice == "3":
            enable = input("是否为测试数据弹窗？(y/n): ").lower() == 'y'
            config["popup_for_test_data"] = enable
        elif choice == "4":
            enable = input("是否启用智能弹窗？(y/n): ").lower() == 'y'
            config["intelligent_popup"] = enable
        elif choice == "5":
            return
        else:
            print("❌ 无效选项")
            return
        
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        print("✅ 配置已更新")
        
    except Exception as e:
        print(f"❌ 修改配置失败: {str(e)}")

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        "email_send_progress.json",
        "email_send_progress_important.json",
        "email_send_progress_test.json", 
        "email_send_progress_small.json",
        "email_send_progress_old.json"
    ]
    
    cleaned = 0
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            cleaned += 1
    
    print(f"🗑️ 已清理 {cleaned} 个测试文件")

def main():
    """主函数"""
    print("🧠 智能弹窗系统测试工具")
    print("=" * 50)
    print("📝 使用说明:")
    print("1. 选择不同的测试场景")
    print("2. 启动GUI程序观察弹窗行为")
    print("3. 查看日志了解判断过程")
    print("4. 根据需要调整配置参数")
    print("=" * 50)
    
    interactive_test()

if __name__ == "__main__":
    main()
