# -*- coding: utf-8 -*-
"""
测试邮件发送功能
验证系统是否能正常发送邮件
"""

from email_sender import EmailSender

def test_send_to_self():
    """测试发送邮件给自己"""
    print("测试邮件发送功能")
    print("=" * 40)
    
    # 获取发送者邮箱
    sender_email = input("请输入您的QQ邮箱地址: ").strip()
    if not sender_email.endswith('@qq.com'):
        print("❌ 请输入有效的QQ邮箱地址")
        return
    
    print(f"\n将发送测试邮件给您自己: {sender_email}")
    print("这样可以验证发送功能是否正常工作")
    
    confirm = input("\n确认发送测试邮件吗？(y/n): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    try:
        # 创建邮件发送器
        sender = EmailSender(sender_email)
        
        # 发送测试邮件
        print("\n开始发送测试邮件...")
        success = sender.send_email(
            to_emails=[sender_email],  # 发送给自己
            subject="邮件发送功能测试",
            body="这是一封测试邮件，用于验证邮件发送功能是否正常工作。\n\n如果您收到这封邮件，说明发送功能正常。",
            attachments=None  # 不带附件
        )
        
        if success:
            print("✅ 测试邮件发送成功！")
            print("请检查您的邮箱收件箱（可能在垃圾邮件文件夹中）")
            print("\n如果收到邮件，说明发送功能正常")
            print("如果没收到，可能是：")
            print("1. 邮件在垃圾邮件文件夹中")
            print("2. 邮件服务器延迟")
            print("3. 其他网络问题")
        else:
            print("❌ 测试邮件发送失败")
            print("请检查日志文件获取详细错误信息")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")

def test_send_with_attachment():
    """测试带附件的邮件发送"""
    print("\n" + "=" * 40)
    print("测试带附件的邮件发送")
    print("=" * 40)
    
    # 获取发送者邮箱
    sender_email = input("请输入您的QQ邮箱地址: ").strip()
    if not sender_email.endswith('@qq.com'):
        print("❌ 请输入有效的QQ邮箱地址")
        return
    
    # 检查附件文件
    attachment_file = "弹幕说他是反派，但他在救我狗命。.docx"
    import os
    if not os.path.exists(attachment_file):
        print(f"❌ 附件文件不存在: {attachment_file}")
        return
    
    print(f"\n将发送带附件的测试邮件给您自己: {sender_email}")
    print(f"附件: {attachment_file}")
    
    confirm = input("\n确认发送带附件的测试邮件吗？(y/n): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    try:
        # 创建邮件发送器
        sender = EmailSender(sender_email)
        
        # 发送带附件的测试邮件
        print("\n开始发送带附件的测试邮件...")
        success = sender.send_email(
            to_emails=[sender_email],  # 发送给自己
            subject="带附件的邮件发送功能测试",
            body="这是一封带附件的测试邮件。\n\n如果您收到这封邮件和附件，说明带附件发送功能正常。",
            attachments=[attachment_file]
        )
        
        if success:
            print("✅ 带附件的测试邮件发送成功！")
            print("请检查您的邮箱收件箱和附件")
        else:
            print("❌ 带附件的测试邮件发送失败")
            print("可能的原因：")
            print("1. 附件太大")
            print("2. 附件格式不支持")
            print("3. 邮件服务器限制")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")

def main():
    """主函数"""
    print("邮件发送功能测试工具")
    print("=" * 40)
    print("此工具将帮助您验证邮件发送功能是否正常")
    print("=" * 40)
    
    while True:
        print("\n请选择测试类型：")
        print("1. 测试基本邮件发送（无附件）")
        print("2. 测试带附件的邮件发送")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            test_send_to_self()
        elif choice == '2':
            test_send_with_attachment()
        elif choice == '3':
            print("测试结束")
            break
        else:
            print("❌ 无效选择，请输入 1-3")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n程序出错: {str(e)}")
    
    input("\n按回车键退出...")
