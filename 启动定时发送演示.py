#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 定时发送功能演示脚本
展示如何使用定时发送管理器创建和管理定时任务
"""

import datetime
import uuid
import time
from schedule_manager import ScheduleManager, ScheduledTask

def create_demo_task():
    """创建演示定时任务"""
    print("🎯 创建演示定时任务")
    print("=" * 40)
    
    # 创建管理器
    manager = ScheduleManager()
    
    # 创建一个5分钟后执行的演示任务
    demo_task = ScheduledTask(
        id=str(uuid.uuid4()),
        name="定时发送演示任务",
        sender_email="<EMAIL>",
        recipient_emails="<EMAIL>\<EMAIL>",
        subject="🕐 定时发送演示邮件",
        body=f"""这是一封定时发送的演示邮件！

📅 创建时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
⏰ 计划执行时间: {(datetime.datetime.now() + datetime.timedelta(minutes=5)).strftime('%Y-%m-%d %H:%M:%S')}

✨ 功能特性:
- 🛡️ 反垃圾策略: 适中模式
- 📡 自动回复监控: 已启用
- 📊 质量数据库: 已启用
- 🆘 应急系统: 已启用
- 🔗 深度协调: 已启用

这封邮件展示了定时发送系统的完整功能集成！

---
定时发送系统 v1.0
""",
        attachments=[],
        scheduled_time=(datetime.datetime.now() + datetime.timedelta(minutes=5)).isoformat(),
        send_mode="standard",
        anti_spam_strategy="moderate",
        enable_monitoring=True,
        enable_quality_db=True,
        enable_emergency=True,
        enable_coordination=True,
        status="pending",
        created_time=datetime.datetime.now().isoformat()
    )
    
    # 添加任务
    if manager.add_scheduled_task(demo_task):
        print(f"✅ 演示任务创建成功!")
        print(f"   任务ID: {demo_task.id}")
        print(f"   任务名称: {demo_task.name}")
        print(f"   执行时间: {demo_task.scheduled_time}")
        print(f"   发送模式: {demo_task.send_mode}")
        print(f"   反垃圾策略: {demo_task.anti_spam_strategy}")
        
        return demo_task.id
    else:
        print("❌ 演示任务创建失败")
        return None

def show_task_status(task_id):
    """显示任务状态"""
    print("\n📋 任务状态监控")
    print("=" * 40)
    
    manager = ScheduleManager()
    
    for i in range(10):  # 监控10次
        all_tasks = manager.get_all_tasks()
        
        for task in all_tasks:
            if task.id == task_id:
                print(f"⏰ {datetime.datetime.now().strftime('%H:%M:%S')} - 任务状态: {task.status}")
                
                if task.status == 'completed':
                    print("🎉 任务执行完成!")
                    if task.result:
                        print(f"   执行结果: {task.result}")
                    return
                elif task.status == 'failed':
                    print("❌ 任务执行失败!")
                    if task.result:
                        print(f"   失败原因: {task.result}")
                    return
                
                break
        
        time.sleep(30)  # 每30秒检查一次
    
    print("⏰ 监控结束")

def show_statistics():
    """显示统计信息"""
    print("\n📊 系统统计信息")
    print("=" * 40)
    
    manager = ScheduleManager()
    stats = manager.get_task_statistics()
    
    if stats:
        print(f"📈 总任务数: {stats.get('total_tasks', 0)}")
        print(f"⏳ 待执行: {stats.get('pending', 0)}")
        print(f"🔄 执行中: {stats.get('running', 0)}")
        print(f"✅ 已完成: {stats.get('completed', 0)}")
        print(f"❌ 失败: {stats.get('failed', 0)}")
        print(f"⏸️ 已取消: {stats.get('cancelled', 0)}")
        print(f"📊 成功率: {stats.get('success_rate', 0)}%")
        print(f"📅 今日执行: {stats.get('today_executed', 0)}")
    else:
        print("❌ 无法获取统计信息")

def show_optimal_time():
    """显示最佳时间分析"""
    print("\n🕐 最佳时间分析")
    print("=" * 40)
    
    manager = ScheduleManager()
    optimal_time = manager.get_optimal_send_time("<EMAIL>")
    
    print(f"🎯 推荐最佳发送时间: {optimal_time.strftime('%Y-%m-%d %H:%M')}")
    print(f"📝 说明: 基于历史数据分析得出的最佳发送时间")

def list_all_tasks():
    """列出所有任务"""
    print("\n📋 所有定时任务")
    print("=" * 40)
    
    manager = ScheduleManager()
    tasks = manager.get_all_tasks()
    
    if tasks:
        for i, task in enumerate(tasks, 1):
            status_emoji = {
                'pending': '⏳',
                'running': '🔄',
                'completed': '✅',
                'failed': '❌',
                'cancelled': '⏸️'
            }.get(task.status, '❓')
            
            print(f"{i}. {status_emoji} {task.name}")
            print(f"   ID: {task.id[:8]}...")
            print(f"   执行时间: {task.scheduled_time}")
            print(f"   状态: {task.status}")
            print(f"   发送者: {task.sender_email}")
            print()
    else:
        print("📭 暂无定时任务")

def cleanup_demo_tasks():
    """清理演示任务"""
    print("\n🧹 清理演示任务")
    print("=" * 40)
    
    manager = ScheduleManager()
    tasks = manager.get_all_tasks()
    
    demo_tasks = [task for task in tasks if "演示" in task.name or "demo" in task.sender_email.lower()]
    
    if demo_tasks:
        print(f"找到 {len(demo_tasks)} 个演示任务")
        
        for task in demo_tasks:
            if manager.delete_task(task.id):
                print(f"✅ 已删除: {task.name}")
            else:
                print(f"❌ 删除失败: {task.name}")
    else:
        print("📭 没有找到演示任务")

def main():
    """主函数"""
    print("🚀 定时发送功能演示")
    print("=" * 50)
    print("这个演示将展示定时发送系统的主要功能")
    print()
    
    while True:
        print("\n📋 请选择操作:")
        print("1. 创建演示定时任务")
        print("2. 查看任务状态")
        print("3. 显示统计信息")
        print("4. 显示最佳时间分析")
        print("5. 列出所有任务")
        print("6. 清理演示任务")
        print("0. 退出")
        
        try:
            choice = input("\n请输入选项 (0-6): ").strip()
            
            if choice == '0':
                print("👋 再见!")
                break
            elif choice == '1':
                task_id = create_demo_task()
                if task_id:
                    print(f"\n💡 提示: 任务将在5分钟后执行")
                    print(f"   您可以选择选项2来监控任务状态")
            elif choice == '2':
                tasks = ScheduleManager().get_all_tasks()
                if tasks:
                    print("\n📋 选择要监控的任务:")
                    for i, task in enumerate(tasks, 1):
                        print(f"{i}. {task.name} ({task.status})")
                    
                    try:
                        task_index = int(input("请输入任务编号: ")) - 1
                        if 0 <= task_index < len(tasks):
                            show_task_status(tasks[task_index].id)
                        else:
                            print("❌ 无效的任务编号")
                    except ValueError:
                        print("❌ 请输入有效的数字")
                else:
                    print("📭 暂无任务可监控")
            elif choice == '3':
                show_statistics()
            elif choice == '4':
                show_optimal_time()
            elif choice == '5':
                list_all_tasks()
            elif choice == '6':
                cleanup_demo_tasks()
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n\n👋 用户中断，退出演示")
            break
        except Exception as e:
            print(f"\n❌ 操作失败: {str(e)}")

if __name__ == "__main__":
    main()
