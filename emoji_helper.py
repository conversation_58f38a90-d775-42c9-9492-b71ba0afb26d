# -*- coding: utf-8 -*-
"""
Emoji输入助手 - 解决Windows Tkinter中Emoji输入和显示问题
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import os

class EmojiHelper:
    """Emoji输入助手类"""
    
    def __init__(self):
        self.emoji_data = {
            "笑脸表情": {
                "😀": "开心", "😃": "大笑", "😄": "哈哈", "😁": "嘿嘿", "😆": "笑哭",
                "😅": "苦笑", "😂": "笑哭", "🤣": "狂笑", "😊": "微笑", "😇": "天使",
                "🙂": "微笑", "🙃": "倒脸", "😉": "眨眼", "😌": "满足", "😍": "爱心眼",
                "🥰": "爱心脸", "😘": "飞吻", "😗": "亲亲", "😙": "亲亲", "😚": "亲亲"
            },
            "爱心表情": {
                "❤️": "红心", "🧡": "橙心", "💛": "黄心", "💚": "绿心", "💙": "蓝心",
                "💜": "紫心", "🖤": "黑心", "🤍": "白心", "🤎": "棕心", "💔": "碎心",
                "❣️": "感叹心", "💕": "两心", "💞": "旋转心", "💓": "跳动心", "💗": "成长心",
                "💖": "闪亮心", "💘": "丘比特", "💝": "心礼盒", "💟": "心装饰", "☮️": "和平"
            },
            "手势表情": {
                "👍": "点赞", "👎": "踩", "👌": "OK", "🤏": "捏", "✌️": "胜利",
                "🤞": "交叉", "🤟": "爱你", "🤘": "摇滚", "🤙": "电话", "👈": "左指",
                "👉": "右指", "👆": "上指", "👇": "下指", "☝️": "食指", "👋": "挥手",
                "🤚": "举手", "🖐️": "张手", "✋": "停止", "🖖": "瓦肯", "👏": "鼓掌"
            },
            "动物表情": {
                "🐶": "狗", "🐱": "猫", "🐭": "鼠", "🐹": "仓鼠", "🐰": "兔子",
                "🦊": "狐狸", "🐻": "熊", "🐼": "熊猫", "🐨": "考拉", "🐯": "老虎",
                "🦁": "狮子", "🐮": "牛", "🐷": "猪", "🐸": "青蛙", "🐵": "猴子",
                "🙈": "非礼勿视", "🙉": "非礼勿听", "🙊": "非礼勿言", "🐒": "猴子", "🐔": "鸡"
            },
            "食物表情": {
                "🍎": "苹果", "🍊": "橙子", "🍋": "柠檬", "🍌": "香蕉", "🍉": "西瓜",
                "🍇": "葡萄", "🍓": "草莓", "🫐": "蓝莓", "🍈": "甜瓜", "🍒": "樱桃",
                "🍑": "桃子", "🥭": "芒果", "🍍": "菠萝", "🥥": "椰子", "🥝": "猕猴桃",
                "🍅": "番茄", "🍆": "茄子", "🥑": "牛油果", "🥦": "西兰花", "🥬": "生菜"
            },
            "活动表情": {
                "⚽": "足球", "🏀": "篮球", "🏈": "橄榄球", "⚾": "棒球", "🥎": "垒球",
                "🎾": "网球", "🏐": "排球", "🏉": "橄榄球", "🥏": "飞盘", "🎱": "台球",
                "🪀": "悠悠球", "🏓": "乒乓球", "🏸": "羽毛球", "🏒": "冰球", "🏑": "曲棍球",
                "🥍": "长曲棍球", "🏏": "板球", "🪃": "回旋镖", "🥅": "球门", "⛳": "高尔夫"
            }
        }
    
    def create_emoji_window(self, parent_widget=None):
        """创建Emoji选择窗口"""
        emoji_window = tk.Toplevel()
        emoji_window.title("Emoji表情选择器")
        emoji_window.geometry("600x500")
        emoji_window.resizable(True, True)
        
        # 如果有父组件，设置为模态窗口
        if parent_widget:
            emoji_window.transient(parent_widget.winfo_toplevel())
            emoji_window.grab_set()
        
        # 主框架
        main_frame = ttk.Frame(emoji_window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        emoji_window.columnconfigure(0, weight=1)
        emoji_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="Emoji表情选择器", 
                               font=('Arial', 14, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 分类列表
        category_frame = ttk.LabelFrame(main_frame, text="分类", padding="5")
        category_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        category_frame.rowconfigure(0, weight=1)
        
        category_listbox = tk.Listbox(category_frame, width=15)
        category_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        for category in self.emoji_data.keys():
            category_listbox.insert(tk.END, category)
        
        # Emoji显示区域
        emoji_frame = ttk.LabelFrame(main_frame, text="表情", padding="5")
        emoji_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        emoji_frame.columnconfigure(0, weight=1)
        emoji_frame.rowconfigure(0, weight=1)
        
        # 创建滚动文本框显示Emoji
        emoji_text = scrolledtext.ScrolledText(emoji_frame, width=40, height=20,
                                              font=('Segoe UI Emoji', 12),
                                              wrap=tk.WORD)
        emoji_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 选中的Emoji显示
        selected_frame = ttk.Frame(main_frame)
        selected_frame.grid(row=2, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))
        selected_frame.columnconfigure(1, weight=1)
        
        ttk.Label(selected_frame, text="选中的表情:").grid(row=0, column=0, padx=(0, 10))
        selected_var = tk.StringVar()
        selected_entry = ttk.Entry(selected_frame, textvariable=selected_var, 
                                  font=('Segoe UI Emoji', 12), width=30)
        selected_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        def on_category_select(event):
            """分类选择事件"""
            selection = category_listbox.curselection()
            if selection:
                category = category_listbox.get(selection[0])
                emoji_text.delete(1.0, tk.END)
                
                emojis = self.emoji_data[category]
                for emoji, desc in emojis.items():
                    emoji_text.insert(tk.END, f"{emoji} {desc}\n")
                    
                # 绑定点击事件
                emoji_text.bind("<Button-1>", lambda e: self.on_emoji_click(e, emoji_text, selected_var))
        
        category_listbox.bind('<<ListboxSelect>>', on_category_select)
        
        # 按钮
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=3, column=0, columnspan=2, pady=10)
        
        def copy_emoji():
            """复制选中的Emoji到剪贴板"""
            emoji = selected_var.get()
            if emoji:
                emoji_window.clipboard_clear()
                emoji_window.clipboard_append(emoji)
                messagebox.showinfo("成功", f"已复制 {emoji} 到剪贴板")
            else:
                messagebox.showwarning("提示", "请先选择一个表情")
        
        def insert_to_parent():
            """插入到父组件"""
            emoji = selected_var.get()
            if emoji and parent_widget:
                try:
                    cursor_pos = parent_widget.index(tk.INSERT)
                    parent_widget.insert(cursor_pos, emoji)
                    parent_widget.focus_set()
                    emoji_window.destroy()
                except Exception as e:
                    messagebox.showerror("错误", f"插入失败: {str(e)}")
            else:
                messagebox.showwarning("提示", "请先选择一个表情")
        
        def open_system_emoji():
            """打开系统Emoji面板"""
            try:
                if os.name == 'nt':  # Windows系统
                    # 发送Win+. 快捷键
                    import win32api
                    import win32con
                    win32api.keybd_event(0x5B, 0, 0, 0)  # Win键按下
                    win32api.keybd_event(0xBE, 0, 0, 0)  # .键按下
                    win32api.keybd_event(0xBE, 0, win32con.KEYEVENTF_KEYUP, 0)  # .键释放
                    win32api.keybd_event(0x5B, 0, win32con.KEYEVENTF_KEYUP, 0)  # Win键释放
                    messagebox.showinfo("提示", "已打开Windows Emoji面板")
                else:
                    messagebox.showinfo("提示", "请使用 Win+. 快捷键打开系统Emoji面板")
            except ImportError:
                messagebox.showinfo("提示", "请手动使用 Win+. 快捷键打开系统Emoji面板")
            except Exception as e:
                messagebox.showerror("错误", f"打开系统Emoji面板失败: {str(e)}")
        
        ttk.Button(btn_frame, text="复制到剪贴板", command=copy_emoji).pack(side=tk.LEFT, padx=5)
        if parent_widget:
            ttk.Button(btn_frame, text="插入到正文", command=insert_to_parent).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="打开系统Emoji面板", command=open_system_emoji).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="关闭", command=emoji_window.destroy).pack(side=tk.LEFT, padx=5)
        
        # 使用说明
        info_label = ttk.Label(main_frame, 
                              text="使用说明：1. 选择分类 2. 点击表情 3. 复制或插入\n"
                                   "提示：也可以使用 Win+. 快捷键打开Windows系统Emoji面板",
                              font=('Arial', 9), foreground='gray', justify=tk.CENTER)
        info_label.grid(row=4, column=0, columnspan=2, pady=5)
        
        # 默认选择第一个分类
        if self.emoji_data:
            category_listbox.selection_set(0)
            on_category_select(None)
        
        return emoji_window
    
    def on_emoji_click(self, event, text_widget, selected_var):
        """处理Emoji点击事件"""
        try:
            # 获取点击位置的字符
            index = text_widget.index(f"@{event.x},{event.y}")
            line_start = text_widget.index(f"{index} linestart")
            line_end = text_widget.index(f"{index} lineend")
            line_text = text_widget.get(line_start, line_end)
            
            # 提取Emoji（第一个字符）
            if line_text:
                emoji = line_text[0]
                selected_var.set(emoji)
        except Exception as e:
            print(f"点击事件处理失败: {str(e)}")

def main():
    """测试函数"""
    root = tk.Tk()
    root.title("Emoji助手测试")
    root.geometry("400x300")
    
    # 测试文本框
    text_widget = scrolledtext.ScrolledText(root, width=40, height=10,
                                           font=('Segoe UI Emoji', 10))
    text_widget.pack(pady=20, padx=20, fill=tk.BOTH, expand=True)
    
    helper = EmojiHelper()
    
    def open_emoji_helper():
        helper.create_emoji_window(text_widget)
    
    ttk.Button(root, text="打开Emoji选择器", command=open_emoji_helper).pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()
