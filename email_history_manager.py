# -*- coding: utf-8 -*-
"""
邮件发送历史记录管理器
支持持久化存储、重复检测、RAG检索功能
"""

import json
import os
import hashlib
import datetime
import logging
from typing import List, Dict, Set, Tuple, Optional
import sqlite3
from dataclasses import dataclass, asdict
import re

@dataclass
class EmailRecord:
    """邮件记录数据类"""
    id: str
    sender_email: str
    recipient_email: str
    subject: str
    body: str
    content_hash: str  # 邮件内容哈希值
    send_time: str
    success: bool
    batch_id: Optional[str] = None
    attachments: Optional[List[str]] = None

class EmailHistoryManager:
    """邮件发送历史记录管理器"""
    
    def __init__(self, db_path: str = "email_history.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"初始化邮件历史记录管理器，数据库路径: {db_path}")
        try:
            self.init_database()
            self.logger.info("邮件历史记录数据库初始化成功")
        except Exception as e:
            self.logger.error(f"邮件历史记录数据库初始化失败: {str(e)}", exc_info=True)
            raise
    
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建邮件记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_records (
                id TEXT PRIMARY KEY,
                sender_email TEXT NOT NULL,
                recipient_email TEXT NOT NULL,
                subject TEXT NOT NULL,
                body TEXT NOT NULL,
                content_hash TEXT NOT NULL,
                send_time TEXT NOT NULL,
                success INTEGER NOT NULL,
                batch_id TEXT,
                attachments TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 创建索引以提高查询性能
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_content_hash ON email_records(content_hash)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_sender_recipient ON email_records(sender_email, recipient_email)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_send_time ON email_records(send_time)')
        
        conn.commit()
        conn.close()
    
    def generate_content_hash(self, subject: str, body: str, attachments: List[str] = None) -> str:
        """生成邮件内容哈希值"""
        # 标准化内容（去除多余空格、换行等）
        normalized_subject = re.sub(r'\s+', ' ', subject.strip())
        normalized_body = re.sub(r'\s+', ' ', body.strip())
        
        # 包含附件信息
        attachment_str = ""
        if attachments:
            # 只使用文件名，不包含路径
            attachment_names = [os.path.basename(att) for att in attachments]
            attachment_str = "|".join(sorted(attachment_names))
        
        # 生成哈希
        content = f"{normalized_subject}|{normalized_body}|{attachment_str}"
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def add_email_record(self, sender_email: str, recipient_email: str, 
                        subject: str, body: str, success: bool,
                        batch_id: str = None, attachments: List[str] = None) -> str:
        """添加邮件发送记录"""
        record_id = f"{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_{hashlib.md5(f'{sender_email}{recipient_email}'.encode()).hexdigest()[:8]}"
        content_hash = self.generate_content_hash(subject, body, attachments)
        send_time = datetime.datetime.now().isoformat()
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO email_records 
            (id, sender_email, recipient_email, subject, body, content_hash, 
             send_time, success, batch_id, attachments)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            record_id, sender_email, recipient_email, subject, body, 
            content_hash, send_time, int(success), batch_id,
            json.dumps(attachments) if attachments else None
        ))
        
        conn.commit()
        conn.close()
        
        return record_id
    
    def check_duplicate_emails(self, sender_email: str, subject: str, body: str, 
                             recipient_emails: List[str], attachments: List[str] = None) -> Dict:
        """检查重复邮件发送"""
        content_hash = self.generate_content_hash(subject, body, attachments)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 查找相同内容的成功发送记录
        cursor.execute('''
            SELECT recipient_email, send_time, id 
            FROM email_records 
            WHERE sender_email = ? AND content_hash = ? AND success = 1
            ORDER BY send_time DESC
        ''', (sender_email, content_hash))
        
        sent_records = cursor.fetchall()
        conn.close()
        
        # 分析结果
        sent_recipients = set(record[0] for record in sent_records)
        new_recipients = []
        duplicate_recipients = []
        
        for email in recipient_emails:
            email = email.strip()
            if email in sent_recipients:
                # 找到最近的发送记录
                latest_record = next((r for r in sent_records if r[0] == email), None)
                duplicate_recipients.append({
                    'email': email,
                    'last_sent': latest_record[1] if latest_record else None,
                    'record_id': latest_record[2] if latest_record else None
                })
            else:
                new_recipients.append(email)
        
        return {
            'content_hash': content_hash,
            'total_recipients': len(recipient_emails),
            'new_recipients': new_recipients,
            'duplicate_recipients': duplicate_recipients,
            'has_duplicates': len(duplicate_recipients) > 0,
            'can_send_to_new': len(new_recipients) > 0
        }
    
    def search_email_history(self, query: str, sender_email: str = None, 
                           limit: int = 50) -> List[Dict]:
        """RAG检索邮件历史记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 构建搜索条件
        where_conditions = []
        params = []
        
        if sender_email:
            where_conditions.append("sender_email = ?")
            params.append(sender_email)
        
        # 简单的文本搜索（可以后续升级为更复杂的RAG）
        if query:
            search_condition = "(subject LIKE ? OR body LIKE ? OR recipient_email LIKE ?)"
            where_conditions.append(search_condition)
            search_term = f"%{query}%"
            params.extend([search_term, search_term, search_term])
        
        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"
        
        cursor.execute(f'''
            SELECT id, sender_email, recipient_email, subject, body, 
                   send_time, success, batch_id, attachments
            FROM email_records 
            WHERE {where_clause}
            ORDER BY send_time DESC
            LIMIT ?
        ''', params + [limit])
        
        records = cursor.fetchall()
        conn.close()
        
        # 转换为字典格式
        result = []
        for record in records:
            # 确保时间字段不为None
            send_time = record[5] if record[5] else datetime.datetime.now().isoformat()

            result.append({
                'id': record[0],
                'sender_email': record[1],
                'recipient_email': record[2],
                'subject': record[3],
                'body': record[4][:200] + "..." if len(record[4]) > 200 else record[4],
                'send_time': send_time,
                'success': bool(record[6]),
                'batch_id': record[7],
                'attachments': json.loads(record[8]) if record[8] else None
            })
        
        return result
    
    def get_statistics(self, sender_email: str = None) -> Dict:
        """获取发送统计信息"""
        try:
            self.logger.debug(f"获取统计信息 - 发件人: {sender_email}")

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

        except Exception as e:
            self.logger.error(f"获取统计信息时数据库连接失败: {str(e)}", exc_info=True)
            return {
                'total_emails': 0,
                'successful_emails': 0,
                'failed_emails': 0,
                'unique_recipients': 0,
                'unique_contents': 0,
                'today_sent': 0,
                'success_rate': 0
            }
        
        try:
            where_clause = "WHERE sender_email = ?" if sender_email else ""
            params = [sender_email] if sender_email else []

            # 总体统计
            cursor.execute(f'''
                SELECT
                    COUNT(*) as total_emails,
                    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) as successful_emails,
                    COUNT(DISTINCT recipient_email) as unique_recipients,
                    COUNT(DISTINCT content_hash) as unique_contents
                FROM email_records
                {where_clause}
            ''', params)

            stats = cursor.fetchone()
            self.logger.debug(f"总体统计查询结果: {stats}")

            # 最近发送统计
            cursor.execute(f'''
                SELECT COUNT(*)
                FROM email_records
                {where_clause}
                {"AND" if where_clause else "WHERE"}
                date(send_time) = date('now')
            ''', params)

            today_count = cursor.fetchone()[0]
            self.logger.debug(f"今日发送统计: {today_count}")

            conn.close()

        except Exception as e:
            self.logger.error(f"统计查询执行失败: {str(e)}", exc_info=True)
            conn.close()
            return {
                'total_emails': 0,
                'successful_emails': 0,
                'failed_emails': 0,
                'unique_recipients': 0,
                'unique_contents': 0,
                'today_sent': 0,
                'success_rate': 0
            }
        
        # 安全处理统计数据，防止None值
        total_emails = stats[0] if stats[0] is not None else 0
        successful_emails = stats[1] if stats[1] is not None else 0
        unique_recipients = stats[2] if stats[2] is not None else 0
        unique_contents = stats[3] if stats[3] is not None else 0
        today_sent = today_count if today_count is not None else 0

        return {
            'total_emails': total_emails,
            'successful_emails': successful_emails,
            'failed_emails': total_emails - successful_emails,
            'unique_recipients': unique_recipients,
            'unique_contents': unique_contents,
            'today_sent': today_sent,
            'success_rate': (successful_emails / total_emails * 100) if total_emails > 0 else 0
        }
    
    def export_history(self, filepath: str, sender_email: str = None):
        """导出历史记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        where_clause = "WHERE sender_email = ?" if sender_email else ""
        params = [sender_email] if sender_email else []
        
        cursor.execute(f'''
            SELECT * FROM email_records 
            {where_clause}
            ORDER BY send_time DESC
        ''', params)
        
        records = cursor.fetchall()
        conn.close()
        
        # 转换为JSON格式导出
        export_data = []
        for record in records:
            export_data.append({
                'id': record[0],
                'sender_email': record[1],
                'recipient_email': record[2],
                'subject': record[3],
                'body': record[4],
                'content_hash': record[5],
                'send_time': record[6],
                'success': bool(record[7]),
                'batch_id': record[8],
                'attachments': json.loads(record[9]) if record[9] else None
            })
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
    
    def cleanup_old_records(self, days_to_keep: int = 365):
        """清理旧记录"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cutoff_date = (datetime.datetime.now() - datetime.timedelta(days=days_to_keep)).isoformat()
        
        cursor.execute('DELETE FROM email_records WHERE send_time < ?', (cutoff_date,))
        deleted_count = cursor.rowcount
        
        conn.commit()
        conn.close()
        
        return deleted_count
