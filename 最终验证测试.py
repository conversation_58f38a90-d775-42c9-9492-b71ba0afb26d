#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证测试 - 一键启用和重置功能
"""

import tkinter as tk
import time
from gui_main import EmailSenderGUI

def test_full_cycle():
    """测试完整的启用-重置循环"""
    print("🚀 最终验证测试 - 一键启用和重置功能")
    print("=" * 80)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建GUI实例
        app = EmailSenderGUI(root)
        
        print("✅ GUI创建成功")
        
        # 设置测试邮箱
        test_email = "<EMAIL>"
        app.sender_email.delete(0, tk.END)
        app.sender_email.insert(0, test_email)
        print(f"✅ 设置测试邮箱: {test_email}")
        
        # 检查初始状态
        print(f"\n📊 初始状态:")
        print(f"  全功能模式: {getattr(app, 'all_features_enabled', False)}")
        print(f"  一键启用按钮状态: {app.enable_all_button['state']}")
        print(f"  重置按钮状态: {app.reset_all_button['state']}")
        
        # 测试一键启用功能
        print(f"\n🚀 测试一键启用功能...")
        
        # 模拟启用过程
        test_steps = [
            ("初始化系统组件", app._init_all_components),
            ("启用自动回复监控", app._enable_auto_reply_monitoring),
            ("启用质量数据库", app._enable_quality_database),
            ("启用反垃圾邮件", app._enable_anti_spam),
            ("启用QQ应急管理", app._enable_qq_emergency),
            ("启用智能队列", app._enable_smart_queue),
            ("启用深度协调", app._enable_deep_coordination),
            ("完成全功能配置", app._finalize_all_features)
        ]
        
        enable_success = True
        for step_name, step_func in test_steps:
            try:
                result = step_func(test_email)
                if result['success']:
                    print(f"  ✅ {step_name}")
                else:
                    print(f"  ❌ {step_name}: {result['error']}")
                    enable_success = False
            except Exception as e:
                print(f"  ❌ {step_name}: {str(e)}")
                enable_success = False
        
        if enable_success:
            print(f"\n📈 启用后状态:")
            print(f"  全功能模式: {getattr(app, 'all_features_enabled', False)}")
            print(f"  一键启用按钮状态: {app.enable_all_button['state']}")
            print(f"  重置按钮状态: {app.reset_all_button['state']}")
            print(f"  功能状态: {getattr(app, 'feature_status', {})}")
        
        # 测试重置功能
        print(f"\n🔄 测试重置功能...")
        
        reset_steps = [
            ("重置自动回复监控", app._reset_auto_reply_monitoring),
            ("重置质量数据库", app._reset_quality_database),
            ("重置反垃圾邮件", app._reset_anti_spam),
            ("重置QQ应急管理", app._reset_qq_emergency),
            ("重置智能队列", app._reset_smart_queue),
            ("重置深度协调", app._reset_deep_coordination),
            ("清理配置文件", app._cleanup_all_features_config),
            ("完成重置操作", app._finalize_reset)
        ]
        
        reset_success = True
        for step_name, step_func in reset_steps:
            try:
                result = step_func()
                if result['success']:
                    print(f"  ✅ {step_name}")
                else:
                    print(f"  ❌ {step_name}: {result['error']}")
                    reset_success = False
            except Exception as e:
                print(f"  ❌ {step_name}: {str(e)}")
                reset_success = False
        
        if reset_success:
            print(f"\n📉 重置后状态:")
            print(f"  全功能模式: {getattr(app, 'all_features_enabled', False)}")
            print(f"  一键启用按钮状态: {app.enable_all_button['state']}")
            print(f"  重置按钮状态: {app.reset_all_button['state']}")
            print(f"  功能状态: {getattr(app, 'feature_status', {})}")
        
        # 测试自动监控功能
        print(f"\n📡 测试自动监控功能...")
        
        # 设置收件人
        app.recipient_emails.delete(1.0, tk.END)
        app.recipient_emails.insert(1.0, test_email)
        
        try:
            # 启动自动监控
            if hasattr(app, 'start_auto_monitoring'):
                app.start_auto_monitoring()
                print("  ✅ 自动监控启动成功")
            else:
                print("  ⚠️ 自动监控方法不存在")
        except Exception as e:
            print(f"  ❌ 自动监控启动失败: {str(e)}")
        
        # 检查数据库状态
        print(f"\n🗄️ 检查数据库状态...")
        
        try:
            # 检查质量数据库
            if hasattr(app, 'quality_manager') and app.quality_manager:
                recipients = app.quality_manager.get_quality_recipients(min_quality_score=0)
                print(f"  ✅ 质量数据库收件人数量: {len(recipients)}")
            else:
                print("  ⚠️ 质量管理器未初始化")
            
            # 检查历史记录
            if hasattr(app, 'history_manager') and app.history_manager:
                history = app.history_manager.search_email_history("", test_email, limit=10)
                print(f"  ✅ 邮件历史记录数量: {len(history)}")
            else:
                print("  ⚠️ 历史管理器未初始化")
                
        except Exception as e:
            print(f"  ❌ 数据库检查失败: {str(e)}")
        
        # 关闭窗口
        root.destroy()
        
        # 最终结果
        print(f"\n" + "=" * 80)
        if enable_success and reset_success:
            print("🎉 最终验证测试完全成功！")
            print("✅ 一键启用功能正常工作")
            print("✅ 一键重置功能正常工作")
            print("✅ 按钮状态切换正确")
            print("✅ 数据库操作正常")
            print("✅ 自动监控功能可用")
            print("\n💡 系统现在完全可用，您可以：")
            print("  🚀 使用一键启用功能启用所有高级功能")
            print("  🔄 使用一键重置功能恢复到基础模式")
            print("  📡 享受自动监控和智能分析功能")
            print("  📊 使用质量数据库管理收件人")
            return True
        else:
            print("⚠️ 最终验证测试存在问题")
            print(f"  启用功能: {'✅ 正常' if enable_success else '❌ 异常'}")
            print(f"  重置功能: {'✅ 正常' if reset_success else '❌ 异常'}")
            return False
        
    except Exception as e:
        print(f"❌ 最终验证测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_monitoring_with_real_email():
    """测试真实邮箱的监控功能"""
    print(f"\n📧 测试真实邮箱监控功能")
    print("=" * 60)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建GUI实例
        app = EmailSenderGUI(root)
        
        # 设置真实邮箱
        real_email = "<EMAIL>"
        app.sender_email.delete(0, tk.END)
        app.sender_email.insert(0, real_email)
        
        # 设置收件人
        app.recipient_emails.delete(1.0, tk.END)
        app.recipient_emails.insert(1.0, real_email)
        
        print(f"✅ 设置邮箱: {real_email}")
        
        # 启用全功能模式
        enable_result = app._init_all_components(real_email)
        if enable_result['success']:
            print("✅ 系统组件初始化成功")
        else:
            print(f"❌ 系统组件初始化失败: {enable_result['error']}")
        
        # 测试质量数据库查询
        try:
            if hasattr(app, 'quality_manager') and app.quality_manager:
                # 添加测试数据
                app.quality_manager.update_recipient_quality(
                    email=real_email,
                    sender_email=real_email,
                    subject="测试邮件",
                    body="测试内容",
                    success=True
                )
                print("✅ 添加测试质量数据成功")
                
                # 查询数据
                recipients = app.quality_manager.get_quality_recipients(min_quality_score=0)
                print(f"✅ 查询到 {len(recipients)} 个收件人")
                
        except Exception as e:
            print(f"❌ 质量数据库测试失败: {str(e)}")
        
        # 关闭窗口
        root.destroy()
        
        print("✅ 真实邮箱监控测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 真实邮箱监控测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🎯 开始最终验证测试")
    print("=" * 100)
    
    # 测试完整循环
    cycle_success = test_full_cycle()
    
    # 测试真实邮箱监控
    monitor_success = test_monitoring_with_real_email()
    
    # 总结
    print("\n" + "=" * 100)
    print("📊 最终验证结果:")
    print(f"  完整功能循环: {'✅ 成功' if cycle_success else '❌ 失败'}")
    print(f"  真实邮箱监控: {'✅ 成功' if monitor_success else '❌ 失败'}")
    
    if cycle_success and monitor_success:
        print("\n🎉 恭喜！所有功能验证通过！")
        print("🚀 一键启用和重置功能已完全修复并可正常使用")
        print("📡 自动监控功能正常工作")
        print("🗄️ 数据库系统运行正常")
        print("\n💡 现在您可以放心使用所有功能了！")
        return True
    else:
        print("\n⚠️ 部分功能验证失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
