#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强化版自动修复工具 v2.0
真正的自动修复，不只是检测！
检测到问题立即自动修复，绝不手软！
"""

import os
import sys
import ast
import re
import shutil
import time
import logging
from datetime import datetime

class PowerfulAutoFixer:
    """强化版自动修复器"""
    
    def __init__(self):
        self.setup_logging()
        self.fixed_count = 0
        self.error_count = 0
        self.backup_count = 0
        
    def setup_logging(self):
        """设置日志"""
        os.makedirs('logs', exist_ok=True)
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/powerful_auto_fixer.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def fix_all_problems_now(self):
        """立即修复所有问题 - 不只是检测！"""
        print("🔧 强化版自动修复工具 v2.0")
        print("=" * 50)
        print("💪 真正的自动修复，检测到问题立即修复！")
        print("=" * 50)
        
        try:
            # 1. 立即修复语法错误
            print("\n🔧 第一步：立即修复语法错误...")
            self.fix_syntax_errors_immediately()
            
            # 2. 立即修复缺失文件
            print("\n🔧 第二步：立即修复缺失文件...")
            self.fix_missing_files_immediately()
            
            # 3. 立即修复缺失目录
            print("\n🔧 第三步：立即修复缺失目录...")
            self.fix_missing_directories_immediately()
            
            # 4. 立即修复配置文件
            print("\n🔧 第四步：立即修复配置文件...")
            self.fix_missing_configs_immediately()
            
            # 5. 立即修复数据库
            print("\n🔧 第五步：立即修复数据库...")
            self.fix_database_issues_immediately()
            
            # 6. 立即安装依赖
            print("\n🔧 第六步：立即安装缺失依赖...")
            self.install_dependencies_immediately()
            
            # 7. 最终验证
            print("\n✅ 第七步：最终验证修复效果...")
            self.verify_all_fixes()
            
            print(f"\n🎉 强化修复完成！")
            print(f"✅ 修复项目: {self.fixed_count}")
            print(f"❌ 错误项目: {self.error_count}")
            print(f"💾 备份文件: {self.backup_count}")
            
            return True
            
        except Exception as e:
            print(f"💥 强化修复异常: {str(e)}")
            self.logger.error(f"强化修复异常: {str(e)}")
            return False
    
    def fix_syntax_errors_immediately(self):
        """立即修复语法错误"""
        try:
            critical_files = [
                'gui_main.py', 'email_sender.py', 'email_history_manager.py',
                'rag_search_engine.py', 'recipient_quality_manager.py'
            ]
            
            for file_path in critical_files:
                if os.path.exists(file_path):
                    print(f"  🔍 检查文件: {file_path}")
                    
                    # 读取文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查语法
                    try:
                        ast.parse(content)
                        print(f"    ✅ {file_path} 语法正常")
                    except SyntaxError as e:
                        print(f"    🚨 发现语法错误: {str(e)}")
                        print(f"    🔧 立即修复...")
                        
                        # 立即修复
                        if self.fix_syntax_error_in_file(file_path, content, e):
                            print(f"    ✅ {file_path} 修复成功")
                            self.fixed_count += 1
                        else:
                            print(f"    ❌ {file_path} 修复失败")
                            self.error_count += 1
                            
        except Exception as e:
            print(f"  💥 语法修复异常: {str(e)}")
            self.error_count += 1
    
    def fix_syntax_error_in_file(self, file_path, content, syntax_error):
        """修复文件中的语法错误"""
        try:
            # 创建备份
            backup_path = f"{file_path}.backup_{int(time.time())}"
            shutil.copy2(file_path, backup_path)
            self.backup_count += 1
            print(f"      💾 已备份: {backup_path}")
            
            # 获取错误行号
            error_line = syntax_error.lineno
            error_text = syntax_error.text
            
            print(f"      📍 错误位置: 第 {error_line} 行")
            print(f"      📝 错误内容: {error_text.strip() if error_text else 'N/A'}")
            
            lines = content.split('\n')
            
            # 针对不同类型的语法错误进行修复
            if "'(' was never closed" in str(syntax_error):
                # 修复未闭合的括号
                fixed = self.fix_unclosed_parentheses(lines, error_line - 1)
            elif "invalid syntax" in str(syntax_error):
                # 修复一般语法错误
                fixed = self.fix_general_syntax_error(lines, error_line - 1, syntax_error)
            else:
                # 尝试通用修复
                fixed = self.fix_general_syntax_error(lines, error_line - 1, syntax_error)
            
            if fixed:
                # 写入修复后的内容
                fixed_content = '\n'.join(lines)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                
                # 验证修复结果
                try:
                    ast.parse(fixed_content)
                    print(f"      ✅ 语法修复成功")
                    return True
                except SyntaxError as e:
                    print(f"      ❌ 修复后仍有语法错误: {str(e)}")
                    # 恢复备份
                    shutil.copy2(backup_path, file_path)
                    return False
            else:
                print(f"      ❌ 无法自动修复此语法错误")
                return False
                
        except Exception as e:
            print(f"      💥 修复文件异常: {str(e)}")
            return False
    
    def fix_unclosed_parentheses(self, lines, error_line_index):
        """修复未闭合的括号"""
        try:
            if error_line_index >= len(lines):
                return False
            
            error_line = lines[error_line_index]
            print(f"      🔧 修复未闭合括号...")
            
            # 检查当前行的括号平衡
            open_parens = error_line.count('(')
            close_parens = error_line.count(')')
            
            if open_parens > close_parens:
                # 需要添加闭合括号
                missing_parens = open_parens - close_parens
                
                # 查找合适的位置添加闭合括号
                # 通常在下一行或者当前行末尾
                if error_line_index + 1 < len(lines):
                    next_line = lines[error_line_index + 1]
                    
                    # 如果下一行是空行或者只有空白，在那里添加闭合括号
                    if not next_line.strip():
                        lines[error_line_index + 1] = ' ' * (len(error_line) - len(error_line.lstrip())) + ')' * missing_parens
                        print(f"      ✅ 在第 {error_line_index + 2} 行添加了 {missing_parens} 个闭合括号")
                        return True
                    
                    # 如果下一行有内容，检查是否可以在行首添加
                    elif next_line.strip().startswith(')'):
                        # 已经有闭合括号，可能只是数量不够
                        existing_close = next_line.count(')')
                        if existing_close < missing_parens:
                            additional_needed = missing_parens - existing_close
                            lines[error_line_index + 1] = ')' * additional_needed + next_line
                            print(f"      ✅ 在第 {error_line_index + 2} 行添加了 {additional_needed} 个闭合括号")
                            return True
                    else:
                        # 在当前行末尾添加闭合括号
                        lines[error_line_index] = error_line.rstrip() + ')' * missing_parens
                        print(f"      ✅ 在第 {error_line_index + 1} 行末尾添加了 {missing_parens} 个闭合括号")
                        return True
                else:
                    # 在当前行末尾添加闭合括号
                    lines[error_line_index] = error_line.rstrip() + ')' * missing_parens
                    print(f"      ✅ 在第 {error_line_index + 1} 行末尾添加了 {missing_parens} 个闭合括号")
                    return True
            
            return False
            
        except Exception as e:
            print(f"      💥 修复括号异常: {str(e)}")
            return False
    
    def fix_general_syntax_error(self, lines, error_line_index, syntax_error):
        """修复一般语法错误"""
        try:
            if error_line_index >= len(lines):
                return False
            
            error_line = lines[error_line_index]
            error_msg = str(syntax_error)
            
            print(f"      🔧 修复一般语法错误: {error_msg}")
            
            # 常见的语法错误修复模式
            fixes_applied = False
            
            # 修复被注释的参数
            if '# pad_y=' in error_line:
                lines[error_line_index] = error_line.replace('# pad_y=', 'pady=')
                fixes_applied = True
                print(f"      ✅ 修复了 pad_y 参数")
            
            if '# pad_x=' in error_line:
                lines[error_line_index] = error_line.replace('# pad_x=', 'padx=')
                fixes_applied = True
                print(f"      ✅ 修复了 pad_x 参数")
            
            if '# text_variable=' in error_line:
                lines[error_line_index] = error_line.replace('# text_variable=', 'textvariable=')
                fixes_applied = True
                print(f"      ✅ 修复了 text_variable 参数")
            
            # 修复缺失的逗号
            if error_line.strip().endswith('(') and error_line_index + 1 < len(lines):
                next_line = lines[error_line_index + 1].strip()
                if next_line and not next_line.startswith(')'):
                    # 可能需要在参数之间添加逗号
                    pass
            
            return fixes_applied
            
        except Exception as e:
            print(f"      💥 修复一般语法错误异常: {str(e)}")
            return False
    
    def fix_missing_files_immediately(self):
        """立即修复缺失文件"""
        try:
            critical_files = [
                'gui_main.py', 'email_sender.py', 'email_history_manager.py'
            ]
            
            for file_path in critical_files:
                if not os.path.exists(file_path):
                    print(f"  🚨 发现缺失文件: {file_path}")
                    print(f"  🔧 立即修复...")
                    
                    # 尝试从备份恢复
                    if self.restore_from_backup(file_path):
                        print(f"    ✅ 从备份恢复: {file_path}")
                        self.fixed_count += 1
                    else:
                        print(f"    ❌ 无法恢复: {file_path}")
                        self.error_count += 1
                else:
                    print(f"  ✅ 文件存在: {file_path}")
                    
        except Exception as e:
            print(f"  💥 文件修复异常: {str(e)}")
            self.error_count += 1
    
    def restore_from_backup(self, file_path):
        """从备份恢复文件"""
        try:
            import glob
            
            # 查找备份文件
            backup_pattern = f"{file_path}.backup_*"
            backups = glob.glob(backup_pattern)
            
            if backups:
                # 使用最新的备份
                latest_backup = max(backups, key=os.path.getmtime)
                shutil.copy2(latest_backup, file_path)
                return True
            
            return False
            
        except Exception as e:
            print(f"    💥 恢复备份异常: {str(e)}")
            return False
    
    def fix_missing_directories_immediately(self):
        """立即修复缺失目录"""
        try:
            required_dirs = ['logs', 'user_data', 'backups', 'temp', 'logs/emergency']
            
            for dir_path in required_dirs:
                if not os.path.exists(dir_path):
                    print(f"  🚨 发现缺失目录: {dir_path}")
                    print(f"  🔧 立即创建...")
                    
                    os.makedirs(dir_path, exist_ok=True)
                    print(f"    ✅ 目录已创建: {dir_path}")
                    self.fixed_count += 1
                else:
                    print(f"  ✅ 目录存在: {dir_path}")
                    
        except Exception as e:
            print(f"  💥 目录修复异常: {str(e)}")
            self.error_count += 1
    
    def fix_missing_configs_immediately(self):
        """立即修复缺失配置"""
        try:
            configs = {
                'user_data/user_settings.json': {},
                'user_data/email_content.json': {
                    'subject': '',
                    'body': '',
                    'recipient_emails': ''
                },
                'user_data/send_config.json': {
                    'send_mode': 'standard',
                    'delay_range': [1, 3]
                },
                'user_data/monitoring_settings.json': {
                    'enabled': True,
                    'check_interval': 300
                }
            }
            
            import json
            
            for config_path, default_content in configs.items():
                if not os.path.exists(config_path):
                    print(f"  🚨 发现缺失配置: {config_path}")
                    print(f"  🔧 立即创建...")
                    
                    os.makedirs(os.path.dirname(config_path), exist_ok=True)
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(default_content, f, ensure_ascii=False, indent=2)
                    
                    print(f"    ✅ 配置已创建: {config_path}")
                    self.fixed_count += 1
                else:
                    print(f"  ✅ 配置存在: {config_path}")
                    
        except Exception as e:
            print(f"  💥 配置修复异常: {str(e)}")
            self.error_count += 1
    
    def fix_database_issues_immediately(self):
        """立即修复数据库问题"""
        try:
            import sqlite3
            
            databases = [
                'email_history.db', 'recipient_quality.db', 'anti_spam.db',
                'qq_anti_spam.db', 'system_integration.db'
            ]
            
            for db_path in databases:
                print(f"  🔍 检查数据库: {db_path}")
                
                if os.path.exists(db_path):
                    try:
                        # 测试数据库连接
                        conn = sqlite3.connect(db_path, timeout=5)
                        conn.execute("SELECT 1")
                        conn.close()
                        print(f"    ✅ 数据库正常: {db_path}")
                    except sqlite3.Error as e:
                        print(f"    🚨 数据库损坏: {db_path}")
                        print(f"    🔧 立即重建...")
                        
                        # 备份损坏的数据库
                        backup_path = f"{db_path}.corrupted_{int(time.time())}"
                        shutil.move(db_path, backup_path)
                        self.backup_count += 1
                        
                        # 重建数据库
                        self.create_database(db_path)
                        print(f"    ✅ 数据库已重建: {db_path}")
                        self.fixed_count += 1
                else:
                    print(f"    🚨 数据库缺失: {db_path}")
                    print(f"    🔧 立即创建...")
                    
                    self.create_database(db_path)
                    print(f"    ✅ 数据库已创建: {db_path}")
                    self.fixed_count += 1
                    
        except Exception as e:
            print(f"  💥 数据库修复异常: {str(e)}")
            self.error_count += 1
    
    def create_database(self, db_path):
        """创建数据库"""
        try:
            import sqlite3
            
            conn = sqlite3.connect(db_path)
            
            if 'email_history.db' in db_path:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS sent_emails (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sender_email TEXT,
                        recipient_email TEXT,
                        subject TEXT,
                        body TEXT,
                        sent_time TEXT,
                        status TEXT
                    )
                ''')
            elif 'recipient_quality.db' in db_path:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS recipient_quality (
                        email TEXT PRIMARY KEY,
                        quality_score REAL,
                        send_count INTEGER,
                        reply_count INTEGER,
                        last_reply_time TEXT,
                        status TEXT
                    )
                ''')
            # 其他数据库的基础表结构
            else:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS system_info (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        key TEXT UNIQUE,
                        value TEXT,
                        updated_time TEXT
                    )
                ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"    💥 创建数据库异常: {str(e)}")
    
    def install_dependencies_immediately(self):
        """立即安装缺失依赖"""
        try:
            import subprocess
            
            required_packages = ['jieba', 'psutil']
            
            for package in required_packages:
                try:
                    __import__(package)
                    print(f"  ✅ 依赖包存在: {package}")
                except ImportError:
                    print(f"  🚨 发现缺失依赖: {package}")
                    print(f"  🔧 立即安装...")
                    
                    result = subprocess.run([
                        sys.executable, '-m', 'pip', 'install', package
                    ], capture_output=True, text=True, timeout=300)
                    
                    if result.returncode == 0:
                        print(f"    ✅ 依赖已安装: {package}")
                        self.fixed_count += 1
                    else:
                        print(f"    ❌ 安装失败: {package}")
                        print(f"    📝 错误信息: {result.stderr}")
                        self.error_count += 1
                        
        except Exception as e:
            print(f"  💥 依赖安装异常: {str(e)}")
            self.error_count += 1
    
    def verify_all_fixes(self):
        """验证所有修复"""
        try:
            print("  🔍 验证语法修复...")
            syntax_ok = self.verify_syntax()
            
            print("  🔍 验证文件完整性...")
            files_ok = self.verify_files()
            
            print("  🔍 验证依赖包...")
            deps_ok = self.verify_dependencies()
            
            if syntax_ok and files_ok and deps_ok:
                print("  🎉 所有修复验证通过！")
                return True
            else:
                print("  ⚠️ 部分修复需要进一步处理")
                return False
                
        except Exception as e:
            print(f"  💥 验证异常: {str(e)}")
            return False
    
    def verify_syntax(self):
        """验证语法"""
        try:
            critical_files = ['gui_main.py', 'email_sender.py']
            
            for file_path in critical_files:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    try:
                        ast.parse(content)
                        print(f"    ✅ {file_path} 语法正确")
                    except SyntaxError as e:
                        print(f"    ❌ {file_path} 仍有语法错误: {str(e)}")
                        return False
            
            return True
            
        except Exception as e:
            print(f"    💥 语法验证异常: {str(e)}")
            return False
    
    def verify_files(self):
        """验证文件"""
        critical_files = ['gui_main.py', 'email_sender.py']
        
        for file_path in critical_files:
            if not os.path.exists(file_path):
                print(f"    ❌ 文件仍然缺失: {file_path}")
                return False
        
        print(f"    ✅ 所有关键文件存在")
        return True
    
    def verify_dependencies(self):
        """验证依赖"""
        required_packages = ['jieba', 'psutil']
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                print(f"    ❌ 依赖包仍然缺失: {package}")
                return False
        
        print(f"    ✅ 所有依赖包可用")
        return True

def main():
    """主函数"""
    fixer = PowerfulAutoFixer()
    
    print("💪 强化版自动修复工具启动")
    print("🎯 目标：检测到问题立即修复，绝不手软！")
    
    success = fixer.fix_all_problems_now()
    
    if success:
        print("\n🎉 强化修复任务完成！")
        print("✅ 系统现在应该可以正常运行了")
    else:
        print("\n⚠️ 强化修复遇到问题")
        print("💡 请查看日志了解详细信息")

if __name__ == "__main__":
    main()
