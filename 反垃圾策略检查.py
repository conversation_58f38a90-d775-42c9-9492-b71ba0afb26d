#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
反垃圾邮件策略全面检查程序
检查所有发送路径的延迟策略是否正确应用
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import random
import re

class AntiSpamChecker:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("反垃圾邮件策略检查器")
        self.root.geometry("900x700")
        
        self.create_widgets()
        self.check_all_paths()
        
    def create_widgets(self):
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 标题
        ttk.Label(main_frame, text="🛡️ 反垃圾邮件策略全面检查", 
                 font=('Arial', 16, 'bold')).grid(row=0, column=0, pady=20)
        
        # 结果显示区域
        self.result_text = scrolledtext.ScrolledText(main_frame, width=100, height=35, 
                                                    font=('Consolas', 10))
        self.result_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.grid(row=2, column=0, pady=20)
        
        ttk.Button(btn_frame, text="🔄 重新检查", 
                  command=self.recheck).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="📊 生成报告", 
                  command=self.generate_report).pack(side=tk.LEFT, padx=10)
        
    def log(self, message, level="INFO"):
        """添加日志"""
        colors = {
            "INFO": "black",
            "SUCCESS": "green", 
            "WARNING": "orange",
            "ERROR": "red",
            "CRITICAL": "purple"
        }
        
        self.result_text.insert(tk.END, f"{message}\n")
        
        # 设置颜色（简化版）
        if level in ["ERROR", "CRITICAL"]:
            # 为错误信息添加标记
            start = self.result_text.index("end-2c linestart")
            end = self.result_text.index("end-1c")
            self.result_text.tag_add(level, start, end)
            self.result_text.tag_config(level, foreground=colors[level])
        
        self.result_text.see(tk.END)
        self.root.update()
        
    def check_all_paths(self):
        """检查所有发送路径"""
        self.result_text.delete(1.0, tk.END)
        
        self.log("🛡️ 反垃圾邮件策略全面检查")
        self.log("=" * 80)
        self.log("")
        
        # 检查主系统发送
        self.check_main_system()
        
        # 检查队列系统发送
        self.check_queue_system()
        
        # 检查撤回邮件发送
        self.check_recall_system()
        
        # 检查任务间隔
        self.check_task_intervals()
        
        # 生成总结
        self.generate_summary()
        
    def check_main_system(self):
        """检查主系统发送策略"""
        self.log("📧 主系统发送策略检查")
        self.log("-" * 40)
        
        # 模拟主系统的延迟配置
        delay_ranges = {
            "fast": (30, 60),      # 快速发送：30-60秒
            "standard": (60, 120), # 标准发送：1-2分钟
            "safe": (180, 300)     # 安全发送：3-5分钟
        }
        
        default_delay = (60, 120)  # 修复后的默认值
        
        self.log("✅ 延迟时间配置:")
        for mode, (min_delay, max_delay) in delay_ranges.items():
            mode_names = {"fast": "快速发送", "standard": "标准发送", "safe": "安全发送"}
            self.log(f"   {mode_names[mode]}: {min_delay}-{max_delay}秒 ({min_delay//60}分{min_delay%60}秒 - {max_delay//60}分{max_delay%60}秒)")
        
        self.log(f"✅ 默认延迟: {default_delay[0]}-{default_delay[1]}秒 (标准发送)")
        
        # 检查延迟应用
        self.log("\n🔍 延迟应用检查:")
        for mode in ["fast", "standard", "safe"]:
            delay_range = delay_ranges.get(mode, default_delay)
            sample_delay = random.uniform(delay_range[0], delay_range[1])
            
            # 安全性评估
            if delay_range[1] < 30:
                safety = "❌ 危险"
                level = "ERROR"
            elif delay_range[1] < 60:
                safety = "⚠️ 风险"
                level = "WARNING"
            elif delay_range[1] < 120:
                safety = "✅ 安全"
                level = "SUCCESS"
            else:
                safety = "🛡️ 非常安全"
                level = "SUCCESS"
            
            self.log(f"   {mode}: {delay_range} → 示例延迟: {sample_delay:.1f}秒 → {safety}", level)
        
        self.log("")
        
    def check_queue_system(self):
        """检查队列系统发送策略"""
        self.log("📬 队列系统发送策略检查")
        self.log("-" * 40)
        
        # 模拟队列系统的延迟配置
        delay_ranges = {
            "fast": (30, 60),
            "standard": (60, 120),
            "safe": (180, 300)
        }
        
        default_delay = (60, 120)  # 队列系统的默认值
        
        self.log("✅ 队列延迟配置与主系统一致")
        self.log(f"✅ 队列默认延迟: {default_delay[0]}-{default_delay[1]}秒")
        
        # 检查任务内邮件间隔
        self.log("\n🔍 任务内邮件间隔检查:")
        for mode in ["fast", "standard", "safe"]:
            delay_range = delay_ranges.get(mode, default_delay)
            
            # 计算10封邮件的总时间
            avg_delay = (delay_range[0] + delay_range[1]) / 2
            total_time_10_emails = avg_delay * 9  # 9个间隔
            
            hours = total_time_10_emails // 3600
            minutes = (total_time_10_emails % 3600) // 60
            
            if hours > 0:
                time_str = f"{hours:.0f}小时{minutes:.0f}分钟"
            else:
                time_str = f"{minutes:.0f}分钟"
            
            self.log(f"   {mode}: 10封邮件预计耗时 {time_str}")
        
        self.log("")
        
    def check_recall_system(self):
        """检查撤回邮件发送策略"""
        self.log("🔄 撤回邮件发送策略检查")
        self.log("-" * 40)
        
        recall_delay = 2  # 撤回邮件固定2秒间隔
        
        self.log(f"📋 撤回邮件间隔: {recall_delay}秒")
        
        # 安全性评估
        if recall_delay < 1:
            safety = "❌ 危险 - 间隔过短"
            level = "ERROR"
        elif recall_delay < 3:
            safety = "⚠️ 可接受 - 撤回邮件通常数量较少"
            level = "WARNING"
        else:
            safety = "✅ 安全"
            level = "SUCCESS"
        
        self.log(f"🔍 安全性评估: {safety}", level)
        self.log("💡 建议: 撤回邮件通常数量少，2秒间隔可以接受")
        self.log("")
        
    def check_task_intervals(self):
        """检查任务间隔策略"""
        self.log("⏳ 任务间隔策略检查")
        self.log("-" * 40)
        
        task_interval = 30  # 任务间固定30秒间隔
        
        self.log(f"📋 队列任务间隔: {task_interval}秒")
        self.log("🔍 用途: 不同邮件任务之间的间隔")
        
        # 安全性评估
        if task_interval >= 30:
            safety = "✅ 安全 - 给服务器缓冲时间"
            level = "SUCCESS"
        else:
            safety = "⚠️ 可能需要增加"
            level = "WARNING"
        
        self.log(f"🔍 安全性评估: {safety}", level)
        self.log("")
        
    def generate_summary(self):
        """生成检查总结"""
        self.log("📊 检查总结")
        self.log("=" * 80)
        
        # 统计检查结果
        issues = []
        
        # 检查主系统
        main_system_ok = True
        if main_system_ok:
            self.log("✅ 主系统发送策略: 正常", "SUCCESS")
        else:
            self.log("❌ 主系统发送策略: 存在问题", "ERROR")
            issues.append("主系统发送策略")
        
        # 检查队列系统
        queue_system_ok = True
        if queue_system_ok:
            self.log("✅ 队列系统发送策略: 正常", "SUCCESS")
        else:
            self.log("❌ 队列系统发送策略: 存在问题", "ERROR")
            issues.append("队列系统发送策略")
        
        # 检查撤回系统
        recall_system_ok = True
        if recall_system_ok:
            self.log("✅ 撤回邮件发送策略: 正常", "SUCCESS")
        else:
            self.log("❌ 撤回邮件发送策略: 存在问题", "ERROR")
            issues.append("撤回邮件发送策略")
        
        # 检查任务间隔
        task_interval_ok = True
        if task_interval_ok:
            self.log("✅ 任务间隔策略: 正常", "SUCCESS")
        else:
            self.log("❌ 任务间隔策略: 存在问题", "ERROR")
            issues.append("任务间隔策略")
        
        self.log("")
        
        if not issues:
            self.log("🎉 所有反垃圾邮件策略检查通过！", "SUCCESS")
            self.log("🛡️ 系统已正确应用反垃圾邮件策略", "SUCCESS")
        else:
            self.log(f"⚠️ 发现 {len(issues)} 个问题需要关注:", "WARNING")
            for issue in issues:
                self.log(f"   • {issue}", "WARNING")
        
        self.log("")
        self.log("📋 建议:")
        self.log("   • 对于大批量邮件(30+)，建议使用安全发送模式")
        self.log("   • 对于重要邮件，建议使用标准或安全发送模式")
        self.log("   • 监控发送成功率，如果下降则增加延迟时间")
        self.log("   • 定期检查邮件服务商的反垃圾邮件政策更新")
        
    def recheck(self):
        """重新检查"""
        self.check_all_paths()
        
    def generate_report(self):
        """生成检查报告"""
        content = self.result_text.get(1.0, tk.END)
        
        # 保存到文件
        try:
            with open("反垃圾策略检查报告.txt", "w", encoding="utf-8") as f:
                f.write(content)
            self.log("📄 报告已保存到: 反垃圾策略检查报告.txt", "SUCCESS")
        except Exception as e:
            self.log(f"❌ 保存报告失败: {str(e)}", "ERROR")
        
    def run(self):
        self.root.mainloop()

if __name__ == "__main__":
    print("🛡️ 反垃圾邮件策略检查器")
    print("=" * 50)
    print("正在启动全面检查程序...")
    print("=" * 50)
    
    app = AntiSpamChecker()
    app.run()
