#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简单数据库修复方案
"""

import threading
import time
import random
import os
from email_receiver import EmailReceiver
from simple_database_fix import update_recipient_status_simple, save_auto_reply_simple

def test_simple_database_fix():
    """测试简单数据库修复方案"""
    print("🔧 测试简单数据库修复方案")
    print("=" * 60)
    
    test_email = "<EMAIL>"
    test_db = "test_simple_fix.db"
    
    # 清理旧的测试数据库
    if os.path.exists(test_db):
        os.remove(test_db)
    if os.path.exists(test_db + ".lock"):
        os.remove(test_db + ".lock")
    
    results = []
    errors = []
    
    def concurrent_operations(thread_id):
        """并发数据库操作"""
        thread_results = []
        thread_errors = []
        
        for i in range(15):  # 每个线程执行15次操作
            try:
                recipient_email = f"recipient{i}@test{thread_id}.com"
                reply_type = random.choice(['auto_reply', 'bounce', 'auto_reply'])
                
                # 执行数据库操作
                start_time = time.time()
                success = update_recipient_status_simple(test_db, recipient_email, test_email, reply_type)
                end_time = time.time()
                
                operation_time = end_time - start_time
                
                if success:
                    thread_results.append(operation_time)
                    print(f"  线程{thread_id}: 操作{i+1} 成功 ({operation_time:.3f}s)")
                else:
                    thread_errors.append(f"线程{thread_id}操作{i+1}失败")
                    print(f"  线程{thread_id}: 操作{i+1} 失败")
                
                # 短暂延迟
                time.sleep(0.01)
                
            except Exception as e:
                thread_errors.append(f"线程{thread_id}操作{i+1}异常: {str(e)}")
                print(f"  线程{thread_id}: 操作{i+1} 异常: {str(e)}")
        
        results.extend(thread_results)
        errors.extend(thread_errors)
    
    # 启动多个线程
    threads = []
    start_time = time.time()
    
    for i in range(4):  # 4个并发线程
        thread = threading.Thread(target=concurrent_operations, args=(i+1,))
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    
    # 统计结果
    total_time = end_time - start_time
    total_operations = len(results)
    total_attempts = total_operations + len(errors)
    success_rate = (total_operations / total_attempts) * 100 if total_attempts > 0 else 0
    avg_operation_time = sum(results) / len(results) if results else 0
    
    print(f"\n📊 简单修复方案测试结果:")
    print(f"  总尝试操作: {total_attempts}")
    print(f"  成功操作: {total_operations}")
    print(f"  失败操作: {len(errors)}")
    print(f"  成功率: {success_rate:.1f}%")
    print(f"  总耗时: {total_time:.2f}秒")
    print(f"  平均操作时间: {avg_operation_time:.3f}秒")
    print(f"  操作吞吐量: {total_operations/total_time:.2f}操作/秒")
    
    if errors:
        print(f"\n❌ 错误详情:")
        for error in errors[:3]:  # 只显示前3个错误
            print(f"  • {error}")
    
    # 清理测试文件
    try:
        if os.path.exists(test_db):
            os.remove(test_db)
        if os.path.exists(test_db + ".lock"):
            os.remove(test_db + ".lock")
    except:
        pass
    
    # 判断结果
    if success_rate >= 95:
        print(f"\n🎉 简单数据库修复方案成功！")
        print(f"  ✅ {success_rate:.1f}%成功率")
        print(f"  ✅ 平均操作时间: {avg_operation_time:.3f}秒")
        return True
    else:
        print(f"\n⚠️ 简单数据库修复方案需要改进")
        return False

def test_email_receiver_integration():
    """测试EmailReceiver集成"""
    print("\n📧 测试EmailReceiver集成")
    print("=" * 60)
    
    try:
        test_email = "<EMAIL>"
        receiver = EmailReceiver(test_email, "test_password")
        
        print("✅ EmailReceiver创建成功")
        
        # 测试更新收件人状态
        print("  测试更新收件人状态...")
        
        test_cases = [
            ("<EMAIL>", "auto_reply"),
            ("<EMAIL>", "bounce"),
            ("<EMAIL>", "auto_reply"),
            ("<EMAIL>", "auto_reply"),
            ("<EMAIL>", "bounce")
        ]
        
        success_count = 0
        for recipient, reply_type in test_cases:
            try:
                receiver.update_recipient_status(recipient, test_email, reply_type)
                success_count += 1
                print(f"    ✅ {recipient} -> {reply_type}")
            except Exception as e:
                print(f"    ❌ {recipient} -> {reply_type}: {str(e)}")
        
        success_rate = (success_count / len(test_cases)) * 100
        print(f"  更新成功率: {success_rate:.1f}%")
        
        # 测试保存自动回复
        print("  测试保存自动回复...")
        
        reply_info = {
            'recipient_email': '<EMAIL>',
            'sender_email': test_email,
            'reply_type': 'auto_reply',
            'reply_time': '2025-06-12 22:00:00',
            'subject': '测试自动回复',
            'body': '这是一个测试自动回复'
        }
        
        try:
            receiver.save_auto_reply(reply_info)
            print(f"    ✅ 自动回复保存成功")
            save_success = True
        except Exception as e:
            print(f"    ❌ 自动回复保存失败: {str(e)}")
            save_success = False
        
        if success_rate >= 80 and save_success:
            print("✅ EmailReceiver集成测试成功")
            return True
        else:
            print("⚠️ EmailReceiver集成测试需要改进")
            return False
        
    except Exception as e:
        print(f"❌ EmailReceiver集成测试失败: {str(e)}")
        return False

def test_performance_and_stability():
    """测试性能和稳定性"""
    print("\n⚡ 测试性能和稳定性")
    print("=" * 60)
    
    try:
        test_db = "test_performance.db"
        
        # 清理旧文件
        for file_path in [test_db, test_db + ".lock"]:
            if os.path.exists(file_path):
                os.remove(file_path)
        
        # 性能测试
        print("  性能测试...")
        
        start_time = time.time()
        for i in range(50):
            success = update_recipient_status_simple(
                test_db, 
                f"user{i}@test.com", 
                "<EMAIL>", 
                "auto_reply"
            )
            if not success:
                print(f"    操作{i+1}失败")
        
        end_time = time.time()
        
        performance_time = end_time - start_time
        operations_per_second = 50 / performance_time
        
        print(f"    50次操作耗时: {performance_time:.2f}秒")
        print(f"    性能: {operations_per_second:.2f} 操作/秒")
        
        # 稳定性测试
        print("  稳定性测试...")
        
        def stability_test(thread_id):
            success_count = 0
            for i in range(10):
                try:
                    success = update_recipient_status_simple(
                        test_db,
                        f"stable{thread_id}_{i}@test.com",
                        "<EMAIL>",
                        "auto_reply"
                    )
                    if success:
                        success_count += 1
                except:
                    pass
            return success_count
        
        threads = []
        results = []
        
        for i in range(3):
            thread = threading.Thread(target=lambda tid=i: results.append(stability_test(tid+1)))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        total_stability_operations = sum(results)
        stability_rate = (total_stability_operations / 30) * 100  # 3线程 * 10操作
        
        print(f"    稳定性测试: {total_stability_operations}/30 成功")
        print(f"    稳定性: {stability_rate:.1f}%")
        
        # 清理测试文件
        for file_path in [test_db, test_db + ".lock"]:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
            except:
                pass
        
        if operations_per_second > 10 and stability_rate >= 90:
            print("✅ 性能和稳定性测试通过")
            return True
        else:
            print("⚠️ 性能和稳定性需要改进")
            return False
        
    except Exception as e:
        print(f"❌ 性能和稳定性测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始简单数据库修复方案验证")
    print("=" * 80)
    
    # 执行各项测试
    tests = [
        ("简单数据库修复", test_simple_database_fix),
        ("EmailReceiver集成", test_email_receiver_integration),
        ("性能和稳定性", test_performance_and_stability)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {str(e)}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("🎯 测试结果总结")
    print("=" * 80)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("\n🎉 简单数据库修复方案完全成功！")
        print("✅ 使用文件锁有效避免数据库锁定")
        print("✅ 重试机制确保操作可靠性")
        print("✅ 性能表现良好")
        print("✅ 并发稳定性优秀")
        print("✅ EmailReceiver集成正常")
        print("\n💡 现在您可以：")
        print("  🔒 完全避免数据库锁定问题")
        print("  ⚡ 享受稳定的并发操作")
        print("  📊 获得可靠的监控体验")
        print("  🛡️ 确保数据一致性")
        return True
    else:
        print("\n⚠️ 部分功能需要进一步优化")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
