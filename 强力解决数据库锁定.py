#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强力解决数据库锁定问题
"""

import sqlite3
import os
import time
import threading
import subprocess
import shutil
from contextlib import contextmanager

class DatabaseLockSolver:
    def __init__(self):
        self.db_files = [
            'email_history.db',
            'recipient_quality.db',
            'auto_reply_monitor.db',
            'anti_spam.db',
            'qq_anti_spam.db'
        ]
        self.lock = threading.RLock()
    
    def kill_all_python_processes(self):
        """终止所有其他Python进程"""
        print("🔪 终止所有其他Python进程")
        print("-" * 30)
        
        try:
            current_pid = os.getpid()
            
            # 获取所有Python进程
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                killed_count = 0
                
                for line in lines:
                    if 'python.exe' in line:
                        parts = line.split()
                        if len(parts) >= 2:
                            try:
                                pid = int(parts[1])
                                if pid != current_pid:
                                    subprocess.run(['taskkill', '/F', '/PID', str(pid)], 
                                                 capture_output=True)
                                    print(f"🔪 终止进程 PID: {pid}")
                                    killed_count += 1
                            except:
                                continue
                
                if killed_count > 0:
                    print(f"✅ 已终止 {killed_count} 个Python进程")
                    time.sleep(3)  # 等待进程完全终止
                else:
                    print("✅ 没有其他Python进程需要终止")
            
            return True
            
        except Exception as e:
            print(f"❌ 终止进程失败: {str(e)}")
            return False
    
    def backup_and_recreate_databases(self):
        """备份并重建数据库"""
        print("\n🔧 备份并重建数据库")
        print("-" * 30)
        
        recreated_count = 0
        
        for db_file in self.db_files:
            if os.path.exists(db_file):
                try:
                    print(f"🔄 重建数据库: {db_file}")
                    
                    # 备份原数据库
                    backup_file = f"{db_file}.backup_{int(time.time())}"
                    shutil.copy2(db_file, backup_file)
                    print(f"  ✅ 已备份为: {backup_file}")
                    
                    # 删除WAL和SHM文件
                    for suffix in ['-wal', '-shm']:
                        aux_file = db_file + suffix
                        if os.path.exists(aux_file):
                            os.remove(aux_file)
                            print(f"  ✅ 删除辅助文件: {aux_file}")
                    
                    # 重建数据库
                    temp_file = f"{db_file}.temp"
                    
                    # 从备份恢复数据
                    source_conn = sqlite3.connect(backup_file, timeout=30.0)
                    dest_conn = sqlite3.connect(temp_file, timeout=30.0)
                    
                    # 设置最优参数
                    dest_conn.execute("PRAGMA journal_mode=WAL")
                    dest_conn.execute("PRAGMA synchronous=NORMAL")
                    dest_conn.execute("PRAGMA busy_timeout=30000")
                    dest_conn.execute("PRAGMA cache_size=10000")
                    
                    # 复制数据
                    source_conn.backup(dest_conn)
                    
                    source_conn.close()
                    dest_conn.close()
                    
                    # 替换原文件
                    if os.path.exists(db_file):
                        os.remove(db_file)
                    
                    os.rename(temp_file, db_file)
                    
                    print(f"  ✅ {db_file} 重建完成")
                    recreated_count += 1
                    
                except Exception as e:
                    print(f"  ❌ {db_file} 重建失败: {str(e)}")
        
        print(f"\n✅ 成功重建 {recreated_count} 个数据库")
        return recreated_count > 0
    
    @contextmanager
    def safe_db_connection(self, db_file, retries=5):
        """安全的数据库连接上下文管理器"""
        conn = None
        for attempt in range(retries):
            try:
                with self.lock:
                    conn = sqlite3.connect(
                        db_file,
                        timeout=60.0,  # 增加到60秒
                        check_same_thread=False
                    )
                    
                    # 设置最优参数
                    conn.execute("PRAGMA journal_mode=WAL")
                    conn.execute("PRAGMA synchronous=NORMAL")
                    conn.execute("PRAGMA busy_timeout=60000")  # 60秒
                    conn.execute("PRAGMA cache_size=20000")
                    conn.execute("PRAGMA temp_store=memory")
                    
                    yield conn
                    conn.commit()
                    break
                    
            except sqlite3.OperationalError as e:
                if conn:
                    try:
                        conn.rollback()
                        conn.close()
                    except:
                        pass
                    conn = None
                
                if "database is locked" in str(e) and attempt < retries - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    print(f"⚠️ 数据库锁定，第 {attempt + 1} 次重试，等待 {wait_time} 秒...")
                    time.sleep(wait_time)
                    continue
                else:
                    raise
            except Exception as e:
                if conn:
                    try:
                        conn.rollback()
                        conn.close()
                    except:
                        pass
                raise
            finally:
                if conn:
                    try:
                        conn.close()
                    except:
                        pass
    
    def test_all_databases(self):
        """测试所有数据库"""
        print("\n🧪 测试所有数据库")
        print("-" * 30)
        
        success_count = 0
        
        for db_file in self.db_files:
            if os.path.exists(db_file):
                try:
                    print(f"🧪 测试: {db_file}")
                    
                    with self.safe_db_connection(db_file) as conn:
                        cursor = conn.cursor()
                        
                        # 测试查询
                        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")
                        cursor.fetchone()
                        
                        # 测试写入
                        cursor.execute("CREATE TABLE IF NOT EXISTS test_lock_table (id INTEGER PRIMARY KEY, test_time TEXT)")
                        cursor.execute("INSERT OR REPLACE INTO test_lock_table (id, test_time) VALUES (1, ?)", 
                                     (time.strftime('%Y-%m-%d %H:%M:%S'),))
                        
                        # 测试读取
                        cursor.execute("SELECT test_time FROM test_lock_table WHERE id = 1")
                        result = cursor.fetchone()
                        
                        # 清理
                        cursor.execute("DROP TABLE IF EXISTS test_lock_table")
                    
                    print(f"  ✅ {db_file} 测试成功")
                    success_count += 1
                    
                except Exception as e:
                    print(f"  ❌ {db_file} 测试失败: {str(e)}")
        
        print(f"\n✅ {success_count}/{len([f for f in self.db_files if os.path.exists(f)])} 个数据库测试通过")
        return success_count > 0

def update_status_descriptions():
    """更新状态描述，将'待回复'改为'未回复'"""
    print("\n🔧 更新状态描述")
    print("-" * 30)
    
    files_to_update = [
        'gui_main.py',
        'email_receiver.py',
        'recipient_quality_manager.py'
    ]
    
    updated_count = 0
    
    for file_name in files_to_update:
        if os.path.exists(file_name):
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 替换状态描述
                replacements = [
                    ('待回复', '未回复'),
                    ('pending_reply', 'no_reply'),
                    ('等待回复', '未回复'),
                    ('awaiting_reply', 'no_reply')
                ]
                
                original_content = content
                for old_text, new_text in replacements:
                    content = content.replace(old_text, new_text)
                
                if content != original_content:
                    with open(file_name, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"✅ 已更新: {file_name}")
                    updated_count += 1
                else:
                    print(f"⚪ 无需更新: {file_name}")
                    
            except Exception as e:
                print(f"❌ 更新失败: {file_name} - {str(e)}")
    
    print(f"\n✅ 成功更新 {updated_count} 个文件")
    return True

def main():
    """主函数"""
    print("🔧 强力解决数据库锁定问题")
    print("=" * 60)
    
    solver = DatabaseLockSolver()
    
    steps = [
        ("终止所有Python进程", solver.kill_all_python_processes),
        ("备份并重建数据库", solver.backup_and_recreate_databases),
        ("测试所有数据库", solver.test_all_databases),
        ("更新状态描述", update_status_descriptions)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔧 执行: {step_name}")
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} - 成功")
            else:
                print(f"❌ {step_name} - 失败")
        except Exception as e:
            print(f"❌ {step_name} - 异常: {str(e)}")
        
        time.sleep(2)  # 步骤间等待
    
    print(f"\n📊 解决结果")
    print("=" * 30)
    print(f"成功步骤: {success_count}/{len(steps)}")
    
    if success_count >= 3:
        print("\n🎉 数据库锁定问题已强力解决！")
        print("✅ 所有Python进程已终止")
        print("✅ 数据库已重建优化")
        print("✅ 数据库测试通过")
        print("✅ 状态描述已更新")
        
        print("\n💡 现在:")
        print("1. 重启邮件程序")
        print("2. 不应该再出现数据库锁定")
        print("3. 状态显示为'未回复'而不是'待回复'")
        print("4. 系统功能协调配合更好")
        
    else:
        print(f"\n⚠️ 仍有问题，建议重启计算机后重试")

if __name__ == "__main__":
    main()
