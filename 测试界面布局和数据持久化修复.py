#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试2.0系统界面布局和数据持久化修复效果
"""

import tkinter as tk
import os
import json
import time
import datetime

def test_layout_improvements():
    """测试界面布局改进"""
    print("🖥️ 测试界面布局改进...")
    
    try:
        from gui_main import EmailSenderGUI
        
        # 创建主窗口
        root = tk.Tk()
        app = EmailSenderGUI(root)
        
        # 检查窗口尺寸
        geometry = root.geometry()
        print(f"   ✅ 主窗口尺寸: {geometry}")
        
        if "1400x1000" in geometry:
            print("   ✅ 主窗口尺寸已扩大到1400x1000")
        else:
            print("   ❌ 主窗口尺寸未正确设置")
        
        # 检查最小尺寸
        min_width = root.minsize()[0]
        min_height = root.minsize()[1]
        print(f"   ✅ 最小窗口尺寸: {min_width}x{min_height}")
        
        # 检查邮件正文区域
        body_widget = app.body
        body_width = body_widget.cget('width')
        body_height = body_widget.cget('height')
        print(f"   ✅ 邮件正文区域尺寸: {body_width}x{body_height}")
        
        if body_width >= 80 and body_height >= 12:
            print("   ✅ 邮件正文区域已扩大")
        else:
            print("   ❌ 邮件正文区域尺寸不足")
        
        # 检查收件人区域
        recipient_widget = app.recipient_emails
        recipient_width = recipient_widget.cget('width')
        recipient_height = recipient_widget.cget('height')
        print(f"   ✅ 收件人区域尺寸: {recipient_width}x{recipient_height}")
        
        if recipient_width >= 80 and recipient_height >= 6:
            print("   ✅ 收件人区域已扩大")
        else:
            print("   ❌ 收件人区域尺寸不足")
        
        # 检查日志区域
        log_widget = app.log_text
        log_width = log_widget.cget('width')
        log_height = log_widget.cget('height')
        print(f"   ✅ 日志区域尺寸: {log_width}x{log_height}")
        
        if log_width >= 100 and log_height >= 10:
            print("   ✅ 日志区域已扩大")
        else:
            print("   ❌ 日志区域尺寸不足")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"   ❌ 测试界面布局失败: {str(e)}")
        return False

def test_data_persistence():
    """测试数据持久化功能"""
    print("\n💾 测试数据持久化功能...")
    
    try:
        from gui_main import EmailSenderGUI
        
        # 第一步：创建GUI并设置一些数据
        print("   📝 步骤1: 创建GUI并设置测试数据...")
        root1 = tk.Tk()
        app1 = EmailSenderGUI(root1)
        
        # 等待初始化完成
        root1.update()
        time.sleep(1)
        
        # 设置测试数据
        test_sender = "<EMAIL>"
        test_recipients = "<EMAIL>\<EMAIL>"
        test_subject = "测试邮件主题"
        test_body = "这是测试邮件正文内容"
        
        app1.sender_email.delete(0, tk.END)
        app1.sender_email.insert(0, test_sender)
        
        app1.recipient_emails.delete(1.0, tk.END)
        app1.recipient_emails.insert(1.0, test_recipients)
        
        app1.subject.delete(0, tk.END)
        app1.subject.insert(0, test_subject)
        
        app1.body.delete(1.0, tk.END)
        app1.body.insert(1.0, test_body)
        
        # 手动保存数据
        if hasattr(app1, 'memory_manager'):
            app1._save_email_content()
            print("   ✅ 测试数据已保存")
        else:
            print("   ❌ 长期记忆管理器未初始化")
            root1.destroy()
            return False
        
        root1.destroy()
        
        # 第二步：重新创建GUI并检查数据是否恢复
        print("   🔄 步骤2: 重新创建GUI并检查数据恢复...")
        root2 = tk.Tk()
        app2 = EmailSenderGUI(root2)
        
        # 等待初始化和数据恢复完成
        root2.update()
        time.sleep(1)
        
        # 检查数据是否恢复
        restored_sender = app2.sender_email.get()
        restored_recipients = app2.recipient_emails.get(1.0, tk.END).strip()
        restored_subject = app2.subject.get()
        restored_body = app2.body.get(1.0, tk.END).strip()
        
        print(f"   📧 恢复的发件人: {restored_sender}")
        print(f"   📧 恢复的收件人: {restored_recipients[:50]}...")
        print(f"   📧 恢复的主题: {restored_subject}")
        print(f"   📧 恢复的正文: {restored_body[:50]}...")
        
        # 验证数据
        success = True
        if restored_sender != test_sender:
            print("   ❌ 发件人数据未正确恢复")
            success = False
        else:
            print("   ✅ 发件人数据恢复正确")
        
        if restored_recipients != test_recipients:
            print("   ❌ 收件人数据未正确恢复")
            success = False
        else:
            print("   ✅ 收件人数据恢复正确")
        
        if restored_subject != test_subject:
            print("   ❌ 邮件主题未正确恢复")
            success = False
        else:
            print("   ✅ 邮件主题恢复正确")
        
        if restored_body != test_body:
            print("   ❌ 邮件正文未正确恢复")
            success = False
        else:
            print("   ✅ 邮件正文恢复正确")
        
        root2.destroy()
        return success
        
    except Exception as e:
        print(f"   ❌ 测试数据持久化失败: {str(e)}")
        return False

def test_monitor_settings_persistence():
    """测试监控设置持久化"""
    print("\n🔍 测试监控设置持久化...")
    
    try:
        from gui_main import EmailSenderGUI
        
        # 清理旧的监控设置
        if os.path.exists('monitor_settings.json'):
            os.remove('monitor_settings.json')
        
        # 创建GUI
        root = tk.Tk()
        app = EmailSenderGUI(root)
        root.update()
        time.sleep(1)
        
        # 测试保存监控设置
        test_interval = "15"
        test_duration = "6"
        test_auto_start = False
        
        app._save_monitor_settings(test_interval, test_duration, test_auto_start)
        print(f"   ✅ 已保存监控设置: 间隔{test_interval}分钟, 时长{test_duration}小时")
        
        # 测试加载监控设置
        loaded_settings = app._load_monitor_settings()
        print(f"   📋 加载的设置: {loaded_settings}")
        
        # 验证设置
        if (loaded_settings.get('check_interval') == test_interval and
            loaded_settings.get('monitor_duration') == test_duration and
            loaded_settings.get('auto_start') == test_auto_start):
            print("   ✅ 监控设置持久化正常")
            success = True
        else:
            print("   ❌ 监控设置持久化失败")
            success = False
        
        root.destroy()
        return success
        
    except Exception as e:
        print(f"   ❌ 测试监控设置持久化失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🧪 2.0系统界面布局和数据持久化修复测试")
    print("="*60)
    
    # 测试结果
    results = {}
    
    # 1. 测试界面布局改进
    results['layout'] = test_layout_improvements()
    
    # 2. 测试数据持久化
    results['persistence'] = test_data_persistence()
    
    # 3. 测试监控设置持久化
    results['monitor_settings'] = test_monitor_settings_persistence()
    
    # 总结测试结果
    print("\n📊 测试结果总结")
    print("="*40)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        test_display = {
            'layout': '界面布局改进',
            'persistence': '数据持久化',
            'monitor_settings': '监控设置持久化'
        }
        print(f"{test_display[test_name]}: {status}")
    
    # 整体评估
    passed_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有修复都已成功！2.0系统界面和数据持久化问题已解决")
    else:
        print("⚠️ 部分修复需要进一步调整")
    
    print("\n💡 修复说明:")
    print("   • 主窗口尺寸从900x1000扩大到1400x1000")
    print("   • 邮件正文区域从50x8扩大到80x12")
    print("   • 收件人区域从50x4扩大到80x6")
    print("   • 日志区域从60x8扩大到100x10")
    print("   • 增强了监控设置的双重保存机制")
    print("   • 完善了长期记忆功能的数据恢复")

if __name__ == "__main__":
    main()
