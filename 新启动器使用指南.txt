# 新启动器使用指南

## 📁 文件说明
- `新启动器.vbs` - 主启动脚本
- `gui_main.py` - 邮件系统主程序

## 🚀 使用方法

### 方法1: 双击启动
1. 双击 `新启动器.vbs` 文件
2. 系统会自动检测Python环境
3. 自动启动邮件系统GUI

### 方法2: 右键启动
1. 右键点击 `新启动器.vbs`
2. 选择"打开方式" → "Microsoft Windows Based Script Host"

## 🔧 功能特点

### ✅ 智能Python检测
- 自动检测 `python` 命令
- 自动检测 `python3` 命令  
- 自动检测 `py` 命令
- 支持多种Python安装方式

### ✅ 完善错误处理
- 文件缺失检测
- Python环境检测
- 详细错误信息
- 解决方案提示

### ✅ 稳定启动
- 避免闪退问题
- 正确的错误处理
- 清晰的状态反馈

## ❌ 常见问题

### 问题1: "找不到gui_main.py文件"
**解决方案:**
- 确保 `新启动器.vbs` 和 `gui_main.py` 在同一目录
- 检查文件名是否正确

### 问题2: "未找到Python环境"
**解决方案:**
1. 安装Python 3.6或更高版本
2. 安装时勾选"Add Python to PATH"
3. 重启计算机后重试

### 问题3: 启动后没有反应
**解决方案:**
- 等待几秒钟，GUI加载需要时间
- 检查任务管理器中是否有python进程
- 尝试手动运行: `python gui_main.py`

## 🔗 手动启动方法
如果VBS启动失败，可以手动启动:
```
cd /d "邮件系统目录"
python gui_main.py
```

## 📞 技术支持
如果遇到问题，请检查:
1. Python是否正确安装
2. 所有文件是否在同一目录
3. 是否有杀毒软件阻止VBS执行
