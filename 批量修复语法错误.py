#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量修复gui_main.py中的语法错误
主要修复被错误注释的pady、padx、textvariable等参数
"""

import re
import os
import shutil
from datetime import datetime

def backup_file(file_path):
    """备份原文件"""
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"✅ 已备份原文件到: {backup_path}")
    return backup_path

def fix_syntax_errors(file_path):
    """批量修复语法错误"""
    print(f"🔧 开始修复文件: {file_path}")
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_content = content
    
    # 修复规则列表
    fix_rules = [
        # 修复被注释的pady参数
        (r'# pad_y=([^)]+)', r'pady=\1'),
        (r'# pad_y\s*=\s*([^)]+)', r'pady=\1'),
        
        # 修复被注释的padx参数  
        (r'# pad_x=([^)]+)', r'padx=\1'),
        (r'# pad_x\s*=\s*([^)]+)', r'padx=\1'),
        
        # 修复被注释的textvariable参数
        (r'# text_variable=([^)]+)', r'textvariable=\1'),
        (r'# text_variable\s*=\s*([^)]+)', r'textvariable=\1'),
        
        # 修复被注释的yscrollcommand参数
        (r'# y_scroll_command=([^)]+)', r'yscrollcommand=\1'),
        (r'# y_scroll_command\s*=\s*([^)]+)', r'yscrollcommand=\1'),
        
        # 修复被注释的其他常见参数
        (r'# border_width=([^)]+)', r'borderwidth=\1'),
        (r'# insert_background=([^)]+)', r'insertbackground=\1'),
        (r'# select_background=([^)]+)', r'selectbackground=\1'),
        
        # 修复多余的注释符号在参数中间
        (r'\.pack\([^)]*,\s*#\s*([^)]+)\)', r'.pack(\1)'),
        (r'\.grid\([^)]*,\s*#\s*([^)]+)\)', r'.grid(\1)'),
        
        # 修复字符串中的转义问题
        (r"font=\('Courier New', 8\)", r"font=('Courier New', 8)"),
        
        # 修复被错误注释的完整参数行
        (r'(\s+)# (pad_[xy]=[^,)]+)', r'\1\2'),
        (r'(\s+)# (text_variable=[^,)]+)', r'\1textvariable=\2'),
    ]
    
    # 应用修复规则
    fixes_applied = 0
    for pattern, replacement in fix_rules:
        matches = re.findall(pattern, content)
        if matches:
            content = re.sub(pattern, replacement, content)
            fixes_applied += len(matches)
            print(f"  ✓ 修复了 {len(matches)} 个 '{pattern}' 模式")
    
    # 特殊修复：处理复杂的语法错误
    special_fixes = [
        # 修复未关闭的括号
        (r'\.pack\(fill=tk\.X,\s*#\s*pad_y=\([^)]+\)\)', r'.pack(fill=tk.X, pady=(0, 10))'),
        (r'\.pack\(fill=tk\.BOTH,\s*expand=True,\s*#\s*pad_y=\([^)]+\)\)', r'.pack(fill=tk.BOTH, expand=True, pady=(0, 10))'),
        
        # 修复特定的错误模式
        (r'relief=tk\.SUNKEN,\s*#\s*border_width=1\)', r'relief=tk.SUNKEN)'),
        (r'bg=\'white\',\s*#\s*select_background=\'lightblue\'\)', r'bg=\'white\')'),
        (r'font=\(\'Microsoft YaHei UI\', 10\),\s*wrap=tk\.WORD,\s*#\s*insert_background=\'black\',\s*#\s*select_background=\'lightblue\',\s*relief=tk\.SUNKEN,\s*#\s*border_width=1\)', 
         r'font=(\'Microsoft YaHei UI\', 10), wrap=tk.WORD, relief=tk.SUNKEN)'),
    ]
    
    for pattern, replacement in special_fixes:
        if re.search(pattern, content):
            content = re.sub(pattern, replacement, content)
            fixes_applied += 1
            print(f"  ✓ 应用特殊修复: {pattern[:50]}...")
    
    # 写入修复后的内容
    if content != original_content:
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ 修复完成！共应用了 {fixes_applied} 个修复")
        return True
    else:
        print("ℹ️ 未发现需要修复的语法错误")
        return False

def validate_syntax(file_path):
    """验证Python语法"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试编译代码检查语法
        compile(content, file_path, 'exec')
        print("✅ 语法验证通过")
        return True
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        print(f"   位置: 第 {e.lineno} 行, 第 {e.offset} 列")
        print(f"   错误内容: {e.text}")
        return False
    except Exception as e:
        print(f"⚠️ 验证时出现其他错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 GUI语法错误批量修复工具")
    print("=" * 60)
    
    file_path = "gui_main.py"
    
    # 检查文件是否存在
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return
    
    # 备份原文件
    backup_path = backup_file(file_path)
    
    try:
        # 执行修复
        success = fix_syntax_errors(file_path)
        
        if success:
            # 验证修复后的语法
            print("\n🔍 验证修复后的语法...")
            if validate_syntax(file_path):
                print("\n🎉 修复成功！文件语法正确")
            else:
                print("\n⚠️ 修复后仍有语法错误，请手动检查")
        else:
            print("\n📝 文件无需修复")
            
    except Exception as e:
        print(f"\n❌ 修复过程中出现错误: {e}")
        print(f"正在恢复备份文件...")
        shutil.copy2(backup_path, file_path)
        print("✅ 已恢复原文件")

if __name__ == "__main__":
    main()
