# -*- coding: utf-8 -*-
"""
邮件发送配置文件
"""

# QQ邮箱SMTP配置
SMTP_CONFIG = {
    'server': 'smtp.qq.com',
    'port': 587,  # 使用TLS加密
    'use_tls': True,
    'username': '',  # 您的QQ邮箱地址，需要在main.py中设置
    'password': 'kqppwmjnuovsddji',  # 您的SMTP授权码
}

# 邮件默认设置
DEFAULT_SETTINGS = {
    'charset': 'utf-8',
    'timeout': 30,  # 连接超时时间（秒）
    'max_attachment_size': 25 * 1024 * 1024,  # 最大附件大小 25MB
    'supported_file_types': [
        '.txt', '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
        '.jpg', '.jpeg', '.png', '.gif', '.zip', '.rar', '.7z'
    ]
}

# 避免进入垃圾箱的邮件头设置（增强版）
EMAIL_HEADERS = {
    # 基础邮件客户端标识
    'X-Mailer': 'Microsoft Outlook 16.0',
    'User-Agent': 'Microsoft-MacOutlook/16.77.23091003',
    'X-MimeOLE': 'Produced By Microsoft MimeOLE V16.0.5',

    # 邮件优先级设置
    'X-Priority': '3',
    'Importance': 'Normal',
    'X-MSMail-Priority': 'Normal',
    'Priority': 'normal',

    # 反垃圾邮件标识
    'X-Spam-Status': 'No',
    'X-Spam-Score': '0.0',
    'X-Spam-Flag': 'NO',
    'X-Spam-Checker-Version': 'SpamAssassin 3.4.6',

    # 邮件认证相关
    'Authentication-Results': 'pass',
    'X-Microsoft-Antispam': 'BCL:0;',
    'X-Forefront-Antispam-Report': 'CIP:***************;CTRY:;LANG:zh;SCL:1;SRV:;IPV:NLI;SFV:NSPM;H:;PTR:;CAT:NONE;SFS:;DIR:OUT;',

    # 动态生成字段
    'Message-ID': None,  # 将在发送时动态生成
    'Date': None,       # 将在发送时动态生成
    'X-Originating-IP': None,  # 将在发送时动态生成
}

# 发送频率控制（避免被识别为垃圾邮件）
RATE_LIMIT = {
    'delay_between_emails': 2,  # 邮件间隔时间（秒）
    'max_emails_per_hour': 50,  # 每小时最大发送数量
}

# 批次发送控制（新增 - 解决垃圾邮件问题）
BATCH_CONTROL = {
    # 批次大小设置
    'batch_sizes': {
        'fast': 20,      # 快速模式：每批20封
        'standard': 30,  # 标准模式：每批30封
        'safe': 50       # 安全模式：每批50封
    },

    # 批次间隔时间（分钟）
    'batch_intervals': {
        'fast': (5, 8),      # 快速模式：5-8分钟间隔
        'standard': (8, 12), # 标准模式：8-12分钟间隔
        'safe': (12, 18)     # 安全模式：12-18分钟间隔
    },

    # 批次内邮件间隔（秒）
    'email_intervals': {
        'fast': (30, 60),    # 快速模式：30-60秒
        'standard': (60, 90), # 标准模式：60-90秒
        'safe': (90, 120)    # 安全模式：90-120秒
    },

    # 每日发送限制（防止账号被限制）
    'daily_limits': {
        'fast': 200,     # 快速模式：每日最多200封
        'standard': 300, # 标准模式：每日最多300封
        'safe': 500      # 安全模式：每日最多500封
    }
}
