#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 GUI定时发送功能测试脚本
测试GUI中定时发送功能的集成
"""

import sys
import os
import datetime
import uuid

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_schedule_manager_import():
    """测试ScheduleManager导入"""
    print("🔍 测试ScheduleManager导入...")
    
    try:
        from schedule_manager import ScheduleManager, ScheduledTask
        print("✅ ScheduleManager导入成功")
        
        # 创建实例
        manager = ScheduleManager()
        print("✅ ScheduleManager实例创建成功")
        
        # 检查方法
        required_methods = [
            'add_scheduled_task',
            'get_all_tasks', 
            'get_pending_tasks',
            'cancel_task',
            'delete_task',
            'get_task_statistics',
            'get_optimal_send_time',
            'cleanup_old_tasks',
            'export_tasks',
            'get_execution_history'
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(manager, method):
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需方法都存在")
            return True
            
    except Exception as e:
        print(f"❌ 导入失败: {str(e)}")
        return False

def test_schedule_manager_functionality():
    """测试ScheduleManager基本功能"""
    print("\n🔧 测试ScheduleManager基本功能...")
    
    try:
        from schedule_manager import ScheduleManager, ScheduledTask
        
        manager = ScheduleManager()
        
        # 测试创建任务
        test_task = ScheduledTask(
            id=str(uuid.uuid4()),
            name="GUI测试任务",
            sender_email="<EMAIL>",
            recipient_emails="<EMAIL>\<EMAIL>",
            subject="GUI测试邮件",
            body="这是GUI测试邮件内容",
            attachments=[],
            scheduled_time=(datetime.datetime.now() + datetime.timedelta(hours=1)).isoformat(),
            send_mode="standard",
            anti_spam_strategy="moderate",
            enable_monitoring=True,
            enable_quality_db=True,
            enable_emergency=True,
            enable_coordination=True,
            status="pending",
            created_time=datetime.datetime.now().isoformat()
        )
        
        # 添加任务
        if manager.add_scheduled_task(test_task):
            print("✅ 任务添加成功")
        else:
            print("❌ 任务添加失败")
            return False
        
        # 获取任务列表
        all_tasks = manager.get_all_tasks()
        print(f"✅ 获取任务列表成功，共 {len(all_tasks)} 个任务")
        
        # 获取统计信息
        stats = manager.get_task_statistics()
        if stats:
            print(f"✅ 获取统计信息成功: 总任务数 {stats.get('total_tasks', 0)}")
        else:
            print("❌ 获取统计信息失败")
        
        # 获取最佳时间
        optimal_time = manager.get_optimal_send_time("<EMAIL>")
        print(f"✅ 获取最佳时间成功: {optimal_time.strftime('%Y-%m-%d %H:%M')}")
        
        # 清理测试任务
        if manager.delete_task(test_task.id):
            print("✅ 测试任务清理成功")
        else:
            print("❌ 测试任务清理失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 功能测试失败: {str(e)}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    print("\n🖥️ 测试GUI集成...")
    
    try:
        # 模拟GUI中的导入方式
        import importlib
        import schedule_manager
        importlib.reload(schedule_manager)
        from schedule_manager import ScheduleManager
        
        manager = ScheduleManager()
        
        # 检查方法是否存在（模拟GUI中的检查）
        if not hasattr(manager, 'get_all_tasks'):
            print("❌ GUI集成测试失败: 缺少get_all_tasks方法")
            return False
        
        if not hasattr(manager, 'get_task_statistics'):
            print("❌ GUI集成测试失败: 缺少get_task_statistics方法")
            return False
        
        # 测试方法调用
        tasks = manager.get_all_tasks()
        stats = manager.get_task_statistics()
        
        print("✅ GUI集成测试成功")
        return True
        
    except Exception as e:
        print(f"❌ GUI集成测试失败: {str(e)}")
        return False

def test_database_persistence():
    """测试数据库持久化"""
    print("\n💾 测试数据库持久化...")
    
    try:
        from schedule_manager import ScheduleManager, ScheduledTask
        
        # 创建第一个管理器实例
        manager1 = ScheduleManager()
        
        # 添加测试任务
        test_task = ScheduledTask(
            id=str(uuid.uuid4()),
            name="持久化测试任务",
            sender_email="<EMAIL>",
            recipient_emails="<EMAIL>",
            subject="持久化测试",
            body="测试数据库持久化功能",
            attachments=[],
            scheduled_time=(datetime.datetime.now() + datetime.timedelta(hours=2)).isoformat(),
            send_mode="standard",
            anti_spam_strategy="moderate",
            enable_monitoring=True,
            enable_quality_db=True,
            enable_emergency=True,
            enable_coordination=True,
            status="pending",
            created_time=datetime.datetime.now().isoformat()
        )
        
        if manager1.add_scheduled_task(test_task):
            print("✅ 任务已添加到第一个管理器实例")
        else:
            print("❌ 任务添加失败")
            return False
        
        # 停止第一个管理器
        manager1.stop_scheduler()
        print("✅ 第一个管理器已停止")
        
        # 创建第二个管理器实例
        manager2 = ScheduleManager()
        print("✅ 第二个管理器已创建")
        
        # 检查任务是否仍然存在
        all_tasks = manager2.get_all_tasks()
        task_found = False
        for task in all_tasks:
            if task.id == test_task.id:
                task_found = True
                print("✅ 任务在新管理器实例中找到")
                break
        
        if not task_found:
            print("❌ 任务在新管理器实例中未找到")
            return False
        
        # 清理测试任务
        manager2.delete_task(test_task.id)
        manager2.stop_scheduler()
        print("✅ 测试任务已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 持久化测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 GUI定时发送功能集成测试")
    print("=" * 50)
    
    test_results = []
    
    # 1. 测试导入
    result1 = test_schedule_manager_import()
    test_results.append(("ScheduleManager导入测试", result1))
    
    if result1:
        # 2. 测试基本功能
        result2 = test_schedule_manager_functionality()
        test_results.append(("基本功能测试", result2))
        
        # 3. 测试GUI集成
        result3 = test_gui_integration()
        test_results.append(("GUI集成测试", result3))
        
        # 4. 测试数据库持久化
        result4 = test_database_persistence()
        test_results.append(("数据库持久化测试", result4))
    
    # 测试结果汇总
    print("\n📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！GUI定时发送功能集成正常")
        print("\n💡 建议:")
        print("1. 可以在主界面点击 '⏰ 定时发送' 按钮测试GUI功能")
        print("2. 运行 'python 启动定时发送演示.py' 体验完整功能")
        print("3. 查看 '定时发送功能说明.md' 了解详细使用方法")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
        print("\n🔧 故障排除建议:")
        print("1. 确保 schedule_manager.py 文件存在且完整")
        print("2. 检查Python环境和依赖包")
        print("3. 查看错误日志了解具体问题")
    
    return passed == total

if __name__ == "__main__":
    main()
