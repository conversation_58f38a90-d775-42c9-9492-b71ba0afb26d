#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整版v3的队列滚动功能
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import datetime

def test_queue_scroll_v3():
    """测试完整版v3的队列滚动功能"""
    root = tk.Tk()
    root.title("完整版v3队列滚动功能测试")
    root.geometry("600x700")
    
    # 创建主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    ttk.Label(main_frame, text="完整版v3队列滚动功能测试", 
             font=('Microsoft YaHei UI', 16, 'bold')).pack(pady=(0, 10))
    
    # 模拟队列数据
    email_queue = []
    
    # 队列状态显示区域
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(fill=tk.X, pady=(0, 10))
    
    ttk.Label(status_frame, text="📊 队列状态:",
             font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor=tk.W)
    
    # 状态指示器
    status_indicators = ttk.Frame(status_frame)
    status_indicators.pack(fill=tk.X, pady=(5, 0))
    
    queue_task_count = ttk.Label(status_indicators, text="📊 任务: 0个",
                                font=('Microsoft YaHei UI', 9))
    queue_task_count.pack(side=tk.LEFT)
    
    queue_progress = ttk.Label(status_indicators, text="📈 进度: 0%",
                              font=('Microsoft YaHei UI', 9))
    queue_progress.pack(side=tk.LEFT, padx=(15, 0))
    
    queue_speed = ttk.Label(status_indicators, text="⚡ 速度: 0封/分",
                           font=('Microsoft YaHei UI', 9))
    queue_speed.pack(side=tk.LEFT, padx=(15, 0))
    
    # 分隔线
    ttk.Separator(main_frame, orient='horizontal').pack(fill=tk.X, pady=(8, 8))
    
    # 队列任务列表显示区域 - 滚动列表
    queue_list_frame = ttk.LabelFrame(main_frame, text="📋 队列任务列表", padding="5")
    queue_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 8))
    
    # 创建滚动文本框显示队列任务
    queue_list_text = scrolledtext.ScrolledText(
        queue_list_frame,
        width=45,
        height=15,  # 增加高度以便测试滚动
        font=('Consolas', 8),
        wrap=tk.WORD,
        relief='solid',
        borderwidth=1,
        bg='#1e293b',
        fg='#e2e8f0',
        insertbackground='#3b82f6',
        selectbackground='#374151',
        state=tk.DISABLED
    )
    queue_list_text.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
    
    def update_queue_list_display():
        """更新队列列表显示"""
        try:
            # 清空现有内容
            queue_list_text.config(state=tk.NORMAL)
            queue_list_text.delete(1.0, tk.END)
            
            if not email_queue:
                queue_list_text.insert(tk.END, "📭 队列为空，暂无任务\n")
                queue_list_text.insert(tk.END, "💡 提示：点击'添加测试任务'按钮添加邮件发送任务\n")
                queue_list_text.insert(tk.END, "\n" + "="*50 + "\n")
                queue_list_text.insert(tk.END, "🖱️ 滚动功能说明：\n")
                queue_list_text.insert(tk.END, "• 使用鼠标滚轮可以上下滚动\n")
                queue_list_text.insert(tk.END, "• 使用右侧滚动条可以拖拽滚动\n")
                queue_list_text.insert(tk.END, "• 添加任务后会显示实际队列内容\n")
                queue_list_text.insert(tk.END, "• 当内容超过显示区域时会自动出现滚动条\n")
                queue_list_text.insert(tk.END, "\n📝 操作说明：\n")
                queue_list_text.insert(tk.END, "1. 点击'添加测试任务'按钮\n")
                queue_list_text.insert(tk.END, "2. 任务会显示在此列表中\n")
                queue_list_text.insert(tk.END, "3. 可以实时查看任务状态变化\n")
                queue_list_text.insert(tk.END, "4. 测试滚动功能是否正常\n")
                queue_list_text.insert(tk.END, "\n🎯 状态图标说明：\n")
                queue_list_text.insert(tk.END, "⏳ 待发送  📤 发送中  ✅ 已完成  ❌ 失败\n")
            else:
                # 添加队列标题
                queue_list_text.insert(tk.END, f"📋 队列任务列表 (共 {len(email_queue)} 个任务)\n")
                queue_list_text.insert(tk.END, "=" * 50 + "\n")
                
                # 显示每个任务
                for i, task in enumerate(email_queue, 1):
                    # 状态图标
                    status_icons = {
                        'pending': '⏳',
                        'sending': '📤', 
                        'completed': '✅',
                        'failed': '❌'
                    }
                    status_icon = status_icons.get(task.get('status'), '❓')
                    
                    # 计算收件人数量
                    recipients = task.get('recipient_emails', '')
                    if recipients:
                        recipient_count = len([email.strip() for email in
                                              recipients.replace(',', '\n').replace(';', '\n').split('\n')
                                              if email.strip()])
                    else:
                        recipient_count = 0
                    
                    # 格式化主题（限制长度）
                    subject = task.get('subject', '无主题')
                    subject = subject[:25] + '...' if len(subject) > 25 else subject
                    
                    # 发送模式
                    mode_text = {
                        'fast': '快速',
                        'standard': '标准', 
                        'safe': '安全'
                    }.get(task.get('send_mode'), '标准')
                    
                    # 创建时间
                    created_time = task.get('created_time', '')
                    if hasattr(created_time, 'strftime'):
                        time_str = created_time.strftime("%H:%M:%S")
                    else:
                        time_str = "未知"
                    
                    # 插入任务信息
                    task_line = f"{status_icon} #{i:02d} | {subject:<27} | {recipient_count:2d}人 | {mode_text:4s} | {time_str}\n"
                    queue_list_text.insert(tk.END, task_line)
                
                # 添加统计信息
                pending_count = len([task for task in email_queue if task.get('status') == 'pending'])
                completed_count = len([task for task in email_queue if task.get('status') == 'completed'])
                failed_count = len([task for task in email_queue if task.get('status') == 'failed'])
                sending_count = len([task for task in email_queue if task.get('status') == 'sending'])
                
                queue_list_text.insert(tk.END, "=" * 50 + "\n")
                queue_list_text.insert(tk.END, f"📊 统计: 待发送 {pending_count} | 发送中 {sending_count} | 已完成 {completed_count} | 失败 {failed_count}\n")
                
                # 添加操作提示
                queue_list_text.insert(tk.END, "\n💡 操作提示：\n")
                queue_list_text.insert(tk.END, "• 使用鼠标滚轮或滚动条查看更多任务\n")
                queue_list_text.insert(tk.END, "• 点击下方按钮可以添加更多测试任务\n")
            
            # 自动滚动到底部
            queue_list_text.see(tk.END)
            queue_list_text.config(state=tk.DISABLED)
            
        except Exception as e:
            print(f"更新队列列表显示错误: {e}")
    
    def update_queue_status():
        """更新队列状态显示"""
        try:
            queue_count = len(email_queue)
            pending_count = len([task for task in email_queue if task.get('status') == 'pending'])
            completed_count = len([task for task in email_queue if task.get('status') == 'completed'])
            failed_count = len([task for task in email_queue if task.get('status') == 'failed'])
            sending_count = len([task for task in email_queue if task.get('status') == 'sending'])

            # 更新状态标签
            queue_task_count.configure(text=f"📊 任务: {queue_count}个")
            progress = (completed_count + failed_count) / queue_count * 100 if queue_count > 0 else 0
            queue_progress.configure(text=f"📈 进度: {progress:.1f}%")
            queue_speed.configure(text=f"⚡ 速度: {sending_count}封/分")

            # 更新队列列表显示
            update_queue_list_display()

        except Exception as e:
            print(f"更新队列状态错误: {e}")
    
    def add_test_task():
        """添加测试任务"""
        import random
        
        task_id = len(email_queue) + 1
        subjects = ['重要系统通知', '用户反馈处理', '月度数据报告', '紧急故障通知', '版本发布公告', 
                   '培训邀请通知', '会议安排通知', '安全警报通知', '客户服务通知', '系统升级通知']
        modes = ['fast', 'standard', 'safe']
        statuses = ['pending', 'sending', 'completed', 'failed']
        
        new_task = {
            'id': task_id,
            'subject': f'{random.choice(subjects)} #{task_id}',
            'recipient_emails': f'test{task_id}@example.com;user{task_id}@test.com',
            'send_mode': random.choice(modes),
            'status': random.choice(statuses),
            'created_time': datetime.datetime.now()
        }
        
        email_queue.append(new_task)
        update_queue_status()
    
    def add_many_tasks():
        """添加大量测试任务"""
        for _ in range(10):
            add_test_task()
        messagebox.showinfo("添加完成", "已添加10个测试任务，请测试滚动功能")
    
    def clear_queue():
        """清空队列"""
        email_queue.clear()
        update_queue_status()
    
    def update_task_status():
        """随机更新任务状态"""
        if not email_queue:
            return
            
        import random
        statuses = ['pending', 'sending', 'completed', 'failed']
        
        for task in email_queue:
            if random.random() < 0.3:  # 30%概率更新状态
                task['status'] = random.choice(statuses)
                
        update_queue_status()
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))
    
    ttk.Button(button_frame, text="添加测试任务", command=add_test_task).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="添加10个任务", command=add_many_tasks).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="更新状态", command=update_task_status).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="清空队列", command=clear_queue).pack(side=tk.LEFT, padx=5)
    
    # 状态信息
    status_info = ttk.Label(main_frame, 
                           text="🔍 请测试滚动功能：使用鼠标滚轮、拖拽滚动条、点击滚动条箭头", 
                           font=('Microsoft YaHei UI', 10))
    status_info.pack(pady=(10, 0))
    
    # 初始化显示
    update_queue_list_display()
    
    root.mainloop()

if __name__ == "__main__":
    test_queue_scroll_v3()
