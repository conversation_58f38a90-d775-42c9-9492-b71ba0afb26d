' cSpell:words jieba smtp imap vbs gui api json sql utf ascii regex oauth ssl tls qq rag coord antispam auth config objshell objfso currentdir scriptpath nul ubound folderexists createfolder fileexists getparentfoldername scriptfullname wscript msgbox vbcrlf vbcritical vbinformation vbexclamation createobject chinesegmentercheck chinesegmenterresult installchinesegmenter missingfiles corefiles basicsystemrepair comprehensivestabilitycheck stabilitycommand stabilityresult basicrepairresult recoverycommand recoveryresult savestatecommand
' ================================================================
' 🏷️ 邮件系统 2.0 版本 - 全面稳定启动器
' 版本: 2.3 (全面稳定保障版)
' 系统: 2.0 完整功能版本 ⭐
' 功能: 全面稳定启动邮件系统GUI (包含所有2.0原始功能)
' 更新: 2025-06-14 - 全面稳定性保障和自动恢复
'
' 📋 2.0系统特色功能：
' • 完整的撤回功能
' • 自动回复监控
' • 质量数据库管理
' • 应急管理系统
' • 深度协调系统
' • 智能检索功能
'
' 🛡️ 稳定性保障功能：
' • 全面系统检查
' • 自动依赖修复
' • 系统状态恢复
' • 错误自动处理
' • 重启后自动恢复
' ================================================================
Option Explicit

Dim objShell, objFSO, currentDir
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 检查主脚本文件
Dim scriptPath
scriptPath = currentDir & "\gui_main.py"

If Not objFSO.FileExists(scriptPath) Then
    MsgBox "错误：找不到gui_main.py文件！" & vbCrLf & vbCrLf & _
           "当前目录：" & currentDir & vbCrLf & vbCrLf & _
           "请确保gui_main.py文件在同一目录中。", vbCritical, "文件不存在"
    WScript.Quit
End If

' 全面系统稳定性检查和修复
Dim stabilityCheck
stabilityCheck = ComprehensiveStabilityCheck()

If Not stabilityCheck Then
    MsgBox "系统稳定性检查失败！" & vbCrLf & vbCrLf & _
           "请检查以下项目：" & vbCrLf & _
           "1. Python环境是否正常" & vbCrLf & _
           "2. 依赖包是否完整" & vbCrLf & _
           "3. 核心文件是否存在" & vbCrLf & _
           "4. 数据库是否正常" & vbCrLf & vbCrLf & _
           "建议运行系统修复工具。", vbCritical, "系统检查失败"
    WScript.Quit
End If

' 启动图形界面
On Error Resume Next
Dim command, result

' 构建启动命令
command = "cmd /c ""cd /d """ & currentDir & """ && python gui_main.py"""

' 尝试启动
result = objShell.Run(command, 1, False)

' 检查是否有错误
If Err.Number <> 0 Then
    ' 如果有错误，尝试python3
    Err.Clear
    command = "cmd /c ""cd /d """ & currentDir & """ && python3 gui_main.py"""
    result = objShell.Run(command, 1, False)

    ' 如果还是有错误，显示错误信息
    If Err.Number <> 0 Then
        On Error GoTo 0
        MsgBox "启动失败！" & vbCrLf & vbCrLf & _
               "错误信息：" & Err.Description & vbCrLf & vbCrLf & _
               "请尝试以下解决方案：" & vbCrLf & _
               "1. 手动运行：python gui_main.py" & vbCrLf & _
               "2. 检查Python是否正确安装" & vbCrLf & _
               "3. 检查Python是否添加到PATH环境变量" & vbCrLf & _
               "4. 手动安装依赖：pip install jieba (中文分词库)" & vbCrLf & _
               "5. 尝试重新安装Python", vbCritical, "启动错误"
        WScript.Quit
    End If
End If

On Error GoTo 0

' 全面系统稳定性检查函数
Function ComprehensiveStabilityCheck()
    On Error Resume Next

    ' 显示检查进度
    MsgBox "正在进行全面系统稳定性检查..." & vbCrLf & vbCrLf & _
           "这可能需要几秒钟时间，请稍等。", vbInformation, "系统检查中"

    ' 1. 运行系统稳定性检查
    Dim stabilityCommand, stabilityResult
    stabilityCommand = "cmd /c ""cd /d """ & currentDir & """ && python 系统稳定性全面保障方案.py --auto-repair"""
    stabilityResult = objShell.Run(stabilityCommand, 0, True)

    If stabilityResult <> 0 Then
        ' 稳定性检查失败，尝试基础修复
        Dim basicRepairResult
        basicRepairResult = BasicSystemRepair()
        If Not basicRepairResult Then
            ComprehensiveStabilityCheck = False
            Exit Function
        End If
    End If

    ' 2. 运行系统恢复检查
    Dim recoveryCommand, recoveryResult
    recoveryCommand = "cmd /c ""cd /d """ & currentDir & """ && python 系统重启恢复机制.py --auto-recovery"""
    recoveryResult = objShell.Run(recoveryCommand, 0, True)

    ' 3. 保存当前系统状态
    Dim saveStateCommand
    saveStateCommand = "cmd /c ""cd /d """ & currentDir & """ && python 系统重启恢复机制.py --save-state"""
    objShell.Run saveStateCommand, 0, True

    ComprehensiveStabilityCheck = True
    On Error GoTo 0
End Function

' 基础系统修复函数
Function BasicSystemRepair()
    On Error Resume Next

    ' 1. 检查并安装jieba依赖包
    Dim jiebaCheck, jiebaResult
    jiebaCheck = "cmd /c ""cd /d """ & currentDir & """ && python -c ""import jieba"" 2>nul"""
    jiebaResult = objShell.Run(jiebaCheck, 0, True)

    If jiebaResult <> 0 Then
        ' 安装jieba依赖包
        Dim installJieba
        installJieba = "cmd /c ""cd /d """ & currentDir & """ && pip install jieba"""
        objShell.Run installJieba, 1, True
    End If

    ' 2. 检查核心文件
    Dim coreFiles(10)
    coreFiles(0) = "gui_main.py"
    coreFiles(1) = "email_sender.py"
    coreFiles(2) = "email_history_manager.py"
    coreFiles(3) = "rag_search_engine.py"
    coreFiles(4) = "深度系统协调实现.py"
    coreFiles(5) = "recipient_quality_manager.py"
    coreFiles(6) = "anti_spam_manager.py"
    coreFiles(7) = "qq_email_anti_spam.py"
    coreFiles(8) = "system_coordinator.py"
    coreFiles(9) = "batch_manager.py"
    coreFiles(10) = "queue_system.py"

    Dim missingFiles, i
    missingFiles = ""
    For i = 0 To UBound(coreFiles)
        If Not objFSO.FileExists(currentDir & "\" & coreFiles(i)) Then
            missingFiles = missingFiles & coreFiles(i) & vbCrLf
        End If
    Next

    If missingFiles <> "" Then
        MsgBox "发现缺失的核心文件：" & vbCrLf & vbCrLf & missingFiles & vbCrLf & _
               "请确保所有文件完整后重新启动。", vbExclamation, "文件缺失"
        BasicSystemRepair = False
        Exit Function
    End If

    ' 3. 创建必要目录
    If Not objFSO.FolderExists(currentDir & "\logs") Then
        objFSO.CreateFolder(currentDir & "\logs")
    End If

    If Not objFSO.FolderExists(currentDir & "\user_data") Then
        objFSO.CreateFolder(currentDir & "\user_data")
    End If

    If Not objFSO.FolderExists(currentDir & "\config_backup") Then
        objFSO.CreateFolder(currentDir & "\config_backup")
    End If

    BasicSystemRepair = True
    On Error GoTo 0
End Function

' 如果没有错误，显示启动信息（可选）
' MsgBox "邮件系统正在启动..." & vbCrLf & "请稍等片刻。", vbInformation, "启动中"
