# -*- coding: utf-8 -*-
"""
批次发送功能测试脚本
"""

import sys
import time
from email_sender import EmailSender
from batch_manager import <PERSON>chMana<PERSON>

def test_batch_manager():
    """测试批次管理器"""
    print("🧪 测试批次管理器")
    print("=" * 50)
    
    # 测试不同模式
    modes = ['fast', 'standard', 'safe']
    test_email_counts = [50, 100, 200]
    
    for mode in modes:
        print(f"\n📋 测试模式: {mode}")
        print("-" * 30)
        
        manager = BatchManager(mode)
        
        for email_count in test_email_counts:
            batch_info = manager.calculate_batches(email_count)
            
            print(f"邮件数量: {email_count}")
            print(f"  批次数量: {batch_info['total_batches']}")
            print(f"  批次大小: {batch_info['batch_size']}")
            print(f"  预计耗时: {batch_info['estimated_time_hours']:.1f} 小时")
            print(f"  是否超限: {'是' if not batch_info['within_limit'] else '否'}")
            print()

def test_batch_intervals():
    """测试批次间隔"""
    print("\n🕐 测试批次间隔")
    print("=" * 50)
    
    manager = BatchManager('standard')
    
    print("邮件间隔测试 (10次):")
    for i in range(10):
        interval = manager.get_email_interval()
        print(f"  第{i+1}次: {interval:.1f}秒")
    
    print("\n批次间隔测试 (5次):")
    for i in range(5):
        interval = manager.get_batch_interval()
        print(f"  第{i+1}次: {interval:.1f}分钟")

def test_recommendations():
    """测试发送建议"""
    print("\n💡 测试发送建议")
    print("=" * 50)
    
    manager = BatchManager('safe')
    
    test_counts = [50, 150, 600]
    for count in test_counts:
        print(f"\n邮件数量: {count}")
        recommendations = manager.get_recommendations(count)
        for i, rec in enumerate(recommendations, 1):
            print(f"  {i}. {rec}")

def simulate_batch_sending():
    """模拟批次发送过程"""
    print("\n🚀 模拟批次发送过程")
    print("=" * 50)
    
    # 模拟邮件列表
    email_list = []
    for i in range(25):  # 模拟25封邮件
        email_list.append({
            'to_emails': [f'test{i+1}@example.com'],
            'subject': f'测试邮件 #{i+1}',
            'body': f'这是第 {i+1} 封测试邮件的内容。',
            'attachments': None
        })
    
    print(f"准备发送 {len(email_list)} 封邮件")
    
    # 创建批次管理器
    manager = BatchManager('fast')  # 使用快速模式进行测试
    batch_info = manager.calculate_batches(len(email_list))
    
    print(f"批次信息:")
    print(f"  总批次: {batch_info['total_batches']}")
    print(f"  批次大小: {batch_info['batch_size']}")
    print(f"  预计耗时: {batch_info['estimated_time_minutes']:.1f} 分钟")
    
    # 模拟发送过程
    print(f"\n开始模拟发送...")
    
    for batch_num in range(1, batch_info['total_batches'] + 1):
        batch_emails = manager.get_batch_emails(email_list, batch_num)
        manager.log_batch_start(batch_num, len(batch_emails))
        
        # 模拟发送批次内的邮件
        success_count = 0
        failed_count = 0
        
        for i, email_info in enumerate(batch_emails, 1):
            print(f"  📤 模拟发送: {email_info['to_emails'][0]}")
            
            # 模拟发送结果 (90% 成功率)
            import random
            if random.random() < 0.9:
                success_count += 1
                print(f"    ✅ 发送成功")
            else:
                failed_count += 1
                print(f"    ❌ 发送失败")
            
            # 模拟邮件间隔
            if i < len(batch_emails):
                email_interval = manager.get_email_interval()
                print(f"    ⏱️ 等待 {email_interval:.1f}秒...")
                time.sleep(0.1)  # 实际测试时缩短等待时间
        
        manager.log_batch_complete(batch_num, success_count, failed_count)
        
        # 批次间隔
        should_wait, wait_minutes = manager.should_wait_between_batches(batch_num)
        if should_wait:
            manager.log_batch_wait(wait_minutes)
            print(f"    💤 模拟批次间隔等待...")
            time.sleep(0.2)  # 实际测试时缩短等待时间
    
    # 显示最终统计
    progress_info = manager.get_progress_info()
    print(f"\n📊 发送完成统计:")
    for key, value in progress_info.items():
        print(f"  {key}: {value}")

def main():
    """主函数"""
    print("🧪 批次发送功能测试")
    print("=" * 60)
    
    try:
        # 运行各项测试
        test_batch_manager()
        test_batch_intervals()
        test_recommendations()
        simulate_batch_sending()
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
