#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查深度协调系统是否真正在GUI中生效
"""

import tkinter as tk
import sys
import os

def check_coordination_status():
    """检查深度协调系统状态"""
    print("=== 深度协调系统状态检查 ===")
    
    try:
        # 导入GUI
        from gui_main import EmailSenderGUI
        
        # 创建测试窗口（不显示）
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 创建应用实例
        app = EmailSenderGUI(root)
        
        # 检查深度协调系统状态
        print(f"是否有coordinator属性: {hasattr(app, 'coordinator')}")
        
        if hasattr(app, 'coordinator'):
            print(f"coordinator是否为None: {app.coordinator is None}")
            if app.coordinator:
                print(f"coordinator类型: {type(app.coordinator)}")
                print("✅ 深度协调系统已启用")
                
                # 检查协调器的具体功能
                if hasattr(app.coordinator, 'data_center'):
                    print("✅ 数据中心已初始化")
                if hasattr(app.coordinator, 'decision_engine'):
                    print("✅ 决策引擎已初始化")
                    
            else:
                print("❌ 深度协调系统未启用（coordinator为None）")
        else:
            print("❌ 没有coordinator属性")
        
        # 检查深度协调模块是否可用
        try:
            from 深度系统协调实现 import get_coordinator, DEEP_COORDINATION_AVAILABLE
            print(f"\n深度协调模块可用性: {DEEP_COORDINATION_AVAILABLE}")
            if DEEP_COORDINATION_AVAILABLE:
                coordinator = get_coordinator()
                print(f"协调器实例类型: {type(coordinator)}")
            else:
                print("❌ 深度协调模块不可用")
        except ImportError as e:
            print(f"❌ 无法导入深度协调模块: {e}")
        
        # 检查界面日志内容
        print(f"\n=== 界面日志检查 ===")
        if hasattr(app, 'log_text'):
            log_content = app.log_text.get('1.0', 'end')
            coordination_logs = []
            
            lines = log_content.split('\n')
            for line in lines:
                if any(keyword in line for keyword in ['深度协调', '协调系统', '🔧', '✅']):
                    if line.strip():
                        coordination_logs.append(line.strip())
            
            if coordination_logs:
                print("✅ 发现深度协调相关日志:")
                for log in coordination_logs:
                    print(f"  {log}")
            else:
                print("❌ 未发现深度协调相关日志")
                print("前10行日志内容:")
                for i, line in enumerate(lines[:10]):
                    if line.strip():
                        print(f"  {i+1}: {line.strip()}")
        else:
            print("❌ 没有log_text组件")
        
        # 检查界面按钮
        print(f"\n=== 界面功能检查 ===")
        
        # 检查是否有系统协调器按钮
        if hasattr(app, 'open_system_coordinator'):
            print("✅ 系统协调器方法存在")
        else:
            print("❌ 系统协调器方法不存在")
        
        # 检查窗口标题
        print(f"窗口标题: {root.title()}")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_coordination_files():
    """检查深度协调相关文件"""
    print("\n=== 深度协调文件检查 ===")
    
    coordination_files = [
        "深度系统协调实现.py",
        "gui_main.py"
    ]
    
    for file_name in coordination_files:
        if os.path.exists(file_name):
            print(f"✅ {file_name} 存在")
            
            # 检查文件内容
            try:
                with open(file_name, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if file_name == "深度系统协调实现.py":
                    if "DEEP_COORDINATION_AVAILABLE = True" in content:
                        print("  ✅ 深度协调功能已启用")
                    else:
                        print("  ❌ 深度协调功能未启用")
                
                elif file_name == "gui_main.py":
                    if "setup_deep_coordination" in content:
                        print("  ✅ 包含深度协调设置方法")
                    if "self.coordinator" in content:
                        print("  ✅ 包含协调器属性")
                    if "深度协调系统已启用" in content:
                        print("  ✅ 包含协调系统启用日志")
                        
            except Exception as e:
                print(f"  ❌ 读取文件失败: {e}")
        else:
            print(f"❌ {file_name} 不存在")

def check_coordination_impact():
    """检查深度协调的实际影响"""
    print("\n=== 深度协调影响检查 ===")
    
    try:
        from 深度系统协调实现 import get_coordinator
        coordinator = get_coordinator()
        
        # 测试协调器功能
        test_email = "<EMAIL>"
        
        # 获取系统状态
        status = coordinator.get_system_status(test_email)
        print(f"✅ 系统状态获取成功:")
        print(f"  发件人: {status['sender_email']}")
        print(f"  协调状态: {status['coordination_status']}")
        print(f"  风险等级: {status['state']['risk_level']}")
        
        # 获取推荐策略
        strategy = status['strategy']
        print(f"✅ 推荐策略:")
        print(f"  发送模式: {strategy['recommended_mode']}")
        print(f"  最大批次: {strategy['max_batch_size']}")
        print(f"  发送间隔: {strategy['send_interval']} 秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 深度协调功能测试失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🔧 深度协调系统全面检查")
    print("=" * 60)
    
    # 检查文件
    check_coordination_files()
    
    # 检查状态
    status_ok = check_coordination_status()
    
    # 检查影响
    impact_ok = check_coordination_impact()
    
    print(f"\n📊 检查结果总结")
    print("=" * 40)
    
    if status_ok and impact_ok:
        print("🎉 深度协调系统正常工作！")
        print("✅ 系统功能已深度协调配合")
        print("✅ 界面集成成功")
        print("✅ 功能测试通过")
        
        print("\n💡 深度协调效果:")
        print("• 智能风险评估和策略推荐")
        print("• 事件驱动的功能协调")
        print("• 多维度数据分析")
        print("• 自动化决策支持")
        
    else:
        print("⚠️ 深度协调系统存在问题")
        print("💡 可能的原因:")
        print("• 深度协调模块未正确导入")
        print("• GUI集成不完整")
        print("• 初始化顺序问题")
        
        print("\n🔧 建议解决方案:")
        print("1. 检查深度系统协调实现.py文件")
        print("2. 确认DEEP_COORDINATION_AVAILABLE = True")
        print("3. 重新启动程序")

if __name__ == "__main__":
    main()
