# 自动化邮件发送助手 - 项目说明

## 项目概述

这是一个专为QQ邮箱设计的自动化邮件发送工具，旨在简化日常邮件发送工作，特别是需要发送附件的场景。项目采用Python开发，提供了命令行和图形界面两种使用方式。

## 核心功能

### ✅ 已实现功能
1. **QQ邮箱SMTP发送** - 完全支持QQ邮箱的SMTP服务
2. **多格式附件支持** - 支持文档、图片、压缩包等常见格式
3. **多收件人发送** - 支持同时发送给多个收件人
4. **批量邮件发送** - 支持批量发送不同内容的邮件
5. **图形界面** - 提供友好的GUI界面，操作简单直观
6. **命令行界面** - 提供灵活的命令行操作方式
7. **智能防垃圾邮件** - 采用最佳实践避免邮件进入垃圾箱
8. **详细日志记录** - 完整的操作日志，便于问题排查
9. **环境检查** - 自动检查运行环境和依赖
10. **错误处理** - 完善的异常处理和用户提示

### 🔒 安全特性
- SMTP授权码已预配置（vwpboqxircdudgfa）
- 支持TLS加密传输
- 本地运行，数据不上传到第三方服务器
- 详细的操作日志记录

### 🚀 性能优化
- 发送频率控制，避免被识别为垃圾邮件
- 文件大小检查，防止发送过大附件
- 多线程处理，界面不会卡顿
- 内存优化，支持大文件附件

## 文件结构

```
自动发邮件/
├── config.py              # 配置文件（SMTP设置、邮件头等）
├── email_sender.py         # 核心邮件发送类
├── main.py                 # 命令行主程序
├── gui_main.py            # 图形界面主程序
├── test_email.py          # 功能测试脚本
├── setup.py               # 环境检查与设置脚本
├── example_usage.py       # 使用示例代码
├── start.bat              # Windows启动脚本
├── requirements.txt       # 依赖包列表
├── README.md              # 详细使用说明
├── 项目说明.md            # 项目概述（本文件）
└── sample_attachment.txt  # 示例附件文件
```

## 使用方式

### 1. 快速开始（推荐）
```bash
# 双击运行启动脚本
start.bat

# 选择 "3. 图形界面版本（推荐）"
```

### 2. 命令行使用
```bash
# 单邮件发送
python main.py

# 批量发送
python main.py --batch

# 功能测试
python test_email.py
```

### 3. 编程调用
```python
from email_sender import EmailSender

sender = EmailSender("<EMAIL>")
success = sender.send_email(
    to_emails=["<EMAIL>"],
    subject="测试邮件",
    body="邮件内容",
    attachments=["file.pdf"]
)
```

## 技术特点

### 防垃圾邮件策略
1. **规范的邮件头设置**
   - 设置合适的邮件优先级
   - 添加标准的邮件客户端标识
   - 使用正确的字符编码

2. **发送频率控制**
   - 邮件间隔发送（默认2秒）
   - 限制每小时发送数量
   - 避免被识别为垃圾邮件

3. **内容规范**
   - 支持的附件格式检查
   - 文件大小限制（25MB）
   - 合理的邮件结构

### 错误处理
- SMTP认证错误处理
- 网络连接异常处理
- 文件不存在检查
- 邮箱地址格式验证
- 附件大小和格式检查

## 配置说明

### SMTP配置（config.py）
```python
SMTP_CONFIG = {
    'server': 'smtp.qq.com',
    'port': 587,
    'use_tls': True,
    'username': '',  # 自动设置为用户输入的邮箱
    'password': 'vwpboqxircdudgfa',  # 您的授权码
}
```

### 支持的附件格式
- 文档：.txt, .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx
- 图片：.jpg, .jpeg, .png, .gif
- 压缩包：.zip, .rar, .7z

## 常见问题解决

### Q: 邮件发送失败？
**A:** 请检查：
1. QQ邮箱地址是否正确
2. 网络连接是否正常
3. 查看 `email_sender.log` 日志文件

### Q: 收件人收不到邮件？
**A:** 请提醒收件人：
1. 检查垃圾邮件文件夹
2. 将您的邮箱添加到通讯录
3. 检查邮箱设置

### Q: 附件太大无法发送？
**A:** 
1. 单个附件限制25MB
2. 建议压缩大文件
3. 或使用网盘链接方式

## 系统要求

- **操作系统**: Windows 7/8/10/11
- **Python版本**: 3.6 或更高版本
- **网络要求**: 能够访问QQ邮箱SMTP服务器
- **依赖库**: 仅使用Python标准库，无需安装额外包

## 安全提示

⚠️ **重要提醒**：
- 请妥善保管您的SMTP授权码
- 不要在公共场所使用本工具
- 定期更换授权码以确保安全
- 请遵守相关法律法规，不要用于发送垃圾邮件

## 更新日志

### v1.0.0 (当前版本)
- ✅ 实现基本邮件发送功能
- ✅ 添加图形界面支持
- ✅ 实现批量发送功能
- ✅ 添加防垃圾邮件机制
- ✅ 完善错误处理和日志记录
- ✅ 提供完整的使用文档和示例

## 技术支持

如遇问题，请：
1. 查看 `README.md` 详细说明
2. 运行 `python setup.py` 进行环境检查
3. 查看 `email_sender.log` 日志文件
4. 参考 `example_usage.py` 示例代码

---

**开发说明**: 本项目使用Python标准库开发，无需安装额外依赖，确保了良好的兼容性和稳定性。
