# -*- coding: utf-8 -*-
"""
验证QQ邮箱授权码是否有效
"""

import smtplib
from config import SMTP_CONFIG

def test_qq_smtp():
    """测试QQ邮箱SMTP连接"""
    print("QQ邮箱SMTP授权码验证")
    print("=" * 40)
    
    # 显示当前配置
    print(f"SMTP服务器: {SMTP_CONFIG['server']}")
    print(f"端口: {SMTP_CONFIG['port']}")
    print(f"授权码: {SMTP_CONFIG['password']}")
    print("-" * 40)
    
    # 获取用户邮箱
    sender_email = input("请输入您的QQ邮箱地址: ").strip()
    
    if not sender_email.endswith('@qq.com'):
        print("❌ 请输入有效的QQ邮箱地址")
        return False
    
    try:
        print("\n开始测试SMTP连接...")
        
        # 1. 连接SMTP服务器
        print("1. 连接SMTP服务器...")
        server = smtplib.SMTP(SMTP_CONFIG['server'], SMTP_CONFIG['port'])
        print("✓ 连接成功")
        
        # 2. 启用TLS加密
        print("2. 启用TLS加密...")
        server.starttls()
        print("✓ TLS加密启用成功")
        
        # 3. SMTP认证
        print("3. 进行SMTP认证...")
        server.login(sender_email, SMTP_CONFIG['password'])
        print("✓ SMTP认证成功")
        
        # 4. 关闭连接
        server.quit()
        print("✓ 连接已关闭")
        
        print("\n🎉 授权码验证成功！")
        print("您的QQ邮箱SMTP配置正常，可以发送邮件。")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"\n❌ SMTP认证失败: {str(e)}")
        print("\n可能的原因：")
        print("1. 邮箱地址错误")
        print("2. 授权码错误或已过期")
        print("3. 未开启SMTP服务")
        print("\n解决方案：")
        print("1. 检查邮箱地址是否正确")
        print("2. 重新获取SMTP授权码")
        print("3. 确认已开启SMTP/IMAP服务")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"\n❌ SMTP连接失败: {str(e)}")
        print("\n可能的原因：")
        print("1. 网络连接问题")
        print("2. 防火墙阻止连接")
        print("3. SMTP服务器暂时不可用")
        return False
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    try:
        success = test_qq_smtp()
        
        if success:
            print("\n" + "=" * 40)
            print("✅ 测试通过！现在可以使用图形界面发送邮件了。")
            print("=" * 40)
        else:
            print("\n" + "=" * 40)
            print("❌ 测试失败！请检查配置后重试。")
            print("=" * 40)
            
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n程序出错: {str(e)}")
    
    input("\n按回车键退出...")
