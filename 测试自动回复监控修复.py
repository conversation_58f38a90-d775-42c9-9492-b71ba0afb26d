#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动回复监控修复 - 验证只监控指定收件人的回复
"""

import tkinter as tk
from email_receiver import EmailReceiver
from gui_main import EmailSenderGUI

def test_targeted_reply_monitoring():
    """测试目标收件人回复监控"""
    print("🎯 测试目标收件人回复监控")
    print("=" * 60)
    
    try:
        test_email = "<EMAIL>"
        test_password = "test_password"
        
        # 模拟发送的收件人列表
        sent_recipients = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ]
        
        print(f"📧 模拟发送邮件给: {sent_recipients}")
        
        # 创建邮件接收器
        receiver = EmailReceiver(test_email, test_password)
        
        print("\n🔍 测试目标收件人筛选功能...")
        
        # 模拟邮件内容
        test_emails = [
            {
                'from': '<EMAIL>',
                'subject': 'QQ邮箱自动回复',
                'body': '谢谢您的邮件，我目前不在办公室。',
                'reply_time': '2025-06-12 21:00:00'
            },
            {
                'from': '<EMAIL>',  # 不在目标列表中
                'subject': 'Auto Reply',
                'body': 'I am out of office.',
                'reply_time': '2025-06-12 21:01:00'
            },
            {
                'from': '<EMAIL>',
                'subject': 'Mail delivery failed',
                'body': 'The following message could not be delivered',
                'reply_time': '2025-06-12 21:02:00'
            }
        ]
        
        # 测试自动回复识别
        target_replies = []
        non_target_replies = []
        
        for email_content in test_emails:
            is_auto, reply_type = receiver.is_auto_reply(email_content)
            sender = receiver.extract_original_recipient(email_content)
            
            if is_auto and sender:
                if sender in sent_recipients:
                    target_replies.append({
                        'recipient_email': sender,
                        'reply_type': reply_type,
                        'subject': email_content['subject']
                    })
                    print(f"  ✅ 目标收件人回复: {sender} -> {reply_type}")
                else:
                    non_target_replies.append({
                        'recipient_email': sender,
                        'reply_type': reply_type,
                        'subject': email_content['subject']
                    })
                    print(f"  ⚪ 非目标收件人回复: {sender} -> {reply_type}")
            else:
                print(f"  ❌ 非自动回复: {sender}")
        
        print(f"\n📊 筛选结果:")
        print(f"  目标收件人回复: {len(target_replies)} 个")
        print(f"  非目标收件人回复: {len(non_target_replies)} 个")
        
        # 验证筛选逻辑
        expected_target_count = 2  # <EMAIL> 和 <EMAIL>
        expected_non_target_count = 1  # <EMAIL>
        
        if len(target_replies) == expected_target_count and len(non_target_replies) == expected_non_target_count:
            print("  ✅ 筛选逻辑正确")
            return True
        else:
            print(f"  ❌ 筛选逻辑错误: 期望目标{expected_target_count}个，非目标{expected_non_target_count}个")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_auto_monitoring_integration():
    """测试自动监控集成功能"""
    print("\n🔗 测试自动监控集成功能")
    print("=" * 60)
    
    try:
        # 创建GUI
        root = tk.Tk()
        app = EmailSenderGUI(root)
        
        # 设置测试邮箱
        test_email = "<EMAIL>"
        app.sender_email.delete(0, tk.END)
        app.sender_email.insert(0, test_email)
        
        # 设置收件人
        test_recipients = ["<EMAIL>", "<EMAIL>"]
        app.recipient_emails.delete(1.0, tk.END)
        app.recipient_emails.insert(1.0, "\n".join(test_recipients))
        
        print(f"📧 设置测试邮箱: {test_email}")
        print(f"📧 设置测试收件人: {test_recipients}")
        
        # 模拟保存密码
        app.auth_codes[test_email] = {
            'auth_code': 'test_password',
            'add_time': '2025-06-12 21:00:00'
        }
        
        print("🔐 模拟保存IMAP密码")
        
        # 测试自动启动监控方法
        print("\n🚀 测试自动启动监控...")
        
        try:
            # 这里不会真正启动监控（因为密码是假的），但会测试逻辑
            app.auto_start_reply_monitoring(test_email, test_recipients)
            print("  ✅ 自动启动监控方法调用成功")
        except Exception as e:
            if "IMAP" in str(e) or "连接" in str(e):
                print("  ✅ 自动启动监控方法逻辑正确（IMAP连接失败是预期的）")
            else:
                print(f"  ❌ 自动启动监控方法异常: {str(e)}")
                return False
        
        # 关闭GUI
        root.destroy()
        
        print("✅ 自动监控集成测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        return False

def test_qq_keyword_fix():
    """测试QQ关键词修复"""
    print("\n🔧 测试QQ关键词修复")
    print("=" * 60)
    
    try:
        test_email = "<EMAIL>"
        receiver = EmailReceiver(test_email, "test_password")
        
        # 测试用例
        test_cases = [
            {
                'name': '普通QQ邮件（不应该被识别为自动回复）',
                'email': {
                    'subject': '工作邮件',
                    'body': '请查看附件中的文档。',
                    'from': '<EMAIL>',
                    'headers': {}
                },
                'expected': False
            },
            {
                'name': '真正的QQ自动回复',
                'email': {
                    'subject': 'QQ邮箱自动回复',
                    'body': '谢谢您的邮件，我目前不在办公室。',
                    'from': '<EMAIL>',
                    'headers': {}
                },
                'expected': True
            },
            {
                'name': '包含自动回复模式的QQ邮件',
                'email': {
                    'subject': '回复',
                    'body': '您好，我现在有事不在，稍后回复您。',
                    'from': '<EMAIL>',
                    'headers': {}
                },
                'expected': True
            }
        ]
        
        success_count = 0
        for test_case in test_cases:
            is_auto, reply_type = receiver.is_auto_reply(test_case['email'])
            expected = test_case['expected']
            
            if is_auto == expected:
                print(f"  ✅ {test_case['name']}: {'自动回复' if is_auto else '普通邮件'}")
                success_count += 1
            else:
                print(f"  ❌ {test_case['name']}: 期望 {'自动回复' if expected else '普通邮件'}, 实际 {'自动回复' if is_auto else '普通邮件'}")
        
        print(f"\n识别准确率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        
        if success_count == len(test_cases):
            print("✅ QQ关键词修复测试成功")
            return True
        else:
            print("⚠️ QQ关键词修复测试部分成功")
            return False
        
    except Exception as e:
        print(f"❌ QQ关键词测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始自动回复监控修复测试")
    print("=" * 80)
    
    # 执行各项测试
    tests = [
        ("目标收件人回复监控", test_targeted_reply_monitoring),
        ("自动监控集成功能", test_auto_monitoring_integration),
        ("QQ关键词修复", test_qq_keyword_fix)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {str(e)}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("🎯 测试结果总结")
    print("=" * 80)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("\n🎉 自动回复监控修复验证成功！")
        print("✅ 目标收件人筛选逻辑正确")
        print("✅ 自动监控集成功能正常")
        print("✅ QQ关键词识别精确")
        print("\n💡 现在监控系统会：")
        print("  🎯 只监控您发送邮件的收件人")
        print("  🔍 准确识别真正的自动回复")
        print("  ⚪ 忽略非目标收件人的邮件")
        print("  📊 提供精确的监控结果")
        return True
    else:
        print("\n⚠️ 部分修复需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
