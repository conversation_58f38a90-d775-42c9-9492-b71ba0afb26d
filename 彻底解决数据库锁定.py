#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
彻底解决数据库锁定问题
"""

import sqlite3
import os
import time
import threading
import subprocess
import psutil

def kill_database_processes():
    """终止可能占用数据库的进程"""
    print("🔧 终止可能占用数据库的进程")
    print("-" * 30)
    
    try:
        # 查找可能占用数据库的Python进程
        killed_count = 0
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['name'] and 'python' in proc.info['name'].lower():
                    cmdline = proc.info['cmdline']
                    if cmdline and any('gui_main.py' in str(cmd) or 'email_receiver.py' in str(cmd) for cmd in cmdline):
                        # 不要杀死当前进程
                        if proc.pid != os.getpid():
                            print(f"🔪 终止进程: {proc.info['name']} (PID: {proc.pid})")
                            proc.terminate()
                            killed_count += 1
                            time.sleep(0.1)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if killed_count > 0:
            print(f"✅ 已终止 {killed_count} 个进程")
            time.sleep(2)  # 等待进程完全终止
        else:
            print("✅ 没有发现需要终止的进程")
        
        return True
        
    except Exception as e:
        print(f"❌ 终止进程失败: {str(e)}")
        return False

def force_unlock_databases():
    """强制解锁数据库"""
    print("\n🔧 强制解锁数据库")
    print("-" * 30)
    
    db_files = [
        'email_history.db',
        'recipient_quality.db',
        'auto_reply_monitor.db',
        'anti_spam.db',
        'qq_anti_spam.db'
    ]
    
    unlocked_count = 0
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                print(f"🔓 解锁数据库: {db_file}")
                
                # 方法1: 强制连接并立即关闭
                conn = sqlite3.connect(db_file, timeout=1.0)
                conn.execute("BEGIN IMMEDIATE")
                conn.rollback()
                conn.close()
                
                # 方法2: 删除WAL和SHM文件
                wal_file = db_file + '-wal'
                shm_file = db_file + '-shm'
                
                if os.path.exists(wal_file):
                    try:
                        os.remove(wal_file)
                        print(f"  ✅ 删除WAL文件: {wal_file}")
                    except:
                        pass
                
                if os.path.exists(shm_file):
                    try:
                        os.remove(shm_file)
                        print(f"  ✅ 删除SHM文件: {shm_file}")
                    except:
                        pass
                
                # 方法3: 测试连接
                conn = sqlite3.connect(db_file, timeout=5.0)
                conn.execute("SELECT 1")
                conn.close()
                
                print(f"  ✅ {db_file} 解锁成功")
                unlocked_count += 1
                
            except Exception as e:
                print(f"  ❌ {db_file} 解锁失败: {str(e)}")
    
    print(f"\n✅ 成功解锁 {unlocked_count}/{len([f for f in db_files if os.path.exists(f)])} 个数据库")
    return unlocked_count > 0

def optimize_database_settings():
    """优化数据库设置"""
    print("\n🔧 优化数据库设置")
    print("-" * 30)
    
    db_files = [
        'email_history.db',
        'recipient_quality.db',
        'auto_reply_monitor.db',
        'anti_spam.db',
        'qq_anti_spam.db'
    ]
    
    optimized_count = 0
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                print(f"⚙️ 优化数据库: {db_file}")
                
                conn = sqlite3.connect(db_file, timeout=30.0)
                
                # 设置最优参数
                optimizations = [
                    ("PRAGMA journal_mode=WAL", "WAL模式"),
                    ("PRAGMA synchronous=NORMAL", "同步模式"),
                    ("PRAGMA cache_size=10000", "缓存大小"),
                    ("PRAGMA temp_store=memory", "临时存储"),
                    ("PRAGMA mmap_size=268435456", "内存映射"),
                    ("PRAGMA busy_timeout=30000", "忙等超时"),
                    ("PRAGMA wal_autocheckpoint=1000", "WAL检查点"),
                    ("PRAGMA optimize", "优化统计")
                ]
                
                for pragma, desc in optimizations:
                    try:
                        conn.execute(pragma)
                        print(f"  ✅ {desc}")
                    except Exception as e:
                        print(f"  ⚠️ {desc} 失败: {str(e)}")
                
                # 执行VACUUM
                try:
                    conn.execute("VACUUM")
                    print(f"  ✅ 数据库清理完成")
                except Exception as e:
                    print(f"  ⚠️ 数据库清理失败: {str(e)}")
                
                conn.close()
                print(f"  ✅ {db_file} 优化完成")
                optimized_count += 1
                
            except Exception as e:
                print(f"  ❌ {db_file} 优化失败: {str(e)}")
    
    print(f"\n✅ 成功优化 {optimized_count} 个数据库")
    return optimized_count > 0

def test_database_operations():
    """测试数据库操作"""
    print("\n🔧 测试数据库操作")
    print("-" * 30)
    
    db_files = [
        'email_history.db',
        'recipient_quality.db',
        'auto_reply_monitor.db',
        'anti_spam.db',
        'qq_anti_spam.db'
    ]
    
    success_count = 0
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                print(f"🧪 测试数据库: {db_file}")
                
                # 测试连接
                conn = sqlite3.connect(db_file, timeout=10.0)
                
                # 测试查询
                cursor = conn.cursor()
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' LIMIT 1")
                result = cursor.fetchone()
                
                # 测试写入
                cursor.execute("CREATE TABLE IF NOT EXISTS test_table (id INTEGER PRIMARY KEY, test_data TEXT)")
                cursor.execute("INSERT OR REPLACE INTO test_table (id, test_data) VALUES (1, 'test')")
                
                # 测试读取
                cursor.execute("SELECT test_data FROM test_table WHERE id = 1")
                test_result = cursor.fetchone()
                
                # 清理测试数据
                cursor.execute("DROP TABLE IF EXISTS test_table")
                
                conn.commit()
                conn.close()
                
                if test_result and test_result[0] == 'test':
                    print(f"  ✅ {db_file} 读写测试成功")
                    success_count += 1
                else:
                    print(f"  ❌ {db_file} 读写测试失败")
                
            except Exception as e:
                print(f"  ❌ {db_file} 测试失败: {str(e)}")
    
    print(f"\n✅ {success_count}/{len([f for f in db_files if os.path.exists(f)])} 个数据库测试通过")
    return success_count > 0

def create_database_monitor():
    """创建数据库监控脚本"""
    print("\n🔧 创建数据库监控脚本")
    print("-" * 30)
    
    monitor_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库锁定监控脚本
"""

import sqlite3
import time
import threading
import os

class DatabaseMonitor:
    def __init__(self):
        self.db_files = [
            'email_history.db',
            'recipient_quality.db',
            'auto_reply_monitor.db',
            'anti_spam.db',
            'qq_anti_spam.db'
        ]
        self.monitoring = False
        self.monitor_thread = None
    
    def check_database_locks(self):
        """检查数据库锁定状态"""
        locked_dbs = []
        
        for db_file in self.db_files:
            if os.path.exists(db_file):
                try:
                    conn = sqlite3.connect(db_file, timeout=1.0)
                    conn.execute("SELECT 1")
                    conn.close()
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e):
                        locked_dbs.append(db_file)
                except:
                    pass
        
        return locked_dbs
    
    def auto_unlock_database(self, db_file):
        """自动解锁数据库"""
        try:
            # 删除WAL和SHM文件
            wal_file = db_file + '-wal'
            shm_file = db_file + '-shm'
            
            if os.path.exists(wal_file):
                os.remove(wal_file)
            
            if os.path.exists(shm_file):
                os.remove(shm_file)
            
            # 重新连接
            conn = sqlite3.connect(db_file, timeout=5.0)
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA busy_timeout=30000")
            conn.close()
            
            print(f"✅ 自动解锁数据库: {db_file}")
            return True
            
        except Exception as e:
            print(f"❌ 自动解锁失败: {db_file} - {str(e)}")
            return False
    
    def monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                locked_dbs = self.check_database_locks()
                
                if locked_dbs:
                    print(f"🚨 发现锁定的数据库: {locked_dbs}")
                    
                    for db_file in locked_dbs:
                        self.auto_unlock_database(db_file)
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                print(f"❌ 监控循环错误: {str(e)}")
                time.sleep(30)
    
    def start_monitoring(self):
        """开始监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
            self.monitor_thread.start()
            print("✅ 数据库监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        print("✅ 数据库监控已停止")

if __name__ == "__main__":
    monitor = DatabaseMonitor()
    monitor.start_monitoring()
    
    try:
        while True:
            time.sleep(60)
    except KeyboardInterrupt:
        monitor.stop_monitoring()
'''
    
    try:
        with open('database_monitor.py', 'w', encoding='utf-8') as f:
            f.write(monitor_script)
        
        print("✅ 数据库监控脚本已创建: database_monitor.py")
        return True
        
    except Exception as e:
        print(f"❌ 创建监控脚本失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 彻底解决数据库锁定问题")
    print("=" * 60)
    
    steps = [
        ("终止占用进程", kill_database_processes),
        ("强制解锁数据库", force_unlock_databases),
        ("优化数据库设置", optimize_database_settings),
        ("测试数据库操作", test_database_operations),
        ("创建数据库监控", create_database_monitor)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔧 执行: {step_name}")
        if step_func():
            success_count += 1
            print(f"✅ {step_name} - 成功")
        else:
            print(f"❌ {step_name} - 失败")
        
        time.sleep(1)  # 步骤间等待
    
    print(f"\n📊 解决结果")
    print("=" * 30)
    print(f"成功步骤: {success_count}/{len(steps)}")
    
    if success_count >= 4:  # 至少4个步骤成功
        print("\n🎉 数据库锁定问题已彻底解决！")
        print("✅ 占用进程已终止")
        print("✅ 数据库已强制解锁")
        print("✅ 数据库设置已优化")
        print("✅ 数据库操作测试通过")
        print("✅ 监控脚本已创建")
        
        print("\n💡 现在可以:")
        print("1. 重启邮件程序")
        print("2. 测试自动回复监控")
        print("3. 运行 python database_monitor.py 启动监控")
        print("4. 不应该再出现数据库锁定错误")
        
    else:
        print(f"\n⚠️ 仍有 {len(steps) - success_count} 个问题")
        print("💡 建议:")
        print("1. 重启计算机")
        print("2. 重新运行此脚本")
        print("3. 检查磁盘空间和权限")

if __name__ == "__main__":
    main()
