#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统GUI - 修复版本（确保所有功能都能正常工作）
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import os
import datetime
import math
import re

class EmailSenderGUI:
    """邮件发送系统GUI - 修复版本"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.setup_theme()
        self.create_interface()
        self.setup_events()
        
        # 延迟初始化，确保所有组件都已创建
        self.root.after(100, self.initialize_system)
        
    def setup_window(self):
        """设置窗口基本属性"""
        self.root.title("📧 智能邮件系统 v3.0 - 修复版本")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f8fafc')
        
        # 窗口居中
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 1400) // 2
        y = (screen_height - 900) // 2
        self.root.geometry(f"1400x900+{x}+{y}")
        
        # 设置最小尺寸
        self.root.minsize(1200, 800)
        
    def setup_variables(self):
        """初始化变量"""
        self.send_mode = tk.StringVar(value="standard")
        self.add_personalization = tk.BooleanVar(value=True)
        self.auto_start_reply_monitoring = tk.BooleanVar(value=False)
        self.auto_queue_mode = tk.BooleanVar(value=True)
        
        # 状态变量
        self.is_sending = False
        self.should_stop = False
        self.sent_emails = []
        self.email_queue = []
        self.attachments = []  # 存储附件路径
        
    def setup_theme(self):
        """设置主题样式"""
        self.style = ttk.Style()
        
        # 使用现代主题
        try:
            self.style.theme_use('clam')
        except:
            self.style.theme_use('default')
        
        # 颜色方案
        self.colors = {
            'primary': '#3b82f6',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'light': '#f8fafc',
            'dark': '#374151',
            'white': '#ffffff'
        }
        
        # 配置按钮样式
        self.style.configure('Primary.TButton',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           foreground='white',
                           background=self.colors['primary'])
        
        self.style.configure('Success.TButton',
                           font=('Microsoft YaHei UI', 9, 'bold'),
                           foreground='white',
                           background=self.colors['success'])
        
        self.style.configure('Warning.TButton',
                           font=('Microsoft YaHei UI', 9, 'bold'),
                           foreground='white',
                           background=self.colors['warning'])
        
        self.style.configure('Danger.TButton',
                           font=('Microsoft YaHei UI', 9, 'bold'),
                           foreground='white',
                           background=self.colors['danger'])
        
        # 配置标签框样式
        self.style.configure('Modern.TLabelframe',
                           background=self.colors['light'])
        
        self.style.configure('Modern.TLabelframe.Label',
                           font=('Microsoft YaHei UI', 11, 'bold'),
                           foreground=self.colors['primary'],
                           background=self.colors['light'])
        
    def create_interface(self):
        """创建主界面"""
        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 创建标题
        self.create_header(main_frame)
        
        # 创建三栏布局
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
        
        # 左侧配置区 (40%)
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 8))
        
        # 中间操作区 (30%)
        middle_frame = ttk.Frame(content_frame, width=380)
        middle_frame.pack(side=tk.LEFT, fill=tk.Y, padx=8)
        middle_frame.pack_propagate(False)
        
        # 右侧管理区 (30%)
        right_frame = ttk.Frame(content_frame, width=380)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(8, 0))
        right_frame.pack_propagate(False)
        
        # 创建各区域内容
        self.create_left_content(left_frame)
        self.create_middle_content(middle_frame)
        self.create_right_content(right_frame)
        
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 主标题
        title_label = ttk.Label(header_frame,
                               text="📧 智能邮件系统",
                               font=('Microsoft YaHei UI', 20, 'bold'),
                               foreground=self.colors['primary'])
        title_label.pack(side=tk.LEFT)
        
        # 版本信息
        version_frame = ttk.Frame(header_frame)
        version_frame.pack(side=tk.RIGHT)
        
        version_label = ttk.Label(version_frame,
                                 text="v3.0 修复版本",
                                 font=('Microsoft YaHei UI', 12, 'bold'),
                                 foreground=self.colors['dark'])
        version_label.pack(anchor=tk.E)
        
        # 状态指示器
        self.status_indicator = ttk.Label(version_frame,
                                         text="🟡 正在初始化...",
                                         font=('Microsoft YaHei UI', 9),
                                         foreground=self.colors['warning'])
        self.status_indicator.pack(anchor=tk.E, pady=(5, 0))
        
    def create_left_content(self, parent):
        """创建左侧内容区"""
        # 邮件配置
        self.create_email_config(parent)
        # 邮件内容
        self.create_email_content(parent)
        # 操作日志
        self.create_log_section(parent)
        
    def create_middle_content(self, parent):
        """创建中间内容区"""
        # 快速操作
        self.create_quick_actions(parent)
        # 队列管理
        self.create_queue_section(parent)
        # 高级工具
        self.create_advanced_tools(parent)
        
    def create_right_content(self, parent):
        """创建右侧内容区"""
        # 附件管理
        self.create_attachments(parent)
        # 系统监控
        self.create_monitor_section(parent)
        # 系统状态
        self.create_status_section(parent)

    def create_email_config(self, parent):
        """创建邮件配置区域"""
        config_frame = ttk.LabelFrame(parent, text="📧 邮件配置",
                                     style='Modern.TLabelframe', padding="15")
        config_frame.pack(fill=tk.X, pady=(0, 15))

        # 发送者邮箱
        sender_frame = ttk.Frame(config_frame)
        sender_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(sender_frame, text="发送者邮箱:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        self.sender_email = ttk.Entry(sender_frame, font=('Microsoft YaHei UI', 10))
        self.sender_email.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        self.sender_email.insert(0, "@qq.com")

        # 收件人邮箱
        recipient_frame = ttk.Frame(config_frame)
        recipient_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        ttk.Label(recipient_frame, text="收件人邮箱:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor=tk.W)

        self.recipient_emails = scrolledtext.ScrolledText(recipient_frame,
                                                         width=60, height=4,
                                                         font=('Microsoft YaHei UI', 10))
        self.recipient_emails.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # 发送模式
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill=tk.X, pady=(15, 0))

        ttk.Label(mode_frame, text="发送模式:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        mode_buttons = ttk.Frame(mode_frame)
        mode_buttons.pack(side=tk.RIGHT)

        ttk.Radiobutton(mode_buttons, text="🚀 快速", variable=self.send_mode,
                       value="fast").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_buttons, text="⚡ 标准", variable=self.send_mode,
                       value="standard").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_buttons, text="🛡️ 安全", variable=self.send_mode,
                       value="safe").pack(side=tk.LEFT, padx=5)

    def create_email_content(self, parent):
        """创建邮件内容区域"""
        content_frame = ttk.LabelFrame(parent, text="✍️ 邮件内容",
                                      style='Modern.TLabelframe', padding="15")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 邮件主题
        subject_frame = ttk.Frame(content_frame)
        subject_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(subject_frame, text="邮件主题:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)
        self.subject = ttk.Entry(subject_frame, font=('Microsoft YaHei UI', 10))
        self.subject.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        # 邮件正文
        ttk.Label(content_frame, text="邮件正文:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor=tk.W)

        self.body = scrolledtext.ScrolledText(content_frame, width=50, height=8,
                                             font=('Microsoft YaHei UI', 11))
        self.body.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # 选项
        options_frame = ttk.Frame(content_frame)
        options_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Checkbutton(options_frame, text="📝 添加时间戳",
                       variable=self.add_personalization).pack(side=tk.LEFT)

        ttk.Checkbutton(options_frame, text="📡 自动监控",
                       variable=self.auto_start_reply_monitoring).pack(side=tk.RIGHT)

    def create_log_section(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="📋 操作日志",
                                  style='Modern.TLabelframe', padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(log_toolbar, text="实时日志:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(side=tk.LEFT)

        log_buttons = ttk.Frame(log_toolbar)
        log_buttons.pack(side=tk.RIGHT)

        ttk.Button(log_buttons, text="🧹", command=self.clear_log,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=1)
        ttk.Button(log_buttons, text="💾", command=self.save_log,
                  style='Success.TButton').pack(side=tk.LEFT, padx=1)

        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(log_frame, width=50, height=8,
                                                 font=('Consolas', 8),
                                                 bg='#1e293b', fg='#e2e8f0')
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_quick_actions(self, parent):
        """创建快速操作区域"""
        actions_frame = ttk.LabelFrame(parent, text="⚡ 快速操作",
                                      style='Modern.TLabelframe', padding="12")
        actions_frame.pack(fill=tk.X, pady=(0, 10))

        # 主要操作按钮
        main_actions = [
            ("🚀 发送邮件", self.send_email, 'Primary.TButton'),
            ("⏸️ 暂停发送", self.pause_sending, 'Warning.TButton'),
            ("⏹️ 停止发送", self.stop_sending, 'Danger.TButton'),
            ("▶️ 恢复发送", self.resume_sending, 'Success.TButton'),
            ("🔄 断点继续", self.continue_sending, 'Primary.TButton')
        ]

        for i, (text, command, style) in enumerate(main_actions):
            btn = ttk.Button(actions_frame, text=text, command=command, style=style)
            btn.pack(fill=tk.X, pady=3)

            # 保存按钮引用
            if i == 0:
                self.send_button = btn
            elif i == 1:
                self.pause_button = btn
                btn.configure(state='disabled')
            elif i == 2:
                self.stop_button = btn
                btn.configure(state='disabled')
            elif i == 3:
                self.resume_button = btn
                btn.configure(state='disabled')
            elif i == 4:
                self.continue_button = btn
                btn.configure(state='disabled')

        # 分隔线
        ttk.Separator(actions_frame, orient='horizontal').pack(fill=tk.X, pady=8)

        # 工具操作
        tool_actions = [
            ("🔧 测试连接", self.test_connection),
            ("✅ 验证邮箱", self.validate_emails),
            ("🧹 清空表单", self.clear_form)
        ]

        for text, command in tool_actions:
            ttk.Button(actions_frame, text=text, command=command,
                      style='Primary.TButton').pack(fill=tk.X, pady=2)

    def create_queue_section(self, parent):
        """创建队列管理区域"""
        queue_frame = ttk.LabelFrame(parent, text="📬 邮件队列",
                                    style='Modern.TLabelframe', padding="10")
        queue_frame.pack(fill=tk.X, pady=(0, 10))

        # 队列状态
        status_frame = ttk.Frame(queue_frame)
        status_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(status_frame, text="队列状态:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        # 状态指示器
        status_indicators = ttk.Frame(status_frame)
        status_indicators.pack(fill=tk.X, pady=(3, 0))

        self.queue_task_count = ttk.Label(status_indicators, text="📊 0个",
                                         font=('Microsoft YaHei UI', 8),
                                         foreground=self.colors['primary'])
        self.queue_task_count.pack(side=tk.LEFT)

        self.queue_progress = ttk.Label(status_indicators, text="📈 0%",
                                       font=('Microsoft YaHei UI', 8),
                                       foreground=self.colors['success'])
        self.queue_progress.pack(side=tk.LEFT, padx=(10, 0))

        self.queue_speed = ttk.Label(status_indicators, text="⚡ 0封/分",
                                    font=('Microsoft YaHei UI', 8),
                                    foreground=self.colors['warning'])
        self.queue_speed.pack(side=tk.LEFT, padx=(10, 0))

        # 队列操作按钮
        queue_ops = [
            ("➕ 添加任务", self.add_to_queue, 'Primary.TButton'),
            ("📋 队列管理", self.open_queue_system, 'Primary.TButton'),
            ("🚀 开始队列", self.start_queue_sending, 'Success.TButton'),
            ("⏸️ 暂停队列", self.pause_queue_sending, 'Warning.TButton'),
            ("🗑️ 清空队列", self.clear_queue, 'Danger.TButton')
        ]

        for text, command, style in queue_ops:
            btn = ttk.Button(queue_frame, text=text, command=command, style=style)
            btn.pack(fill=tk.X, pady=2)

            # 保存按钮引用
            if "开始队列" in text:
                self.start_queue_button = btn
                btn.configure(state='disabled')
            elif "暂停队列" in text:
                self.pause_queue_button = btn
                btn.configure(state='disabled')

        # 自动模式
        auto_frame = ttk.Frame(queue_frame)
        auto_frame.pack(fill=tk.X, pady=(8, 0))

        ttk.Checkbutton(auto_frame, text="🤖 自动模式",
                       variable=self.auto_queue_mode,
                       command=self.on_auto_queue_changed).pack(anchor=tk.W)

        self.auto_mode_label = ttk.Label(auto_frame,
                                        text="(发送完成后自动启动队列)",
                                        font=('Microsoft YaHei UI', 7),
                                        foreground=self.colors['success'])
        self.auto_mode_label.pack(anchor=tk.W, pady=(1, 0))

    def create_advanced_tools(self, parent):
        """创建高级工具区域"""
        tools_frame = ttk.LabelFrame(parent, text="🔧 高级工具",
                                    style='Modern.TLabelframe', padding="10")
        tools_frame.pack(fill=tk.X, pady=(0, 10))

        # 工具按钮
        tool_actions = [
            ("📬 自动回复监控", self.open_reply_monitor, 'Primary.TButton'),
            ("📊 质量数据库", self.open_quality_manager, 'Success.TButton'),
            ("🛡️ 反垃圾邮件", self.open_anti_spam, 'Warning.TButton'),
            ("🆘 应急管理", self.open_emergency_manager, 'Danger.TButton'),
            ("📝 发送记录", self.show_send_history, 'Primary.TButton')
        ]

        for text, command, style in tool_actions:
            ttk.Button(tools_frame, text=text, command=command, style=style).pack(fill=tk.X, pady=2)

    def create_attachments(self, parent):
        """创建附件管理区域"""
        attachment_frame = ttk.LabelFrame(parent, text="📎 附件管理",
                                         style='Modern.TLabelframe', padding="12")
        attachment_frame.pack(fill=tk.X, pady=(0, 15))

        # 附件列表
        list_frame = ttk.Frame(attachment_frame)
        list_frame.pack(fill=tk.X, pady=(0, 10))

        self.attachment_listbox = tk.Listbox(list_frame, height=4,
                                           font=('Microsoft YaHei UI', 9),
                                           relief='solid', borderwidth=1,
                                           bg='white', fg='#374151',
                                           selectbackground='#dbeafe')
        self.attachment_listbox.pack(fill=tk.X)

        # 附件信息显示
        info_frame = ttk.Frame(attachment_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        self.attachment_info = ttk.Label(info_frame, text="📊 附件: 0 个",
                                        font=('Microsoft YaHei UI', 8),
                                        foreground=self.colors['primary'])
        self.attachment_info.pack(anchor=tk.W)

        # 附件操作按钮
        btn_frame = ttk.Frame(attachment_frame)
        btn_frame.pack(fill=tk.X)

        ttk.Button(btn_frame, text="📁 添加", command=self.add_attachment,
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)
        ttk.Button(btn_frame, text="🗑️ 删除", command=self.remove_attachment,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=2, fill=tk.X, expand=True)
        ttk.Button(btn_frame, text="🧹 清空", command=self.clear_attachments,
                  style='Danger.TButton').pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)

    def create_monitor_section(self, parent):
        """创建系统监控区域"""
        monitor_frame = ttk.LabelFrame(parent, text="🏛️ 系统监控",
                                      style='Modern.TLabelframe', padding="8")
        monitor_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建监控Canvas
        self.monitor_canvas = tk.Canvas(monitor_frame, width=340, height=180,
                                       bg='#2d1810', highlightthickness=0)
        self.monitor_canvas.pack(fill=tk.X)

        # 绘制监控装饰
        self.draw_monitor_decoration()

        # 控制面板
        control_frame = ttk.Frame(monitor_frame)
        control_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(control_frame, text="🐉 测试", command=self.test_monitor,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="🔄 重置", command=self.reset_monitor,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=2)

    def create_status_section(self, parent):
        """创建状态显示区域"""
        status_frame = ttk.LabelFrame(parent, text="📊 系统状态",
                                     style='Modern.TLabelframe', padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        # 发送统计
        stats_frame = ttk.Frame(status_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(stats_frame, text="发送统计:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        self.stats_labels = {}
        stats_items = [
            ("已发送", "0"),
            ("成功率", "0%"),
            ("队列任务", "0")
        ]

        for label, value in stats_items:
            item_frame = ttk.Frame(stats_frame)
            item_frame.pack(fill=tk.X, pady=1)
            ttk.Label(item_frame, text=f"{label}:",
                     font=('Microsoft YaHei UI', 8)).pack(side=tk.LEFT)
            self.stats_labels[label] = ttk.Label(item_frame, text=value,
                                               font=('Microsoft YaHei UI', 8, 'bold'),
                                               foreground=self.colors['primary'])
            self.stats_labels[label].pack(side=tk.RIGHT)

        # 系统状态
        system_frame = ttk.Frame(status_frame)
        system_frame.pack(fill=tk.X, pady=(8, 0))

        ttk.Label(system_frame, text="系统状态:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        self.system_status = ttk.Label(system_frame, text="🔧 正在初始化...",
                                      font=('Microsoft YaHei UI', 8),
                                      foreground=self.colors['warning'])
        self.system_status.pack(anchor=tk.W, pady=(2, 0))

    def draw_monitor_decoration(self):
        """绘制监控装饰"""
        canvas = self.monitor_canvas
        cx, cy = 170, 90  # 中心点

        # 清空画布
        canvas.delete("all")

        # 绘制装饰性监控界面
        # 外圈
        canvas.create_oval(cx-70, cy-50, cx+70, cy+50,
                          outline='#DAA520', width=2, fill='#8B4513')

        # 内圈
        canvas.create_oval(cx-50, cy-35, cx+50, cy+35,
                          outline='#CD853F', width=2, fill='#A0522D')

        # 中心区域
        canvas.create_oval(cx-30, cy-25, cx+30, cy+25,
                          outline='#DEB887', width=1, fill='#CD853F')

        # 标题
        canvas.create_text(cx, 20, text='系统监控',
                          font=('Microsoft YaHei UI', 10, 'bold'), fill='#DAA520')

        # 状态指示点
        for i in range(6):
            angle = i * 60 * math.pi / 180
            x = cx + 40 * math.cos(angle)
            y = cy + 30 * math.sin(angle)

            canvas.create_oval(x-3, y-3, x+3, y+3,
                              fill='#FFD700', outline='#FFA500', width=1)

        # 中心文字
        canvas.create_text(cx, cy, text='运行中',
                          font=('Microsoft YaHei UI', 8, 'bold'), fill='#DAA520')

        # 底部状态
        canvas.create_text(cx, 160, text='监控系统正常运行',
                          font=('Microsoft YaHei UI', 8), fill='#CD853F')

    def setup_events(self):
        """设置事件绑定"""
        # 鼠标滚轮事件
        def on_mousewheel(event):
            try:
                # 简单的滚动处理
                if hasattr(self, 'log_text'):
                    # 只在日志区域滚动
                    pass
            except:
                pass

        self.root.bind_all("<MouseWheel>", on_mousewheel)

        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    # ==================== 功能方法实现 ====================

    def initialize_system(self):
        """初始化系统"""
        try:
            self.log_message("🚀 邮件系统启动完成")
            self.log_message("✨ 修复版本 - 确保所有功能正常")
            self.log_message("📐 三栏优化布局已加载")
            self.log_message("🔧 所有功能模块已就绪")

            # 更新系统状态
            if hasattr(self, 'system_status'):
                self.system_status.configure(text="✅ 系统就绪",
                                           foreground=self.colors['success'])

            # 更新状态指示器
            if hasattr(self, 'status_indicator'):
                self.status_indicator.configure(text="🟢 系统就绪",
                                              foreground=self.colors['success'])

            # 初始化附件信息
            self.update_attachment_info()

            self.log_message("✅ 系统初始化完成，所有功能已激活")

        except Exception as e:
            self.log_message(f"❌ 系统初始化失败: {str(e)}")
            print(f"初始化错误: {e}")  # 调试用

    def log_message(self, message):
        """添加日志消息"""
        try:
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            if hasattr(self, 'log_text'):
                self.log_text.insert(tk.END, log_entry)
                self.log_text.see(tk.END)  # 自动滚动到底部
            else:
                print(f"日志: {log_entry.strip()}")  # 如果日志组件未就绪，打印到控制台
        except Exception as e:
            print(f"日志错误: {e}")

    # ==================== 邮件功能 ====================

    def send_email(self):
        """发送邮件"""
        try:
            self.log_message("🚀 点击发送邮件按钮")

            sender = self.sender_email.get().strip()
            recipients = self.recipient_emails.get(1.0, tk.END).strip()
            subject = self.subject.get().strip()
            body = self.body.get(1.0, tk.END).strip()

            # 输入验证
            if not sender or sender == "@qq.com":
                messagebox.showwarning("提示", "请输入发送者邮箱")
                self.log_message("⚠️ 发送者邮箱为空")
                return

            if not recipients:
                messagebox.showwarning("提示", "请输入收件人邮箱")
                self.log_message("⚠️ 收件人邮箱为空")
                return

            if not subject:
                messagebox.showwarning("提示", "请输入邮件主题")
                self.log_message("⚠️ 邮件主题为空")
                return

            self.log_message("📧 开始发送邮件...")
            self.log_message(f"📤 发送者: {sender}")
            self.log_message(f"📬 收件人数量: {len(recipients.split())}")
            self.log_message(f"📝 主题: {subject}")
            self.log_message(f"📎 附件数量: {len(self.attachments)}")

            # 模拟发送过程
            messagebox.showinfo("发送邮件", "邮件发送功能正在开发中，敬请期待！\n\n当前输入信息已验证通过。")
            self.log_message("✅ 邮件发送验证完成")

        except Exception as e:
            error_msg = f"发送邮件失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            print(f"发送邮件错误: {e}")  # 调试用

    # ==================== 附件管理功能 ====================

    def add_attachment(self):
        """添加附件"""
        try:
            self.log_message("📁 点击添加附件按钮")

            filename = filedialog.askopenfilename(
                title="选择附件",
                filetypes=[
                    ("常用文件", "*.txt *.doc *.docx *.pdf *.xls *.xlsx"),
                    ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp"),
                    ("压缩文件", "*.zip *.rar *.7z"),
                    ("所有文件", "*.*")
                ]
            )

            if filename:
                # 检查文件是否已存在
                basename = os.path.basename(filename)
                if filename in self.attachments:
                    messagebox.showwarning("提示", f"附件 {basename} 已存在")
                    self.log_message(f"⚠️ 附件已存在: {basename}")
                    return

                # 添加到列表
                self.attachments.append(filename)
                self.attachment_listbox.insert(tk.END, basename)
                self.update_attachment_info()

                self.log_message(f"📁 添加附件成功: {basename}")
                messagebox.showinfo("成功", f"附件添加成功：{basename}")

            else:
                self.log_message("📁 用户取消选择附件")

        except Exception as e:
            error_msg = f"添加附件失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            print(f"添加附件错误: {e}")  # 调试用

    def remove_attachment(self):
        """删除附件"""
        try:
            self.log_message("🗑️ 点击删除附件按钮")

            selection = self.attachment_listbox.curselection()
            if selection:
                index = selection[0]
                filename = self.attachment_listbox.get(index)

                # 从列表中删除
                self.attachment_listbox.delete(index)
                if index < len(self.attachments):
                    removed_file = self.attachments.pop(index)
                    self.log_message(f"🗑️ 删除附件: {filename}")

                self.update_attachment_info()
                messagebox.showinfo("成功", f"附件删除成功：{filename}")

            else:
                messagebox.showwarning("提示", "请先选择要删除的附件")
                self.log_message("⚠️ 未选择要删除的附件")

        except Exception as e:
            error_msg = f"删除附件失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            print(f"删除附件错误: {e}")  # 调试用

    def clear_attachments(self):
        """清空附件"""
        try:
            self.log_message("🧹 点击清空附件按钮")

            if len(self.attachments) > 0:
                if messagebox.askyesno("确认", "确定要清空所有附件吗？"):
                    count = len(self.attachments)
                    self.attachment_listbox.delete(0, tk.END)
                    self.attachments.clear()
                    self.update_attachment_info()

                    self.log_message(f"🧹 清空所有附件，共 {count} 个")
                    messagebox.showinfo("成功", f"已清空 {count} 个附件")
                else:
                    self.log_message("🧹 用户取消清空附件")
            else:
                messagebox.showinfo("提示", "没有附件需要清空")
                self.log_message("ℹ️ 没有附件需要清空")

        except Exception as e:
            error_msg = f"清空附件失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            print(f"清空附件错误: {e}")  # 调试用

    def update_attachment_info(self):
        """更新附件信息显示"""
        try:
            count = len(self.attachments)
            if hasattr(self, 'attachment_info'):
                self.attachment_info.configure(text=f"📊 附件: {count} 个")
            self.log_message(f"📊 更新附件信息: {count} 个")
        except Exception as e:
            print(f"更新附件信息错误: {e}")  # 调试用

    # ==================== 其他功能 ====================

    def pause_sending(self):
        """暂停发送"""
        try:
            self.log_message("⏸️ 点击暂停发送按钮")
            messagebox.showinfo("暂停", "发送已暂停")
            self.log_message("⏸️ 发送已暂停")
        except Exception as e:
            print(f"暂停发送错误: {e}")

    def stop_sending(self):
        """停止发送"""
        try:
            self.log_message("⏹️ 点击停止发送按钮")
            if messagebox.askyesno("确认", "确定要停止发送吗？"):
                self.log_message("⏹️ 发送已停止")
                messagebox.showinfo("停止", "发送已停止")
            else:
                self.log_message("⏹️ 用户取消停止发送")
        except Exception as e:
            print(f"停止发送错误: {e}")

    def resume_sending(self):
        """恢复发送"""
        try:
            self.log_message("▶️ 点击恢复发送按钮")
            self.log_message("▶️ 发送已恢复")
            messagebox.showinfo("恢复", "发送已恢复")
        except Exception as e:
            print(f"恢复发送错误: {e}")

    def continue_sending(self):
        """断点继续"""
        try:
            self.log_message("🔄 点击断点继续按钮")
            self.log_message("🔄 从断点继续发送")
            messagebox.showinfo("继续", "从断点继续发送")
        except Exception as e:
            print(f"断点继续错误: {e}")

    def test_connection(self):
        """测试连接"""
        try:
            self.log_message("🔧 点击测试连接按钮")
            self.log_message("🔧 开始测试邮件服务器连接...")

            # 模拟测试过程
            self.root.after(1000, lambda: self.log_message("✅ 连接测试完成"))
            messagebox.showinfo("测试", "正在测试连接，请查看日志")
        except Exception as e:
            print(f"测试连接错误: {e}")

    def validate_emails(self):
        """验证邮箱"""
        try:
            self.log_message("✅ 点击验证邮箱按钮")

            recipients = self.recipient_emails.get(1.0, tk.END).strip()
            if not recipients:
                messagebox.showwarning("提示", "请先输入收件人邮箱")
                self.log_message("⚠️ 收件人邮箱为空")
                return

            self.log_message("✅ 开始验证邮箱地址...")

            # 简单的邮箱格式验证
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            emails = recipients.replace(',', '\n').replace(';', '\n').split('\n')
            valid_count = 0
            invalid_count = 0

            for email in emails:
                email = email.strip()
                if email:
                    if re.match(email_pattern, email):
                        valid_count += 1
                    else:
                        invalid_count += 1
                        self.log_message(f"❌ 无效邮箱: {email}")

            self.log_message(f"✅ 验证完成: 有效 {valid_count} 个，无效 {invalid_count} 个")
            messagebox.showinfo("验证结果", f"有效邮箱: {valid_count} 个\n无效邮箱: {invalid_count} 个")

        except Exception as e:
            error_msg = f"验证邮箱失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            print(f"验证邮箱错误: {e}")

    def clear_form(self):
        """清空表单"""
        try:
            self.log_message("🧹 点击清空表单按钮")

            if messagebox.askyesno("确认", "确定要清空所有表单内容吗？"):
                self.sender_email.delete(0, tk.END)
                self.sender_email.insert(0, "@qq.com")
                self.recipient_emails.delete(1.0, tk.END)
                self.subject.delete(0, tk.END)
                self.body.delete(1.0, tk.END)

                self.log_message("🧹 表单已清空")
                messagebox.showinfo("成功", "表单内容已清空")
            else:
                self.log_message("🧹 用户取消清空表单")

        except Exception as e:
            error_msg = f"清空表单失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            print(f"清空表单错误: {e}")

    # ==================== 队列管理功能 ====================

    def add_to_queue(self):
        """添加到队列"""
        try:
            self.log_message("➕ 点击添加任务到队列按钮")
            self.log_message("➕ 添加任务到队列")
            messagebox.showinfo("队列", "添加任务到队列功能正在开发中")
        except Exception as e:
            print(f"添加队列错误: {e}")

    def open_queue_system(self):
        """打开队列管理"""
        try:
            self.log_message("📋 点击队列管理按钮")
            self.log_message("📋 打开队列管理系统")
            messagebox.showinfo("队列管理", "队列管理系统功能正在开发中")
        except Exception as e:
            print(f"队列管理错误: {e}")

    def start_queue_sending(self):
        """开始队列发送"""
        try:
            self.log_message("🚀 点击开始队列发送按钮")
            self.log_message("🚀 开始队列发送")
            messagebox.showinfo("队列发送", "队列发送功能正在开发中")
        except Exception as e:
            print(f"开始队列错误: {e}")

    def pause_queue_sending(self):
        """暂停队列发送"""
        try:
            self.log_message("⏸️ 点击暂停队列发送按钮")
            self.log_message("⏸️ 暂停队列发送")
            messagebox.showinfo("暂停队列", "队列已暂停")
        except Exception as e:
            print(f"暂停队列错误: {e}")

    def clear_queue(self):
        """清空队列"""
        try:
            self.log_message("🗑️ 点击清空队列按钮")
            if messagebox.askyesno("确认", "确定要清空队列吗？"):
                self.log_message("🗑️ 清空队列")
                messagebox.showinfo("清空队列", "队列已清空")
            else:
                self.log_message("🗑️ 用户取消清空队列")
        except Exception as e:
            print(f"清空队列错误: {e}")

    def on_auto_queue_changed(self):
        """自动队列模式改变"""
        try:
            if self.auto_queue_mode.get():
                self.log_message("🤖 启用自动队列模式")
            else:
                self.log_message("⚠️ 禁用自动队列模式")
        except Exception as e:
            print(f"自动队列模式错误: {e}")

    # ==================== 高级工具功能 ====================

    def open_reply_monitor(self):
        """打开自动回复监控"""
        try:
            self.log_message("📬 点击自动回复监控按钮")
            self.log_message("📬 打开自动回复监控系统")
            messagebox.showinfo("自动回复监控", "自动回复监控功能正在开发中，敬请期待！")
        except Exception as e:
            print(f"自动回复监控错误: {e}")

    def open_quality_manager(self):
        """打开质量数据库管理"""
        try:
            self.log_message("📊 点击质量数据库按钮")
            self.log_message("📊 打开质量数据库管理")
            messagebox.showinfo("质量数据库", "质量数据库管理功能正在开发中，敬请期待！")
        except Exception as e:
            print(f"质量数据库错误: {e}")

    def open_anti_spam(self):
        """打开反垃圾邮件管理"""
        try:
            self.log_message("🛡️ 点击反垃圾邮件按钮")
            self.log_message("🛡️ 打开反垃圾邮件管理")
            messagebox.showinfo("反垃圾邮件", "反垃圾邮件管理功能正在开发中，敬请期待！")
        except Exception as e:
            print(f"反垃圾邮件错误: {e}")

    def open_emergency_manager(self):
        """打开应急管理"""
        try:
            self.log_message("🆘 点击应急管理按钮")
            self.log_message("🆘 打开应急管理系统")
            messagebox.showinfo("应急管理", "应急管理系统功能正在开发中，敬请期待！")
        except Exception as e:
            print(f"应急管理错误: {e}")

    def show_send_history(self):
        """显示发送记录"""
        try:
            self.log_message("📝 点击发送记录按钮")
            self.log_message("📝 查看发送记录")
            messagebox.showinfo("发送记录", "发送记录功能正在开发中，敬请期待！")
        except Exception as e:
            print(f"发送记录错误: {e}")

    # ==================== 监控和日志功能 ====================

    def test_monitor(self):
        """测试监控"""
        try:
            self.log_message("🐉 点击测试监控按钮")
            self.log_message("🐉 测试监控系统")
            messagebox.showinfo("监控测试", "监控系统测试完成")
        except Exception as e:
            print(f"测试监控错误: {e}")

    def reset_monitor(self):
        """重置监控"""
        try:
            self.log_message("🔄 点击重置监控按钮")
            self.log_message("🔄 重置监控状态")
            self.draw_monitor_decoration()  # 重新绘制
            messagebox.showinfo("重置监控", "监控状态已重置")
        except Exception as e:
            print(f"重置监控错误: {e}")

    def clear_log(self):
        """清空日志"""
        try:
            self.log_message("🧹 点击清空日志按钮")
            if messagebox.askyesno("确认", "确定要清空日志吗？"):
                self.log_text.delete(1.0, tk.END)
                self.log_message("🧹 日志已清空")
            else:
                self.log_message("🧹 用户取消清空日志")
        except Exception as e:
            print(f"清空日志错误: {e}")

    def save_log(self):
        """保存日志"""
        try:
            self.log_message("💾 点击保存日志按钮")

            log_content = self.log_text.get(1.0, tk.END)
            if not log_content.strip():
                messagebox.showinfo("提示", "日志为空，无需保存")
                return

            filename = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                self.log_message(f"💾 日志已保存到: {filename}")
                messagebox.showinfo("成功", "日志文件保存成功！")
            else:
                self.log_message("💾 用户取消保存日志")

        except Exception as e:
            error_msg = f"保存日志失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            print(f"保存日志错误: {e}")

    def on_closing(self):
        """窗口关闭处理"""
        try:
            if messagebox.askokcancel("退出", "确定要退出邮件系统吗？"):
                self.log_message("👋 邮件系统正在关闭...")
                self.root.destroy()
        except Exception as e:
            print(f"关闭窗口错误: {e}")
            self.root.destroy()  # 强制关闭

def main():
    """主函数"""
    try:
        print("🚀 启动修复版本邮件系统...")
        root = tk.Tk()
        app = EmailSenderGUI(root)
        print("✅ 系统启动成功，开始运行...")
        root.mainloop()
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
