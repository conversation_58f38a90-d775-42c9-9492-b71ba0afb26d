{"timestamp": "2025-06-15T00:21:57.583064", "python_path": "C:\\Program Files\\Python311\\python.exe", "working_directory": "E:\\自动发邮件", "environment_variables": {"ALLUSERSPROFILE": "C:\\ProgramData", "ANDROID_HOME": "E:\\android", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "C:\\USERS\\<USER>\\ONEDRIVE\\图片\\文档": "C:\\Users\\<USER>\\OneDrive\\图片\\文档", "CHOCOLATEYINSTALL": "C:\\ProgramData\\chocolatey", "CHOCOLATEYLASTPATHUPDATE": "133871853904485099", "COMMONPROGRAMFILES": "C:\\Program Files\\Common Files", "COMMONPROGRAMFILES(X86)": "C:\\Program Files (x86)\\Common Files", "COMMONPROGRAMW6432": "C:\\Program Files\\Common Files", "COMPUTERNAME": "杨东海", "COMSPEC": "C:\\Windows\\system32\\cmd.exe", "DRIVERDATA": "C:\\Windows\\System32\\Drivers\\DriverData", "EFC_9960": "1", "FPS_BROWSER_APP_PROFILE_STRING": "Internet Explorer", "FPS_BROWSER_USER_PROFILE_STRING": "<PERSON><PERSON><PERSON>", "GRADLE_HOME": "C:\\Gradle\\gradle-8.6", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\东海", "HYOFFICEAI_PATH": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\HaiYingSoft\\OfficeAI", "HYOFFICEAI_SERVER_PATH": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\HaiYingSoft\\OfficeAI_Server", "INTELLIJ IDEA COMMUNITY EDITION": "E:\\IntelliJ IDEA Community Edition 2025.1.1.1\\bin;", "INTEL_DEV_REDIST": "C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\", "JAVA_HOME": "C:\\Program Files\\Java\\jdk-17", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\杨东海", "NUMBER_OF_PROCESSORS": "16", "OLLAMA_HOST": " 0.0.0.0", "OLLAMA_ORIGINS": " *", "ONEDRIVE": "C:\\Users\\<USER>\\OneDrive", "ORIGINAL_XDG_CURRENT_DESKTOP": "undefined", "OS": "Windows_NT", "PATH": "C:\\Program Files\\PowerShell\\7;C:\\Program Files\\Python311\\Scripts\\;C:\\Program Files\\Python311\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\Common Files\\Oracle\\Java\\javapath;C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64_win\\compiler;C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\dotnet\\;D:\\微信web开发者工具\\dll;D:\\;E;\\Git\\cmd;E:\\Git\\cmd;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Java\\jdk-17\\bin;C:\\Program Files\\Java\\jdk-17\\bin;C:\\Gradle\\gradle-8.6\\bin;C:\\\\Program Files\\\\Java\\\\jdk-17\\bin;C:\\\\Program Files\\\\Java\\\\jdk-17\\bin;C:\\\\Gradle\\\\gradle-8.6\\bin;C:\\\\Program Files\\\\Java\\\\jdk-17\\\\bin;E:\\android\\platform-tools;E:\\android\\tools;E:\\android\\tools\\bin;;C:\\Program Files\\Docker\\Docke;C:\\Program Files\\PowerShell\\7\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA App\\NvDLISR;C:\\Users\\<USER>\\.cargo\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Program Files (x86)\\Microsoft\\Edge\\Application;C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64_win\\compiler;C:\\Program Files (x86)\\Common Files\\Intel\\Shared Libraries\\redist\\intel64\\compiler;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA NvDLISR;C:\\Program Files (x86)\\EasyShare\\x86\\;C:\\Program Files (x86)\\EasyShare\\x64\\;C:\\Program Files\\dotnet\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae\\bin;e:\\Trae\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39;C:\\Users\\<USER>\\OneDrive\\图片\\文档;C:\\Program Files\\Java\\jdk-17\\bin;C:\\Gradle\\gradle-8.6\\bin;C:\\Program Files\\Java\\jdk-17\\bin;C:\\Gradle\\gradle-8.6\\bin;E:\\android\\platform-tools;E:\\android\\tools;E:\\android\\tools\\bin;E:\\IntelliJ IDEA Community Edition 2025.1.1.1\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL", "POWERSHELL_DISTRIBUTION_CHANNEL": "MSI:Windows 10 Home China", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "AMD64 Family 25 Model 68 Stepping 1, AuthenticAMD", "PROCESSOR_LEVEL": "25", "PROCESSOR_REVISION": "4401", "PROGRAMDATA": "C:\\ProgramData", "PROGRAMFILES": "C:\\Program Files", "PROGRAMFILES(X86)": "C:\\Program Files (x86)", "PROGRAMW6432": "C:\\Program Files", "PSMODULEPATH": "C:\\Users\\<USER>\\OneDrive\\图片\\文档\\PowerShell\\Modules;C:\\Program Files\\PowerShell\\Modules;c:\\program files\\powershell\\7\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules", "PUBLIC": "C:\\Users\\<USER>", "SESSIONNAME": "<PERSON><PERSON><PERSON>", "SYSTEMDRIVE": "C:", "SYSTEMROOT": "C:\\Windows", "TEMP": "E:\\九猫临时文件", "TMP": "E:\\九猫临时文件", "USERDOMAIN": "杨东海", "USERDOMAIN_ROAMINGPROFILE": "杨东海", "USERNAME": "东海", "USERPROFILE": "C:\\Users\\<USER>", "WINDIR": "C:\\Windows", "GIT_PAGER": "", "TERM_PROGRAM": "vscode", "TERM_PROGRAM_VERSION": "1.101.0", "LANG": "zh_CN.UTF-8", "COLORTERM": "truecolor", "GIT_ASKPASS": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh", "VSCODE_GIT_ASKPASS_NODE": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe", "VSCODE_GIT_ASKPASS_EXTRA_ARGS": "", "VSCODE_GIT_ASKPASS_MAIN": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js", "VSCODE_GIT_IPC_HANDLE": "\\\\.\\pipe\\vscode-git-fc15345561-sock", "PYDEVD_DISABLE_FILE_VALIDATION": "1", "VSCODE_DEBUGPY_ADAPTER_ENDPOINTS": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-76e6d03d7360591b.txt", "BUNDLED_DEBUGPY_PATH": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy", "VSCODE_INJECTION": "1"}, "installed_packages": "Package            Version\n------------------ ---------\nautocommand        2.2.2\nbackports.tarfile  1.2.0\ncertifi            2025.4.26\ncharset-normalizer 3.4.2\ncustomtkinter      5.2.2\ndarkdetect         0.8.0\nidna               3.10\nimportlib_metadata 8.7.0\njaraco.context     6.0.1\njaraco.functools   4.1.0\njaraco.text        4.0.0\njieba              0.42.1\nmore-itertools     10.7.0\npackaging          25.0\npillow             11.2.1\npip                25.1.1\npsutil             7.0.0\nrequests           2.32.4\nsetuptools         65.5.0\nurllib3            2.4.0\nzipp               3.23.0\n", "system_info": {"platform": "win32", "python_version": "3.11.0 (main, Oct 24 2022, 18:26:48) [MSC v.1933 64 bit (AMD64)]"}}