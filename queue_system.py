# -*- coding: utf-8 -*-
"""
邮件队列系统 - 独立的队列管理子系统
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import datetime
import threading
import time
import random
import os


class QueueSystemWindow:
    """邮件队列系统窗口"""
    
    def __init__(self, parent):
        self.parent = parent  # 主程序实例
        self.queue_window = None
        self.create_queue_window()
        
    def create_queue_window(self):
        """创建队列系统窗口"""
        self.queue_window = tk.Toplevel(self.parent.root)
        self.queue_window.title("📬 邮件队列系统")
        self.queue_window.geometry("1200x800")
        self.queue_window.resizable(True, True)
        
        # 设置窗口图标和属性
        try:
            self.queue_window.iconbitmap("icon.ico")
        except:
            pass
        
        # 主框架
        main_frame = ttk.Frame(self.queue_window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.queue_window.columnconfigure(0, weight=1)
        self.queue_window.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # 左侧控制面板
        self.create_control_panel(main_frame)
        
        # 右侧队列列表和日志
        self.create_queue_panel(main_frame)
        
        # 底部状态栏
        self.create_status_bar(main_frame)
        
        # 更新显示
        self.refresh_queue_display()
        
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = ttk.LabelFrame(parent, text="📝 任务创建", padding="10")
        control_frame.grid(row=0, column=0, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        control_frame.columnconfigure(1, weight=1)
        
        # 发送者邮箱
        ttk.Label(control_frame, text="发送者邮箱:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.sender_email = ttk.Entry(control_frame, width=30)
        self.sender_email.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        self.sender_email.insert(0, self.parent.sender_email.get())
        
        # 收件人邮箱
        ttk.Label(control_frame, text="收件人邮箱:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=2)
        self.recipient_emails = scrolledtext.ScrolledText(control_frame, width=30, height=4)
        self.recipient_emails.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        
        # 邮件主题
        ttk.Label(control_frame, text="邮件主题:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.subject = ttk.Entry(control_frame, width=30)
        self.subject.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        
        # 邮件正文
        ttk.Label(control_frame, text="邮件正文:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=2)
        self.body = scrolledtext.ScrolledText(control_frame, width=30, height=8)
        self.body.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=2, padx=(10, 0))
        
        # 发送模式
        mode_frame = ttk.Frame(control_frame)
        mode_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Label(mode_frame, text="发送模式:", font=('Arial', 9, 'bold')).pack(side=tk.LEFT)
        self.send_mode = tk.StringVar(value="safe")
        ttk.Radiobutton(mode_frame, text="快速", variable=self.send_mode, value="fast").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_frame, text="标准", variable=self.send_mode, value="standard").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_frame, text="安全", variable=self.send_mode, value="safe").pack(side=tk.LEFT, padx=5)
        
        # 个性化设置
        self.add_personalization = tk.BooleanVar(value=True)
        ttk.Checkbutton(control_frame, text="添加邮件编号和时间戳", 
                       variable=self.add_personalization).grid(row=5, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # 附件管理
        attachment_frame = ttk.LabelFrame(control_frame, text="附件管理", padding="5")
        attachment_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        attachment_frame.columnconfigure(0, weight=1)
        
        self.attachment_listbox = tk.Listbox(attachment_frame, height=3)
        self.attachment_listbox.grid(row=0, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Button(attachment_frame, text="添加", command=self.add_attachment).grid(row=1, column=0, padx=2, pady=2)
        ttk.Button(attachment_frame, text="删除", command=self.remove_attachment).grid(row=1, column=1, padx=2, pady=2)
        ttk.Button(attachment_frame, text="清空", command=self.clear_attachments).grid(row=1, column=2, padx=2, pady=2)
        
        # 操作按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.grid(row=7, column=0, columnspan=2, pady=20)
        
        ttk.Button(button_frame, text="➕ 添加到队列", 
                  command=self.add_to_queue, style='Accent.TButton').pack(side=tk.TOP, pady=5, fill=tk.X)
        ttk.Button(button_frame, text="📋 从主界面导入", 
                  command=self.import_from_main).pack(side=tk.TOP, pady=2, fill=tk.X)
        ttk.Button(button_frame, text="🗑️ 清空表单", 
                  command=self.clear_form).pack(side=tk.TOP, pady=2, fill=tk.X)
        
    def create_queue_panel(self, parent):
        """创建右侧队列面板"""
        right_frame = ttk.Frame(parent)
        right_frame.grid(row=0, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(0, weight=1)
        right_frame.rowconfigure(1, weight=1)
        
        # 队列列表
        queue_frame = ttk.LabelFrame(right_frame, text="📋 发送队列", padding="5")
        queue_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 5))
        queue_frame.columnconfigure(0, weight=1)
        queue_frame.rowconfigure(0, weight=1)
        
        # 队列控制按钮
        queue_control_frame = ttk.Frame(queue_frame)
        queue_control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.start_button = ttk.Button(queue_control_frame, text="🚀 开始队列发送", 
                                      command=self.start_queue, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(queue_control_frame, text="⏹️ 停止发送",
                                     command=self.stop_queue, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=5)

        self.pause_button = ttk.Button(queue_control_frame, text="⏸️ 暂停发送",
                                      command=self.pause_queue, state='disabled')
        self.pause_button.pack(side=tk.LEFT, padx=5)

        self.resume_button = ttk.Button(queue_control_frame, text="▶️ 恢复发送",
                                       command=self.resume_queue, state='disabled')
        self.resume_button.pack(side=tk.LEFT, padx=5)

        self.continue_button = ttk.Button(queue_control_frame, text="🔄 断点继续",
                                         command=self.continue_from_breakpoint, state='disabled')
        self.continue_button.pack(side=tk.LEFT, padx=5)

        ttk.Button(queue_control_frame, text="🗑️ 清空队列",
                  command=self.clear_queue).pack(side=tk.LEFT, padx=5)
        
        # 队列状态
        self.queue_status_label = ttk.Label(queue_control_frame, text="队列: 0 个任务", 
                                           font=('Arial', 10, 'bold'), foreground='blue')
        self.queue_status_label.pack(side=tk.RIGHT, padx=10)
        
        # 队列列表
        columns = ('ID', '状态', '主题', '收件人数', '模式')
        self.queue_tree = ttk.Treeview(queue_frame, columns=columns, show='headings', height=12)
        
        # 设置列
        self.queue_tree.heading('ID', text='ID')
        self.queue_tree.heading('状态', text='状态')
        self.queue_tree.heading('主题', text='主题')
        self.queue_tree.heading('收件人数', text='收件人数')
        self.queue_tree.heading('模式', text='模式')
        
        self.queue_tree.column('ID', width=50)
        self.queue_tree.column('状态', width=80)
        self.queue_tree.column('主题', width=200)
        self.queue_tree.column('收件人数', width=80)
        self.queue_tree.column('模式', width=80)
        
        # 滚动条
        queue_scrollbar = ttk.Scrollbar(queue_frame, orient=tk.VERTICAL, command=self.queue_tree.yview)
        self.queue_tree.configure(yscrollcommand=queue_scrollbar.set)
        
        self.queue_tree.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        queue_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        
        # 队列操作按钮
        queue_ops_frame = ttk.Frame(queue_frame)
        queue_ops_frame.grid(row=2, column=0, columnspan=2, pady=5)
        
        ttk.Button(queue_ops_frame, text="编辑", command=self.edit_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(queue_ops_frame, text="删除", command=self.delete_task).pack(side=tk.LEFT, padx=2)
        ttk.Button(queue_ops_frame, text="上移", command=self.move_up).pack(side=tk.LEFT, padx=2)
        ttk.Button(queue_ops_frame, text="下移", command=self.move_down).pack(side=tk.LEFT, padx=2)
        
        # 日志面板
        log_frame = ttk.LabelFrame(right_frame, text="📝 发送日志", padding="5")
        log_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(5, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志控制按钮
        log_control_frame = ttk.Frame(log_frame)
        log_control_frame.grid(row=1, column=0, pady=5)
        
        ttk.Button(log_control_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=2)
        ttk.Button(log_control_frame, text="保存日志", command=self.save_log).pack(side=tk.LEFT, padx=2)
        
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_var = tk.StringVar()
        self.status_var.set("队列系统就绪")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=2)
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        
        # 同时记录到主程序日志
        self.parent.log_message(f"[队列] {message}")
        
    def add_attachment(self):
        """添加附件"""
        files = filedialog.askopenfilenames(
            title="选择附件文件",
            filetypes=[
                ("所有支持的文件", "*.txt;*.pdf;*.doc;*.docx;*.xls;*.xlsx;*.ppt;*.pptx;*.jpg;*.jpeg;*.png;*.gif;*.zip;*.rar;*.7z"),
                ("文档文件", "*.txt;*.pdf;*.doc;*.docx"),
                ("所有文件", "*.*")
            ]
        )
        
        for file in files:
            if file not in self.attachment_listbox.get(0, tk.END):
                self.attachment_listbox.insert(tk.END, file)
                self.log_message(f"添加附件: {os.path.basename(file)}")
                
    def remove_attachment(self):
        """删除选中的附件"""
        selection = self.attachment_listbox.curselection()
        if selection:
            filename = self.attachment_listbox.get(selection[0])
            self.attachment_listbox.delete(selection[0])
            self.log_message(f"删除附件: {os.path.basename(filename)}")
            
    def clear_attachments(self):
        """清空所有附件"""
        count = self.attachment_listbox.size()
        self.attachment_listbox.delete(0, tk.END)
        if count > 0:
            self.log_message(f"清空了 {count} 个附件")
            
    def clear_form(self):
        """清空表单"""
        self.recipient_emails.delete(1.0, tk.END)
        self.subject.delete(0, tk.END)
        self.body.delete(1.0, tk.END)
        self.clear_attachments()
        self.log_message("表单已清空")
        
    def import_from_main(self):
        """从主界面导入当前内容"""
        # 导入发送者
        self.sender_email.delete(0, tk.END)
        self.sender_email.insert(0, self.parent.sender_email.get())
        
        # 导入收件人
        self.recipient_emails.delete(1.0, tk.END)
        self.recipient_emails.insert(tk.END, self.parent.recipient_emails.get(1.0, tk.END))
        
        # 导入主题
        self.subject.delete(0, tk.END)
        self.subject.insert(0, self.parent.subject.get())
        
        # 导入正文
        self.body.delete(1.0, tk.END)
        self.body.insert(tk.END, self.parent.body.get(1.0, tk.END))
        
        # 导入发送模式
        self.send_mode.set(self.parent.send_mode.get())
        self.add_personalization.set(self.parent.add_personalization.get())
        
        # 导入附件
        self.clear_attachments()
        if hasattr(self.parent, 'attachment_listbox'):
            for i in range(self.parent.attachment_listbox.size()):
                file_path = self.parent.attachment_listbox.get(i)
                self.attachment_listbox.insert(tk.END, file_path)
        
        self.log_message("已从主界面导入内容")
        messagebox.showinfo("导入成功", "已从主界面导入当前内容")

    def add_to_queue(self):
        """添加任务到队列"""
        # 验证输入
        sender_email = self.sender_email.get().strip()
        recipient_emails = self.recipient_emails.get(1.0, tk.END).strip()
        subject = self.subject.get().strip()
        body = self.body.get(1.0, tk.END).strip()

        if not sender_email or not sender_email.endswith('@qq.com'):
            messagebox.showerror("错误", "请输入有效的QQ邮箱地址")
            return

        if not recipient_emails:
            messagebox.showerror("错误", "请输入收件人邮箱地址")
            return

        if not subject:
            subject = "来自邮件队列系统的邮件"

        # 获取附件列表
        attachments = list(self.attachment_listbox.get(0, tk.END))

        # 创建任务
        task = {
            'id': len(self.parent.email_queue) + 1,
            'sender_email': sender_email,
            'recipient_emails': recipient_emails,
            'subject': subject,
            'body': body,
            'attachments': attachments,
            'send_mode': self.send_mode.get(),
            'add_personalization': self.add_personalization.get(),
            'created_time': datetime.datetime.now(),
            'status': 'pending'
        }

        self.parent.email_queue.append(task)
        self.refresh_queue_display()
        self.parent.update_queue_status()

        # 计算收件人数量
        recipient_count = len([email.strip() for email in
                              recipient_emails.replace(',', '\n').replace(';', '\n').split('\n')
                              if email.strip()])

        self.log_message(f"已添加任务 #{task['id']}: {subject} ({recipient_count} 个收件人)")

        # 询问是否清空表单
        if messagebox.askyesno("添加成功", "任务已添加到队列！\n是否清空表单以便添加下一个任务？"):
            self.clear_form()

    def refresh_queue_display(self):
        """刷新队列显示"""
        # 清空现有项目
        for item in self.queue_tree.get_children():
            self.queue_tree.delete(item)

        # 添加队列项目
        for task in self.parent.email_queue:
            recipient_count = len([email.strip() for email in
                                  task['recipient_emails'].replace(',', '\n').replace(';', '\n').split('\n')
                                  if email.strip()])

            status_text = {
                'pending': '⏳ 待发送',
                'sending': '📤 发送中',
                'completed': '✅ 已完成',
                'failed': '❌ 失败'
            }.get(task['status'], task['status'])

            mode_text = {
                'fast': '快速',
                'standard': '标准',
                'safe': '安全'
            }.get(task['send_mode'], task['send_mode'])

            self.queue_tree.insert('', 'end', values=(
                task['id'],
                status_text,
                task['subject'][:30] + '...' if len(task['subject']) > 30 else task['subject'],
                recipient_count,
                mode_text
            ))

        # 更新状态
        total_tasks = len(self.parent.email_queue)
        pending_tasks = len([t for t in self.parent.email_queue if t['status'] == 'pending'])

        self.queue_status_label.config(text=f"队列: {total_tasks} 个任务 ({pending_tasks} 待发送)")

        # 更新按钮状态
        if pending_tasks > 0:
            self.start_button.config(state='normal')
        else:
            self.start_button.config(state='disabled')

    def start_queue(self):
        """开始队列发送"""
        pending_tasks = [task for task in self.parent.email_queue if task['status'] == 'pending']
        if not pending_tasks:
            messagebox.showinfo("提示", "队列中没有待发送的任务")
            return

        # 计算总邮件数
        total_emails = sum(len([email.strip() for email in
                               task['recipient_emails'].replace(',', '\n').replace(';', '\n').split('\n')
                               if email.strip()]) for task in pending_tasks)

        confirm_msg = f"""准备开始队列发送：

📋 任务数量: {len(pending_tasks)} 个
📧 总邮件数: {total_emails} 封
⏱️ 预计耗时: 根据发送模式而定

队列将自动依次处理所有任务，您可以随时停止。

确定开始吗？"""

        if not messagebox.askyesno("确认队列发送", confirm_msg):
            return

        # 开始发送
        self.parent.queue_mode = True
        self.parent.should_stop = False

        # 更新按钮状态
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.pause_button.config(state='normal')
        self.resume_button.config(state='disabled')
        self.continue_button.config(state='disabled')
        self.progress.start()

        self.log_message(f"🚀 开始队列发送：{len(pending_tasks)} 个任务，共 {total_emails} 封邮件")

        # 在新线程中执行
        self.send_thread = threading.Thread(target=self._execute_queue_sending)
        self.send_thread.daemon = True
        self.send_thread.start()

    def stop_queue(self):
        """停止队列发送"""
        self.parent.should_stop = True
        self.log_message("⏹️ 正在停止队列发送...")
        self.status_var.set("正在停止队列发送...")
        messagebox.showinfo("停止发送", "已发出停止指令，将在当前邮件发送完成后停止")

    def pause_queue(self):
        """暂停队列发送"""
        if self.parent.queue_mode:
            self.parent.should_pause = True
            self.parent.is_paused = True

            # 更新按钮状态
            self.pause_button.config(state='disabled')
            self.resume_button.config(state='normal')

            self.log_message("⏸️ 队列发送已暂停，进度已保存")
            self.status_var.set("队列发送已暂停")
            messagebox.showinfo("暂停队列发送", "队列发送已暂停，进度已保存。\n点击'恢复发送'可继续。")
        else:
            messagebox.showwarning("提示", "当前没有正在进行的队列发送任务")

    def resume_queue(self):
        """恢复队列发送"""
        if self.parent.is_paused:
            self.parent.should_pause = False
            self.parent.is_paused = False

            # 更新按钮状态
            self.pause_button.config(state='normal')
            self.resume_button.config(state='disabled')

            self.log_message("▶️ 队列发送已恢复")
            self.status_var.set("队列发送已恢复")
            messagebox.showinfo("恢复队列发送", "队列发送已恢复")
        else:
            messagebox.showwarning("提示", "当前没有暂停的队列发送任务")

    def continue_from_breakpoint(self):
        """从断点继续队列发送"""
        try:
            # 检查是否有进度文件
            progress_file = "email_send_progress.json"
            if not os.path.exists(progress_file):
                messagebox.showwarning("没有断点", "没有找到可恢复的发送进度")
                return

            import json
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)

            # 显示断点信息
            session_id = progress_data.get('session_id', 'unknown')
            send_mode = progress_data.get('send_mode', 'standard')
            current_email = progress_data.get('current_email_index', 0)
            total_emails = progress_data.get('total_emails', 0)
            success_count = progress_data.get('success_count', 0)
            failed_count = progress_data.get('failed_count', 0)

            info_message = f"""队列断点信息：

会话ID: {session_id}
发送模式: {send_mode}
总邮件数: {total_emails}
已发送成功: {success_count}
已发送失败: {failed_count}
当前位置: 第{current_email + 1}封
剩余邮件: {total_emails - current_email}

确定要从断点继续队列发送吗？"""

            if messagebox.askyesno("断点继续", info_message):
                # 设置断点继续标志
                self.parent.resume_from_breakpoint = True
                self.parent.breakpoint_session_id = session_id

                # 禁用断点继续按钮
                self.continue_button.config(state='disabled')

                self.log_message(f"🔄 已设置队列断点继续模式 - 会话: {session_id}")

                # 自动开始队列发送
                self.start_queue()

        except Exception as e:
            self.log_message(f"❌ 读取队列断点信息失败: {str(e)}")
            messagebox.showerror("错误", f"读取断点信息失败：{str(e)}")

    def clear_queue(self):
        """清空队列"""
        if not self.parent.email_queue:
            messagebox.showinfo("提示", "队列已经是空的")
            return

        if messagebox.askyesno("确认清空", f"确定要清空队列中的 {len(self.parent.email_queue)} 个任务吗？\n此操作不可恢复！"):
            self.parent.email_queue.clear()
            self.refresh_queue_display()
            self.parent.update_queue_status()
            self.log_message("队列已清空")

    def edit_task(self):
        """编辑选中的任务"""
        selection = self.queue_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要编辑的任务")
            return

        item = self.queue_tree.item(selection[0])
        task_id = item['values'][0]

        # 找到任务
        task = None
        task_index = -1
        for i, t in enumerate(self.parent.email_queue):
            if t['id'] == task_id:
                task = t
                task_index = i
                break

        if task and task['status'] == 'pending':
            # 加载任务到表单
            self.sender_email.delete(0, tk.END)
            self.sender_email.insert(0, task['sender_email'])

            self.recipient_emails.delete(1.0, tk.END)
            self.recipient_emails.insert(tk.END, task['recipient_emails'])

            self.subject.delete(0, tk.END)
            self.subject.insert(0, task['subject'])

            self.body.delete(1.0, tk.END)
            self.body.insert(tk.END, task['body'])

            self.send_mode.set(task['send_mode'])
            self.add_personalization.set(task['add_personalization'])

            # 加载附件
            self.clear_attachments()
            for attachment in task['attachments']:
                self.attachment_listbox.insert(tk.END, attachment)

            # 删除原任务
            self.parent.email_queue.pop(task_index)
            self.refresh_queue_display()
            self.parent.update_queue_status()

            self.log_message(f"任务 #{task_id} 已加载到编辑表单")
            messagebox.showinfo("编辑任务", "任务已加载到表单，修改后请重新添加到队列")
        else:
            messagebox.showwarning("提示", "只能编辑待发送状态的任务")

    def delete_task(self):
        """删除选中的任务"""
        selection = self.queue_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要删除的任务")
            return

        item = self.queue_tree.item(selection[0])
        task_id = item['values'][0]

        if messagebox.askyesno("确认删除", f"确定要删除任务 #{task_id} 吗？"):
            self.parent.email_queue = [task for task in self.parent.email_queue if task['id'] != task_id]
            self.refresh_queue_display()
            self.parent.update_queue_status()
            self.log_message(f"已删除任务 #{task_id}")

    def move_up(self):
        """上移任务"""
        selection = self.queue_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要移动的任务")
            return

        item = self.queue_tree.item(selection[0])
        task_id = item['values'][0]

        # 找到任务索引
        task_index = -1
        for i, task in enumerate(self.parent.email_queue):
            if task['id'] == task_id:
                task_index = i
                break

        if task_index > 0:
            # 交换位置
            self.parent.email_queue[task_index], self.parent.email_queue[task_index-1] = \
                self.parent.email_queue[task_index-1], self.parent.email_queue[task_index]
            self.refresh_queue_display()
            self.log_message(f"任务 #{task_id} 已上移")
        else:
            messagebox.showinfo("提示", "任务已在最顶部")

    def move_down(self):
        """下移任务"""
        selection = self.queue_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请选择要移动的任务")
            return

        item = self.queue_tree.item(selection[0])
        task_id = item['values'][0]

        # 找到任务索引
        task_index = -1
        for i, task in enumerate(self.parent.email_queue):
            if task['id'] == task_id:
                task_index = i
                break

        if task_index < len(self.parent.email_queue) - 1:
            # 交换位置
            self.parent.email_queue[task_index], self.parent.email_queue[task_index+1] = \
                self.parent.email_queue[task_index+1], self.parent.email_queue[task_index]
            self.refresh_queue_display()
            self.log_message(f"任务 #{task_id} 已下移")
        else:
            messagebox.showinfo("提示", "任务已在最底部")

    def _execute_queue_sending(self):
        """执行队列发送（后台线程）"""
        try:
            pending_tasks = [task for task in self.parent.email_queue if task['status'] == 'pending']

            for task_index, task in enumerate(pending_tasks):
                # 检查是否应该停止
                if self.parent.should_stop:
                    self.queue_window.after(0, lambda: self.log_message("❌ 用户停止了队列发送"))
                    break

                # 检查是否应该暂停
                while self.parent.should_pause and not self.parent.should_stop:
                    self.queue_window.after(0, lambda: self.log_message("⏸️ 队列发送已暂停，等待恢复..."))
                    time.sleep(1)  # 每秒检查一次是否恢复

                # 如果在暂停期间收到停止指令
                if self.parent.should_stop:
                    self.queue_window.after(0, lambda: self.log_message("❌ 用户停止了队列发送"))
                    break

                # 更新任务状态
                task['status'] = 'sending'
                self.queue_window.after(0, self.refresh_queue_display)
                self.queue_window.after(0, lambda t=task: self.log_message(f"📧 开始发送任务 #{t['id']}: {t['subject']}"))

                # 发送任务
                success = self._send_single_task(task)

                if success:
                    task['status'] = 'completed'
                    self.queue_window.after(0, lambda t=task: self.log_message(f"✅ 任务 #{t['id']} 发送完成"))
                else:
                    task['status'] = 'failed'
                    self.queue_window.after(0, lambda t=task: self.log_message(f"❌ 任务 #{t['id']} 发送失败"))

                self.queue_window.after(0, self.refresh_queue_display)

                # 任务间隔
                if task_index < len(pending_tasks) - 1 and not self.parent.should_stop:
                    self.queue_window.after(0, lambda: self.log_message("⏳ 等待30秒后开始下一个任务..."))
                    for i in range(30):
                        if self.parent.should_stop:
                            break
                        # 检查暂停状态
                        while self.parent.should_pause and not self.parent.should_stop:
                            time.sleep(1)
                        if self.parent.should_stop:
                            break
                        time.sleep(1)

            # 发送完成
            completed_count = len([task for task in self.parent.email_queue if task['status'] == 'completed'])
            failed_count = len([task for task in self.parent.email_queue if task['status'] == 'failed'])

            if self.parent.should_stop:
                result_msg = f"队列发送已停止！\n已完成: {completed_count} 个任务\n失败: {failed_count} 个任务"
            else:
                result_msg = f"队列发送完成！\n成功: {completed_count} 个任务\n失败: {failed_count} 个任务"

            self.queue_window.after(0, lambda: self.log_message(f"🎉 {result_msg}"))
            self.queue_window.after(0, lambda: self._queue_send_complete(result_msg))

        except Exception as e:
            error_msg = f"队列发送出错: {str(e)}"
            self.queue_window.after(0, lambda: self.log_message(f"❌ {error_msg}"))
            self.queue_window.after(0, lambda: self._queue_send_complete(error_msg, False))

    def _send_single_task(self, task):
        """发送单个任务"""
        try:
            # 获取授权码 - 修复授权码获取逻辑
            auth_info = self.parent.auth_codes.get(task['sender_email'])
            if not auth_info:
                self.queue_window.after(0, lambda: self.log_message(f"❌ 任务 #{task['id']} 缺少授权码"))
                return False

            # 提取授权码（支持新旧格式）
            if isinstance(auth_info, dict):
                auth_code = auth_info.get('auth_code', '')
            else:
                auth_code = auth_info  # 兼容旧格式

            if not auth_code:
                self.queue_window.after(0, lambda: self.log_message(f"❌ 任务 #{task['id']} 授权码为空"))
                return False

            # 创建邮件发送器
            from email_sender import EmailSender
            sender = EmailSender(task['sender_email'])
            sender.smtp_config['password'] = auth_code

            # 解析收件人
            recipient_list = []
            for email in task['recipient_emails'].replace(',', '\n').replace(';', '\n').split('\n'):
                email = email.strip()
                if email and '@' in email:
                    recipient_list.append(email)

            if not recipient_list:
                self.queue_window.after(0, lambda: self.log_message(f"❌ 任务 #{task['id']} 没有有效收件人"))
                return False

            # 使用新的批次发送功能
            self.queue_window.after(0, lambda task_id=task['id'], mode=task['send_mode']:
                                   self.log_message(f"🚀 任务 #{task_id} 使用批次发送模式: {mode}"))

            # 准备邮件列表
            email_list = []
            for i, email in enumerate(recipient_list, 1):
                # 个性化内容
                personalized_body = task['body']
                if task['add_personalization']:
                    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    personalized_body += f"\n\n---\n邮件编号: #{i:03d} | 发送时间: {timestamp}"

                email_list.append({
                    'to_emails': [email],
                    'subject': task['subject'],
                    'body': personalized_body,
                    'attachments': task['attachments'] if task['attachments'] else None,
                    'original_email': email  # 保存原始邮箱用于记录
                })

            # 定义进度回调
            def progress_callback(progress_info):
                self.queue_window.after(0, lambda info=progress_info:
                    self.log_message(f"📊 批次 {info['current_batch']}/{info['total_batches']} - "
                                   f"已发送 {info['current_email']}/{info['total_emails']} 封 "
                                   f"(成功: {info['success_count']}, 失败: {info['failed_count']})"))

            # 定义停止检查回调
            def stop_callback():
                return self.parent.should_stop

            # 定义暂停检查回调
            def pause_callback():
                return self.parent.should_pause

            # 使用批次发送
            results = sender.send_batch_emails(
                email_list=email_list,
                send_mode=task['send_mode'],
                progress_callback=progress_callback,
                stop_callback=stop_callback,
                pause_callback=pause_callback
            )

            success_count = results['success']
            failed_count = results['failed']

            # 记录成功发送的邮件到主程序
            for i, email_info in enumerate(email_list[:success_count]):
                email_record = {
                    'recipient': email_info['original_email'],
                    'subject': email_info['subject'],
                    'body': email_info['body'],
                    'send_time': datetime.datetime.now(),
                    'batch_id': task['id']
                }
                self.parent.sent_emails.append(email_record)

            # 任务统计
            self.queue_window.after(0, lambda sc=success_count, fc=failed_count, task_id=task['id']:
                                   self.log_message(f"📊 任务 #{task_id} 批次发送完成 - 成功: {sc}, 失败: {fc}"))

            # 显示批次信息
            batch_info = results['batch_info']
            self.queue_window.after(0, lambda info=batch_info:
                self.log_message(f"📈 发送统计 - 总批次: {info['total_batches']}, "
                               f"预计耗时: {info['estimated_time_hours']:.1f}小时, "
                               f"模式: {info['send_mode']}"))

            return success_count > 0

        except Exception as e:
            self.queue_window.after(0, lambda error=str(e), task_id=task['id']:
                                   self.log_message(f"❌ 任务 #{task_id} 执行异常: {error}"))
            return False

    def _queue_send_complete(self, message, success=True):
        """队列发送完成"""
        self.parent.queue_mode = False
        self.parent.should_stop = False

        # 重置按钮状态
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        self.pause_button.config(state='disabled')
        self.resume_button.config(state='disabled')

        # 检查是否有未完成的任务来决定是否启用断点继续按钮
        try:
            progress_file = "email_send_progress.json"
            if os.path.exists(progress_file):
                import json
                with open(progress_file, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)

                current_email = progress_data.get('current_email_index', 0)
                total_emails = progress_data.get('total_emails', 0)

                if current_email < total_emails:
                    self.continue_button.config(state='normal')
                else:
                    self.continue_button.config(state='disabled')
            else:
                self.continue_button.config(state='disabled')
        except:
            self.continue_button.config(state='disabled')

        self.progress.stop()

        # 更新显示
        self.refresh_queue_display()
        self.parent.update_queue_status()

        # 启用主程序撤回按钮
        if self.parent.sent_emails:
            self.parent.recall_button.config(state='normal')

        if success:
            self.status_var.set("队列发送完成")
            messagebox.showinfo("队列发送完成", message)
        else:
            self.status_var.set("队列发送失败")
            messagebox.showerror("队列发送失败", message)

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log_message("日志已清空")

    def save_log(self):
        """保存日志"""
        try:
            log_content = self.log_text.get(1.0, tk.END)
            filename = filedialog.asksaveasfilename(
                title="保存队列日志",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(log_content)
                self.log_message(f"日志已保存到: {filename}")
                messagebox.showinfo("成功", "日志文件保存成功！")
        except Exception as e:
            self.log_message(f"保存日志失败: {str(e)}")
            messagebox.showerror("错误", f"保存日志失败：{str(e)}")
