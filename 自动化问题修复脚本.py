#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2.0系统自动化问题修复脚本
解决全功能模式开启后自动化流程不工作的问题
"""

import os
import json
import datetime

def main():
    """主修复函数"""
    print("🛠️ 开始修复2.0系统自动化问题...")
    print("="*60)
    
    # 1. 诊断问题
    issues = diagnose_issues()
    
    # 2. 修复配置文件
    fix_config_files()
    
    # 3. 创建GUI修复补丁
    create_gui_patch()
    
    # 4. 创建强制触发器
    create_force_trigger()
    
    # 5. 验证修复
    verify_fix()
    
    print("\n✅ 修复完成!")
    print("💡 请按以下步骤操作:")
    print("   1. 重启2.0系统")
    print("   2. 点击'一键启用全功能'")
    print("   3. 发送测试邮件")
    print("   4. 观察日志中的自动化触发信息")

def diagnose_issues():
    """诊断自动化问题"""
    print("🔍 诊断自动化问题...")
    
    issues = []
    
    # 检查配置文件
    if not os.path.exists('all_features_config.json'):
        issues.append("全功能配置文件缺失")
        print("   ❌ 全功能配置文件缺失")
    else:
        print("   ✅ 全功能配置文件存在")
    
    if not os.path.exists('automation_workflow.json'):
        issues.append("自动化工作流配置缺失")
        print("   ❌ 自动化工作流配置缺失")
    else:
        print("   ✅ 自动化工作流配置存在")
    
    # 检查GUI文件
    if os.path.exists('gui_main.py'):
        with open('gui_main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'auto_reply_monitoring.get()' not in content:
            issues.append("GUI中缺少自动化触发检查")
            print("   ❌ GUI中缺少自动化触发检查")
        else:
            print("   ✅ GUI中存在自动化触发检查")
        
        if 'auto_start_reply_monitoring' not in content:
            issues.append("GUI中缺少自动启动监控函数")
            print("   ❌ GUI中缺少自动启动监控函数")
        else:
            print("   ✅ GUI中存在自动启动监控函数")
    
    print(f"   总计发现 {len(issues)} 个问题")
    return issues

def fix_config_files():
    """修复配置文件"""
    print("📋 修复配置文件...")
    
    # 创建全功能配置
    all_features_config = {
        "enabled": True,
        "sender_email": "@qq.com",
        "enabled_time": datetime.datetime.now().isoformat(),
        "features": {
            "auto_reply_monitoring": True,
            "quality_database": True,
            "anti_spam": True,
            "qq_emergency": True,
            "smart_queue": True,
            "deep_coordination": True
        }
    }
    
    with open('all_features_config.json', 'w', encoding='utf-8') as f:
        json.dump(all_features_config, f, ensure_ascii=False, indent=2)
    
    print("   ✅ 全功能配置文件已创建")
    
    # 创建自动化工作流配置
    automation_workflow = {
        "enabled": True,
        "steps": [
            {
                "name": "auto_reply_monitoring",
                "enabled": True,
                "trigger": "after_send",
                "delay": 5,
                "description": "发送后自动启动回复监控"
            },
            {
                "name": "quality_db_import",
                "enabled": True,
                "trigger": "after_monitoring",
                "delay": 10,
                "description": "监控完成后自动导入质量数据库"
            },
            {
                "name": "emergency_check",
                "enabled": True,
                "trigger": "after_import",
                "delay": 5,
                "description": "导入完成后自动检查应急状态"
            }
        ]
    }
    
    with open('automation_workflow.json', 'w', encoding='utf-8') as f:
        json.dump(automation_workflow, f, ensure_ascii=False, indent=2)
    
    print("   ✅ 自动化工作流配置已创建")
    
    # 创建自动回复监控配置
    auto_reply_config = {
        'enabled': True,
        'check_interval': 5,
        'monitor_duration': 2,
        'auto_start': True,
        'sender_email': '@qq.com',
        'auto_import_to_quality': True,
        'auto_emergency_check': True,
        'auto_coordination': True
    }
    
    with open('auto_reply_config.json', 'w', encoding='utf-8') as f:
        json.dump(auto_reply_config, f, ensure_ascii=False, indent=2)
    
    print("   ✅ 自动回复监控配置已创建")

def create_gui_patch():
    """创建GUI修复补丁"""
    print("🖥️ 创建GUI修复补丁...")
    
    patch_content = '''# GUI自动化修复补丁
# 将以下代码添加到gui_main.py中

def _ensure_automation_working(self):
    """确保自动化正常工作"""
    try:
        # 1. 强制启用自动回复监控
        if hasattr(self, 'auto_reply_monitoring'):
            self.auto_reply_monitoring.set(True)
            self.log_message("🔧 已强制启用自动回复监控")
        
        # 2. 检查全功能配置
        if os.path.exists('all_features_config.json'):
            with open('all_features_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if config.get('enabled', False):
                self.all_features_enabled = True
                self.feature_status = config.get('features', {})
                self.log_message("🚀 全功能模式状态已恢复")
        
        # 3. 添加发送后强制检查
        self._add_post_send_check()
        
        return True
        
    except Exception as e:
        self.log_message(f"❌ 确保自动化工作失败: {str(e)}")
        return False

def _add_post_send_check(self):
    """添加发送后强制检查"""
    try:
        # 保存原始发送方法
        if not hasattr(self, '_original_send_emails_method'):
            self._original_send_emails_method = self.send_emails
        
        def enhanced_send_emails():
            """增强的发送邮件方法"""
            # 调用原始方法
            result = self._original_send_emails_method()
            
            # 发送成功后强制检查自动化
            if result:
                self.root.after(3000, self._force_check_automation)
            
            return result
        
        # 替换发送方法
        self.send_emails = enhanced_send_emails
        
        self.log_message("🔧 发送后自动化检查已安装")
        
    except Exception as e:
        self.log_message(f"❌ 安装发送后自动化检查失败: {str(e)}")

def _force_check_automation(self):
    """强制检查自动化"""
    try:
        sender_email = self.sender_email.get().strip()
        recipients_text = self.recipient_emails.get(1.0, tk.END).strip()
        
        if sender_email and recipients_text:
            recipients = self._parse_recipient_emails(recipients_text)
            
            if recipients and hasattr(self, 'auto_reply_monitoring'):
                is_enabled = self.auto_reply_monitoring.get()
                self.log_message(f"🔍 自动化检查: 监控状态={'启用' if is_enabled else '禁用'}")
                
                if is_enabled:
                    self.log_message("🚀 强制启动自动回复监控...")
                    try:
                        self.auto_start_reply_monitoring(sender_email, recipients)
                        self.log_message("✅ 自动回复监控已强制启动")
                    except Exception as e:
                        self.log_message(f"❌ 强制启动失败: {str(e)}")
                else:
                    self.log_message("⚠️ 自动回复监控未启用")
        
    except Exception as e:
        self.log_message(f"❌ 强制检查自动化失败: {str(e)}")

# 在__init__方法末尾添加:
# self._ensure_automation_working()
'''
    
    with open('gui_automation_patch.py', 'w', encoding='utf-8') as f:
        f.write(patch_content)
    
    print("   ✅ GUI修复补丁已创建: gui_automation_patch.py")

def create_force_trigger():
    """创建强制触发器"""
    print("🔧 创建强制触发器...")
    
    trigger_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制自动化触发器
独立运行此脚本来强制触发自动化
"""

import json
import datetime

def force_trigger_automation():
    """强制触发自动化"""
    print("🚀 强制触发自动化系统...")
    
    try:
        # 1. 确保配置文件正确
        ensure_configs()
        
        # 2. 测试深度协调系统
        test_coordination_system()
        
        print("✅ 强制触发完成")
        
    except Exception as e:
        print(f"❌ 强制触发失败: {str(e)}")

def ensure_configs():
    """确保配置正确"""
    config = {
        "enabled": True,
        "sender_email": "@qq.com",
        "enabled_time": datetime.datetime.now().isoformat(),
        "features": {
            "auto_reply_monitoring": True,
            "quality_database": True,
            "anti_spam": True,
            "qq_emergency": True,
            "smart_queue": True,
            "deep_coordination": True
        }
    }
    
    with open('all_features_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)
    
    print("✅ 配置文件已强制更新")

def test_coordination_system():
    """测试协调系统"""
    try:
        from 深度系统协调实现 import get_coordinator, SystemEvent
        coordinator = get_coordinator()
        
        test_data = {
            'sender_email': '<EMAIL>',
            'recipient_count': 3,
            'recipients': ['<EMAIL>', '<EMAIL>'],
            'send_time': datetime.datetime.now().isoformat()
        }
        
        coordinator.data_center.emit_event(SystemEvent.EMAIL_SENT, test_data)
        print("✅ 邮件发送事件已模拟触发")
        
    except Exception as e:
        print(f"❌ 协调系统测试失败: {str(e)}")

if __name__ == "__main__":
    force_trigger_automation()
'''
    
    with open('force_automation_trigger.py', 'w', encoding='utf-8') as f:
        f.write(trigger_content)
    
    print("   ✅ 强制触发器已创建: force_automation_trigger.py")

def verify_fix():
    """验证修复"""
    print("🧪 验证修复...")
    
    # 检查配置文件
    config_files = ['all_features_config.json', 'automation_workflow.json', 'auto_reply_config.json']
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"   ✅ {config_file} 存在")
        else:
            print(f"   ❌ {config_file} 缺失")
    
    # 测试组件导入
    try:
        from 深度系统协调实现 import get_coordinator
        print("   ✅ 深度协调系统可导入")
    except Exception as e:
        print(f"   ❌ 深度协调系统导入失败: {str(e)}")
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        print("   ✅ 质量管理器可导入")
    except Exception as e:
        print(f"   ❌ 质量管理器导入失败: {str(e)}")

if __name__ == "__main__":
    main()
