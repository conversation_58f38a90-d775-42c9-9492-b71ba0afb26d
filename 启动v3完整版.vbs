' 邮件系统v3.0完整功能版 - 简化启动脚本
' 一键启动完整功能版本

Option Explicit

Dim objShell, objFSO, currentDir, scriptName, fullPath, command
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取当前目录
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 要启动的脚本文件名
scriptName = "gui_complete_v3.py"
fullPath = currentDir & "\" & scriptName

' 检查文件是否存在
If Not objFSO.FileExists(fullPath) Then
    MsgBox "❌ 找不到文件：" & scriptName & vbCrLf & vbCrLf & _
           "请确保以下文件存在：" & vbCrLf & _
           "• gui_complete_v3.py" & vbCrLf & vbCrLf & _
           "当前目录：" & currentDir, _
           vbCritical, "文件检查"
    WScript.Quit
End If

' 构建启动命令
command = "python """ & fullPath & """"

' 显示启动信息
MsgBox "🚀 邮件系统v3.0完整功能版" & vbCrLf & vbCrLf & _
       "✨ 包含所有功能：" & vbCrLf & _
       "• 📤 邮件发送系统" & vbCrLf & _
       "• 🔄 邮件撤回功能" & vbCrLf & _
       "• 📊 质量数据库" & vbCrLf & _
       "• 📡 自动回复监控" & vbCrLf & _
       "• 🛡️ 安全防护" & vbCrLf & _
       "• 🧠 深度协调" & vbCrLf & vbCrLf & _
       "正在启动...", _
       vbInformation, "启动中"

' 启动Python脚本
On Error Resume Next
objShell.Run command, 1, False

If Err.Number <> 0 Then
    MsgBox "❌ 启动失败！" & vbCrLf & vbCrLf & _
           "可能的原因：" & vbCrLf & _
           "1. 未安装Python" & vbCrLf & _
           "2. Python未添加到PATH" & vbCrLf & _
           "3. 文件权限问题" & vbCrLf & vbCrLf & _
           "解决方案：" & vbCrLf & _
           "• 安装Python 3.8+版本" & vbCrLf & _
           "• 安装时勾选'Add to PATH'" & vbCrLf & _
           "• 以管理员身份运行", _
           vbCritical, "启动失败"
Else
    ' 启动成功提示
    MsgBox "✅ 邮件系统启动成功！" & vbCrLf & vbCrLf & _
           "🎯 使用提示：" & vbCrLf & _
           "• 首次使用请配置邮箱授权码" & vbCrLf & _
           "• 发送邮件后可使用撤回功能" & vbCrLf & _
           "• 启用监控可跟踪自动回复" & vbCrLf & _
           "• 查看日志了解运行状态" & vbCrLf & vbCrLf & _
           "如果窗口没有显示，请检查任务栏", _
           vbInformation, "启动成功"
End If

On Error GoTo 0
