# GUI自动化修复补丁
# 将以下代码添加到gui_main.py中

def _ensure_automation_working(self):
    """确保自动化正常工作"""
    try:
        # 1. 强制启用自动回复监控
        if hasattr(self, 'auto_reply_monitoring'):
            self.auto_reply_monitoring.set(True)
            self.log_message("🔧 已强制启用自动回复监控")
        
        # 2. 检查全功能配置
        if os.path.exists('all_features_config.json'):
            with open('all_features_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            if config.get('enabled', False):
                self.all_features_enabled = True
                self.feature_status = config.get('features', {})
                self.log_message("🚀 全功能模式状态已恢复")
        
        # 3. 添加发送后强制检查
        self._add_post_send_check()
        
        return True
        
    except Exception as e:
        self.log_message(f"❌ 确保自动化工作失败: {str(e)}")
        return False

def _add_post_send_check(self):
    """添加发送后强制检查"""
    try:
        # 保存原始发送方法
        if not hasattr(self, '_original_send_emails_method'):
            self._original_send_emails_method = self.send_emails
        
        def enhanced_send_emails():
            """增强的发送邮件方法"""
            # 调用原始方法
            result = self._original_send_emails_method()
            
            # 发送成功后强制检查自动化
            if result:
                self.root.after(3000, self._force_check_automation)
            
            return result
        
        # 替换发送方法
        self.send_emails = enhanced_send_emails
        
        self.log_message("🔧 发送后自动化检查已安装")
        
    except Exception as e:
        self.log_message(f"❌ 安装发送后自动化检查失败: {str(e)}")

def _force_check_automation(self):
    """强制检查自动化"""
    try:
        sender_email = self.sender_email.get().strip()
        recipients_text = self.recipient_emails.get(1.0, tk.END).strip()
        
        if sender_email and recipients_text:
            recipients = self._parse_recipient_emails(recipients_text)
            
            if recipients and hasattr(self, 'auto_reply_monitoring'):
                is_enabled = self.auto_reply_monitoring.get()
                self.log_message(f"🔍 自动化检查: 监控状态={'启用' if is_enabled else '禁用'}")
                
                if is_enabled:
                    self.log_message("🚀 强制启动自动回复监控...")
                    try:
                        self.auto_start_reply_monitoring(sender_email, recipients)
                        self.log_message("✅ 自动回复监控已强制启动")
                    except Exception as e:
                        self.log_message(f"❌ 强制启动失败: {str(e)}")
                else:
                    self.log_message("⚠️ 自动回复监控未启用")
        
    except Exception as e:
        self.log_message(f"❌ 强制检查自动化失败: {str(e)}")

# 在__init__方法末尾添加:
# self._ensure_automation_working()
