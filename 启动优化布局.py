#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动优化布局的邮件系统
类似截图风格的三栏布局
"""

import tkinter as tk
import sys
import os

def main():
    """启动优化布局的邮件系统"""
    print("🎨 启动优化布局的邮件系统...")
    print("📐 布局特点：")
    print("   • 左侧：邮件配置和内容编辑")
    print("   • 中间：操作控制和队列管理")
    print("   • 右侧：地动仪监控装饰")
    print("   • 三栏布局，更合理的空间利用")
    
    try:
        # 导入优化后的GUI
        from gui_main import EmailSenderGUI
        
        # 创建主窗口
        root = tk.Tk()
        
        # 设置窗口属性
        root.title("📧 智能邮件系统 v3.0 - 优化布局版")
        root.geometry("1400x900")
        root.configure(bg='#f8fafc')
        
        # 窗口居中
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - 1400) // 2
        y = (screen_height - 900) // 2
        root.geometry(f"1400x900+{x}+{y}")
        
        # 设置最小尺寸
        root.minsize(1200, 800)
        
        # 创建应用实例
        app = EmailSenderGUI(root)
        
        print("✅ 优化布局启动成功！")
        print("🎯 布局说明：")
        print("   • 左侧40%：邮件配置、内容编辑、日志")
        print("   • 中间30%：快速操作、队列管理、附件")
        print("   • 右侧30%：地动仪监控、系统状态")
        print("   • 更紧凑的控件布局")
        print("   • 更合理的空间分配")
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保 gui_main.py 文件存在且可正常导入")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("请检查系统环境和依赖")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
