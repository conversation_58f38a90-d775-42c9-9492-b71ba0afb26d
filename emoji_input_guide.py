# -*- coding: utf-8 -*-
"""
Emoji输入指南 - 解决Windows下Emoji输入问题的完整指南
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import os

def create_emoji_guide():
    """创建Emoji输入指南窗口"""
    guide_window = tk.Tk()
    guide_window.title("Emoji输入完整指南")
    guide_window.geometry("800x600")
    guide_window.resizable(True, True)
    
    # 主框架
    main_frame = ttk.Frame(guide_window, padding="15")
    main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    guide_window.columnconfigure(0, weight=1)
    guide_window.rowconfigure(0, weight=1)
    main_frame.columnconfigure(0, weight=1)
    main_frame.rowconfigure(1, weight=1)
    
    # 标题
    title_label = ttk.Label(main_frame, text="📱 Emoji表情输入完整指南", 
                           font=('Microsoft YaHei UI', 16, 'bold'))
    title_label.grid(row=0, column=0, pady=(0, 20))
    
    # 创建笔记本控件（标签页）
    notebook = ttk.Notebook(main_frame)
    notebook.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    # 第一个标签页：输入方法
    input_frame = ttk.Frame(notebook, padding="10")
    notebook.add(input_frame, text="输入方法")
    
    input_text = scrolledtext.ScrolledText(input_frame, width=70, height=25,
                                          font=('Microsoft YaHei UI', 10),
                                          wrap=tk.WORD)
    input_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    input_frame.columnconfigure(0, weight=1)
    input_frame.rowconfigure(0, weight=1)
    
    input_guide = """🎯 Windows系统Emoji输入方法大全

1️⃣ 【推荐】Windows系统快捷键方法：
   • 按 Win + . (句号键) 或 Win + ; (分号键)
   • 会弹出Windows系统自带的Emoji面板
   • 支持彩色Emoji显示
   • 可以搜索和分类浏览
   • 直接点击即可输入到当前光标位置

2️⃣ 输入法Emoji功能：
   • 搜狗输入法：输入"biaoqing"或"emoji"
   • 微软拼音：按Ctrl+Shift+B打开表情面板
   • QQ输入法：右键菜单选择"表情符号"
   • 百度输入法：输入"bd表情"

3️⃣ 复制粘贴方法：
   • 从网页、聊天软件复制Emoji
   • 直接粘贴到邮件正文中
   • 推荐网站：emojipedia.org

4️⃣ Alt码输入（部分符号）：
   • 按住Alt键，输入数字码
   • 例如：Alt+1 = ☺, Alt+2 = ☻
   • 主要适用于基础符号

🔧 解决输入问题的技巧：

✅ 如果无法输入Emoji：
   1. 确保使用支持Unicode的字体
   2. 检查输入法设置
   3. 尝试重启输入法
   4. 使用Win+.快捷键

✅ 如果显示为黑白方块：
   1. 更新Windows系统
   2. 安装最新的字体包
   3. 使用"Segoe UI Emoji"字体
   4. 检查应用程序字体设置

✅ 如果复制粘贴失败：
   1. 先粘贴到记事本测试
   2. 检查剪贴板内容
   3. 尝试不同的复制源

🎨 常用Emoji快速参考：

😀😃😄😁😆😅😂🤣😊😇🙂🙃😉😌😍🥰😘😗😙😚😋😛😝😜🤪🤨🧐🤓😎🤩

❤️🧡💛💚💙💜🖤🤍🤎💔❣️💕💞💓💗💖💘💝💟☮️✝️☪️🕉️☸️✡️🔯🕎☯️☦️

👍👎👌🤏✌️🤞🤟🤘🤙👈👉👆🖕👇☝️👋🤚🖐️✋🖖👏🙌👐🤲🤝🙏✍️💅

🐶🐱🐭🐹🐰🦊🐻🐼🐨🐯🦁🐮🐷🐽🐸🐵🙈🙉🙊🐒🐔🐧🐦🐤🐣🐥

🍎🍊🍋🍌🍉🍇🍓🫐🍈🍒🍑🥭🍍🥥🥝🍅🍆🥑🥦🥬🥒🌶️🫑🌽🥕

⚽🏀🏈⚾🥎🎾🏐🏉🥏🎱🪀🏓🏸🏒🏑🥍🏏🪃🥅⛳🪁🏹🎣🤿🥊🥋

🚗🚕🚙🚌🚎🏎️🚓🚑🚒🚐🛻🚚🚛🚜🏍️🛵🚲🛴🛹🛼🚁✈️🛩️🚀🛸

⭐🌟💫⭐🌠☀️🌤️⛅🌦️🌧️⛈️🌩️🌨️❄️☃️⛄🌬️💨💧💦☔☂️🌊🌫️

🎵🎶🎼🎹🥁🎷🎺🎸🪕🎻🎤🎧🎙️🎚️🎛️🎬🎥📷📸📹📼💿💾💽📀

📱📞☎️📟📠📧📨📩📤📥📦📫📪📬📭📮🗳️✏️✒️🖋️🖊️🖌️🖍️📝📄📃📑
"""
    
    input_text.insert(tk.END, input_guide)
    input_text.config(state=tk.DISABLED)
    
    # 第二个标签页：测试区域
    test_frame = ttk.Frame(notebook, padding="10")
    notebook.add(test_frame, text="测试输入")
    
    test_frame.columnconfigure(0, weight=1)
    test_frame.rowconfigure(1, weight=1)
    
    ttk.Label(test_frame, text="在下面的文本框中测试Emoji输入：", 
             font=('Microsoft YaHei UI', 12)).grid(row=0, column=0, pady=(0, 10))
    
    test_text = scrolledtext.ScrolledText(test_frame, width=70, height=20,
                                         font=('Segoe UI Emoji', 12),
                                         wrap=tk.WORD)
    test_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    # 预设测试内容
    test_content = """🎉 欢迎测试Emoji输入！

请尝试以下方法：
1. 按 Win + . 打开系统Emoji面板
2. 使用输入法的表情功能
3. 复制粘贴其他地方的Emoji

测试内容：
😀 基础笑脸
❤️ 爱心
👍 点赞
🎵 音乐
🌟 星星
🚀 火箭
🍎 苹果
🐱 小猫

如果能看到彩色的Emoji，说明显示正常！
如果看到黑白方块，可能需要更新字体或系统。
"""
    
    test_text.insert(tk.END, test_content)
    
    # 按钮框架
    btn_frame = ttk.Frame(test_frame)
    btn_frame.grid(row=2, column=0, pady=10)
    
    def open_emoji_panel():
        """打开Windows Emoji面板"""
        try:
            # 尝试发送Win+.快捷键
            import win32api
            import win32con
            win32api.keybd_event(0x5B, 0, 0, 0)  # Win键按下
            win32api.keybd_event(0xBE, 0, 0, 0)  # .键按下
            win32api.keybd_event(0xBE, 0, win32con.KEYEVENTF_KEYUP, 0)  # .键释放
            win32api.keybd_event(0x5B, 0, win32con.KEYEVENTF_KEYUP, 0)  # Win键释放
            messagebox.showinfo("成功", "已尝试打开Windows Emoji面板")
        except ImportError:
            messagebox.showinfo("提示", "请手动按 Win + . 键打开Emoji面板")
        except Exception as e:
            messagebox.showerror("错误", f"操作失败: {str(e)}\n请手动按 Win + . 键")
    
    def clear_test():
        """清空测试区域"""
        test_text.delete(1.0, tk.END)
    
    def get_content():
        """获取测试内容"""
        content = test_text.get(1.0, tk.END)
        messagebox.showinfo("测试内容", f"内容长度: {len(content)} 字符\n\n前200个字符:\n{content[:200]}...")
    
    ttk.Button(btn_frame, text="打开Emoji面板 (Win+.)", command=open_emoji_panel).pack(side=tk.LEFT, padx=5)
    ttk.Button(btn_frame, text="清空测试区", command=clear_test).pack(side=tk.LEFT, padx=5)
    ttk.Button(btn_frame, text="查看内容", command=get_content).pack(side=tk.LEFT, padx=5)
    
    # 第三个标签页：常见问题
    faq_frame = ttk.Frame(notebook, padding="10")
    notebook.add(faq_frame, text="常见问题")
    
    faq_text = scrolledtext.ScrolledText(faq_frame, width=70, height=25,
                                        font=('Microsoft YaHei UI', 10),
                                        wrap=tk.WORD)
    faq_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    faq_frame.columnconfigure(0, weight=1)
    faq_frame.rowconfigure(0, weight=1)
    
    faq_content = """❓ 常见问题解答

Q1: 为什么我按Win+.没有反应？
A1: 可能的原因和解决方法：
   • 确保Windows版本是Windows 10 1903或更高版本
   • 检查是否禁用了Windows快捷键
   • 尝试Win+;（分号键）
   • 重启电脑后再试

Q2: 为什么Emoji显示为黑白方块？
A2: 这是字体问题：
   • 安装"Segoe UI Emoji"字体
   • 更新Windows系统到最新版本
   • 在应用程序中设置支持Emoji的字体
   • 检查应用程序是否支持彩色字体

Q3: 输入法无法输入Emoji怎么办？
A3: 尝试以下方法：
   • 切换到微软拼音输入法
   • 更新输入法到最新版本
   • 检查输入法设置中的表情选项
   • 使用系统自带的Emoji面板

Q4: 复制的Emoji粘贴后变成问号？
A4: 编码问题：
   • 确保目标应用支持UTF-8编码
   • 尝试先粘贴到记事本测试
   • 检查应用程序的字符编码设置
   • 使用支持Unicode的应用程序

Q5: 邮件中的Emoji收件人看不到？
A5: 兼容性问题：
   • 使用HTML格式发送邮件
   • 确保邮件客户端支持Unicode
   • 避免使用过新的Emoji
   • 可以考虑使用图片替代

Q6: 如何在不同设备上保持Emoji一致？
A6: 标准化建议：
   • 使用Unicode标准Emoji
   • 避免使用平台特有的表情
   • 测试在不同设备上的显示效果
   • 提供文字备选方案

🔧 高级技巧：

1. 自定义Emoji快捷键：
   • 在输入法中设置常用Emoji的快捷输入
   • 使用文本替换功能（如:smile: → 😊）

2. 批量Emoji处理：
   • 使用正则表达式查找替换
   • 编写脚本批量转换文本

3. Emoji搜索技巧：
   • 在Windows Emoji面板中使用搜索功能
   • 按分类浏览（笑脸、动物、食物等）
   • 记住常用Emoji的关键词

4. 专业建议：
   • 在正式邮件中谨慎使用Emoji
   • 考虑目标受众的文化背景
   • 保持Emoji使用的一致性和适度

💡 实用网站推荐：
   • Emojipedia.org - Emoji百科全书
   • Unicode.org - Unicode标准官网
   • GetEmoji.com - 在线Emoji复制工具
   • EmojiKeyboard.org - 在线Emoji键盘

🎯 最佳实践：
   1. 优先使用系统自带的Emoji面板
   2. 定期更新系统和字体
   3. 在重要场合前测试Emoji显示
   4. 准备纯文本备选方案
   5. 了解目标平台的Emoji支持情况
"""
    
    faq_text.insert(tk.END, faq_content)
    faq_text.config(state=tk.DISABLED)
    
    # 底部按钮
    bottom_frame = ttk.Frame(main_frame)
    bottom_frame.grid(row=2, column=0, pady=10)
    
    ttk.Button(bottom_frame, text="关闭指南", command=guide_window.destroy).pack()
    
    guide_window.mainloop()

if __name__ == "__main__":
    create_emoji_guide()
