#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试和修复2.0系统全功能自动化模式
诊断并解决自动化流程不工作的问题
"""

import datetime
import time
import os
import json
import sys

def diagnose_automation_issues():
    """诊断自动化问题"""
    print("🔍 诊断2.0系统全功能自动化问题")
    print("="*60)

    issues_found = []

    try:
        # 1. 检查配置文件
        print("\n📋 步骤1: 检查配置文件")
        config_issues = check_config_files()
        issues_found.extend(config_issues)

        # 2. 检查事件触发机制
        print("\n🔧 步骤2: 检查事件触发机制")
        event_issues = check_event_triggers()
        issues_found.extend(event_issues)

        # 3. 检查自动化工作流
        print("\n🤖 步骤3: 检查自动化工作流")
        workflow_issues = check_automation_workflow()
        issues_found.extend(workflow_issues)

        # 4. 检查GUI集成
        print("\n🖥️ 步骤4: 检查GUI集成")
        gui_issues = check_gui_integration()
        issues_found.extend(gui_issues)

        # 5. 生成修复方案
        print("\n🛠️ 步骤5: 生成修复方案")
        generate_fix_solutions(issues_found)

        print("\n" + "="*60)
        print("🎯 诊断完成!")

    except Exception as e:
        print(f"❌ 诊断失败: {str(e)}")

def check_config_files():
    """检查配置文件"""
    issues = []

    # 检查全功能配置
    if os.path.exists('all_features_config.json'):
        try:
            with open('all_features_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)

            print(f"✅ 全功能配置文件存在")
            print(f"   启用状态: {config.get('enabled', False)}")
            print(f"   发件人: {config.get('sender_email', '未设置')}")

            features = config.get('features', {})
            for feature, enabled in features.items():
                status = "✅" if enabled else "❌"
                print(f"   {status} {feature}: {enabled}")

                if not enabled:
                    issues.append(f"功能未启用: {feature}")

        except Exception as e:
            issues.append(f"全功能配置文件损坏: {str(e)}")
            print(f"❌ 全功能配置文件损坏: {str(e)}")
    else:
        issues.append("全功能配置文件不存在")
        print("❌ 全功能配置文件不存在")

    # 检查自动化工作流配置
    if os.path.exists('automation_workflow.json'):
        try:
            with open('automation_workflow.json', 'r', encoding='utf-8') as f:
                workflow = json.load(f)

            print(f"✅ 自动化工作流配置存在")
            print(f"   启用状态: {workflow.get('enabled', False)}")

            steps = workflow.get('steps', [])
            print(f"   工作流步骤: {len(steps)} 个")

            for step in steps:
                name = step.get('name', '未知')
                enabled = step.get('enabled', False)
                trigger = step.get('trigger', '未设置')
                status = "✅" if enabled else "❌"
                print(f"   {status} {name}: {trigger}")

                if not enabled:
                    issues.append(f"工作流步骤未启用: {name}")

        except Exception as e:
            issues.append(f"自动化工作流配置损坏: {str(e)}")
            print(f"❌ 自动化工作流配置损坏: {str(e)}")
    else:
        issues.append("自动化工作流配置不存在")
        print("❌ 自动化工作流配置不存在")

    return issues

def check_event_triggers():
    """检查事件触发机制"""
    issues = []

    # 检查深度协调系统
    try:
        from 深度系统协调实现 import DeepSystemCoordinator, SystemEvent
        coordinator = DeepSystemCoordinator()

        print("✅ 深度协调系统可导入")

        # 测试事件触发
        test_events = [
            SystemEvent.EMAIL_SENT,
            SystemEvent.REPLY_RECEIVED,
            SystemEvent.MONITORING_COMPLETED,
            SystemEvent.QUALITY_IMPORTED,
            SystemEvent.EMERGENCY_CHECKED
        ]

        for event in test_events:
            try:
                # 测试事件监听器
                listeners = coordinator.data_center.event_listeners.get(event, [])
                print(f"   📡 {event.value}: {len(listeners)} 个监听器")

                if len(listeners) == 0:
                    issues.append(f"事件无监听器: {event.value}")

            except Exception as e:
                issues.append(f"事件测试失败: {event.value} - {str(e)}")

    except ImportError as e:
        issues.append(f"深度协调系统导入失败: {str(e)}")
        print(f"❌ 深度协调系统导入失败: {str(e)}")
    except Exception as e:
        issues.append(f"深度协调系统错误: {str(e)}")
        print(f"❌ 深度协调系统错误: {str(e)}")

    return issues

def check_automation_workflow():
    """检查自动化工作流"""
    issues = []

    # 检查自动回复监控
    try:
        from email_receiver import EmailReceiver
        print("✅ 邮件接收器可导入")
    except ImportError as e:
        issues.append(f"邮件接收器导入失败: {str(e)}")
        print(f"❌ 邮件接收器导入失败: {str(e)}")

    # 检查质量管理器
    try:
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        print("✅ 质量管理器可导入")

        # 测试基本功能
        analytics = quality_manager.get_quality_analytics()
        print(f"   📊 质量分析: {analytics.get('overview', {}).get('total_recipients', 0)} 个收件人")

    except ImportError as e:
        issues.append(f"质量管理器导入失败: {str(e)}")
        print(f"❌ 质量管理器导入失败: {str(e)}")
    except Exception as e:
        issues.append(f"质量管理器错误: {str(e)}")
        print(f"❌ 质量管理器错误: {str(e)}")

    # 检查应急管理器
    try:
        from qq_email_anti_spam import QQEmailAntiSpamManager
        qq_manager = QQEmailAntiSpamManager()
        print("✅ QQ应急管理器可导入")
    except ImportError as e:
        issues.append(f"QQ应急管理器导入失败: {str(e)}")
        print(f"❌ QQ应急管理器导入失败: {str(e)}")
    except Exception as e:
        issues.append(f"QQ应急管理器错误: {str(e)}")
        print(f"❌ QQ应急管理器错误: {str(e)}")

    return issues

def check_gui_integration():
    """检查GUI集成"""
    issues = []

    # 检查主GUI文件
    if os.path.exists('gui_main.py'):
        print("✅ 主GUI文件存在")

        # 检查关键函数
        with open('gui_main.py', 'r', encoding='utf-8') as f:
            content = f.read()

        key_functions = [
            'auto_start_reply_monitoring',
            '_enable_auto_reply_monitoring',
            '_auto_coordinate_advanced_features',
            'emit_event',
            'auto_reply_monitoring.get()'
        ]

        for func in key_functions:
            if func in content:
                print(f"   ✅ 找到关键函数: {func}")
            else:
                issues.append(f"缺少关键函数: {func}")
                print(f"   ❌ 缺少关键函数: {func}")

        # 检查自动化触发点
        trigger_patterns = [
            '邮件发送成功，自动启动回复监控',
            'auto_reply_monitoring.get()',
            'EMAIL_SENT',
            '_auto_coordinate_advanced_features'
        ]

        for pattern in trigger_patterns:
            if pattern in content:
                print(f"   ✅ 找到触发模式: {pattern}")
            else:
                issues.append(f"缺少触发模式: {pattern}")
                print(f"   ❌ 缺少触发模式: {pattern}")

    else:
        issues.append("主GUI文件不存在")
        print("❌ 主GUI文件不存在")

    return issues

def generate_fix_solutions(issues):
    """生成修复方案"""
    if not issues:
        print("🎉 未发现问题，自动化系统应该正常工作")
        return

    print(f"🛠️ 发现 {len(issues)} 个问题，生成修复方案:")

    # 创建修复脚本
    fix_script = create_automation_fix_script(issues)

    # 保存修复脚本
    with open('fix_automation_issues.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)

    print("✅ 修复脚本已生成: fix_automation_issues.py")
    print("💡 请运行修复脚本: python fix_automation_issues.py")

def create_automation_fix_script(issues):
    """创建自动化修复脚本"""
    script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化问题修复脚本
自动修复2.0系统全功能自动化模式的问题
"""

import json
import os
import datetime

def fix_all_automation_issues():
    """修复所有自动化问题"""
    print("🛠️ 开始修复自动化问题...")

    # 1. 修复配置文件
    fix_config_files()

    # 2. 修复事件触发机制
    fix_event_triggers()

    # 3. 修复GUI集成
    fix_gui_integration()

    # 4. 创建测试用例
    create_test_cases()

    print("✅ 修复完成!")

def fix_config_files():
    """修复配置文件"""
    print("📋 修复配置文件...")

    # 修复全功能配置
    all_features_config = {
        "enabled": True,
        "sender_email": "@qq.com",
        "enabled_time": datetime.datetime.now().isoformat(),
        "features": {
            "auto_reply_monitoring": True,
            "quality_database": True,
            "anti_spam": True,
            "qq_emergency": True,
            "smart_queue": True,
            "deep_coordination": True
        }
    }

    with open('all_features_config.json', 'w', encoding='utf-8') as f:
        json.dump(all_features_config, f, ensure_ascii=False, indent=2)

    print("✅ 全功能配置文件已修复")

    # 修复自动化工作流配置
    automation_workflow = {
        "enabled": True,
        "steps": [
            {
                "name": "auto_reply_monitoring",
                "enabled": True,
                "trigger": "after_send",
                "delay": 5,
                "description": "发送后自动启动回复监控"
            },
            {
                "name": "quality_db_import",
                "enabled": True,
                "trigger": "after_monitoring",
                "delay": 10,
                "description": "监控完成后自动导入质量数据库"
            },
            {
                "name": "emergency_check",
                "enabled": True,
                "trigger": "after_import",
                "delay": 5,
                "description": "导入完成后自动检查应急状态"
            },
            {
                "name": "coordination_sync",
                "enabled": True,
                "trigger": "after_emergency",
                "delay": 5,
                "description": "应急检查后自动同步协调系统"
            },
            {
                "name": "cleanup_invalid",
                "enabled": True,
                "trigger": "periodic",
                "interval": 3600,
                "description": "定期清理无效收件人"
            }
        ],
        "error_handling": {
            "retry_count": 3,
            "retry_delay": 30,
            "continue_on_error": True,
            "log_errors": True
        }
    }

    with open('automation_workflow.json', 'w', encoding='utf-8') as f:
        json.dump(automation_workflow, f, ensure_ascii=False, indent=2)

    print("✅ 自动化工作流配置已修复")

def fix_event_triggers():
    """修复事件触发机制"""
    print("🔧 修复事件触发机制...")

    # 创建事件触发器修复脚本
    trigger_fix = """
# 在GUI主文件中添加的修复代码
def ensure_automation_triggers(self):
    '''确保自动化触发器正常工作'''
    try:
        # 确保深度协调系统已初始化
        if not hasattr(self, 'coordinator') or not self.coordinator:
            from 深度系统协调实现 import get_coordinator
            self.coordinator = get_coordinator()
            self.log_message("🔧 深度协调系统已重新初始化")

        # 确保自动回复监控变量已设置
        if not hasattr(self, 'auto_reply_monitoring'):
            import tkinter as tk
            self.auto_reply_monitoring = tk.BooleanVar(value=True)
            self.log_message("🔧 自动回复监控变量已重新设置")

        # 检查全功能配置
        if os.path.exists('all_features_config.json'):
            with open('all_features_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)

            if config.get('enabled', False):
                # 强制启用自动回复监控
                self.auto_reply_monitoring.set(True)
                self.log_message("🔧 自动回复监控已强制启用")

                # 确保功能状态正确
                self.feature_status = config.get('features', {})
                self.all_features_enabled = True

                self.log_message("✅ 自动化触发器修复完成")
                return True

        return False

    except Exception as e:
        self.log_message(f"❌ 修复自动化触发器失败: {str(e)}")
        return False
"""

    with open('automation_trigger_fix.py', 'w', encoding='utf-8') as f:
        f.write(trigger_fix)

    print("✅ 事件触发器修复代码已生成")

def fix_gui_integration():
    """修复GUI集成"""
    print("🖥️ 修复GUI集成...")

    # 创建GUI修复补丁
    gui_patch = """
# GUI修复补丁 - 添加到gui_main.py的__init__方法中

# 在__init__方法末尾添加:
self._ensure_automation_ready()

def _ensure_automation_ready(self):
    '''确保自动化系统就绪'''
    try:
        # 检查并修复自动化配置
        self._check_and_fix_automation_config()

        # 强制启用自动化功能
        self._force_enable_automation_features()

        # 设置自动化事件监听
        self._setup_automation_event_listeners()

        self.log_message("🤖 自动化系统就绪检查完成")

    except Exception as e:
        self.log_message(f"❌ 自动化系统就绪检查失败: {str(e)}")

def _check_and_fix_automation_config(self):
    '''检查并修复自动化配置'''
    import os
    import json

    # 检查全功能配置
    if not os.path.exists('all_features_config.json'):
        # 创建默认配置
        config = {
            "enabled": True,
            "sender_email": "",
            "enabled_time": datetime.datetime.now().isoformat(),
            "features": {
                "auto_reply_monitoring": True,
                "quality_database": True,
                "anti_spam": True,
                "qq_emergency": True,
                "smart_queue": True,
                "deep_coordination": True
            }
        }

        with open('all_features_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        self.log_message("🔧 已创建默认全功能配置")

def _force_enable_automation_features(self):
    '''强制启用自动化功能'''
    try:
        # 强制启用自动回复监控
        if hasattr(self, 'auto_reply_monitoring'):
            self.auto_reply_monitoring.set(True)

        # 设置功能状态
        self.feature_status = {
            "auto_reply_monitoring": True,
            "quality_database": True,
            "anti_spam": True,
            "qq_emergency": True,
            "smart_queue": True,
            "deep_coordination": True
        }

        self.all_features_enabled = True

        self.log_message("🚀 自动化功能已强制启用")

    except Exception as e:
        self.log_message(f"❌ 强制启用自动化功能失败: {str(e)}")

def _setup_automation_event_listeners(self):
    '''设置自动化事件监听'''
    try:
        # 确保深度协调系统已初始化
        if not hasattr(self, 'coordinator') or not self.coordinator:
            from 深度系统协调实现 import get_coordinator
            self.coordinator = get_coordinator()

        # 添加自定义事件监听器
        from 深度系统协调实现 import SystemEvent

        def on_email_sent_custom(data):
            '''自定义邮件发送事件处理'''
            sender_email = data.get('sender_email', '')
            recipients = data.get('recipients', [])

            if sender_email and recipients and self.auto_reply_monitoring.get():
                # 强制启动自动监控
                self.root.after(5000, lambda: self.auto_start_reply_monitoring(sender_email, recipients))
                self.log_message(f"🤖 强制触发自动监控: {len(recipients)} 个收件人")

        # 注册事件监听器
        self.coordinator.data_center.add_event_listener(SystemEvent.EMAIL_SENT, on_email_sent_custom)

        self.log_message("🔧 自动化事件监听器已设置")

    except Exception as e:
        self.log_message(f"❌ 设置自动化事件监听器失败: {str(e)}")
"""

    with open('gui_automation_patch.py', 'w', encoding='utf-8') as f:
        f.write(gui_patch)

    print("✅ GUI集成修复补丁已生成")

def create_test_cases():
    """创建测试用例"""
    print("🧪 创建测试用例...")

    test_code = """
def test_automation_workflow():
    '''测试自动化工作流'''
    print("🧪 测试自动化工作流...")

    try:
        # 1. 测试配置文件
        assert os.path.exists('all_features_config.json'), "全功能配置文件不存在"
        assert os.path.exists('automation_workflow.json'), "工作流配置文件不存在"

        # 2. 测试深度协调系统
        from 深度系统协调实现 import get_coordinator, SystemEvent
        coordinator = get_coordinator()

        # 3. 模拟邮件发送事件
        test_data = {
            'sender_email': '<EMAIL>',
            'recipient_count': 5,
            'recipients': ['<EMAIL>', '<EMAIL>'],
            'send_time': datetime.datetime.now().isoformat()
        }

        coordinator.data_center.emit_event(SystemEvent.EMAIL_SENT, test_data)
        print("✅ 邮件发送事件触发成功")

        # 4. 测试自动化组件
        from recipient_quality_manager import RecipientQualityManager
        from qq_email_anti_spam import QQEmailAntiSpamManager

        quality_manager = RecipientQualityManager()
        qq_manager = QQEmailAntiSpamManager()

        print("✅ 所有自动化组件可正常导入")

        print("🎉 自动化工作流测试通过!")
        return True

    except Exception as e:
        print(f"❌ 自动化工作流测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    fix_all_automation_issues()

    # 运行测试
    print("\\n🧪 运行自动化测试...")
    test_automation_workflow()

    print("\\n🎊 修复和测试完成!")
    print("💡 现在请重启2.0系统，自动化功能应该正常工作了")
"""

    with open('test_automation_workflow.py', 'w', encoding='utf-8') as f:
        f.write(test_code)

    print("✅ 测试用例已创建")

if __name__ == "__main__":
    fix_all_automation_issues()
'''

    return script

def test_auto_reply_monitoring():
    """测试自动回复监控功能"""
    try:
        from email_receiver import EmailReceiver
        
        test_email = "<EMAIL>"
        test_password = "test_password"
        
        print(f"📧 测试邮箱: {test_email}")
        
        # 创建接收器
        receiver = EmailReceiver(test_email, test_password)
        
        # 测试获取有效收件人
        valid_recipients = receiver.get_valid_recipients(test_email)
        print(f"✅ 有效收件人: {len(valid_recipients)} 个")
        
        # 测试获取无效收件人
        invalid_recipients = receiver.get_invalid_recipients(test_email)
        print(f"❌ 无效收件人: {len(invalid_recipients)} 个")
        
        print("✅ 自动回复监控功能测试通过")
        
    except Exception as e:
        print(f"❌ 自动回复监控测试失败: {str(e)}")

def test_quality_database():
    """测试质量数据库功能"""
    try:
        from recipient_quality_manager import RecipientQualityManager
        
        # 创建质量管理器
        quality_manager = RecipientQualityManager()
        
        # 测试添加收件人
        test_recipients = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ]
        
        added_count = 0
        for recipient in test_recipients:
            try:
                quality_manager.add_recipient(
                    email=recipient,
                    initial_score=85,
                    source="自动化测试"
                )
                added_count += 1
            except Exception:
                pass  # 可能已存在
        
        print(f"✅ 测试添加收件人: {added_count} 个")
        
        # 测试获取高质量收件人
        high_quality = quality_manager.get_quality_recipients(
            min_quality_score=80,
            limit=10
        )
        print(f"✅ 高质量收件人: {len(high_quality)} 个")
        
        print("✅ 质量数据库功能测试通过")
        
    except Exception as e:
        print(f"❌ 质量数据库测试失败: {str(e)}")

def test_emergency_management():
    """测试应急管理功能"""
    try:
        from qq_email_anti_spam import QQEmailAntiSpamManager
        
        # 创建应急管理器
        qq_manager = QQEmailAntiSpamManager()
        test_email = "<EMAIL>"
        
        print(f"📧 测试邮箱: {test_email}")
        
        # 测试获取应急状态
        status = qq_manager.get_qq_emergency_status(test_email)
        
        if status:
            emergency_info = status.get('emergency_info', {})
            is_active = emergency_info.get('is_active', False)
            consecutive_no_reply = emergency_info.get('consecutive_no_reply', 0)
            
            print(f"🆘 应急状态: {'激活' if is_active else '正常'}")
            print(f"📊 连续无回复: {consecutive_no_reply} 封")
            
            # 显示日常统计
            daily_stats = status.get('daily_stats', {})
            print(f"📈 今日发送: {daily_stats.get('total_sent', 0)} 封")
            print(f"📬 收到回复: {daily_stats.get('total_replies', 0)} 封")
            print(f"📊 回复率: {daily_stats.get('reply_rate', 0):.1f}%")
        else:
            print("⚠️ 无法获取应急状态")
        
        print("✅ 应急管理功能测试通过")
        
    except Exception as e:
        print(f"❌ 应急管理测试失败: {str(e)}")

def test_automation_integration():
    """测试自动化集成"""
    try:
        print("🔗 测试系统集成...")
        
        # 测试配置文件
        config_files = [
            'auto_reply_config.json',
            'all_features_config.json'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"✅ 配置文件存在: {config_file}")
            else:
                print(f"⚠️ 配置文件不存在: {config_file}")
        
        # 测试数据库文件
        db_files = [
            'email_history.db',
            'recipient_quality.db',
            'qq_anti_spam.db'
        ]
        
        for db_file in db_files:
            if os.path.exists(db_file):
                print(f"✅ 数据库文件存在: {db_file}")
            else:
                print(f"⚠️ 数据库文件不存在: {db_file}")
        
        print("✅ 自动化集成测试通过")
        
    except Exception as e:
        print(f"❌ 自动化集成测试失败: {str(e)}")

def simulate_full_workflow():
    """模拟完整的自动化工作流"""
    print("\n🎯 模拟完整自动化工作流")
    print("-"*40)
    
    try:
        # 模拟发送邮件
        print("📤 步骤1: 模拟发送邮件...")
        time.sleep(1)

        # 模拟自动启动监控
        print("📡 步骤2: 自动启动回复监控...")
        time.sleep(1)

        # 模拟检测回复
        print("📬 步骤3: 检测邮件回复...")
        time.sleep(1)

        # 模拟质量数据库导入
        print("📊 步骤4: 自动导入质量数据库...")
        time.sleep(1)

        # 模拟应急检查
        print("🆘 步骤5: 自动应急状态检查...")
        time.sleep(1)

        # 模拟深度协调
        print("🔧 步骤6: 深度系统协调...")
        time.sleep(1)

        print("✅ 完整自动化工作流模拟完成!")

    except Exception as e:
        print(f"❌ 工作流模拟失败: {str(e)}")

def create_real_automation_fix():
    """创建真实的自动化修复脚本"""
    print("\n🛠️ 创建自动化修复脚本...")

    fix_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2.0系统自动化问题实际修复脚本
解决自动化流程不工作的根本问题
"""

import os
import json
import datetime
import shutil

def main():
    """主修复函数"""
    print("🛠️ 开始修复2.0系统自动化问题...")
    print("="*60)

    # 1. 诊断问题
    issues = diagnose_issues()

    # 2. 修复配置文件
    fix_config_files()

    # 3. 修复GUI集成
    fix_gui_integration()

    # 4. 创建强制触发器
    create_force_trigger()

    # 5. 验证修复
    verify_fix()

    print("\\n✅ 修复完成!")
    print("💡 请重启2.0系统测试自动化功能")

def diagnose_issues():
    """诊断问题"""
    print("🔍 诊断自动化问题...")

    issues = []

    # 检查配置文件
    if not os.path.exists('all_features_config.json'):
        issues.append("全功能配置文件缺失")

    if not os.path.exists('automation_workflow.json'):
        issues.append("自动化工作流配置缺失")

    # 检查GUI文件
    if os.path.exists('gui_main.py'):
        with open('gui_main.py', 'r', encoding='utf-8') as f:
            content = f.read()

        if 'auto_reply_monitoring.get()' not in content:
            issues.append("GUI中缺少自动化触发检查")

        if 'auto_start_reply_monitoring' not in content:
            issues.append("GUI中缺少自动启动监控函数")

    print(f"   发现 {len(issues)} 个问题")
    for issue in issues:
        print(f"   ❌ {issue}")

    return issues

def fix_config_files():
    """修复配置文件"""
    print("📋 修复配置文件...")

    # 创建全功能配置
    all_features_config = {
        "enabled": True,
        "sender_email": "@qq.com",
        "enabled_time": datetime.datetime.now().isoformat(),
        "features": {
            "auto_reply_monitoring": True,
            "quality_database": True,
            "anti_spam": True,
            "qq_emergency": True,
            "smart_queue": True,
            "deep_coordination": True
        }
    }

    with open('all_features_config.json', 'w', encoding='utf-8') as f:
        json.dump(all_features_config, f, ensure_ascii=False, indent=2)

    print("   ✅ 全功能配置文件已创建")

    # 创建自动化工作流配置
    automation_workflow = {
        "enabled": True,
        "steps": [
            {
                "name": "auto_reply_monitoring",
                "enabled": True,
                "trigger": "after_send",
                "delay": 5,
                "description": "发送后自动启动回复监控"
            },
            {
                "name": "quality_db_import",
                "enabled": True,
                "trigger": "after_monitoring",
                "delay": 10,
                "description": "监控完成后自动导入质量数据库"
            },
            {
                "name": "emergency_check",
                "enabled": True,
                "trigger": "after_import",
                "delay": 5,
                "description": "导入完成后自动检查应急状态"
            }
        ]
    }

    with open('automation_workflow.json', 'w', encoding='utf-8') as f:
        json.dump(automation_workflow, f, ensure_ascii=False, indent=2)

    print("   ✅ 自动化工作流配置已创建")

    # 创建自动回复监控配置
    auto_reply_config = {
        'enabled': True,
        'check_interval': 5,
        'monitor_duration': 2,
        'auto_start': True,
        'sender_email': '@qq.com',
        'auto_import_to_quality': True,
        'auto_emergency_check': True,
        'auto_coordination': True
    }

    with open('auto_reply_config.json', 'w', encoding='utf-8') as f:
        json.dump(auto_reply_config, f, ensure_ascii=False, indent=2)

    print("   ✅ 自动回复监控配置已创建")

def fix_gui_integration():
    """修复GUI集成"""
    print("🖥️ 修复GUI集成...")

    # 创建GUI修复补丁
    patch_content = """
# GUI自动化修复补丁
# 将以下代码添加到gui_main.py中

def _ensure_automation_working(self):
    '''确保自动化正常工作'''
    try:
        # 1. 检查并强制启用自动回复监控
        if hasattr(self, 'auto_reply_monitoring'):
            current_value = self.auto_reply_monitoring.get()
            if not current_value:
                self.auto_reply_monitoring.set(True)
                self.log_message("🔧 已强制启用自动回复监控")

        # 2. 检查全功能配置
        if os.path.exists('all_features_config.json'):
            with open('all_features_config.json', 'r', encoding='utf-8') as f:
                config = json.load(f)

            if config.get('enabled', False):
                self.all_features_enabled = True
                self.feature_status = config.get('features', {})
                self.log_message("🚀 全功能模式状态已恢复")

        # 3. 添加发送后自动化检查
        self._add_post_send_automation_check()

        return True

    except Exception as e:
        self.log_message(f"❌ 确保自动化工作失败: {str(e)}")
        return False

def _add_post_send_automation_check(self):
    '''添加发送后自动化检查'''
    try:
        # 保存原始发送方法
        if not hasattr(self, '_original_send_emails_method'):
            self._original_send_emails_method = self.send_emails

        def enhanced_send_emails():
            '''增强的发送邮件方法'''
            # 调用原始方法
            result = self._original_send_emails_method()

            # 发送成功后强制检查自动化
            if result:
                self.root.after(2000, self._force_check_automation_trigger)

            return result

        # 替换发送方法
        self.send_emails = enhanced_send_emails

        self.log_message("🔧 发送后自动化检查已安装")

    except Exception as e:
        self.log_message(f"❌ 安装发送后自动化检查失败: {str(e)}")

def _force_check_automation_trigger(self):
    '''强制检查自动化触发'''
    try:
        sender_email = self.sender_email.get().strip()
        recipients_text = self.recipient_emails.get(1.0, tk.END).strip()

        if sender_email and recipients_text:
            recipients = self._parse_recipient_emails(recipients_text)

            if recipients:
                self.log_message(f"🔍 检查自动化触发: {len(recipients)} 个收件人")

                # 强制检查自动回复监控状态
                if hasattr(self, 'auto_reply_monitoring'):
                    is_enabled = self.auto_reply_monitoring.get()
                    self.log_message(f"📡 自动回复监控状态: {'启用' if is_enabled else '禁用'}")

                    if is_enabled:
                        # 强制启动自动监控
                        self.log_message("🚀 强制启动自动回复监控...")
                        try:
                            self.auto_start_reply_monitoring(sender_email, recipients)
                            self.log_message("✅ 自动回复监控已强制启动")
                        except Exception as e:
                            self.log_message(f"❌ 强制启动自动监控失败: {str(e)}")
                    else:
                        self.log_message("⚠️ 自动回复监控未启用，请检查全功能模式")

                # 触发深度协调事件
                if hasattr(self, 'coordinator') and self.coordinator:
                    try:
                        from 深度系统协调实现 import SystemEvent
                        self.coordinator.data_center.emit_event(SystemEvent.EMAIL_SENT, {
                            'sender_email': sender_email,
                            'recipient_count': len(recipients),
                            'recipients': recipients,
                            'send_time': datetime.datetime.now().isoformat()
                        })
                        self.log_message("🔧 深度协调事件已触发")
                    except Exception as e:
                        self.log_message(f"❌ 触发深度协调事件失败: {str(e)}")

    except Exception as e:
        self.log_message(f"❌ 强制检查自动化触发失败: {str(e)}")

# 在__init__方法末尾添加:
# self._ensure_automation_working()
"""

    with open('gui_automation_fix_patch.py', 'w', encoding='utf-8') as f:
        f.write(patch_content)

    print("   ✅ GUI修复补丁已创建: gui_automation_fix_patch.py")

def create_force_trigger():
    """创建强制触发器"""
    print("🔧 创建强制触发器...")

    trigger_content = """
# 强制自动化触发器
# 独立运行此脚本来强制触发自动化

import json
import datetime

def force_trigger_automation():
    '''强制触发自动化'''
    print("🚀 强制触发自动化系统...")

    try:
        # 1. 确保配置文件正确
        ensure_configs()

        # 2. 测试深度协调系统
        test_coordination_system()

        # 3. 模拟邮件发送事件
        simulate_email_sent_event()

        print("✅ 强制触发完成")

    except Exception as e:
        print(f"❌ 强制触发失败: {str(e)}")

def ensure_configs():
    '''确保配置正确'''
    # 强制设置全功能启用
    config = {
        "enabled": True,
        "sender_email": "@qq.com",
        "enabled_time": datetime.datetime.now().isoformat(),
        "features": {
            "auto_reply_monitoring": True,
            "quality_database": True,
            "anti_spam": True,
            "qq_emergency": True,
            "smart_queue": True,
            "deep_coordination": True
        }
    }

    with open('all_features_config.json', 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

    print("✅ 配置文件已强制更新")

def test_coordination_system():
    '''测试协调系统'''
    try:
        from 深度系统协调实现 import get_coordinator, SystemEvent
        coordinator = get_coordinator()
        print("✅ 深度协调系统连接成功")
        return coordinator
    except Exception as e:
        print(f"❌ 深度协调系统连接失败: {str(e)}")
        return None

def simulate_email_sent_event():
    '''模拟邮件发送事件'''
    try:
        coordinator = test_coordination_system()
        if coordinator:
            from 深度系统协调实现 import SystemEvent

            test_data = {
                'sender_email': '<EMAIL>',
                'recipient_count': 3,
                'recipients': ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
                'send_time': datetime.datetime.now().isoformat()
            }

            coordinator.data_center.emit_event(SystemEvent.EMAIL_SENT, test_data)
            print("✅ 邮件发送事件已模拟触发")

            # 等待一下再触发回复事件
            import time
            time.sleep(2)

            coordinator.data_center.emit_event(SystemEvent.REPLY_RECEIVED, {
                'sender_email': '<EMAIL>',
                'recipient_email': '<EMAIL>',
                'reply_time': datetime.datetime.now().isoformat()
            })
            print("✅ 回复接收事件已模拟触发")

    except Exception as e:
        print(f"❌ 模拟事件触发失败: {str(e)}")

if __name__ == "__main__":
    force_trigger_automation()
"""

    with open('force_automation_trigger.py', 'w', encoding='utf-8') as f:
        f.write(trigger_content)

    print("   ✅ 强制触发器已创建: force_automation_trigger.py")

def verify_fix():
    """验证修复"""
    print("🧪 验证修复...")

    # 检查配置文件
    config_files = ['all_features_config.json', 'automation_workflow.json', 'auto_reply_config.json']
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"   ✅ {config_file} 存在")
        else:
            print(f"   ❌ {config_file} 缺失")

    # 测试组件导入
    try:
        from 深度系统协调实现 import get_coordinator
        coordinator = get_coordinator()
        print("   ✅ 深度协调系统可导入")
    except Exception as e:
        print(f"   ❌ 深度协调系统导入失败: {str(e)}")

    try:
        from recipient_quality_manager import RecipientQualityManager
        print("   ✅ 质量管理器可导入")
    except Exception as e:
        print(f"   ❌ 质量管理器导入失败: {str(e)}")

    try:
        from qq_email_anti_spam import QQEmailAntiSpamManager
        print("   ✅ QQ应急管理器可导入")
    except Exception as e:
        print(f"   ❌ QQ应急管理器导入失败: {str(e)}")

if __name__ == "__main__":
    main()
'''

    with open('automation_real_fix.py', 'w', encoding='utf-8') as f:
        f.write(fix_script)

    print("✅ 真实修复脚本已创建: automation_real_fix.py")

if __name__ == "__main__":
    print("🎯 2.0系统全功能自动化问题诊断和修复工具")
    print("="*70)

    # 运行完整诊断
    run_full_diagnosis()

    # 创建修复脚本
    create_real_automation_fix()

    print("\n🎊 诊断和修复脚本生成完成!")
    print("💡 请按以下步骤操作:")
    print("   1. 运行修复脚本: python automation_real_fix.py")
    print("   2. 重启2.0系统")
    print("   3. 测试自动化功能")
    print("   4. 如果仍有问题，运行强制触发器: python force_automation_trigger.py")
