# 批次发送功能使用说明

## 🎯 功能概述

为了解决大量邮件发送时容易进入垃圾箱的问题，我们新增了**智能批次发送功能**。该功能通过以下方式提高邮件送达率：

- ✅ **批次分组发送** - 将大量邮件分成小批次发送
- ✅ **智能间隔控制** - 批次间设置较长休息时间
- ✅ **增强邮件头** - 添加反垃圾邮件标识
- ✅ **发送者信誉保护** - 控制每日发送量

## 📊 发送模式对比

| 模式 | 批次大小 | 批次间隔 | 邮件间隔 | 每日限制 | 适用场景 |
|------|----------|----------|----------|----------|----------|
| **快速模式** | 20封/批次 | 5-8分钟 | 30-60秒 | 200封/天 | 少量邮件，时间紧急 |
| **标准模式** | 30封/批次 | 8-12分钟 | 60-90秒 | 300封/天 | 中等数量，平衡效率 |
| **安全模式** | 50封/批次 | 12-18分钟 | 90-120秒 | 500封/天 | 大量邮件，最高送达率 |

## 🚀 使用方法

### 1. 通过主程序界面

1. 在主程序中选择发送模式（快速/标准/安全）
2. 添加收件人和邮件内容
3. 点击"发送邮件"或"添加到队列"
4. 系统会自动使用批次发送功能

### 2. 通过队列系统

1. 打开"队列管理"窗口
2. 创建发送任务时选择合适的发送模式
3. 点击"开始队列发送"
4. 系统会按批次依次处理所有任务

### 3. 程序化调用

```python
from email_sender import EmailSender

# 创建发送器
sender = EmailSender("<EMAIL>")

# 准备邮件列表
email_list = [
    {
        'to_emails': ['<EMAIL>'],
        'subject': '邮件主题1',
        'body': '邮件内容1',
        'attachments': None
    },
    # ... 更多邮件
]

# 批次发送
results = sender.send_batch_emails(
    email_list=email_list,
    send_mode='safe',  # 使用安全模式
    progress_callback=lambda info: print(f"进度: {info}"),
    stop_callback=lambda: False  # 不停止
)

print(f"发送结果: {results}")
```

## 📈 发送建议

### 新账号预热策略

如果您是第一次使用该邮箱大量发送邮件，建议按以下计划预热：

```
第1天：发送 20封邮件（安全模式）
第2天：发送 30封邮件（安全模式）
第3天：发送 50封邮件（安全模式）
第4天：发送 80封邮件（安全模式）
第5天：发送 120封邮件（标准模式）
第6天：发送 150封邮件（标准模式）
第7天：发送 200封邮件（标准模式）
```

### 最佳发送时间

- ⏰ **工作日 9:00-18:00** - 最佳发送时间
- 🌙 **避免深夜发送** - 容易被标记为垃圾邮件
- 📅 **避免周末大量发送** - 收件人查看率低

### 邮件内容优化

1. **主题行优化**
   - 简洁明了，避免过多感叹号
   - 不使用"免费"、"促销"等敏感词汇
   - 个性化主题，避免完全相同

2. **正文内容**
   - 提供有价值的信息
   - 避免纯广告内容
   - 包含明确的发件人信息

3. **附件处理**
   - 控制在25MB以内
   - 使用常见格式
   - 避免可执行文件

## 🛡️ 反垃圾邮件措施

### 增强的邮件头

系统自动添加以下邮件头以提高送达率：

- `X-Mailer`: 模拟常见邮件客户端
- `X-Spam-Status`: 标记为非垃圾邮件
- `Authentication-Results`: 认证通过标识
- `Message-ID`: 唯一邮件标识符

### 发送频率控制

- **邮件间隔**: 30-120秒随机间隔
- **批次间隔**: 5-18分钟随机间隔
- **每日限制**: 根据模式限制发送量

## 📊 监控和统计

### 发送进度监控

系统提供实时进度监控：
- 当前批次进度
- 总体发送进度
- 成功/失败统计
- 预计剩余时间

### 日志记录

详细的日志记录包括：
- 每封邮件的发送状态
- 批次间隔等待时间
- 错误信息和建议
- 发送统计汇总

## ⚠️ 注意事项

1. **首次使用**
   - 建议先发送少量邮件测试
   - 观察邮件是否进入垃圾箱
   - 根据结果调整发送策略

2. **大量发送**
   - 优先使用"安全模式"
   - 分多天发送大量邮件
   - 监控发送效果

3. **账号安全**
   - 不要频繁更换发送模式
   - 避免短时间内大量发送
   - 定期检查账号状态

## 🔧 故障排除

### 常见问题

**Q: 邮件仍然进入垃圾箱怎么办？**
A: 
1. 尝试使用更安全的发送模式
2. 减少每日发送量
3. 优化邮件内容和主题
4. 考虑账号预热

**Q: 发送速度太慢怎么办？**
A:
1. 可以使用"快速模式"
2. 但要注意监控送达率
3. 如果进入垃圾箱增多，立即切换回安全模式

**Q: 如何查看发送统计？**
A:
1. 查看程序日志文件
2. 使用队列系统的统计功能
3. 观察邮件发送历史记录

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看日志文件获取详细错误信息
2. 参考本说明文档的故障排除部分
3. 检查网络连接和邮箱设置
4. 尝试使用测试脚本验证功能

---

**提示**: 批次发送功能已集成到现有程序中，无需额外配置即可使用。系统会根据您选择的发送模式自动优化发送策略。
