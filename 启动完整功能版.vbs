' 邮件系统v3.0完整功能版启动脚本
' 直接启动完整功能版本

Option Explicit

Dim objShell, objFSO, currentDir, pythonPath
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取当前目录
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 显示启动信息
MsgBox "🚀 邮件系统v3.0完整功能版" & vbCrLf & vbCrLf & _
       "✨ 包含所有2.0系统功能：" & vbCrLf & _
       "• 📤 完整发送功能" & vbCrLf & _
       "• 🔄 邮件撤回功能" & vbCrLf & _
       "• 📊 质量数据库管理" & vbCrLf & _
       "• 📡 自动回复监控" & vbCrLf & _
       "• 🛡️ 安全防护系统" & vbCrLf & _
       "• 🧠 深度协调功能" & vbCrLf & _
       "• 🔑 授权码管理" & vbCrLf & vbCrLf & _
       "正在启动系统...", _
       vbInformation, "邮件系统v3.0"

' 检查Python环境并启动
If CheckPythonEnvironment() Then
    LaunchCompleteVersion()
Else
    MsgBox "❌ 未找到Python环境！" & vbCrLf & vbCrLf & _
           "请确保已安装Python并添加到系统PATH中。" & vbCrLf & vbCrLf & _
           "安装建议：" & vbCrLf & _
           "1. 下载Python 3.8+版本" & vbCrLf & _
           "2. 安装时勾选'Add to PATH'" & vbCrLf & _
           "3. 重启计算机后再试", _
           vbCritical, "Python环境检查"
End If

' 检查Python环境
Function CheckPythonEnvironment()
    Dim result
    On Error Resume Next
    
    ' 尝试运行python --version
    result = objShell.Run("python --version", 0, True)
    
    If Err.Number = 0 And result = 0 Then
        CheckPythonEnvironment = True
        pythonPath = "python"
    Else
        ' 尝试python3
        Err.Clear
        result = objShell.Run("python3 --version", 0, True)
        If Err.Number = 0 And result = 0 Then
            CheckPythonEnvironment = True
            pythonPath = "python3"
        Else
            CheckPythonEnvironment = False
        End If
    End If
    
    On Error GoTo 0
End Function

' 启动完整功能版
Sub LaunchCompleteVersion()
    Dim scriptName, fullPath, command, result
    
    scriptName = "gui_complete_v3.py"
    fullPath = currentDir & "\" & scriptName
    
    ' 检查文件是否存在
    If Not objFSO.FileExists(fullPath) Then
        MsgBox "❌ 文件不存在：" & scriptName & vbCrLf & vbCrLf & _
               "请确保以下文件存在于当前目录：" & vbCrLf & _
               "• gui_complete_v3.py" & vbCrLf & vbCrLf & _
               "当前目录：" & currentDir, _
               vbCritical, "文件检查"
        Exit Sub
    End If
    
    ' 构建启动命令
    command = """" & pythonPath & """ """ & fullPath & """"
    
    ' 启动Python脚本
    On Error Resume Next
    result = objShell.Run(command, 1, False)
    
    If Err.Number <> 0 Then
        MsgBox "❌ 启动失败！" & vbCrLf & vbCrLf & _
               "错误信息：" & Err.Description & vbCrLf & _
               "命令：" & command & vbCrLf & vbCrLf & _
               "解决方案：" & vbCrLf & _
               "1. 检查Python环境" & vbCrLf & _
               "2. 检查文件路径" & vbCrLf & _
               "3. 检查文件权限", _
               vbCritical, "启动错误"
    Else
        ' 启动成功，显示使用提示
        MsgBox "✅ 邮件系统启动成功！" & vbCrLf & vbCrLf & _
               "🎯 使用提示：" & vbCrLf & _
               "• 如果窗口没有显示，请检查任务栏" & vbCrLf & _
               "• 首次使用请配置邮箱和授权码" & vbCrLf & _
               "• 发送邮件后可使用撤回功能" & vbCrLf & _
               "• 查看日志了解系统运行状态" & vbCrLf & vbCrLf & _
               "📚 如需帮助，请查看说明文档", _
               vbInformation, "启动成功"
    End If
    
    On Error GoTo 0
End Sub
