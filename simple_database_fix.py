#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单有效的数据库锁定解决方案
"""

import sqlite3
import threading
import time
import os
import logging
from contextlib import contextmanager
from typing import Dict, Any
import platform

# Windows兼容的文件锁
if platform.system() == "Windows":
    import msvcrt
    def lock_file(file_handle):
        msvcrt.locking(file_handle.fileno(), msvcrt.LK_LOCK, 1)
    def unlock_file(file_handle):
        msvcrt.locking(file_handle.fileno(), msvcrt.LK_UNLCK, 1)
else:
    import fcntl
    def lock_file(file_handle):
        fcntl.flock(file_handle.fileno(), fcntl.LOCK_EX)
    def unlock_file(file_handle):
        fcntl.flock(file_handle.fileno(), fcntl.LOCK_UN)

class SimpleDatabaseManager:
    """简单有效的数据库管理器 - 使用文件锁解决锁定问题"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.lock_path = db_path + ".lock"
        self.logger = logging.getLogger(__name__)
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path) if os.path.dirname(db_path) else '.', exist_ok=True)
    
    @contextmanager
    def get_db_connection(self):
        """获取数据库连接（使用文件锁）"""
        lock_file = None
        conn = None
        
        try:
            # 创建锁文件
            lock_file = open(self.lock_path, 'w')
            
            # 获取文件锁（阻塞直到获得锁）
            lock_file(lock_file)
            
            # 创建数据库连接
            conn = sqlite3.connect(
                self.db_path,
                timeout=5.0,  # 较短的超时时间
                check_same_thread=False
            )
            
            # 基本优化设置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA busy_timeout=5000")
            
            yield conn
            
        except Exception as e:
            self.logger.error(f"数据库连接错误: {str(e)}")
            raise
        finally:
            # 关闭连接
            if conn:
                try:
                    conn.close()
                except:
                    pass
            
            # 释放文件锁
            if lock_file:
                try:
                    unlock_file(lock_file)
                    lock_file.close()
                except:
                    pass
    
    def execute_with_retry(self, operation_func, *args, max_retries=3, **kwargs):
        """执行数据库操作（带重试）"""
        for attempt in range(max_retries):
            try:
                with self.get_db_connection() as conn:
                    return operation_func(conn, *args, **kwargs)
            except Exception as e:
                if attempt < max_retries - 1:
                    delay = 0.1 * (2 ** attempt)  # 指数退避
                    self.logger.warning(f"数据库操作失败，第 {attempt + 1} 次重试（延迟 {delay:.2f}s）: {str(e)}")
                    time.sleep(delay)
                else:
                    self.logger.error(f"数据库操作最终失败: {str(e)}")
                    raise

# 全局简单数据库管理器
_simple_db_managers = {}
_simple_db_lock = threading.Lock()

def get_simple_db_manager(db_path: str) -> SimpleDatabaseManager:
    """获取简单数据库管理器实例"""
    with _simple_db_lock:
        if db_path not in _simple_db_managers:
            _simple_db_managers[db_path] = SimpleDatabaseManager(db_path)
        return _simple_db_managers[db_path]

# 数据库操作函数
def simple_update_recipient_status(conn: sqlite3.Connection, recipient_email: str, sender_email: str, status: str):
    """简单的更新收件人状态操作"""
    cursor = conn.cursor()
    
    # 确保表存在
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS recipient_status (
            recipient_email TEXT,
            sender_email TEXT,
            last_reply_time TEXT,
            reply_count INTEGER DEFAULT 0,
            bounce_count INTEGER DEFAULT 0,
            status TEXT,
            updated_at TEXT,
            PRIMARY KEY (recipient_email, sender_email)
        )
    ''')
    
    # 获取当前状态
    cursor.execute('''
        SELECT reply_count, bounce_count FROM recipient_status 
        WHERE recipient_email = ? AND sender_email = ?
    ''', (recipient_email, sender_email))
    
    result = cursor.fetchone()
    if result:
        reply_count, bounce_count = result
    else:
        reply_count, bounce_count = 0, 0
    
    # 更新计数
    if status == 'auto_reply':
        reply_count += 1
    elif status == 'bounce':
        bounce_count += 1
    
    # 插入或更新记录
    cursor.execute('''
        INSERT OR REPLACE INTO recipient_status 
        (recipient_email, sender_email, last_reply_time, reply_count, bounce_count, status, updated_at)
        VALUES (?, ?, datetime('now'), ?, ?, ?, datetime('now'))
        ''', (recipient_email, sender_email, reply_count, bounce_count, status))
    
    conn.commit()
    return True

def simple_save_auto_reply(conn: sqlite3.Connection, reply_info: Dict[str, Any]):
    """简单的保存自动回复操作"""
    cursor = conn.cursor()
    
    # 确保表存在
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS auto_replies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            recipient_email TEXT,
            sender_email TEXT,
            reply_type TEXT,
            reply_time TEXT,
            subject TEXT,
            body TEXT,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        INSERT INTO auto_replies 
        (recipient_email, sender_email, reply_type, reply_time, subject, body)
        VALUES (?, ?, ?, ?, ?, ?)
    ''', (
        reply_info['recipient_email'],
        reply_info['sender_email'],
        reply_info['reply_type'],
        reply_info['reply_time'],
        reply_info['subject'],
        reply_info['body']
    ))
    
    conn.commit()
    return True

# 为EmailReceiver提供简单的更新方法
def update_recipient_status_simple(db_path: str, recipient_email: str, sender_email: str, reply_type: str):
    """简单的收件人状态更新方法"""
    try:
        db_manager = get_simple_db_manager(db_path)
        db_manager.execute_with_retry(simple_update_recipient_status, recipient_email, sender_email, reply_type)
        return True
    except Exception as e:
        logging.getLogger(__name__).error(f"更新收件人状态失败: {str(e)}")
        return False

def save_auto_reply_simple(db_path: str, reply_info: Dict[str, Any]):
    """简单的自动回复保存方法"""
    try:
        db_manager = get_simple_db_manager(db_path)
        db_manager.execute_with_retry(simple_save_auto_reply, reply_info)
        return True
    except Exception as e:
        logging.getLogger(__name__).error(f"保存自动回复失败: {str(e)}")
        return False
