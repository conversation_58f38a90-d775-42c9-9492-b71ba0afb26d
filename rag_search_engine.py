# -*- coding: utf-8 -*-
"""
RAG检索增强生成搜索引擎
为邮件历史记录提供智能搜索功能
"""

import re
import jieba
import sqlite3
import datetime
import logging
from typing import List, Dict, Tuple
from collections import Counter
import math

class RAGSearchEngine:
    """RAG检索搜索引擎"""
    
    def __init__(self, db_path: str = "email_history.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"初始化RAG搜索引擎，数据库路径: {db_path}")

        # 初始化中文分词
        try:
            jieba.initialize()
            self.logger.info("中文分词初始化成功")
        except Exception as e:
            self.logger.error(f"中文分词初始化失败: {str(e)}", exc_info=True)
        
        # 停用词列表
        self.stop_words = {
            '的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这', '那', '他', '她', '它', '们', '我们', '你们', '他们', '她们', '它们',
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
        }
    
    def preprocess_text(self, text: str) -> List[str]:
        """文本预处理和分词"""
        if not text:
            return []
        
        # 清理文本
        text = re.sub(r'[^\w\s\u4e00-\u9fff]', ' ', text)
        text = re.sub(r'\s+', ' ', text.strip().lower())
        
        # 中英文混合分词
        words = []
        
        # 中文分词
        chinese_words = jieba.lcut(text)
        for word in chinese_words:
            if len(word.strip()) > 1 and word not in self.stop_words:
                words.append(word.strip())
        
        # 英文单词提取
        english_words = re.findall(r'[a-zA-Z]+', text)
        for word in english_words:
            if len(word) > 2 and word.lower() not in self.stop_words:
                words.append(word.lower())
        
        return list(set(words))  # 去重
    
    def calculate_tf_idf(self, query_terms: List[str], documents: List[Dict]) -> List[Tuple[Dict, float]]:
        """计算TF-IDF相似度分数"""
        if not query_terms or not documents:
            return []
        
        # 计算文档词频
        doc_term_freq = []
        all_terms = set()
        
        for doc in documents:
            # 合并标题和正文进行分析
            content = f"{doc['subject']} {doc['body']}"
            terms = self.preprocess_text(content)
            term_freq = Counter(terms)
            doc_term_freq.append(term_freq)
            all_terms.update(terms)
        
        # 计算逆文档频率（修复除零问题）
        doc_count = len(documents)
        idf = {}
        for term in all_terms:
            containing_docs = sum(1 for tf in doc_term_freq if term in tf)
            # 使用平滑的IDF计算，避免log(1)=0的问题
            if containing_docs == doc_count:
                # 如果所有文档都包含该词，给予较小但非零的权重
                idf[term] = 0.1
            else:
                idf[term] = math.log(doc_count / (containing_docs + 1)) + 1
        
        # 计算每个文档的TF-IDF分数
        scored_docs = []
        for i, doc in enumerate(documents):
            score = 0
            doc_tf = doc_term_freq[i]
            doc_length = sum(doc_tf.values())

            # 计算匹配的查询词数量
            matched_terms = 0
            total_query_terms = len(query_terms)

            for term in query_terms:
                if term in doc_tf:
                    tf = doc_tf[term] / doc_length if doc_length > 0 else 0
                    score += tf * idf.get(term, 0)
                    matched_terms += 1

            # 如果匹配了大部分查询词，给予额外的相似度奖励
            if matched_terms > 0:
                match_ratio = matched_terms / total_query_terms
                # 基础分数 + 匹配比例奖励
                final_score = score + match_ratio * 0.5
                scored_docs.append((doc, final_score))
        
        # 按分数排序
        scored_docs.sort(key=lambda x: x[1], reverse=True)
        return scored_docs
    
    def semantic_search(self, query: str, sender_email: str = None, limit: int = 20) -> List[Dict]:
        """语义搜索邮件历史"""
        try:
            self.logger.debug(f"开始语义搜索 - 查询: {query[:50]}..., 发件人: {sender_email}, 限制: {limit}")

            # 预处理查询
            query_terms = self.preprocess_text(query)
            if not query_terms:
                self.logger.warning("查询预处理后为空，返回空结果")
                return []

            self.logger.debug(f"查询词汇: {query_terms}")

        except Exception as e:
            self.logger.error(f"语义搜索预处理失败: {str(e)}", exc_info=True)
            return []
        
        # 从数据库获取候选文档
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 构建SQL查询
        where_conditions = []
        params = []
        
        if sender_email:
            where_conditions.append("sender_email = ?")
            params.append(sender_email)
        
        # 使用LIKE进行初步筛选（如果有查询词汇的话）
        like_conditions = []
        for term in query_terms[:5]:  # 限制搜索词数量
            like_conditions.append("(subject LIKE ? OR body LIKE ?)")
            params.extend([f"%{term}%", f"%{term}%"])

        # 如果有查询词汇，添加LIKE条件；否则获取所有记录进行TF-IDF计算
        if like_conditions:
            where_conditions.append(f"({' OR '.join(like_conditions)})")

        where_clause = " AND ".join(where_conditions) if where_conditions else "1=1"

        # 如果没有LIKE条件但有发件人限制，确保能获取到该发件人的所有邮件
        if not like_conditions and sender_email:
            self.logger.debug("没有LIKE条件，将获取该发件人的所有邮件进行相似度计算")
        
        cursor.execute(f'''
            SELECT id, sender_email, recipient_email, subject, body,
                   send_time, success, batch_id, content_hash
            FROM email_records
            WHERE {where_clause}
            ORDER BY send_time DESC
            LIMIT ?
        ''', params + [limit * 3])  # 获取更多候选文档

        records = cursor.fetchall()

        # 如果LIKE查询没有结果，但有发件人限制，尝试获取该发件人的所有邮件
        if not records and sender_email and like_conditions:
            self.logger.debug("LIKE查询无结果，尝试获取该发件人的所有邮件")
            cursor.execute('''
                SELECT id, sender_email, recipient_email, subject, body,
                       send_time, success, batch_id, content_hash
                FROM email_records
                WHERE sender_email = ?
                ORDER BY send_time DESC
                LIMIT ?
            ''', [sender_email, limit * 3])
            records = cursor.fetchall()

        conn.close()

        if not records:
            self.logger.debug("没有找到任何邮件记录")
            return []
        
        # 转换为文档格式
        documents = []
        for record in records:
            # 确保时间字段不为None
            send_time = record[5] if record[5] else datetime.datetime.now().isoformat()

            documents.append({
                'id': record[0],
                'sender_email': record[1],
                'recipient_email': record[2],
                'subject': record[3],
                'body': record[4],
                'send_time': send_time,
                'success': bool(record[6]),
                'batch_id': record[7],
                'content_hash': record[8]
            })
        
        # 计算相似度并排序
        scored_docs = self.calculate_tf_idf(query_terms, documents)
        
        # 返回前N个结果
        results = []
        for doc, score in scored_docs[:limit]:
            doc['relevance_score'] = round(score, 4)
            # 截断正文显示
            if len(doc['body']) > 200:
                doc['body_preview'] = doc['body'][:200] + "..."
            else:
                doc['body_preview'] = doc['body']
            results.append(doc)
        
        return results
    
    def find_similar_emails(self, subject: str, body: str, sender_email: str = None, limit: int = 10) -> List[Dict]:
        """查找相似邮件"""
        # 合并标题和正文作为查询
        query = f"{subject} {body}"
        return self.semantic_search(query, sender_email, limit)
    
    def suggest_recipients(self, subject: str, body: str, sender_email: str = None, limit: int = 10) -> Dict:
        """智能收件人建议 - 防重复发送逻辑"""
        try:
            self.logger.debug(f"开始智能收件人建议 - 主题: {subject[:30]}..., 发件人: {sender_email}")

            # 1. 查找相似邮件，识别已发送过的收件人
            similar_emails = self.find_similar_emails(subject, body, sender_email, 100)
            self.logger.debug(f"找到 {len(similar_emails)} 封相似邮件")

            # 2. 统计已发送过相似内容的收件人（使用更严格的相似度阈值）
            sent_recipients = set()
            duplicate_details = []

            for email in similar_emails:
                recipient = email['recipient_email']
                relevance = email['relevance_score']

                # 使用更严格的相似度阈值（>0.6），避免误判
                if relevance > 0.6:
                    sent_recipients.add(recipient)
                    duplicate_details.append({
                        'recipient': recipient,
                        'last_sent_time': email['send_time'],
                        'similarity': relevance,
                        'previous_subject': email['subject'],
                        'previous_content': email['body_preview']
                    })
                    self.logger.debug(f"发现重复收件人: {recipient}, 相似度: {relevance:.3f}")

            # 3. 获取该发件人的所有历史收件人
            all_recipients = self._get_all_recipients_for_sender(sender_email)
            self.logger.debug(f"获取到 {len(all_recipients)} 个历史收件人")

            # 4. 推荐未发送过相似内容的收件人
            safe_recipients = []
            for recipient in all_recipients:
                if recipient not in sent_recipients:
                    safe_recipients.append(recipient)

            self.logger.info(f"智能建议完成 - 重复收件人: {len(sent_recipients)}, 安全收件人: {len(safe_recipients)}")

            # 5. 如果没有安全收件人，尝试降低相似度阈值重新分析
            if not safe_recipients and sent_recipients:
                self.logger.warning("没有安全收件人，尝试使用更严格的相似度阈值重新分析")
                sent_recipients_strict = set()
                duplicate_details_strict = []

                for email in similar_emails:
                    recipient = email['recipient_email']
                    relevance = email['relevance_score']

                    # 使用更严格的阈值（>0.8）
                    if relevance > 0.8:
                        sent_recipients_strict.add(recipient)
                        duplicate_details_strict.append({
                            'recipient': recipient,
                            'last_sent_time': email['send_time'],
                            'similarity': relevance,
                            'previous_subject': email['subject'],
                            'previous_content': email['body_preview']
                        })

                # 重新计算安全收件人
                safe_recipients = []
                for recipient in all_recipients:
                    if recipient not in sent_recipients_strict:
                        safe_recipients.append(recipient)

                if safe_recipients:
                    self.logger.info(f"使用严格阈值后 - 重复收件人: {len(sent_recipients_strict)}, 安全收件人: {len(safe_recipients)}")
                    sent_recipients = sent_recipients_strict
                    duplicate_details = duplicate_details_strict

            return {
                'safe_recipients': safe_recipients[:limit],  # 安全的收件人（未发送过相似内容）
                'duplicate_recipients': list(sent_recipients),  # 重复的收件人
                'duplicate_details': duplicate_details,  # 重复详情
                'recommendation': self._generate_recipient_recommendation(len(safe_recipients), len(sent_recipients))
            }

        except Exception as e:
            self.logger.error(f"智能收件人建议失败: {str(e)}", exc_info=True)
            return {
                'safe_recipients': [],
                'duplicate_recipients': [],
                'duplicate_details': [],
                'recommendation': "智能建议功能暂时不可用"
            }

    def _get_all_recipients_for_sender(self, sender_email: str) -> List[str]:
        """获取发件人的所有历史收件人"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                SELECT DISTINCT recipient_email
                FROM email_records
                WHERE sender_email = ? AND success = 1
                ORDER BY recipient_email
            ''', (sender_email,))

            recipients = [row[0] for row in cursor.fetchall()]
            conn.close()

            self.logger.debug(f"获取到 {len(recipients)} 个历史收件人")
            return recipients

        except Exception as e:
            self.logger.error(f"获取历史收件人失败: {str(e)}", exc_info=True)
            return []

    def _generate_recipient_recommendation(self, safe_count: int, duplicate_count: int) -> str:
        """生成收件人推荐建议"""
        if duplicate_count == 0:
            return f"✅ 未发现重复发送风险，可以正常发送"
        elif safe_count > 0:
            return f"⚠️ 发现 {duplicate_count} 个收件人可能重复发送，建议使用 {safe_count} 个安全收件人"
        else:
            return f"🚫 所有收件人都可能重复发送，建议重新选择收件人或修改邮件内容"
    
    def get_content_clusters(self, sender_email: str = None, min_cluster_size: int = 2) -> List[Dict]:
        """获取内容聚类（相似邮件分组）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        where_clause = "WHERE sender_email = ?" if sender_email else ""
        params = [sender_email] if sender_email else []
        
        cursor.execute(f'''
            SELECT content_hash, subject, COUNT(*) as count,
                   GROUP_CONCAT(recipient_email) as recipients,
                   MIN(send_time) as first_sent,
                   MAX(send_time) as last_sent
            FROM email_records 
            {where_clause}
            GROUP BY content_hash
            HAVING count >= ?
            ORDER BY count DESC, last_sent DESC
        ''', params + [min_cluster_size])
        
        clusters = cursor.fetchall()
        conn.close()
        
        result = []
        for cluster in clusters:
            recipients = cluster[3].split(',') if cluster[3] else []
            result.append({
                'content_hash': cluster[0],
                'subject': cluster[1],
                'email_count': cluster[2],
                'unique_recipients': len(set(recipients)),
                'recipients': list(set(recipients)),
                'first_sent': cluster[4],
                'last_sent': cluster[5]
            })
        
        return result
    
    def advanced_duplicate_detection(self, subject: str, body: str, recipients: List[str],
                                   sender_email: str, similarity_threshold: float = 0.6) -> Dict:
        """高级重复检测（基于内容相似度）"""
        try:
            self.logger.debug(f"开始高级重复检测 - 收件人数量: {len(recipients)}, 相似度阈值: {similarity_threshold}")

            # 查找相似邮件
            similar_emails = self.find_similar_emails(subject, body, sender_email, 100)
            self.logger.debug(f"找到 {len(similar_emails)} 封相似邮件用于重复检测")

            # 分析结果
            exact_matches = []
            similar_matches = []
            safe_recipients = []

            for recipient in recipients:
                recipient = recipient.strip()
                if not recipient:
                    continue

                found_exact = False
                found_similar = False
                best_match = None
                best_similarity = 0

                # 查找该收件人的最佳匹配
                for similar_email in similar_emails:
                    if similar_email['recipient_email'] == recipient:
                        similarity = similar_email['relevance_score']
                        if similarity > best_similarity:
                            best_similarity = similarity
                            best_match = similar_email

                # 根据最佳匹配的相似度进行分类
                if best_match:
                    if best_similarity >= 0.9:  # 几乎完全相同
                        exact_matches.append({
                            'recipient': recipient,
                            'previous_email': best_match,
                            'match_type': 'exact',
                            'similarity': best_similarity
                        })
                        found_exact = True
                        self.logger.debug(f"发现完全重复: {recipient}, 相似度: {best_similarity:.3f}")
                    elif best_similarity >= similarity_threshold:  # 相似内容
                        similar_matches.append({
                            'recipient': recipient,
                            'previous_email': best_match,
                            'match_type': 'similar',
                            'similarity': best_similarity
                        })
                        found_similar = True
                        self.logger.debug(f"发现相似重复: {recipient}, 相似度: {best_similarity:.3f}")

                if not found_exact and not found_similar:
                    safe_recipients.append(recipient)
                    self.logger.debug(f"安全收件人: {recipient}")

            self.logger.info(f"重复检测完成 - 完全重复: {len(exact_matches)}, 相似重复: {len(similar_matches)}, 安全: {len(safe_recipients)}")

            return {
                'total_recipients': len(recipients),
                'safe_recipients': safe_recipients,
                'exact_matches': exact_matches,
                'similar_matches': similar_matches,
                'has_duplicates': len(exact_matches) > 0,
                'has_similar': len(similar_matches) > 0,
                'recommendation': self._generate_recommendation(exact_matches, similar_matches, safe_recipients)
            }

        except Exception as e:
            self.logger.error(f"高级重复检测失败: {str(e)}", exc_info=True)
            return {
                'total_recipients': len(recipients),
                'safe_recipients': recipients,  # 出错时认为所有收件人都安全
                'exact_matches': [],
                'similar_matches': [],
                'has_duplicates': False,
                'has_similar': False,
                'recommendation': "重复检测功能暂时不可用，建议手动检查"
            }
    
    def _generate_recommendation(self, exact_matches: List, similar_matches: List, safe_recipients: List) -> str:
        """生成发送建议"""
        if not exact_matches and not similar_matches:
            return "✅ 所有收件人都是安全的，可以正常发送"
        
        recommendations = []
        
        if exact_matches:
            recommendations.append(f"⚠️ 发现 {len(exact_matches)} 个收件人已收到完全相同的邮件")
        
        if similar_matches:
            recommendations.append(f"🔍 发现 {len(similar_matches)} 个收件人已收到相似内容的邮件")
        
        if safe_recipients:
            recommendations.append(f"✅ {len(safe_recipients)} 个收件人可以安全发送")
        
        return " | ".join(recommendations)

    def debug_similarity_analysis(self, subject: str, body: str, sender_email: str = None) -> Dict:
        """调试相似度分析 - 帮助诊断重复检测问题"""
        try:
            self.logger.info(f"开始调试相似度分析 - 主题: {subject[:30]}..., 发件人: {sender_email}")

            # 查找相似邮件
            similar_emails = self.find_similar_emails(subject, body, sender_email, 50)

            # 获取所有历史收件人
            all_recipients = self._get_all_recipients_for_sender(sender_email)

            # 分析每个相似邮件的详细信息
            analysis_details = []
            for email in similar_emails:
                analysis_details.append({
                    'recipient': email['recipient_email'],
                    'similarity': email['relevance_score'],
                    'send_time': email['send_time'],
                    'subject': email['subject'],
                    'body_preview': email['body_preview'],
                    'is_duplicate_0_6': email['relevance_score'] > 0.6,
                    'is_duplicate_0_7': email['relevance_score'] > 0.7,
                    'is_duplicate_0_8': email['relevance_score'] > 0.8,
                })

            # 统计不同阈值下的重复情况
            duplicates_0_6 = set(email['recipient_email'] for email in similar_emails if email['relevance_score'] > 0.6)
            duplicates_0_7 = set(email['recipient_email'] for email in similar_emails if email['relevance_score'] > 0.7)
            duplicates_0_8 = set(email['recipient_email'] for email in similar_emails if email['relevance_score'] > 0.8)

            return {
                'total_similar_emails': len(similar_emails),
                'total_historical_recipients': len(all_recipients),
                'historical_recipients': all_recipients,
                'analysis_details': analysis_details,
                'duplicates_threshold_0_6': list(duplicates_0_6),
                'duplicates_threshold_0_7': list(duplicates_0_7),
                'duplicates_threshold_0_8': list(duplicates_0_8),
                'safe_recipients_0_6': [r for r in all_recipients if r not in duplicates_0_6],
                'safe_recipients_0_7': [r for r in all_recipients if r not in duplicates_0_7],
                'safe_recipients_0_8': [r for r in all_recipients if r not in duplicates_0_8],
                'query_terms': self.preprocess_text(f"{subject} {body}"),
                'current_content': {
                    'subject': subject,
                    'body': body[:200] + "..." if len(body) > 200 else body
                }
            }

        except Exception as e:
            self.logger.error(f"调试相似度分析失败: {str(e)}", exc_info=True)
            return {
                'error': str(e),
                'total_similar_emails': 0,
                'total_historical_recipients': 0,
                'analysis_details': []
            }
