#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试 RecipientQualityManager 错误
重现和修复 "got an unexpected keyword argument 'max_quality_score'" 错误
"""

import sys
import traceback
import inspect

def test_all_possible_calls():
    """测试所有可能的调用方式"""
    print("🔍 开始调试 RecipientQualityManager 错误")
    print("=" * 60)
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        
        print("✅ RecipientQualityManager 导入成功")
        
        # 检查方法签名
        print("\n📋 检查方法签名:")
        print("-" * 30)
        
        # 检查 get_quality_recipients 方法
        get_quality_method = getattr(quality_manager, 'get_quality_recipients', None)
        if get_quality_method:
            sig = inspect.signature(get_quality_method)
            print(f"get_quality_recipients 签名: {sig}")
            print(f"参数: {list(sig.parameters.keys())}")
        
        # 检查 get_low_quality_recipients 方法
        get_low_quality_method = getattr(quality_manager, 'get_low_quality_recipients', None)
        if get_low_quality_method:
            sig = inspect.signature(get_low_quality_method)
            print(f"get_low_quality_recipients 签名: {sig}")
            print(f"参数: {list(sig.parameters.keys())}")
        
        print("\n🧪 测试正确的调用:")
        print("-" * 30)
        
        # 测试1: 正确调用 get_quality_recipients
        try:
            high_quality = quality_manager.get_quality_recipients(
                min_quality_score=60.0,
                limit=10
            )
            print(f"✅ get_quality_recipients 正确调用成功: {len(high_quality)} 个收件人")
        except Exception as e:
            print(f"❌ get_quality_recipients 正确调用失败: {str(e)}")
        
        # 测试2: 正确调用 get_low_quality_recipients
        try:
            low_quality = quality_manager.get_low_quality_recipients(
                sender_email="<EMAIL>",
                max_quality_score=40.0
            )
            print(f"✅ get_low_quality_recipients 正确调用成功: {len(low_quality)} 个收件人")
        except Exception as e:
            print(f"❌ get_low_quality_recipients 正确调用失败: {str(e)}")
        
        print("\n🚨 测试错误的调用:")
        print("-" * 30)
        
        # 测试3: 错误调用 get_quality_recipients (这应该会失败)
        try:
            wrong_call = quality_manager.get_quality_recipients(
                max_quality_score=40.0  # 错误：这个参数不存在
            )
            print(f"⚠️ 错误调用竟然成功了: {len(wrong_call)} 个收件人")
        except Exception as e:
            print(f"✅ 错误调用正确地失败了: {str(e)}")
            print(f"   这就是我们要修复的错误！")
        
        print("\n🔧 寻找可能的错误来源:")
        print("-" * 30)
        
        # 检查是否有其他方法可能被错误调用
        all_methods = [method for method in dir(quality_manager) if not method.startswith('_')]
        print(f"所有公共方法: {all_methods}")
        
        # 检查是否有方法接受 max_quality_score 参数
        for method_name in all_methods:
            method = getattr(quality_manager, method_name)
            if callable(method):
                try:
                    sig = inspect.signature(method)
                    params = list(sig.parameters.keys())
                    if 'max_quality_score' in params:
                        print(f"✅ {method_name} 接受 max_quality_score 参数: {params}")
                except:
                    pass
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        traceback.print_exc()
        return False

def simulate_gui_cleanup_call():
    """模拟GUI中的清理调用"""
    print("\n🎯 模拟GUI中的清理调用:")
    print("-" * 30)
    
    try:
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        
        # 模拟 gui_main.py 中的 _auto_cleanup_low_quality_recipients 方法
        sender_email = "<EMAIL>"
        
        print("📞 模拟调用: quality_manager.get_low_quality_recipients()")
        low_quality_recipients = quality_manager.get_low_quality_recipients(
            sender_email=sender_email,
            max_quality_score=40.0
        )
        
        print(f"✅ 模拟GUI调用成功: 获取到 {len(low_quality_recipients)} 个低质量收件人")
        
        # 模拟标记为无效
        if low_quality_recipients:
            marked_count = 0
            for recipient in low_quality_recipients:
                try:
                    success = quality_manager.update_recipient_score(
                        email=recipient.email,
                        new_score=0.0,
                        sender_email=sender_email
                    )
                    if success:
                        marked_count += 1
                except Exception as e:
                    print(f"⚠️ 标记 {recipient.email} 失败: {str(e)}")
            
            print(f"✅ 成功标记 {marked_count} 个收件人为无效")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟GUI调用失败: {str(e)}")
        traceback.print_exc()
        return False

def check_for_version_conflicts():
    """检查是否有版本冲突"""
    print("\n🔍 检查版本冲突:")
    print("-" * 30)
    
    try:
        import os
        import glob
        
        # 查找所有可能的 RecipientQualityManager 文件
        py_files = glob.glob("**/recipient_quality_manager*.py", recursive=True)
        py_files.extend(glob.glob("recipient_quality_manager*.py"))
        
        print(f"找到的相关文件: {py_files}")
        
        for file_path in py_files:
            if os.path.exists(file_path):
                print(f"\n📄 检查文件: {file_path}")
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查 get_quality_recipients 方法定义
                if 'def get_quality_recipients' in content:
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if 'def get_quality_recipients' in line:
                            # 显示方法定义的几行
                            start = max(0, i)
                            end = min(len(lines), i + 5)
                            print(f"  方法定义 (行 {i+1}):")
                            for j in range(start, end):
                                print(f"    {j+1}: {lines[j]}")
                            break
        
        return True
        
    except Exception as e:
        print(f"❌ 检查版本冲突失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 RecipientQualityManager 错误调试工具")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 所有可能的调用
    if test_all_possible_calls():
        success_count += 1
    
    # 测试2: 模拟GUI调用
    if simulate_gui_cleanup_call():
        success_count += 1
    
    # 测试3: 检查版本冲突
    if check_for_version_conflicts():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 调试结果: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 调试完成！")
        print("\n💡 可能的解决方案:")
        print("1. 确保所有调用都使用正确的方法名和参数")
        print("2. 检查是否有旧版本的代码仍在使用错误的参数")
        print("3. 确保没有动态调用或间接调用导致的问题")
    else:
        print("❌ 部分测试失败，需要进一步调查")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()
