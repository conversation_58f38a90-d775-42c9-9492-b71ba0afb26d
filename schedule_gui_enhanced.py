#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的定时发送GUI模块
修复保存功能和添加预发送功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime
import uuid
import json
import os
from typing import Dict, List, Optional
from schedule_manager import ScheduleManager, ScheduledTask

class ScheduleGUIEnhanced:
    """增强的定时发送GUI类"""
    
    def __init__(self, parent_window, log_callback=None):
        self.parent = parent_window
        self.log_callback = log_callback or print
        self.manager = ScheduleManager()
        self.draft_file = "schedule_drafts.json"
        
    def log_message(self, message: str):
        """记录日志消息"""
        self.log_callback(message)
    
    def create_enhanced_schedule_tab(self, notebook):
        """创建增强的定时发送标签页"""
        try:
            # 创建主框架
            schedule_frame = ttk.Frame(notebook)
            notebook.add(schedule_frame, text="⏰ 定时发送")
            
            # 创建子标签页
            sub_notebook = ttk.Notebook(schedule_frame)
            sub_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # 1. 创建任务标签页
            self._create_task_creation_tab(sub_notebook)
            
            # 2. 任务管理标签页
            self._create_task_management_tab(sub_notebook)
            
            # 3. 草稿管理标签页
            self._create_draft_management_tab(sub_notebook)
            
            # 4. 预发送测试标签页
            self._create_preview_tab(sub_notebook)
            
            self.log_message("✅ 增强定时发送界面创建成功")
            
        except Exception as e:
            self.log_message(f"❌ 创建增强定时发送界面失败: {str(e)}")
    
    def _create_task_creation_tab(self, notebook):
        """创建任务创建标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📝 创建任务")
        
        # 创建滚动框架
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 基本信息区域
        basic_frame = ttk.LabelFrame(scrollable_frame, text="📋 基本信息", padding=10)
        basic_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 任务名称
        ttk.Label(basic_frame, text="任务名称:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.task_name_var = tk.StringVar(value=f"定时任务_{datetime.datetime.now().strftime('%m%d_%H%M')}")
        ttk.Entry(basic_frame, textvariable=self.task_name_var, width=40).grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        
        # 发件人邮箱
        ttk.Label(basic_frame, text="发件人:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.sender_email_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.sender_email_var, width=40).grid(row=1, column=1, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        
        # 收件人
        ttk.Label(basic_frame, text="收件人:").grid(row=2, column=0, sticky=tk.NW, padx=(0, 5), pady=(5, 0))
        self.recipient_text = tk.Text(basic_frame, height=4, width=50)
        self.recipient_text.grid(row=2, column=1, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        
        # 邮件主题
        ttk.Label(basic_frame, text="邮件主题:").grid(row=3, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.subject_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.subject_var, width=50).grid(row=3, column=1, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        
        # 邮件内容
        ttk.Label(basic_frame, text="邮件内容:").grid(row=4, column=0, sticky=tk.NW, padx=(0, 5), pady=(5, 0))
        self.body_text = tk.Text(basic_frame, height=6, width=50)
        self.body_text.grid(row=4, column=1, sticky=tk.W, padx=(0, 10), pady=(5, 0))
        
        # 定时设置区域
        time_frame = ttk.LabelFrame(scrollable_frame, text="⏰ 定时设置", padding=10)
        time_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 日期时间设置
        now = datetime.datetime.now()
        
        ttk.Label(time_frame, text="执行日期:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.date_var = tk.StringVar(value=now.strftime("%Y-%m-%d"))
        ttk.Entry(time_frame, textvariable=self.date_var, width=12).grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        
        ttk.Label(time_frame, text="执行时间:").grid(row=0, column=2, sticky=tk.W, padx=(10, 5))
        self.hour_var = tk.StringVar(value=str(now.hour + 1))
        hour_combo = ttk.Combobox(time_frame, textvariable=self.hour_var, width=5, values=[str(i) for i in range(24)])
        hour_combo.grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        
        ttk.Label(time_frame, text=":").grid(row=0, column=4, sticky=tk.W)
        self.minute_var = tk.StringVar(value="00")
        minute_combo = ttk.Combobox(time_frame, textvariable=self.minute_var, width=5, values=[f"{i:02d}" for i in range(0, 60, 5)])
        minute_combo.grid(row=0, column=5, sticky=tk.W, padx=(5, 10))
        
        # 快速设置按钮
        ttk.Button(time_frame, text="🕐 最佳时间", command=self._set_optimal_time).grid(row=0, column=6, sticky=tk.W, padx=(10, 0))
        
        # 发送策略区域
        strategy_frame = ttk.LabelFrame(scrollable_frame, text="🎯 发送策略", padding=10)
        strategy_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # 发送模式
        ttk.Label(strategy_frame, text="发送模式:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.send_mode_var = tk.StringVar(value="standard")
        mode_combo = ttk.Combobox(strategy_frame, textvariable=self.send_mode_var, width=15, 
                                 values=["fast", "standard", "safe"], state="readonly")
        mode_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        
        # 反垃圾策略
        ttk.Label(strategy_frame, text="反垃圾策略:").grid(row=0, column=2, sticky=tk.W, padx=(10, 5))
        self.anti_spam_var = tk.StringVar(value="moderate")
        spam_combo = ttk.Combobox(strategy_frame, textvariable=self.anti_spam_var, width=15,
                                 values=["conservative", "moderate", "aggressive"], state="readonly")
        spam_combo.grid(row=0, column=3, sticky=tk.W, padx=(0, 10))
        
        # 功能开关区域
        features_frame = ttk.LabelFrame(scrollable_frame, text="🔧 功能开关", padding=10)
        features_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.enable_monitoring_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(features_frame, text="📡 自动回复监控", variable=self.enable_monitoring_var).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        
        self.enable_quality_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(features_frame, text="📊 质量数据库", variable=self.enable_quality_var).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        self.enable_emergency_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(features_frame, text="🆘 应急系统", variable=self.enable_emergency_var).grid(row=1, column=0, sticky=tk.W, padx=(0, 20))
        
        self.enable_coordination_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(features_frame, text="🔗 深度协调", variable=self.enable_coordination_var).grid(row=1, column=1, sticky=tk.W, padx=(0, 20))
        
        # 操作按钮区域
        button_frame = ttk.Frame(scrollable_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(button_frame, text="💾 保存草稿", command=self._save_draft).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📋 加载草稿", command=self._load_draft).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="👁️ 预览邮件", command=self._preview_email).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="✅ 创建任务", command=self._create_task).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🔄 重置表单", command=self._reset_form).pack(side=tk.LEFT, padx=5)
        
        # 配置滚动
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def _create_task_management_tab(self, notebook):
        """创建任务管理标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📋 任务管理")
        
        # 任务列表
        list_frame = ttk.LabelFrame(frame, text="📋 定时任务列表", padding=10)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 创建树形视图
        columns = ("name", "sender", "scheduled_time", "status", "created_time")
        self.task_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        self.task_tree.heading("name", text="任务名称")
        self.task_tree.heading("sender", text="发件人")
        self.task_tree.heading("scheduled_time", text="执行时间")
        self.task_tree.heading("status", text="状态")
        self.task_tree.heading("created_time", text="创建时间")
        
        # 设置列宽
        self.task_tree.column("name", width=200)
        self.task_tree.column("sender", width=150)
        self.task_tree.column("scheduled_time", width=150)
        self.task_tree.column("status", width=80)
        self.task_tree.column("created_time", width=150)
        
        # 添加滚动条
        tree_scroll = ttk.Scrollbar(list_frame, orient="vertical", command=self.task_tree.yview)
        self.task_tree.configure(yscrollcommand=tree_scroll.set)
        
        self.task_tree.pack(side="left", fill="both", expand=True)
        tree_scroll.pack(side="right", fill="y")
        
        # 操作按钮
        task_button_frame = ttk.Frame(frame)
        task_button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(task_button_frame, text="🔄 刷新列表", command=self._refresh_task_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(task_button_frame, text="👁️ 查看详情", command=self._view_task_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(task_button_frame, text="❌ 取消任务", command=self._cancel_task).pack(side=tk.LEFT, padx=5)
        ttk.Button(task_button_frame, text="🗑️ 删除任务", command=self._delete_task).pack(side=tk.LEFT, padx=5)
        
        # 初始加载任务列表
        self._refresh_task_list()
    
    def _create_draft_management_tab(self, notebook):
        """创建草稿管理标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="📄 草稿管理")
        
        # 草稿列表
        draft_frame = ttk.LabelFrame(frame, text="📄 保存的草稿", padding=10)
        draft_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 草稿列表框
        self.draft_listbox = tk.Listbox(draft_frame, height=15)
        self.draft_listbox.pack(side="left", fill="both", expand=True)
        
        draft_scroll = ttk.Scrollbar(draft_frame, orient="vertical", command=self.draft_listbox.yview)
        self.draft_listbox.configure(yscrollcommand=draft_scroll.set)
        draft_scroll.pack(side="right", fill="y")
        
        # 草稿操作按钮
        draft_button_frame = ttk.Frame(frame)
        draft_button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(draft_button_frame, text="🔄 刷新草稿", command=self._refresh_draft_list).pack(side=tk.LEFT, padx=5)
        ttk.Button(draft_button_frame, text="📋 加载草稿", command=self._load_selected_draft).pack(side=tk.LEFT, padx=5)
        ttk.Button(draft_button_frame, text="🗑️ 删除草稿", command=self._delete_draft).pack(side=tk.LEFT, padx=5)
        ttk.Button(draft_button_frame, text="🧹 清空所有", command=self._clear_all_drafts).pack(side=tk.LEFT, padx=5)
        
        # 初始加载草稿列表
        self._refresh_draft_list()
    
    def _create_preview_tab(self, notebook):
        """创建预发送测试标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="👁️ 预发送测试")
        
        # 预览区域
        preview_frame = ttk.LabelFrame(frame, text="📧 邮件预览", padding=10)
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 预览文本框
        self.preview_text = tk.Text(preview_frame, height=20, wrap=tk.WORD)
        preview_scroll = ttk.Scrollbar(preview_frame, orient="vertical", command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scroll.set)
        
        self.preview_text.pack(side="left", fill="both", expand=True)
        preview_scroll.pack(side="right", fill="y")
        
        # 测试按钮
        test_button_frame = ttk.Frame(frame)
        test_button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(test_button_frame, text="📧 生成预览", command=self._generate_preview).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_button_frame, text="✉️ 发送测试邮件", command=self._send_test_email).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_button_frame, text="🔍 验证设置", command=self._validate_settings).pack(side=tk.LEFT, padx=5)

    def _set_optimal_time(self):
        """设置最佳发送时间"""
        try:
            # 获取最佳时间推荐
            optimal_times = self.manager.get_optimal_sending_times(self.sender_email_var.get())

            if optimal_times:
                best_time = optimal_times[0]  # 取第一个推荐时间

                # 计算下一个推荐时间点
                now = datetime.datetime.now()
                target_hour = best_time.get('hour', now.hour + 1)

                # 如果推荐时间已过，设置为明天
                target_time = now.replace(hour=target_hour, minute=0, second=0, microsecond=0)
                if target_time <= now:
                    target_time += datetime.timedelta(days=1)

                self.date_var.set(target_time.strftime("%Y-%m-%d"))
                self.hour_var.set(str(target_time.hour))
                self.minute_var.set("00")

                self.log_message(f"✅ 已设置最佳发送时间: {target_time.strftime('%Y-%m-%d %H:%M')}")
            else:
                # 默认设置为明天上午9点
                tomorrow = datetime.datetime.now() + datetime.timedelta(days=1)
                tomorrow = tomorrow.replace(hour=9, minute=0, second=0, microsecond=0)

                self.date_var.set(tomorrow.strftime("%Y-%m-%d"))
                self.hour_var.set("09")
                self.minute_var.set("00")

                self.log_message("✅ 已设置默认最佳时间: 明天上午9点")

        except Exception as e:
            self.log_message(f"❌ 设置最佳时间失败: {str(e)}")

    def _save_draft(self):
        """保存草稿"""
        try:
            draft_data = {
                'name': self.task_name_var.get(),
                'sender_email': self.sender_email_var.get(),
                'recipients': self.recipient_text.get(1.0, tk.END).strip(),
                'subject': self.subject_var.get(),
                'body': self.body_text.get(1.0, tk.END).strip(),
                'date': self.date_var.get(),
                'hour': self.hour_var.get(),
                'minute': self.minute_var.get(),
                'send_mode': self.send_mode_var.get(),
                'anti_spam_strategy': self.anti_spam_var.get(),
                'enable_monitoring': self.enable_monitoring_var.get(),
                'enable_quality': self.enable_quality_var.get(),
                'enable_emergency': self.enable_emergency_var.get(),
                'enable_coordination': self.enable_coordination_var.get(),
                'saved_time': datetime.datetime.now().isoformat()
            }

            # 加载现有草稿
            drafts = self._load_drafts()

            # 添加新草稿
            draft_name = f"草稿_{datetime.datetime.now().strftime('%m%d_%H%M%S')}"
            drafts[draft_name] = draft_data

            # 保存到文件
            with open(self.draft_file, 'w', encoding='utf-8') as f:
                json.dump(drafts, f, ensure_ascii=False, indent=2)

            self.log_message(f"✅ 草稿已保存: {draft_name}")
            self._refresh_draft_list()

        except Exception as e:
            self.log_message(f"❌ 保存草稿失败: {str(e)}")

    def _load_draft(self):
        """加载草稿"""
        try:
            drafts = self._load_drafts()
            if not drafts:
                messagebox.showinfo("提示", "没有保存的草稿")
                return

            # 创建选择对话框
            draft_names = list(drafts.keys())

            # 简单的选择对话框
            selection_window = tk.Toplevel(self.parent)
            selection_window.title("选择草稿")
            selection_window.geometry("400x300")

            ttk.Label(selection_window, text="选择要加载的草稿:").pack(pady=10)

            listbox = tk.Listbox(selection_window)
            for name in draft_names:
                listbox.insert(tk.END, name)
            listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

            def load_selected():
                selection = listbox.curselection()
                if selection:
                    draft_name = draft_names[selection[0]]
                    self._apply_draft(drafts[draft_name])
                    selection_window.destroy()
                    self.log_message(f"✅ 草稿已加载: {draft_name}")
                else:
                    messagebox.showwarning("提示", "请选择一个草稿")

            ttk.Button(selection_window, text="加载", command=load_selected).pack(pady=10)

        except Exception as e:
            self.log_message(f"❌ 加载草稿失败: {str(e)}")

    def _load_drafts(self) -> Dict:
        """从文件加载草稿"""
        try:
            if os.path.exists(self.draft_file):
                with open(self.draft_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            self.log_message(f"❌ 读取草稿文件失败: {str(e)}")
            return {}

    def _apply_draft(self, draft_data: Dict):
        """应用草稿数据到表单"""
        try:
            self.task_name_var.set(draft_data.get('name', ''))
            self.sender_email_var.set(draft_data.get('sender_email', ''))

            self.recipient_text.delete(1.0, tk.END)
            self.recipient_text.insert(1.0, draft_data.get('recipients', ''))

            self.subject_var.set(draft_data.get('subject', ''))

            self.body_text.delete(1.0, tk.END)
            self.body_text.insert(1.0, draft_data.get('body', ''))

            self.date_var.set(draft_data.get('date', ''))
            self.hour_var.set(draft_data.get('hour', ''))
            self.minute_var.set(draft_data.get('minute', ''))

            self.send_mode_var.set(draft_data.get('send_mode', 'standard'))
            self.anti_spam_var.set(draft_data.get('anti_spam_strategy', 'moderate'))

            self.enable_monitoring_var.set(draft_data.get('enable_monitoring', True))
            self.enable_quality_var.set(draft_data.get('enable_quality', True))
            self.enable_emergency_var.set(draft_data.get('enable_emergency', True))
            self.enable_coordination_var.set(draft_data.get('enable_coordination', True))

        except Exception as e:
            self.log_message(f"❌ 应用草稿数据失败: {str(e)}")

    def _preview_email(self):
        """预览邮件内容"""
        try:
            preview_content = f"""
📧 邮件预览
{'='*50}

发件人: {self.sender_email_var.get()}
收件人: {self.recipient_text.get(1.0, tk.END).strip()}
主题: {self.subject_var.get()}

内容:
{'-'*30}
{self.body_text.get(1.0, tk.END).strip()}
{'-'*30}

定时设置:
执行时间: {self.date_var.get()} {self.hour_var.get()}:{self.minute_var.get()}
发送模式: {self.send_mode_var.get()}
反垃圾策略: {self.anti_spam_var.get()}

功能开关:
📡 自动回复监控: {'✅' if self.enable_monitoring_var.get() else '❌'}
📊 质量数据库: {'✅' if self.enable_quality_var.get() else '❌'}
🆘 应急系统: {'✅' if self.enable_emergency_var.get() else '❌'}
🔗 深度协调: {'✅' if self.enable_coordination_var.get() else '❌'}
"""

            # 显示预览窗口
            preview_window = tk.Toplevel(self.parent)
            preview_window.title("邮件预览")
            preview_window.geometry("600x500")

            text_widget = tk.Text(preview_window, wrap=tk.WORD)
            text_widget.insert(1.0, preview_content)
            text_widget.config(state=tk.DISABLED)

            scrollbar = ttk.Scrollbar(preview_window, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

        except Exception as e:
            self.log_message(f"❌ 预览邮件失败: {str(e)}")

    def _create_task(self):
        """创建定时任务"""
        try:
            # 验证输入
            if not self._validate_form():
                return

            # 构建定时时间
            date_str = self.date_var.get()
            hour_str = self.hour_var.get()
            minute_str = self.minute_var.get()

            scheduled_time = datetime.datetime.strptime(f"{date_str} {hour_str}:{minute_str}", "%Y-%m-%d %H:%M")

            # 检查时间是否在未来
            if scheduled_time <= datetime.datetime.now():
                messagebox.showerror("时间错误", "定时时间必须在未来")
                return

            # 创建任务对象
            task = ScheduledTask(
                id=str(uuid.uuid4()),
                name=self.task_name_var.get(),
                sender_email=self.sender_email_var.get(),
                recipient_emails=self.recipient_text.get(1.0, tk.END).strip(),
                subject=self.subject_var.get(),
                body=self.body_text.get(1.0, tk.END).strip(),
                attachments=[],
                scheduled_time=scheduled_time.isoformat(),
                send_mode=self.send_mode_var.get(),
                anti_spam_strategy=self.anti_spam_var.get(),
                enable_monitoring=self.enable_monitoring_var.get(),
                enable_quality_db=self.enable_quality_var.get(),
                enable_emergency=self.enable_emergency_var.get(),
                enable_coordination=self.enable_coordination_var.get(),
                status="pending",
                created_time=datetime.datetime.now().isoformat()
            )

            # 添加到管理器
            if self.manager.add_scheduled_task(task):
                messagebox.showinfo("成功", f"定时任务已创建！\n\n任务名称: {task.name}\n执行时间: {scheduled_time.strftime('%Y-%m-%d %H:%M')}")
                self.log_message(f"✅ 定时任务已创建: {task.name} -> {scheduled_time.strftime('%Y-%m-%d %H:%M')}")

                # 刷新任务列表
                self._refresh_task_list()

                # 询问是否重置表单
                if messagebox.askyesno("提示", "任务创建成功！是否重置表单以创建新任务？"):
                    self._reset_form()
            else:
                messagebox.showerror("失败", "创建定时任务失败")

        except Exception as e:
            error_msg = f"创建定时任务失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _validate_form(self) -> bool:
        """验证表单输入"""
        try:
            # 检查必填字段
            if not self.task_name_var.get().strip():
                messagebox.showerror("输入错误", "请填写任务名称")
                return False

            if not self.sender_email_var.get().strip():
                messagebox.showerror("输入错误", "请填写发件人邮箱")
                return False

            if not self.recipient_text.get(1.0, tk.END).strip():
                messagebox.showerror("输入错误", "请填写收件人")
                return False

            if not self.subject_var.get().strip():
                messagebox.showerror("输入错误", "请填写邮件主题")
                return False

            if not self.body_text.get(1.0, tk.END).strip():
                messagebox.showerror("输入错误", "请填写邮件内容")
                return False

            # 验证日期时间格式
            try:
                date_str = self.date_var.get()
                hour_str = self.hour_var.get()
                minute_str = self.minute_var.get()
                datetime.datetime.strptime(f"{date_str} {hour_str}:{minute_str}", "%Y-%m-%d %H:%M")
            except ValueError:
                messagebox.showerror("时间错误", "请检查日期时间格式")
                return False

            return True

        except Exception as e:
            self.log_message(f"❌ 表单验证失败: {str(e)}")
            return False

    def _reset_form(self):
        """重置表单"""
        try:
            now = datetime.datetime.now()
            self.task_name_var.set(f"定时任务_{now.strftime('%m%d_%H%M')}")
            self.sender_email_var.set("")
            self.recipient_text.delete(1.0, tk.END)
            self.subject_var.set("")
            self.body_text.delete(1.0, tk.END)

            self.date_var.set(now.strftime("%Y-%m-%d"))
            self.hour_var.set(str(now.hour + 1))
            self.minute_var.set("00")

            self.send_mode_var.set("standard")
            self.anti_spam_var.set("moderate")

            self.enable_monitoring_var.set(True)
            self.enable_quality_var.set(True)
            self.enable_emergency_var.set(True)
            self.enable_coordination_var.set(True)

            self.log_message("✅ 表单已重置")

        except Exception as e:
            self.log_message(f"❌ 重置表单失败: {str(e)}")

    def _refresh_task_list(self):
        """刷新任务列表"""
        try:
            # 清空现有项目
            for item in self.task_tree.get_children():
                self.task_tree.delete(item)

            # 获取所有任务
            tasks = self.manager.get_all_scheduled_tasks()

            for task in tasks:
                # 格式化时间显示
                try:
                    scheduled_time = datetime.datetime.fromisoformat(task.scheduled_time)
                    scheduled_str = scheduled_time.strftime("%Y-%m-%d %H:%M")
                except:
                    scheduled_str = task.scheduled_time

                try:
                    created_time = datetime.datetime.fromisoformat(task.created_time)
                    created_str = created_time.strftime("%Y-%m-%d %H:%M")
                except:
                    created_str = task.created_time

                # 状态显示
                status_map = {
                    'pending': '⏳ 待执行',
                    'running': '🔄 执行中',
                    'completed': '✅ 已完成',
                    'failed': '❌ 失败',
                    'cancelled': '🚫 已取消'
                }
                status_display = status_map.get(task.status, task.status)

                self.task_tree.insert("", "end", values=(
                    task.name,
                    task.sender_email,
                    scheduled_str,
                    status_display,
                    created_str
                ), tags=(task.id,))

            self.log_message(f"✅ 任务列表已刷新，共 {len(tasks)} 个任务")

        except Exception as e:
            self.log_message(f"❌ 刷新任务列表失败: {str(e)}")

    def _view_task_details(self):
        """查看任务详情"""
        try:
            selection = self.task_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择一个任务")
                return

            # 获取任务ID
            item = selection[0]
            task_id = self.task_tree.item(item)['tags'][0]

            # 获取任务详情
            task = self.manager.get_scheduled_task(task_id)
            if not task:
                messagebox.showerror("错误", "任务不存在")
                return

            # 显示详情窗口
            details_window = tk.Toplevel(self.parent)
            details_window.title(f"任务详情 - {task.name}")
            details_window.geometry("600x500")

            # 创建详情内容
            details_content = f"""
📋 任务详情
{'='*50}

基本信息:
任务ID: {task.id}
任务名称: {task.name}
发件人: {task.sender_email}
状态: {task.status}

邮件信息:
主题: {task.subject}
收件人数量: {len(task.recipient_emails.split())}

定时设置:
执行时间: {task.scheduled_time}
创建时间: {task.created_time}
执行时间: {task.executed_time or '未执行'}

发送策略:
发送模式: {task.send_mode}
反垃圾策略: {task.anti_spam_strategy}
重试次数: {task.retry_count}/{task.max_retries}

功能开关:
📡 自动回复监控: {'✅' if task.enable_monitoring else '❌'}
📊 质量数据库: {'✅' if task.enable_quality_db else '❌'}
🆘 应急系统: {'✅' if task.enable_emergency else '❌'}
🔗 深度协调: {'✅' if task.enable_coordination else '❌'}

收件人列表:
{'-'*30}
{task.recipient_emails}
{'-'*30}

邮件内容:
{'-'*30}
{task.body}
{'-'*30}

执行结果:
{json.dumps(task.result, ensure_ascii=False, indent=2) if task.result else '暂无结果'}
"""

            text_widget = tk.Text(details_window, wrap=tk.WORD)
            text_widget.insert(1.0, details_content)
            text_widget.config(state=tk.DISABLED)

            scrollbar = ttk.Scrollbar(details_window, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

        except Exception as e:
            self.log_message(f"❌ 查看任务详情失败: {str(e)}")

    def _cancel_task(self):
        """取消任务"""
        try:
            selection = self.task_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择一个任务")
                return

            # 获取任务ID
            item = selection[0]
            task_id = self.task_tree.item(item)['tags'][0]

            # 确认取消
            if not messagebox.askyesno("确认", "确定要取消这个任务吗？"):
                return

            # 取消任务
            if self.manager.cancel_scheduled_task(task_id):
                self.log_message(f"✅ 任务已取消: {task_id}")
                self._refresh_task_list()
            else:
                messagebox.showerror("错误", "取消任务失败")

        except Exception as e:
            self.log_message(f"❌ 取消任务失败: {str(e)}")

    def _delete_task(self):
        """删除任务"""
        try:
            selection = self.task_tree.selection()
            if not selection:
                messagebox.showwarning("提示", "请选择一个任务")
                return

            # 获取任务ID
            item = selection[0]
            task_id = self.task_tree.item(item)['tags'][0]

            # 确认删除
            if not messagebox.askyesno("确认", "确定要删除这个任务吗？\n删除后无法恢复！"):
                return

            # 删除任务
            if self.manager.delete_scheduled_task(task_id):
                self.log_message(f"✅ 任务已删除: {task_id}")
                self._refresh_task_list()
            else:
                messagebox.showerror("错误", "删除任务失败")

        except Exception as e:
            self.log_message(f"❌ 删除任务失败: {str(e)}")

    def _refresh_draft_list(self):
        """刷新草稿列表"""
        try:
            self.draft_listbox.delete(0, tk.END)

            drafts = self._load_drafts()
            for draft_name, draft_data in drafts.items():
                saved_time = draft_data.get('saved_time', '')
                display_text = f"{draft_name} ({saved_time[:16]})"
                self.draft_listbox.insert(tk.END, display_text)

            self.log_message(f"✅ 草稿列表已刷新，共 {len(drafts)} 个草稿")

        except Exception as e:
            self.log_message(f"❌ 刷新草稿列表失败: {str(e)}")

    def _load_selected_draft(self):
        """加载选中的草稿"""
        try:
            selection = self.draft_listbox.curselection()
            if not selection:
                messagebox.showwarning("提示", "请选择一个草稿")
                return

            # 获取草稿名称
            selected_text = self.draft_listbox.get(selection[0])
            draft_name = selected_text.split(' (')[0]  # 提取草稿名称

            drafts = self._load_drafts()
            if draft_name in drafts:
                self._apply_draft(drafts[draft_name])
                self.log_message(f"✅ 草稿已加载: {draft_name}")
            else:
                messagebox.showerror("错误", "草稿不存在")

        except Exception as e:
            self.log_message(f"❌ 加载草稿失败: {str(e)}")

    def _delete_draft(self):
        """删除选中的草稿"""
        try:
            selection = self.draft_listbox.curselection()
            if not selection:
                messagebox.showwarning("提示", "请选择一个草稿")
                return

            # 获取草稿名称
            selected_text = self.draft_listbox.get(selection[0])
            draft_name = selected_text.split(' (')[0]

            # 确认删除
            if not messagebox.askyesno("确认", f"确定要删除草稿 '{draft_name}' 吗？"):
                return

            # 删除草稿
            drafts = self._load_drafts()
            if draft_name in drafts:
                del drafts[draft_name]

                # 保存更新后的草稿
                with open(self.draft_file, 'w', encoding='utf-8') as f:
                    json.dump(drafts, f, ensure_ascii=False, indent=2)

                self.log_message(f"✅ 草稿已删除: {draft_name}")
                self._refresh_draft_list()
            else:
                messagebox.showerror("错误", "草稿不存在")

        except Exception as e:
            self.log_message(f"❌ 删除草稿失败: {str(e)}")

    def _clear_all_drafts(self):
        """清空所有草稿"""
        try:
            if not messagebox.askyesno("确认", "确定要清空所有草稿吗？\n此操作无法撤销！"):
                return

            # 清空草稿文件
            with open(self.draft_file, 'w', encoding='utf-8') as f:
                json.dump({}, f)

            self.log_message("✅ 所有草稿已清空")
            self._refresh_draft_list()

        except Exception as e:
            self.log_message(f"❌ 清空草稿失败: {str(e)}")

    def _generate_preview(self):
        """生成邮件预览"""
        try:
            if not self._validate_form():
                return

            preview_content = f"""
📧 邮件预览
{'='*50}

发件人: {self.sender_email_var.get()}
主题: {self.subject_var.get()}

收件人列表:
{'-'*30}
{self.recipient_text.get(1.0, tk.END).strip()}
{'-'*30}

邮件内容:
{'-'*30}
{self.body_text.get(1.0, tk.END).strip()}
{'-'*30}

定时设置:
执行时间: {self.date_var.get()} {self.hour_var.get()}:{self.minute_var.get()}

发送策略:
发送模式: {self.send_mode_var.get()}
反垃圾策略: {self.anti_spam_var.get()}

功能开关:
📡 自动回复监控: {'✅' if self.enable_monitoring_var.get() else '❌'}
📊 质量数据库: {'✅' if self.enable_quality_var.get() else '❌'}
🆘 应急系统: {'✅' if self.enable_emergency_var.get() else '❌'}
🔗 深度协调: {'✅' if self.enable_coordination_var.get() else '❌'}

预计收件人数量: {len([email.strip() for email in self.recipient_text.get(1.0, tk.END).strip().split('\n') if email.strip()])}
"""

            self.preview_text.config(state=tk.NORMAL)
            self.preview_text.delete(1.0, tk.END)
            self.preview_text.insert(1.0, preview_content)
            self.preview_text.config(state=tk.DISABLED)

            self.log_message("✅ 邮件预览已生成")

        except Exception as e:
            self.log_message(f"❌ 生成预览失败: {str(e)}")

    def _send_test_email(self):
        """发送测试邮件"""
        try:
            if not self._validate_form():
                return

            # 确认发送测试邮件
            if not messagebox.askyesno("确认", "确定要发送测试邮件吗？\n这将立即发送邮件到指定收件人。"):
                return

            # 这里应该调用实际的邮件发送功能
            # 为了安全起见，我们只是模拟发送
            self.log_message("🧪 测试邮件发送功能暂未实现，请使用正式的邮件发送功能")
            messagebox.showinfo("提示", "测试邮件发送功能暂未实现\n请使用正式的邮件发送功能进行测试")

        except Exception as e:
            self.log_message(f"❌ 发送测试邮件失败: {str(e)}")

    def _validate_settings(self):
        """验证设置"""
        try:
            validation_results = []

            # 验证邮箱格式
            sender_email = self.sender_email_var.get().strip()
            if '@' not in sender_email:
                validation_results.append("❌ 发件人邮箱格式不正确")
            else:
                validation_results.append("✅ 发件人邮箱格式正确")

            # 验证收件人
            recipients = [email.strip() for email in self.recipient_text.get(1.0, tk.END).strip().split('\n') if email.strip()]
            valid_recipients = [email for email in recipients if '@' in email]

            if len(valid_recipients) == len(recipients):
                validation_results.append(f"✅ 所有收件人邮箱格式正确 ({len(recipients)} 个)")
            else:
                validation_results.append(f"❌ 有 {len(recipients) - len(valid_recipients)} 个收件人邮箱格式不正确")

            # 验证时间设置
            try:
                date_str = self.date_var.get()
                hour_str = self.hour_var.get()
                minute_str = self.minute_var.get()
                scheduled_time = datetime.datetime.strptime(f"{date_str} {hour_str}:{minute_str}", "%Y-%m-%d %H:%M")

                if scheduled_time > datetime.datetime.now():
                    validation_results.append("✅ 定时时间设置正确")
                else:
                    validation_results.append("❌ 定时时间必须在未来")
            except ValueError:
                validation_results.append("❌ 时间格式不正确")

            # 验证内容长度
            subject_len = len(self.subject_var.get().strip())
            body_len = len(self.body_text.get(1.0, tk.END).strip())

            if subject_len > 0:
                validation_results.append(f"✅ 邮件主题长度合适 ({subject_len} 字符)")
            else:
                validation_results.append("❌ 邮件主题不能为空")

            if body_len > 0:
                validation_results.append(f"✅ 邮件内容长度合适 ({body_len} 字符)")
            else:
                validation_results.append("❌ 邮件内容不能为空")

            # 显示验证结果
            result_text = "\n".join(validation_results)

            # 显示验证结果窗口
            result_window = tk.Toplevel(self.parent)
            result_window.title("设置验证结果")
            result_window.geometry("500x400")

            text_widget = tk.Text(result_window, wrap=tk.WORD)
            text_widget.insert(1.0, f"🔍 设置验证结果\n{'='*30}\n\n{result_text}")
            text_widget.config(state=tk.DISABLED)

            scrollbar = ttk.Scrollbar(result_window, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

            self.log_message("✅ 设置验证完成")

        except Exception as e:
            self.log_message(f"❌ 验证设置失败: {str(e)}")
