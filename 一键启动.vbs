' 邮件系统一键启动脚本
' 最简单的启动方式，自动选择最佳版本

Option Explicit

Dim objShell, objFSO, currentDir, bestScript
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' 获取当前目录
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 按优先级查找可用的脚本文件
Dim scripts(3)
scripts(0) = "gui_complete_v3.py"     ' 最优先：完整功能版
scripts(1) = "gui_main_clean.py"      ' 次优先：清洁版本
scripts(2) = "gui_main.py"            ' 备选：修复后原版
scripts(3) = "gui_clean.py"           ' 最后：简化版本

bestScript = ""
Dim i
For i = 0 To 3
    If objFSO.FileExists(currentDir & "\" & scripts(i)) Then
        bestScript = scripts(i)
        Exit For
    End If
Next

' 检查是否找到可用脚本
If bestScript = "" Then
    MsgBox "❌ 找不到可启动的邮件系统文件！" & vbCrLf & vbCrLf & _
           "请确保以下文件之一存在：" & vbCrLf & _
           "• gui_complete_v3.py（推荐）" & vbCrLf & _
           "• gui_main_clean.py" & vbCrLf & _
           "• gui_main.py" & vbCrLf & _
           "• gui_clean.py", _
           vbCritical, "文件检查"
    WScript.Quit
End If

' 显示启动信息
Dim versionInfo
Select Case bestScript
    Case "gui_complete_v3.py"
        versionInfo = "🚀 完整功能版v3.0" & vbCrLf & _
                     "✨ 包含所有高级功能：" & vbCrLf & _
                     "• 邮件撤回、监控、质量管理" & vbCrLf & _
                     "• 安全防护、深度协调" & vbCrLf & _
                     "• 四栏优化布局"
    Case "gui_main_clean.py"
        versionInfo = "🎨 清洁版本" & vbCrLf & _
                     "• 基于完整功能版" & vbCrLf & _
                     "• 无拼写警告" & vbCrLf & _
                     "• 稳定可靠"
    Case "gui_main.py"
        versionInfo = "🔧 修复版本" & vbCrLf & _
                     "• 原版功能" & vbCrLf & _
                     "• 修复拼写警告" & vbCrLf & _
                     "• 兼容性好"
    Case "gui_clean.py"
        versionInfo = "⚡ 简化版本" & vbCrLf & _
                     "• 基础功能" & vbCrLf & _
                     "• 轻量快速" & vbCrLf & _
                     "• 适合测试"
End Select

MsgBox "🎯 邮件系统自动启动" & vbCrLf & vbCrLf & _
       "检测到最佳版本：" & bestScript & vbCrLf & vbCrLf & _
       versionInfo & vbCrLf & vbCrLf & _
       "正在启动...", _
       vbInformation, "自动启动"

' 构建启动命令
Dim command
command = "python """ & currentDir & "\" & bestScript & """"

' 启动Python脚本
On Error Resume Next
objShell.Run command, 1, False

If Err.Number <> 0 Then
    MsgBox "❌ 启动失败！" & vbCrLf & vbCrLf & _
           "可能的原因：" & vbCrLf & _
           "1. 未安装Python" & vbCrLf & _
           "2. Python未添加到PATH" & vbCrLf & _
           "3. 文件权限问题" & vbCrLf & vbCrLf & _
           "解决方案：" & vbCrLf & _
           "• 安装Python 3.8+版本" & vbCrLf & _
           "• 安装时勾选'Add to PATH'" & vbCrLf & _
           "• 以管理员身份运行", _
           vbCritical, "启动失败"
Else
    ' 启动成功提示
    MsgBox "✅ 邮件系统启动成功！" & vbCrLf & vbCrLf & _
           "🎯 当前版本：" & bestScript & vbCrLf & vbCrLf & _
           "💡 使用提示：" & vbCrLf & _
           "• 首次使用请配置邮箱授权码" & vbCrLf & _
           "• 查看操作日志了解运行状态" & vbCrLf & _
           "• 如有问题请查看控制台输出" & vbCrLf & vbCrLf & _
           "如果窗口没有显示，请检查任务栏", _
           vbInformation, "启动成功"
End If

On Error GoTo 0
