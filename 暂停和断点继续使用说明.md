# 暂停和断点继续功能使用说明

## 🎯 功能概述

为了解决大量邮件发送过程中的中断问题，我们新增了**暂停和断点继续功能**。该功能让您可以：

- ⏸️ **随时暂停发送** - 在发送过程中暂停，保存当前进度
- ▶️ **恢复发送** - 从暂停的地方继续发送
- 🔄 **断点继续** - 程序重启后从上次停止的地方继续
- 💾 **自动保存进度** - 每10封邮件自动保存一次进度

## 🎛️ 控制按钮说明

### 主界面新增按钮

| 按钮 | 功能 | 状态 | 说明 |
|------|------|------|------|
| **⏸️ 暂停发送** | 暂停当前发送 | 发送时可用 | 立即暂停并保存进度 |
| **▶️ 恢复发送** | 恢复暂停的发送 | 暂停时可用 | 从暂停处继续发送 |
| **🔄 断点继续** | 从断点继续 | 有未完成任务时可用 | 程序重启后继续发送 |

### 队列系统按钮

| 按钮 | 功能 | 说明 |
|------|------|------|
| **⏹️ 停止队列** | 停止队列发送 | 保存进度并停止 |
| **🔄 断点继续** | 继续队列发送 | 从停止处继续队列 |

## 🚀 使用方法

### 1. 暂停和恢复发送

#### 场景：发送过程中需要暂停
```
1. 点击"发送邮件"开始发送
2. 发送过程中点击"⏸️ 暂停发送"
3. 系统自动保存当前进度
4. 需要继续时点击"▶️ 恢复发送"
5. 从暂停处继续发送
```

#### 实际操作示例
```
正在发送第15/100封邮件...
↓ 点击"暂停发送"
⏸️ 发送已暂停，进度已保存
↓ 点击"恢复发送"  
▶️ 从第16封邮件继续发送...
```

### 2. 断点继续发送

#### 场景：程序意外关闭或主动停止
```
1. 发送过程中程序关闭或点击停止
2. 重新启动程序
3. 系统自动检测到未完成任务
4. 弹出提示询问是否继续
5. 点击"是"从断点继续发送
```

#### 断点信息显示
```
发现未完成的发送任务：

会话ID: session_20241211_143022
总邮件数: 200
已发送: 85
剩余: 115

是否要继续发送？
```

### 3. 队列系统断点继续

#### 队列任务中断恢复
```
1. 队列发送过程中点击"停止队列"
2. 重新打开队列管理窗口
3. 点击"🔄 断点继续"按钮
4. 从停止的任务继续发送
```

## 💾 进度保存机制

### 自动保存时机
- ✅ **每10封邮件** - 自动保存一次进度
- ✅ **每个批次完成** - 批次结束时保存
- ✅ **用户暂停时** - 立即保存当前状态
- ✅ **用户停止时** - 保存停止位置

### 保存的信息
```json
{
  "session_id": "session_20241211_143022",
  "send_mode": "safe",
  "total_emails": 200,
  "current_email_index": 85,
  "current_batch": 3,
  "total_batches": 4,
  "success_count": 82,
  "failed_count": 3,
  "today_sent": 82,
  "start_time": "2024-12-11T14:30:22",
  "save_time": "2024-12-11T15:45:18"
}
```

## 🔍 进度文件管理

### 进度文件位置
- **文件名**: `email_send_progress.json`
- **位置**: 程序根目录
- **格式**: JSON格式，可读性强

### 手动管理进度
```python
# 查看进度信息
pause_manager.show_progress_info()

# 清除所有进度
pause_manager.clear_all_progress()

# 检查是否可以恢复
can_resume, progress_data = batch_manager.can_resume()
```

## ⚠️ 注意事项

### 1. 进度文件有效期
- 📅 **24小时内有效** - 超过24小时的进度可能过期
- 🔄 **模式匹配** - 只能在相同发送模式下恢复
- 💾 **自动清理** - 发送完成后自动清除进度文件

### 2. 暂停期间注意事项
- ⏸️ **保持程序运行** - 暂停期间不要关闭程序
- 🔌 **网络连接** - 确保网络连接稳定
- 💻 **系统资源** - 避免系统休眠或关机

### 3. 断点继续限制
- 📧 **邮件列表一致** - 恢复时邮件列表应与原来一致
- 🎛️ **发送模式一致** - 必须使用相同的发送模式
- 📊 **批次大小一致** - 批次设置应保持不变

## 🛠️ 高级功能

### 1. 程序化控制
```python
from email_sender import EmailSender

# 创建发送器
sender = EmailSender("<EMAIL>")

# 定义控制回调
def pause_callback():
    return user_wants_to_pause  # 返回True暂停

def stop_callback():
    return user_wants_to_stop   # 返回True停止

# 批次发送（支持暂停和断点）
results = sender.send_batch_emails(
    email_list=email_list,
    send_mode='safe',
    pause_callback=pause_callback,
    stop_callback=stop_callback,
    session_id='my_session_001',
    resume_from_progress=True  # 启用断点继续
)
```

### 2. 自定义会话ID
```python
# 使用自定义会话ID便于管理
session_id = f"campaign_{datetime.now().strftime('%Y%m%d_%H%M')}"

# 发送时指定会话ID
results = sender.send_batch_emails(
    email_list=email_list,
    session_id=session_id,
    resume_from_progress=True
)
```

### 3. 进度监控
```python
def progress_callback(progress_info):
    print(f"进度: {progress_info['current_email']}/{progress_info['total_emails']}")
    print(f"会话: {progress_info['session_id']}")
    print(f"成功: {progress_info['success_count']}")
    
    # 可以在这里实现自定义的进度显示
    update_progress_bar(progress_info)
```

## 🔧 故障排除

### 常见问题

**Q: 断点继续时提示"无法恢复发送"？**
A: 检查以下几点：
1. 发送模式是否一致
2. 进度文件是否存在且未损坏
3. 保存时间是否超过24小时

**Q: 暂停后无法恢复？**
A: 
1. 确保程序没有关闭
2. 检查"恢复发送"按钮是否可用
3. 查看日志文件获取详细信息

**Q: 进度文件在哪里？**
A: 
1. 文件名：`email_send_progress.json`
2. 位置：程序根目录
3. 可以手动删除来清除所有进度

**Q: 如何查看当前进度？**
A:
1. 使用进度回调函数
2. 查看日志文件
3. 直接打开进度文件（JSON格式）

## 📊 使用建议

### 1. 大量邮件发送
- 🎯 **使用安全模式** - 提高送达率
- ⏰ **合理安排时间** - 避免在系统维护时间发送
- 💾 **定期检查进度** - 确保进度正常保存

### 2. 长时间发送
- 🔌 **确保电源稳定** - 避免意外断电
- 🌐 **保持网络连接** - 确保网络稳定
- 📱 **设置提醒** - 定期检查发送状态

### 3. 测试建议
- 🧪 **先小量测试** - 验证暂停恢复功能
- 📊 **观察送达率** - 确保功能不影响送达率
- 🔄 **测试断点继续** - 验证重启后的恢复能力

---

**提示**: 暂停和断点继续功能已完全集成到现有系统中，无需额外配置即可使用。系统会自动处理进度保存和恢复，确保您的邮件发送任务不会因为意外中断而丢失进度。
