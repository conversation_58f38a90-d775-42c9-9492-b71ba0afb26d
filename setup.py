# -*- coding: utf-8 -*-
"""
自动化邮件发送助手 - 快速设置脚本
"""

import os
import sys

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 6):
        print("错误: 需要Python 3.6或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    else:
        print(f"✓ Python版本检查通过: {sys.version.split()[0]}")
        return True

def check_required_modules():
    """检查必需的模块"""
    required_modules = [
        'smtplib', 'email', 'os', 'time', 'logging', 'typing'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✓ {module} 模块可用")
        except ImportError:
            missing_modules.append(module)
            print(f"✗ {module} 模块缺失")
    
    if missing_modules:
        print(f"\n错误: 缺少必需模块: {', '.join(missing_modules)}")
        return False
    else:
        print("✓ 所有必需模块都可用")
        return True

def test_smtp_connection():
    """测试SMTP连接"""
    print("\n测试QQ邮箱SMTP连接...")
    
    try:
        import smtplib
        server = smtplib.SMTP('smtp.qq.com', 587)
        server.starttls()
        server.quit()
        print("✓ SMTP连接测试成功")
        return True
    except Exception as e:
        print(f"✗ SMTP连接测试失败: {str(e)}")
        print("请检查网络连接")
        return False

def create_sample_files():
    """创建示例文件"""
    print("\n创建示例文件...")
    
    # 创建示例文本文件
    sample_txt = "sample_attachment.txt"
    if not os.path.exists(sample_txt):
        with open(sample_txt, 'w', encoding='utf-8') as f:
            f.write("这是一个示例附件文件。\n")
            f.write("您可以用它来测试邮件附件功能。\n")
            f.write("创建时间: " + str(__import__('datetime').datetime.now()))
        print(f"✓ 创建示例文件: {sample_txt}")
    else:
        print(f"✓ 示例文件已存在: {sample_txt}")

def show_configuration_guide():
    """显示配置指南"""
    print("\n" + "=" * 60)
    print("    QQ邮箱SMTP配置指南")
    print("=" * 60)
    
    print("\n1. 登录QQ邮箱网页版 (mail.qq.com)")
    print("2. 点击右上角的 '设置' → '账户'")
    print("3. 找到 'POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务'")
    print("4. 开启 'IMAP/SMTP服务'")
    print("5. 按提示发送短信验证")
    print("6. 获取授权码（16位字符）")
    
    print(f"\n注意: 您的授权码已配置为: vwpboqxircdudgfa")
    print("如需更换，请修改 config.py 文件中的 password 字段")

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "=" * 60)
    print("    使用方法")
    print("=" * 60)
    
    print("\n方法1: 使用启动脚本（推荐）")
    print("  双击 start.bat 文件")
    
    print("\n方法2: 命令行运行")
    print("  发送单封邮件: python main.py")
    print("  批量发送邮件: python main.py --batch")
    print("  功能测试: python test_email.py")
    
    print("\n方法3: 编程调用")
    print("  参考 example_usage.py 文件中的示例代码")

def main():
    """主函数"""
    print("=" * 60)
    print("    自动化邮件发送助手 - 环境检查与设置")
    print("=" * 60)
    
    # 环境检查
    print("\n1. 环境检查")
    print("-" * 30)
    
    if not check_python_version():
        return False
    
    if not check_required_modules():
        return False
    
    if not test_smtp_connection():
        print("警告: SMTP连接测试失败，但程序仍可正常使用")
    
    # 创建示例文件
    print("\n2. 创建示例文件")
    print("-" * 30)
    create_sample_files()
    
    # 显示配置指南
    show_configuration_guide()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n" + "=" * 60)
    print("    设置完成")
    print("=" * 60)
    print("✓ 环境检查通过")
    print("✓ 示例文件已创建")
    print("✓ 程序已准备就绪")
    
    print("\n现在您可以:")
    print("1. 双击 start.bat 开始使用")
    print("2. 运行 python test_email.py 进行功能测试")
    print("3. 查看 README.md 了解详细说明")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n设置过程中遇到问题，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n设置被用户中断")
    except Exception as e:
        print(f"\n设置过程中发生错误: {str(e)}")
        input("按回车键退出...")
