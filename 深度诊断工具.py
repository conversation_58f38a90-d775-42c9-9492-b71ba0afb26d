# -*- coding: utf-8 -*-
"""
深度诊断工具 - 分析QQ邮箱发送失败的具体原因
"""

import smtplib
import socket
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from config import SMTP_CONFIG

def test_smtp_detailed(sender_email, recipient_email):
    """详细测试SMTP发送过程"""
    print(f"详细测试: {sender_email} -> {recipient_email}")
    print("=" * 60)
    
    try:
        # 1. 连接测试
        print("1. 连接SMTP服务器...")
        server = smtplib.SMTP(SMTP_CONFIG['server'], SMTP_CONFIG['port'])
        server.set_debuglevel(1)  # 开启详细调试
        print("✓ 连接成功")
        
        # 2. TLS测试
        print("\n2. 启用TLS加密...")
        server.starttls()
        print("✓ TLS启用成功")
        
        # 3. 认证测试
        print(f"\n3. 进行SMTP认证 ({sender_email})...")
        server.login(sender_email, SMTP_CONFIG['password'])
        print("✓ 认证成功")
        
        # 4. 发件人验证
        print(f"\n4. 设置发件人 ({sender_email})...")
        server.mail(sender_email)
        print("✓ 发件人设置成功")
        
        # 5. 收件人验证 - 关键步骤
        print(f"\n5. 验证收件人 ({recipient_email})...")
        try:
            code, message = server.rcpt(recipient_email)
            print(f"RCPT TO 响应码: {code}")
            print(f"响应消息: {message}")
            
            if code == 250:
                print("✓ 收件人验证成功")
                
                # 6. 尝试发送简单邮件
                print(f"\n6. 尝试发送简单邮件...")
                msg = MIMEText("测试邮件", 'plain', 'utf-8')
                msg['From'] = sender_email
                msg['To'] = recipient_email
                msg['Subject'] = "测试"
                
                server.data(msg.as_string())
                print("✓ 邮件发送成功")
                
            else:
                print(f"❌ 收件人验证失败: 错误码 {code}")
                analyze_error_code(code, message)
                
        except smtplib.SMTPRecipientsRefused as e:
            print(f"❌ 收件人被拒绝: {e}")
            for email, (code, msg) in e.recipients.items():
                print(f"  邮箱: {email}")
                print(f"  错误码: {code}")
                print(f"  错误信息: {msg}")
                analyze_error_code(code, msg)
                
        except smtplib.SMTPResponseException as e:
            print(f"❌ SMTP响应异常: {e}")
            print(f"  错误码: {e.smtp_code}")
            print(f"  错误信息: {e.smtp_error}")
            analyze_error_code(e.smtp_code, e.smtp_error)
        
        server.quit()
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ 认证失败: {e}")
    except Exception as e:
        print(f"❌ 其他错误: {e}")

def analyze_error_code(code, message):
    """分析SMTP错误码"""
    print(f"\n📋 错误分析:")
    
    if code == 550:
        print("  错误类型: 邮箱不存在或被拒绝")
        print("  可能原因:")
        print("    1. 邮箱地址确实不存在")
        print("    2. 邮箱设置拒收外部邮件")
        print("    3. 反垃圾邮件策略拒收")
        print("    4. 邮箱已被停用")
        
    elif code == 551:
        print("  错误类型: 用户不在本地")
        print("  可能原因:")
        print("    1. 邮箱地址格式错误")
        print("    2. 域名不正确")
        
    elif code == 552:
        print("  错误类型: 邮箱存储空间不足")
        print("  可能原因:")
        print("    1. 收件人邮箱已满")
        print("    2. 附件太大")
        
    elif code == 553:
        print("  错误类型: 邮箱地址格式错误")
        print("  可能原因:")
        print("    1. 邮箱地址语法错误")
        print("    2. 包含非法字符")
        
    elif code == 554:
        print("  错误类型: 邮件被拒绝")
        print("  可能原因:")
        print("    1. 被识别为垃圾邮件")
        print("    2. 发送者信誉问题")
        print("    3. 内容被过滤")
    
    # 分析具体的QQ邮箱错误信息
    if isinstance(message, bytes):
        message = message.decode('utf-8', errors='ignore')
    
    message_str = str(message).lower()
    
    if 'user unknown' in message_str:
        print("  🎯 QQ邮箱反馈: 用户不存在")
        print("  建议: 确认邮箱地址是否正确")
        
    elif 'mailbox unavailable' in message_str:
        print("  🎯 QQ邮箱反馈: 邮箱不可用")
        print("  建议: 邮箱可能被停用或设置拒收")
        
    elif 'anti-spam' in message_str or 'spam' in message_str:
        print("  🎯 QQ邮箱反馈: 反垃圾邮件拦截")
        print("  建议: 优化邮件内容和发送方式")

def test_qq_specific_issues():
    """测试QQ邮箱特定问题"""
    print("\n" + "=" * 60)
    print("QQ邮箱特定问题检测")
    print("=" * 60)
    
    sender_email = input("请输入发送者邮箱: ").strip()
    if not sender_email.endswith('@qq.com'):
        print("❌ 请输入QQ邮箱地址")
        return
    
    print("\n请输入要测试的收件人邮箱（一行一个）:")
    print("输入空行结束")
    
    recipients = []
    while True:
        email = input().strip()
        if not email:
            break
        if '@' in email:
            recipients.append(email)
    
    if not recipients:
        print("❌ 没有输入收件人邮箱")
        return
    
    print(f"\n将测试 {len(recipients)} 个收件人邮箱:")
    for email in recipients:
        print(f"  - {email}")
    
    confirm = input("\n确认开始测试吗？(y/n): ").strip().lower()
    if confirm != 'y':
        return
    
    # 逐个测试
    for i, recipient in enumerate(recipients, 1):
        print(f"\n{'='*60}")
        print(f"测试 {i}/{len(recipients)}: {recipient}")
        print(f"{'='*60}")
        test_smtp_detailed(sender_email, recipient)
        
        if i < len(recipients):
            input("\n按回车键继续下一个测试...")

def check_qq_mail_settings():
    """检查QQ邮箱设置建议"""
    print("\n" + "=" * 60)
    print("QQ邮箱设置检查建议")
    print("=" * 60)
    
    print("\n📋 发送者邮箱设置检查:")
    print("1. 登录QQ邮箱网页版")
    print("2. 检查 设置 -> 账户 -> POP3/IMAP/SMTP服务")
    print("3. 确保SMTP服务已开启")
    print("4. 确认授权码正确")
    
    print("\n📋 收件人邮箱可能的问题:")
    print("1. 邮箱设置了拒收外部邮件")
    print("2. 反垃圾邮件设置过于严格")
    print("3. 邮箱已被停用或注销")
    print("4. 邮箱存储空间已满")
    
    print("\n📋 解决建议:")
    print("1. 让收件人检查邮箱设置")
    print("2. 将发送者邮箱加入白名单")
    print("3. 检查垃圾邮件文件夹")
    print("4. 尝试从其他邮箱发送测试")

def main():
    """主函数"""
    print("QQ邮箱深度诊断工具")
    print("=" * 60)
    print("此工具将深入分析QQ邮箱发送失败的具体原因")
    print("=" * 60)
    
    while True:
        print("\n请选择诊断类型:")
        print("1. 详细SMTP测试")
        print("2. QQ邮箱设置检查建议")
        print("3. 退出")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == '1':
            test_qq_specific_issues()
        elif choice == '2':
            check_qq_mail_settings()
        elif choice == '3':
            print("诊断结束")
            break
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n诊断已取消")
    except Exception as e:
        print(f"\n程序出错: {str(e)}")
    
    input("\n按回车键退出...")
