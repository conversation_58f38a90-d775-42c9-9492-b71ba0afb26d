#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复定时任务管理器错误
解决 'ScheduleManager' object has no attribute 'get_all_scheduled_tasks' 问题
"""

import os

def main():
    """主修复函数"""
    print("🔧 修复定时任务管理器错误")
    print("="*50)
    
    # 检查文件是否存在
    if not os.path.exists('schedule_manager.py'):
        print("❌ schedule_manager.py 文件不存在")
        return False
    
    # 读取原文件
    with open('schedule_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否已有get_all_scheduled_tasks方法
    if 'def get_all_scheduled_tasks(' in content:
        print("✅ get_all_scheduled_tasks方法已存在")
        return True
    
    print("🔧 添加缺失的方法...")
    
    # 要添加的方法
    additional_methods = '''
    def get_all_scheduled_tasks(self) -> List[ScheduledTask]:
        """获取所有定时任务 - 修复方法名称"""
        return self.get_all_tasks()
    
    def get_scheduled_tasks_by_status(self, status: str) -> List[ScheduledTask]:
        """根据状态获取定时任务"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT * FROM scheduled_tasks 
                    WHERE status = ? 
                    ORDER BY scheduled_time DESC
                """, (status,))
                rows = cursor.fetchall()
                
                return [self._row_to_task(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"根据状态获取任务失败: {str(e)}")
            return []
    
    def get_task_by_id(self, task_id: str) -> ScheduledTask:
        """根据ID获取任务"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM scheduled_tasks WHERE id = ?', (task_id,))
                row = cursor.fetchone()
                
                if row:
                    return self._row_to_task(row)
                return None
                
        except Exception as e:
            self.logger.error(f"根据ID获取任务失败: {str(e)}")
            return None
'''
    
    # 找到类的结束位置并添加方法
    lines = content.split('\n')
    
    # 找到最后一个方法的位置
    last_method_line = -1
    for i, line in enumerate(lines):
        if line.strip().startswith('def ') and 'self' in line:
            last_method_line = i
    
    if last_method_line > 0:
        # 找到该方法的结束位置
        insert_position = last_method_line
        indent_level = 0
        
        # 找到方法的缩进级别
        for char in lines[last_method_line]:
            if char == ' ':
                indent_level += 1
            else:
                break
        
        # 找到方法结束位置
        for i in range(last_method_line + 1, len(lines)):
            line = lines[i]
            if line.strip() == '':
                continue
            
            # 计算当前行的缩进
            current_indent = 0
            for char in line:
                if char == ' ':
                    current_indent += 1
                else:
                    break
            
            # 如果缩进级别相同或更少，说明方法结束了
            if current_indent <= indent_level and line.strip():
                insert_position = i
                break
        else:
            # 如果没找到，就在文件末尾添加
            insert_position = len(lines)
        
        # 插入新方法
        lines.insert(insert_position, additional_methods)
        
        # 写回文件
        with open('schedule_manager.py', 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        print("✅ 已添加缺失的方法到schedule_manager.py")
        return True
    
    print("❌ 无法找到合适的插入位置")
    return False

def test_fix():
    """测试修复结果"""
    print("\n🧪 测试修复结果...")
    
    try:
        # 尝试导入并测试
        import sys
        if '.' not in sys.path:
            sys.path.insert(0, '.')
        
        from schedule_manager import ScheduleManager
        
        # 创建实例
        manager = ScheduleManager()
        
        # 测试方法是否存在
        if hasattr(manager, 'get_all_scheduled_tasks'):
            print("✅ get_all_scheduled_tasks 方法存在")
        else:
            print("❌ get_all_scheduled_tasks 方法不存在")
            return False
        
        if hasattr(manager, 'get_scheduled_tasks_by_status'):
            print("✅ get_scheduled_tasks_by_status 方法存在")
        else:
            print("❌ get_scheduled_tasks_by_status 方法不存在")
            return False
        
        # 尝试调用方法
        try:
            tasks = manager.get_all_scheduled_tasks()
            print(f"✅ 成功获取任务列表: {len(tasks)} 个任务")
        except Exception as e:
            print(f"❌ 调用方法失败: {str(e)}")
            return False
        
        print("🎉 修复测试通过!")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def create_backup():
    """创建备份"""
    print("\n💾 创建备份...")
    
    if os.path.exists('schedule_manager.py'):
        import shutil
        backup_name = 'schedule_manager_backup.py'
        shutil.copy2('schedule_manager.py', backup_name)
        print(f"✅ 已创建备份: {backup_name}")
        return True
    
    print("❌ 原文件不存在，无法创建备份")
    return False

if __name__ == "__main__":
    print("🛠️ 定时任务管理器修复工具")
    print("="*50)
    
    # 1. 创建备份
    create_backup()
    
    # 2. 执行修复
    if main():
        # 3. 测试修复结果
        test_fix()
        
        print("\n🎊 修复完成!")
        print("💡 现在可以重启2.0系统，定时任务功能应该正常工作了")
    else:
        print("\n❌ 修复失败!")
        print("💡 请检查schedule_manager.py文件是否存在且可写入")
