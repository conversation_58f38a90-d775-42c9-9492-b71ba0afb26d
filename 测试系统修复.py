#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试系统修复 - 验证数据库锁定、自动回复识别和应急模式修复
"""

import tkinter as tk
import time
import threading
from gui_main import EmailSenderGUI
from email_receiver import EmailReceiver
from qq_email_anti_spam import QQEmailAntiSpamManager

def test_database_lock_fix():
    """测试数据库锁定修复"""
    print("🔒 测试数据库锁定修复")
    print("=" * 50)
    
    try:
        test_email = "<EMAIL>"
        receiver = EmailReceiver(test_email, "test_password")
        
        # 模拟并发数据库操作
        def concurrent_update(thread_id):
            for i in range(5):
                try:
                    receiver.update_recipient_status(
                        f"recipient{i}@test.com", 
                        test_email, 
                        'auto_reply'
                    )
                    print(f"  线程{thread_id}: 更新{i+1}成功")
                    time.sleep(0.1)
                except Exception as e:
                    print(f"  线程{thread_id}: 更新{i+1}失败: {str(e)}")
        
        # 启动多个线程模拟并发
        threads = []
        for i in range(3):
            thread = threading.Thread(target=concurrent_update, args=(i+1,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        print("✅ 数据库并发测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库锁定测试失败: {str(e)}")
        return False

def test_auto_reply_recognition():
    """测试自动回复识别修复"""
    print("\n🔍 测试自动回复识别修复")
    print("=" * 50)
    
    try:
        test_email = "<EMAIL>"
        receiver = EmailReceiver(test_email, "test_password")
        
        # 测试用例
        test_cases = [
            {
                'name': '真正的QQ自动回复',
                'email': {
                    'subject': 'QQ邮箱自动回复',
                    'body': '谢谢您的邮件，我目前不在办公室。',
                    'from': '<EMAIL>',
                    'headers': {}
                },
                'expected': ('auto_reply', True)
            },
            {
                'name': '普通QQ邮件（不是自动回复）',
                'email': {
                    'subject': '工作邮件',
                    'body': '请查看附件中的文档。',
                    'from': '<EMAIL>',
                    'headers': {}
                },
                'expected': ('normal', False)
            },
            {
                'name': '退信邮件',
                'email': {
                    'subject': 'Mail delivery failed',
                    'body': 'The following message could not be delivered',
                    'from': '<EMAIL>',
                    'headers': {}
                },
                'expected': ('bounce', True)
            },
            {
                'name': '英文自动回复',
                'email': {
                    'subject': 'Auto Reply: Out of Office',
                    'body': 'I am currently out of office and will return on Monday.',
                    'from': '<EMAIL>',
                    'headers': {}
                },
                'expected': ('auto_reply', True)
            }
        ]
        
        success_count = 0
        for test_case in test_cases:
            is_auto, reply_type = receiver.is_auto_reply(test_case['email'])
            expected_type, expected_is_auto = test_case['expected']
            
            if is_auto == expected_is_auto and reply_type == expected_type:
                print(f"  ✅ {test_case['name']}: {reply_type}")
                success_count += 1
            else:
                print(f"  ❌ {test_case['name']}: 期望 {expected_type}, 实际 {reply_type}")
        
        print(f"\n识别准确率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
        
        if success_count == len(test_cases):
            print("✅ 自动回复识别测试完全成功")
            return True
        else:
            print("⚠️ 自动回复识别测试部分成功")
            return False
        
    except Exception as e:
        print(f"❌ 自动回复识别测试失败: {str(e)}")
        return False

def test_emergency_mode_fix():
    """测试应急模式修复"""
    print("\n🆘 测试应急模式修复")
    print("=" * 50)
    
    try:
        test_email = "<EMAIL>"
        qq_manager = QQEmailAntiSpamManager()
        
        print("  测试应急模式激活...")
        
        # 模拟应急情况
        emergency_info = {
            'should_trigger': True,
            'consecutive_no_reply': 5,
            'reason': '测试应急模式激活'
        }
        
        # 记录开始时间
        start_time = time.time()
        
        # 激活应急模式（应该不会阻塞）
        qq_manager._activate_emergency_mode(test_email, emergency_info)
        
        # 检查是否立即返回（不阻塞）
        elapsed_time = time.time() - start_time
        
        if elapsed_time < 1.0:  # 应该在1秒内完成
            print(f"  ✅ 应急模式激活不阻塞: {elapsed_time:.2f}秒")
        else:
            print(f"  ❌ 应急模式激活阻塞: {elapsed_time:.2f}秒")
            return False
        
        # 测试连续无回复计数
        print("  测试连续无回复计数...")
        
        # 重置计数
        qq_manager.reset_consecutive_no_reply(test_email)
        
        # 增加计数
        for i in range(3):
            count = qq_manager.increment_consecutive_no_reply(test_email)
            print(f"    计数增加到: {count}")
        
        print("✅ 应急模式测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 应急模式测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_stability():
    """测试GUI稳定性"""
    print("\n🖥️ 测试GUI稳定性")
    print("=" * 50)
    
    try:
        # 创建GUI
        root = tk.Tk()
        app = EmailSenderGUI(root)
        
        # 设置测试邮箱
        test_email = "<EMAIL>"
        app.sender_email.delete(0, tk.END)
        app.sender_email.insert(0, test_email)
        
        # 设置收件人
        app.recipient_emails.delete(1.0, tk.END)
        app.recipient_emails.insert(1.0, test_email)
        
        print("  ✅ GUI创建成功")
        
        # 测试一键启用功能（应该不会崩溃）
        print("  测试一键启用功能...")
        
        try:
            # 模拟启用过程
            app._init_all_components(test_email)
            app._enable_auto_reply_monitoring(test_email)
            app._enable_quality_database(test_email)
            app._enable_anti_spam(test_email)
            app._enable_qq_emergency(test_email)
            
            print("  ✅ 一键启用功能正常")
            
        except Exception as e:
            print(f"  ❌ 一键启用功能异常: {str(e)}")
            return False
        
        # 关闭GUI
        root.destroy()
        
        print("✅ GUI稳定性测试完成")
        return True
        
    except Exception as e:
        print(f"❌ GUI稳定性测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始系统修复验证测试")
    print("=" * 80)
    
    # 执行各项测试
    tests = [
        ("数据库锁定修复", test_database_lock_fix),
        ("自动回复识别修复", test_auto_reply_recognition),
        ("应急模式修复", test_emergency_mode_fix),
        ("GUI稳定性", test_gui_stability)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {str(e)}")
            results.append((test_name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("🎯 测试结果总结")
    print("=" * 80)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
    
    if success_count == len(results):
        print("\n🎉 所有修复验证成功！")
        print("✅ 数据库锁定问题已修复")
        print("✅ 自动回复识别逻辑已改进")
        print("✅ 应急模式不再阻塞系统")
        print("✅ GUI运行稳定")
        print("\n💡 现在系统应该可以正常运行，不会再出现崩溃问题！")
        return True
    else:
        print("\n⚠️ 部分修复需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
