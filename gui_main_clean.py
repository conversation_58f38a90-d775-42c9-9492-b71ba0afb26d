#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统GUI - 清洁版本（基于完整功能版）
无拼写警告，包含所有功能
"""

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件系统GUI - 完整功能3.0版本
包含所有2.0系统的功能：撤回、自动回复监控、质量数据库、应急管理等
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import datetime
import json
import time
import math
import re
import hashlib
from typing import List, Dict, Optional

class EmailSenderGUI:
    """邮件发送系统GUI - 完整功能3.0版本"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.setup_variables()
        self.setup_theme()
        self.create_interface()
        self.setup_events()

        # 加载保存的授权码
        self.load_auth_codes()

        # 延迟初始化，确保所有组件都已创建
        self.root.after(100, self.initialize_system)
        
    def setup_window(self):
        """设置窗口基本属性"""
        self.root.title("📧 智能邮件系统 v3.0 - 完整功能版")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#f8fafc')
        
        # 窗口居中
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - 1600) // 2
        y = (screen_height - 1000) // 2
        self.root.geometry(f"1600x1000+{x}+{y}")
        
        # 设置最小尺寸
        self.root.minsize(1400, 900)
        
    def setup_variables(self):
        """初始化变量"""
        # 基础变量
        self.send_mode = tk.StringVar(value="standard")
        self.add_personalization = tk.BooleanVar(value=True)
        self.auto_start_reply_monitoring = tk.BooleanVar(value=False)
        self.auto_start_queue_system = tk.BooleanVar(value=False)  # 新增：自动开启队列系统
        self.auto_queue_mode = tk.BooleanVar(value=True)
        
        # 状态变量
        self.is_sending = False
        self.should_stop = False
        self.should_pause = False
        self.sent_emails = []  # 发送历史记录
        self.email_queue = []
        self.attachments = []  # 存储附件路径
        self.auth_codes = {}  # 存储授权码
        self.auth_codes_file = "auth_codes.json"  # 授权码保存文件

        # 高级功能变量
        self.monitoring_active = False
        self.emergency_active = False
        self.deep_coordination_active = False

        # 监控动画变量
        self.monitor_angle = 0
        self.monitor_animation_active = True
        
    def setup_theme(self):
        """设置主题样式"""
        self.style = ttk.Style()
        
        # 使用现代主题
        try:
            self.style.theme_use('clam')
        except:
            self.style.theme_use('default')
        
        # 颜色方案
        self.colors = {
            'primary': '#3b82f6',
            'success': '#10b981',
            'warning': '#f59e0b',
            'danger': '#ef4444',
            'light': '#f8fafc',
            'dark': '#374151',
            'white': '#ffffff',
            'purple': '#8b5cf6',
            'indigo': '#6366f1'
        }
        
        # 配置按钮样式 - 确保文字可见
        self.style.configure('Primary.TButton',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           foreground='white',
                           background=self.colors['primary'],
                           relief='raised',
                           borderwidth=1)

        self.style.configure('Success.TButton',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           foreground='white',
                           background=self.colors['success'],
                           relief='raised',
                           borderwidth=1)

        self.style.configure('Warning.TButton',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           foreground='white',
                           background=self.colors['warning'],
                           relief='raised',
                           borderwidth=1)

        self.style.configure('Danger.TButton',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           foreground='white',
                           background=self.colors['danger'],
                           relief='raised',
                           borderwidth=1)
        
        self.style.configure('Purple.TButton',
                           font=('Microsoft YaHei UI', 9, 'bold'),
                           foreground='white',
                           background=self.colors['purple'])
        
        self.style.configure('Indigo.TButton',
                           font=('Microsoft YaHei UI', 9, 'bold'),
                           foreground='white',
                           background=self.colors['indigo'])

        # 配置紧凑按钮样式 - 确保汉字显示完整
        self.style.configure('Compact.Primary.TButton',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           foreground='white',
                           background=self.colors['primary'],
                           padding=(15, 8),
                           width=10)

        self.style.configure('Compact.Success.TButton',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           foreground='white',
                           background=self.colors['success'],
                           padding=(15, 8),
                           width=10)

        self.style.configure('Compact.Warning.TButton',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           foreground='white',
                           background=self.colors['warning'],
                           padding=(15, 8),
                           width=10)

        self.style.configure('Compact.Danger.TButton',
                           font=('Microsoft YaHei UI', 10, 'bold'),
                           foreground='white',
                           background=self.colors['danger'],
                           padding=(15, 8),
                           width=10)

        # 配置标签框样式
        self.style.configure('Modern.TLabelframe',
                           background=self.colors['light'])

        self.style.configure('Modern.TLabelframe.Label',
                           font=('Microsoft YaHei UI', 11, 'bold'),
                           foreground=self.colors['primary'],
                           background=self.colors['light'])

        # 配置复选框样式
        self.style.configure('Modern.TCheckbutton',
                           font=('Microsoft YaHei UI', 9),
                           background=self.colors['light'])
        
    def create_interface(self):
        """创建主界面"""
        # 主容器
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)
        
        # 创建标题
        self.create_header(main_frame)
        
        # 创建四栏布局（增加一栏用于高级功能）
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
        
        # 左侧配置区 (30%)
        left_frame = ttk.Frame(content_frame)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 中左操作区 (25%)
        middle_left_frame = ttk.Frame(content_frame, width=320)
        middle_left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)
        middle_left_frame.pack_propagate(False)
        
        # 中右管理区 (25%)
        middle_right_frame = ttk.Frame(content_frame, width=320)
        middle_right_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)
        middle_right_frame.pack_propagate(False)
        
        # 右侧高级区 (20%)
        right_frame = ttk.Frame(content_frame, width=280)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.pack_propagate(False)
        
        # 创建各区域内容
        self.create_left_content(left_frame)
        self.create_middle_left_content(middle_left_frame)
        self.create_middle_right_content(middle_right_frame)
        self.create_right_content(right_frame)
        
    def create_header(self, parent):
        """创建标题区域"""
        header_frame = ttk.Frame(parent)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # 主标题
        title_label = ttk.Label(header_frame,
                               text="📧 智能邮件系统 v3.0",
                               font=('Microsoft YaHei UI', 20, 'bold'),
                               foreground=self.colors['primary'])
        title_label.pack(side=tk.LEFT)
        
        # 功能标签
        features_frame = ttk.Frame(header_frame)
        features_frame.pack(side=tk.LEFT, padx=(20, 0))
        
        feature_labels = [
            ("📤 发送", self.colors['primary']),
            ("📬 监控", self.colors['success']),
            ("📊 数据库", self.colors['warning']),
            ("🛡️ 防护", self.colors['danger']),
            ("🔄 撤回", self.colors['purple'])
        ]
        
        for text, color in feature_labels:
            ttk.Label(features_frame, text=text,
                     font=('Microsoft YaHei UI', 9, 'bold'),
                     foreground=color).pack(side=tk.LEFT, padx=5)
        
        # 版本信息
        version_frame = ttk.Frame(header_frame)
        version_frame.pack(side=tk.RIGHT)
        
        version_label = ttk.Label(version_frame,
                                 text="完整功能版",
                                 font=('Microsoft YaHei UI', 12, 'bold'),
                                 foreground=self.colors['dark'])
        version_label.pack(anchor=tk.E)
        
        # 状态指示器
        self.status_indicator = ttk.Label(version_frame,
                                         text="🟡 正在初始化...",
                                         font=('Microsoft YaHei UI', 9),
                                         foreground=self.colors['warning'])
        self.status_indicator.pack(anchor=tk.E, pady=(5, 0))
        
    def create_left_content(self, parent):
        """创建左侧内容区"""
        # 邮件配置
        self.create_email_config(parent)
        # 邮件内容
        self.create_email_content(parent)
        # 操作日志
        self.create_log_section(parent)
        
    def create_middle_left_content(self, parent):
        """创建中左内容区"""
        # 快速操作
        self.create_quick_actions(parent)
        # 发送控制（包含撤回功能）
        self.create_send_control(parent)
        # 队列管理
        self.create_queue_section(parent)
        
    def create_middle_right_content(self, parent):
        """创建中右内容区"""
        # 附件管理
        self.create_attachments(parent)
        # 历史记录管理
        self.create_history_management(parent)
        # 系统监控
        self.create_monitor_section(parent)
        
    def create_right_content(self, parent):
        """创建右侧内容区"""
        # 高级功能
        self.create_advanced_features(parent)
        # 深度协调
        self.create_deep_coordination(parent)
        # 系统状态
        self.create_status_section(parent)

    def create_email_config(self, parent):
        """创建邮件配置区域"""
        config_frame = ttk.LabelFrame(parent, text="📧 邮件配置",
                                     style='Modern.TLabelframe', padding="15")
        config_frame.pack(fill=tk.X, pady=(0, 15))

        # 发送者邮箱
        sender_frame = ttk.Frame(config_frame)
        sender_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(sender_frame, text="发送者邮箱:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        self.sender_email = ttk.Entry(sender_frame, font=('Microsoft YaHei UI', 10))
        self.sender_email.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        self.sender_email.insert(0, "@qq.com")

        # 授权码设置
        auth_frame = ttk.Frame(config_frame)
        auth_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(auth_frame, text="授权码:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        self.auth_code_entry = ttk.Entry(auth_frame, font=('Microsoft YaHei UI', 10), show="*")
        self.auth_code_entry.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        # 收件人邮箱
        recipient_frame = ttk.Frame(config_frame)
        recipient_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        ttk.Label(recipient_frame, text="收件人邮箱:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor=tk.W)

        self.recipient_emails = scrolledtext.ScrolledText(recipient_frame,
                                                         width=60, height=4,
                                                         font=('Microsoft YaHei UI', 10))
        self.recipient_emails.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # 发送模式
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill=tk.X, pady=(15, 0))

        ttk.Label(mode_frame, text="发送模式:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)

        mode_buttons = ttk.Frame(mode_frame)
        mode_buttons.pack(side=tk.RIGHT)

        ttk.Radiobutton(mode_buttons, text="🚀 快速", variable=self.send_mode,
                       value="fast").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_buttons, text="⚡ 标准", variable=self.send_mode,
                       value="standard").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(mode_buttons, text="🛡️ 安全", variable=self.send_mode,
                       value="safe").pack(side=tk.LEFT, padx=5)

    def create_email_content(self, parent):
        """创建邮件内容区域"""
        content_frame = ttk.LabelFrame(parent, text="✍️ 邮件内容",
                                      style='Modern.TLabelframe', padding="15")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 邮件主题
        subject_frame = ttk.Frame(content_frame)
        subject_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(subject_frame, text="邮件主题:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(side=tk.LEFT)
        self.subject = ttk.Entry(subject_frame, font=('Microsoft YaHei UI', 10))
        self.subject.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        # 邮件正文
        ttk.Label(content_frame, text="邮件正文:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor=tk.W)

        self.body = scrolledtext.ScrolledText(content_frame, width=50, height=8,
                                             font=('Microsoft YaHei UI', 11))
        self.body.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # 选项
        options_frame = ttk.Frame(content_frame)
        options_frame.pack(fill=tk.X, pady=(10, 0))

        # 第一行选项
        options_row1 = ttk.Frame(options_frame)
        options_row1.pack(fill=tk.X, pady=(0, 5))

        ttk.Checkbutton(options_row1, text="📝 添加时间戳",
                       variable=self.add_personalization).pack(side=tk.LEFT)

        ttk.Checkbutton(options_row1, text="📡 自动监控",
                       variable=self.auto_start_reply_monitoring).pack(side=tk.RIGHT)

        # 第二行选项
        options_row2 = ttk.Frame(options_frame)
        options_row2.pack(fill=tk.X)

        ttk.Checkbutton(options_row2, text="📬 自动队列",
                       variable=self.auto_start_queue_system,
                       command=self.on_auto_queue_system_changed).pack(side=tk.LEFT)

        # 自动队列说明标签
        self.auto_queue_system_label = ttk.Label(options_row2,
                                               text="✨ 发送完成后自动开启队列系统",
                                               font=('Microsoft YaHei UI', 8),
                                               foreground=self.colors['success'])
        self.auto_queue_system_label.pack(side=tk.RIGHT)

    def create_log_section(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="📋 操作日志",
                                  style='Modern.TLabelframe', padding="10")
        log_frame.pack(fill=tk.BOTH, expand=True)

        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(log_toolbar, text="实时日志:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(side=tk.LEFT)

        log_buttons = ttk.Frame(log_toolbar)
        log_buttons.pack(side=tk.RIGHT)

        ttk.Button(log_buttons, text="🧹", command=self.clear_log,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=1)
        ttk.Button(log_buttons, text="💾", command=self.save_log,
                  style='Success.TButton').pack(side=tk.LEFT, padx=1)

        # 日志显示区域
        self.log_text = scrolledtext.ScrolledText(log_frame, width=50, height=8,
                                                 font=('Consolas', 8),
                                                 bg='#1e293b', fg='#e2e8f0')
        self.log_text.pack(fill=tk.BOTH, expand=True)

    def create_quick_actions(self, parent):
        """创建快速操作区域"""
        actions_frame = ttk.LabelFrame(parent, text="⚡ 快速操作",
                                      style='Modern.TLabelframe', padding="12")
        actions_frame.pack(fill=tk.X, pady=(0, 10))

        # 主要操作按钮
        main_actions = [
            ("🚀 发送邮件", self.send_email, 'Primary.TButton'),
            ("⏸️ 暂停发送", self.pause_sending, 'Warning.TButton'),
            ("⏹️ 停止发送", self.stop_sending, 'Danger.TButton'),
            ("▶️ 恢复发送", self.resume_sending, 'Success.TButton'),
            ("🔄 断点继续", self.continue_sending, 'Primary.TButton')
        ]

        for i, (text, command, style) in enumerate(main_actions):
            btn = ttk.Button(actions_frame, text=text, command=command, style=style)
            btn.pack(fill=tk.X, pady=3)

            # 保存按钮引用
            if i == 0:
                self.send_button = btn
            elif i == 1:
                self.pause_button = btn
                btn.configure(state='disabled')
            elif i == 2:
                self.stop_button = btn
                btn.configure(state='disabled')
            elif i == 3:
                self.resume_button = btn
                btn.configure(state='disabled')
            elif i == 4:
                self.continue_button = btn
                btn.configure(state='disabled')

        # 分隔线
        ttk.Separator(actions_frame, orient='horizontal').pack(fill=tk.X, pady=8)

        # 工具操作
        tool_actions = [
            ("🔧 测试连接", self.test_connection),
            ("✅ 验证邮箱", self.validate_emails),
            ("🔍 重复检测", self.check_duplicates),
            ("🧹 清空表单", self.clear_form)
        ]

        for text, command in tool_actions:
            ttk.Button(actions_frame, text=text, command=command,
                      style='Primary.TButton').pack(fill=tk.X, pady=2)

    def create_send_control(self, parent):
        """创建发送控制区域（包含撤回功能）"""
        control_frame = ttk.LabelFrame(parent, text="🔄 发送控制",
                                      style='Modern.TLabelframe', padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 撤回功能
        recall_frame = ttk.Frame(control_frame)
        recall_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(recall_frame, text="邮件撤回:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        recall_buttons = [
            ("📤 发送撤回邮件", self.send_recall_email, 'Purple.TButton'),
            ("📋 查看发送记录", self.show_send_history, 'Primary.TButton'),
            ("🗑️ 清空发送记录", self.clear_send_history, 'Danger.TButton')
        ]

        for text, command, style in recall_buttons:
            btn = ttk.Button(recall_frame, text=text, command=command, style=style)
            btn.pack(fill=tk.X, pady=2)

            # 保存撤回按钮引用
            if "发送撤回邮件" in text:
                self.recall_button = btn
                btn.configure(state='disabled')  # 初始禁用

        # 分隔线
        ttk.Separator(control_frame, orient='horizontal').pack(fill=tk.X, pady=8)

        # 授权码管理
        auth_frame = ttk.Frame(control_frame)
        auth_frame.pack(fill=tk.X)

        ttk.Label(auth_frame, text="授权码管理:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        auth_buttons = [
            ("💾 保存授权码", self.save_auth_code, 'Success.TButton'),
            ("🔑 管理授权码", self.manage_auth_codes, 'Primary.TButton')
        ]

        for text, command, style in auth_buttons:
            ttk.Button(auth_frame, text=text, command=command, style=style).pack(fill=tk.X, pady=2)

    def create_queue_section(self, parent):
        """创建队列管理区域 - 优化版本"""
        # 扩大队列方框，增加padding和高度
        queue_frame = ttk.LabelFrame(parent, text="📬 邮件队列系统",
                                    style='Modern.TLabelframe', padding="15")
        queue_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 队列状态显示区域 - 更大的显示空间
        status_frame = ttk.Frame(queue_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(status_frame, text="📊 队列状态:",
                 font=('Microsoft YaHei UI', 10, 'bold')).pack(anchor=tk.W)

        # 状态指示器 - 更清晰的布局
        status_indicators = ttk.Frame(status_frame)
        status_indicators.pack(fill=tk.X, pady=(5, 0))

        self.queue_task_count = ttk.Label(status_indicators, text="📊 任务: 0个",
                                         font=('Microsoft YaHei UI', 9),
                                         foreground=self.colors['primary'])
        self.queue_task_count.pack(side=tk.LEFT)

        self.queue_progress = ttk.Label(status_indicators, text="📈 进度: 0%",
                                       font=('Microsoft YaHei UI', 9),
                                       foreground=self.colors['success'])
        self.queue_progress.pack(side=tk.LEFT, padx=(15, 0))

        self.queue_speed = ttk.Label(status_indicators, text="⚡ 速度: 0封/分",
                                    font=('Microsoft YaHei UI', 9),
                                    foreground=self.colors['warning'])
        self.queue_speed.pack(side=tk.LEFT, padx=(15, 0))

        # 分隔线
        ttk.Separator(queue_frame, orient='horizontal').pack(fill=tk.X, pady=(8, 8))

        # 队列操作按钮 - 优化布局确保汉字显示
        ops_frame = ttk.Frame(queue_frame)
        ops_frame.pack(fill=tk.X, pady=(0, 8))

        # 第一行按钮 - 主要操作
        row1_frame = ttk.Frame(ops_frame)
        row1_frame.pack(fill=tk.X, pady=(0, 5))

        queue_ops_row1 = [
            ("添加任务", self.add_to_queue, 'Primary.TButton'),
            ("队列管理", self.open_queue_system, 'Primary.TButton'),
            ("开始发送", self.start_queue_sending, 'Success.TButton')
        ]

        for text, command, style in queue_ops_row1:
            btn = ttk.Button(row1_frame, text=text, command=command, style=style)
            btn.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)

            # 保存按钮引用
            if "开始发送" in text:
                self.start_queue_button = btn
                btn.configure(state='disabled')

        # 第二行按钮 - 控制操作
        row2_frame = ttk.Frame(ops_frame)
        row2_frame.pack(fill=tk.X)

        queue_ops_row2 = [
            ("暂停队列", self.pause_queue_sending, 'Warning.TButton'),
            ("继续发送", self.resume_queue_sending, 'Success.TButton'),
            ("清空队列", self.clear_queue, 'Danger.TButton')
        ]

        for text, command, style in queue_ops_row2:
            btn = ttk.Button(row2_frame, text=text, command=command, style=style)
            btn.pack(side=tk.LEFT, padx=(0, 5), fill=tk.X, expand=True)

            # 保存按钮引用
            if "暂停队列" in text:
                self.pause_queue_button = btn
                btn.configure(state='disabled')
            elif "继续发送" in text:
                self.resume_queue_button = btn
                btn.configure(state='disabled')

        # 分隔线
        ttk.Separator(queue_frame, orient='horizontal').pack(fill=tk.X, pady=(8, 8))

        # 队列任务列表显示区域 - 滚动列表
        queue_list_frame = ttk.LabelFrame(queue_frame, text="📋 队列任务列表", padding="5")
        queue_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 8))

        # 创建滚动文本框显示队列任务
        self.queue_list_text = scrolledtext.ScrolledText(
            queue_list_frame,
            width=40,
            height=6,  # 减小高度，强制出现滚动条
            font=('Consolas', 9),
            wrap=tk.NONE,  # 改为不换行，确保水平滚动也可用
            relief='solid',
            borderwidth=1,
            bg='#f8f9fa',  # 改为浅色背景，更容易看到
            fg='#212529',  # 改为深色文字
            insertbackground='#007bff',
            selectbackground='#e3f2fd',
            state=tk.NORMAL  # 确保可以滚动和编辑
        )
        self.queue_list_text.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # 绑定鼠标滚轮事件，确保滚动功能
        def on_mousewheel(event):
            self.queue_list_text.yview_scroll(int(-1*(event.delta/120)), "units")

        self.queue_list_text.bind("<MouseWheel>", on_mousewheel)
        self.queue_list_text.bind("<Button-4>", lambda e: self.queue_list_text.yview_scroll(-1, "units"))
        self.queue_list_text.bind("<Button-5>", lambda e: self.queue_list_text.yview_scroll(1, "units"))

        # 添加大量测试内容，确保滚动功能可见
        test_content = """📋 队列滚动功能测试
==========================================
🖱️ 滚动测试说明：请使用鼠标滚轮滚动查看更多内容
==========================================
⏳ #01 | 测试邮件1 - 重要通知 | 2人 | 标准 | 10:30:15
✅ #02 | 测试邮件2 - 系统更新 | 3人 | 快速 | 10:31:20
❌ #03 | 测试邮件3 - 紧急维护 | 1人 | 安全 | 10:32:25
📤 #04 | 测试邮件4 - 用户反馈 | 5人 | 标准 | 10:33:30
⏳ #05 | 测试邮件5 - 数据报告 | 2人 | 快速 | 10:34:35
✅ #06 | 测试邮件6 - 月度总结 | 4人 | 安全 | 10:35:40
❌ #07 | 测试邮件7 - 故障通知 | 1人 | 标准 | 10:36:45
📤 #08 | 测试邮件8 - 版本发布 | 6人 | 快速 | 10:37:50
⏳ #09 | 测试邮件9 - 培训通知 | 3人 | 安全 | 10:38:55
✅ #10 | 测试邮件10 - 会议邀请 | 2人 | 标准 | 10:40:00
❌ #11 | 测试邮件11 - 系统维护 | 4人 | 快速 | 10:41:05
📤 #12 | 测试邮件12 - 产品发布 | 1人 | 安全 | 10:42:10
⏳ #13 | 测试邮件13 - 用户调研 | 5人 | 标准 | 10:43:15
✅ #14 | 测试邮件14 - 培训完成 | 3人 | 快速 | 10:44:20
❌ #15 | 测试邮件15 - 年度总结 | 2人 | 安全 | 10:45:25
📤 #16 | 测试邮件16 - 客户服务 | 1人 | 标准 | 10:46:30
⏳ #17 | 测试邮件17 - 系统升级 | 3人 | 快速 | 10:47:35
✅ #18 | 测试邮件18 - 备份完成 | 2人 | 安全 | 10:48:40
❌ #19 | 测试邮件19 - 监控报警 | 4人 | 标准 | 10:49:45
📤 #20 | 测试邮件20 - 审计报告 | 1人 | 快速 | 10:50:50
==========================================
📊 统计: 待发送 8 | 发送中 4 | 已完成 5 | 失败 3

🔍 如果您能看到这行文字，说明滚动功能正常工作！
💡 请尝试：
• 使用鼠标滚轮向上向下滚动
• 拖拽右侧滚动条
• 点击滚动条上下箭头
• 使用键盘上下箭头键（需要先点击文本框）

✅ 滚动功能测试完成！添加任务后会显示实际队列内容。"""

        self.queue_list_text.insert(tk.END, test_content)
        # 滚动到顶部开始
        self.queue_list_text.see(1.0)

        # 分隔线
        ttk.Separator(queue_frame, orient='horizontal').pack(fill=tk.X, pady=(8, 8))

        # 自动模式设置
        auto_frame = ttk.Frame(queue_frame)
        auto_frame.pack(fill=tk.X)

        ttk.Checkbutton(auto_frame, text="🤖 自动队列模式",
                       variable=self.auto_queue_mode,
                       command=self.on_auto_queue_changed,
                       style='Modern.TCheckbutton').pack(anchor=tk.W)

        self.auto_mode_label = ttk.Label(auto_frame,
                                        text="✨ 发送完成后自动启动下一个队列任务",
                                        font=('Microsoft YaHei UI', 8),
                                        foreground=self.colors['success'])
        self.auto_mode_label.pack(anchor=tk.W, pady=(2, 0))

    def create_attachments(self, parent):
        """创建附件管理区域"""
        attachment_frame = ttk.LabelFrame(parent, text="📎 附件管理",
                                         style='Modern.TLabelframe', padding="12")
        attachment_frame.pack(fill=tk.X, pady=(0, 10))

        # 附件列表
        list_frame = ttk.Frame(attachment_frame)
        list_frame.pack(fill=tk.X, pady=(0, 10))

        self.attachment_listbox = tk.Listbox(list_frame, height=3,
                                           font=('Microsoft YaHei UI', 9),
                                           relief='solid', borderwidth=1,
                                           bg='white', fg='#374151',
                                           selectbackground='#dbeafe')
        self.attachment_listbox.pack(fill=tk.X)

        # 附件信息显示
        info_frame = ttk.Frame(attachment_frame)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        self.attachment_info = ttk.Label(info_frame, text="📊 附件: 0 个",
                                        font=('Microsoft YaHei UI', 8),
                                        foreground=self.colors['primary'])
        self.attachment_info.pack(anchor=tk.W)

        # 附件操作按钮
        btn_frame = ttk.Frame(attachment_frame)
        btn_frame.pack(fill=tk.X)

        ttk.Button(btn_frame, text="📁 添加", command=self.add_attachment,
                  style='Success.TButton').pack(side=tk.LEFT, padx=(0, 3), fill=tk.X, expand=True)
        ttk.Button(btn_frame, text="🗑️ 删除", command=self.remove_attachment,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=1, fill=tk.X, expand=True)
        ttk.Button(btn_frame, text="🧹 清空", command=self.clear_attachments,
                  style='Danger.TButton').pack(side=tk.LEFT, padx=(3, 0), fill=tk.X, expand=True)

    def create_history_management(self, parent):
        """创建历史记录管理区域"""
        history_frame = ttk.LabelFrame(parent, text="📚 历史记录",
                                      style='Modern.TLabelframe', padding="10")
        history_frame.pack(fill=tk.X, pady=(0, 10))

        # 历史统计
        stats_frame = ttk.Frame(history_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(stats_frame, text="发送统计:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        self.history_stats = ttk.Label(stats_frame, text="📊 总计: 0 封",
                                      font=('Microsoft YaHei UI', 8),
                                      foreground=self.colors['primary'])
        self.history_stats.pack(anchor=tk.W, pady=(2, 0))

        # 历史操作按钮
        history_ops = [
            ("📋 查看历史", self.view_email_history, 'Primary.TButton'),
            ("📊 统计分析", self.show_statistics, 'Success.TButton'),
            ("📤 导出记录", self.export_history, 'Warning.TButton'),
            ("🔍 智能检索", self.open_smart_search, 'Indigo.TButton')
        ]

        for text, command, style in history_ops:
            ttk.Button(history_frame, text=text, command=command, style=style).pack(fill=tk.X, pady=2)

    def create_monitor_section(self, parent):
        """创建系统监控区域"""
        monitor_frame = ttk.LabelFrame(parent, text="🏛️ 系统监控",
                                      style='Modern.TLabelframe', padding="8")
        monitor_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建监控Canvas
        self.monitor_canvas = tk.Canvas(monitor_frame, width=300, height=120,
                                       bg='#2d1810', highlightthickness=0)
        self.monitor_canvas.pack(fill=tk.X)

        # 绘制监控装饰
        self.draw_monitor_decoration()

        # 控制面板
        control_frame = ttk.Frame(monitor_frame)
        control_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(control_frame, text="🐉 测试", command=self.test_monitor,
                  style='Warning.TButton').pack(side=tk.LEFT, padx=2)
        ttk.Button(control_frame, text="🔄 重置", command=self.reset_monitor,
                  style='Primary.TButton').pack(side=tk.LEFT, padx=2)

    def create_advanced_features(self, parent):
        """创建高级功能区域"""
        advanced_frame = ttk.LabelFrame(parent, text="🔧 高级功能",
                                       style='Modern.TLabelframe', padding="10")
        advanced_frame.pack(fill=tk.X, pady=(0, 10))

        # 自动回复监控
        monitor_section = ttk.Frame(advanced_frame)
        monitor_section.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(monitor_section, text="📬 自动回复监控:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        monitor_buttons = [
            ("📡 开始监控", self.start_reply_monitoring, 'Success.TButton'),
            ("⏹️ 停止监控", self.stop_reply_monitoring, 'Danger.TButton'),
            ("📊 监控设置", self.configure_monitoring, 'Primary.TButton')
        ]

        for text, command, style in monitor_buttons:
            btn = ttk.Button(monitor_section, text=text, command=command, style=style)
            btn.pack(fill=tk.X, pady=1)

            if "开始监控" in text:
                self.start_monitor_button = btn
            elif "停止监控" in text:
                self.stop_monitor_button = btn
                btn.configure(state='disabled')

        # 分隔线
        ttk.Separator(advanced_frame, orient='horizontal').pack(fill=tk.X, pady=8)

        # 质量数据库
        quality_section = ttk.Frame(advanced_frame)
        quality_section.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(quality_section, text="📊 质量数据库:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        quality_buttons = [
            ("📊 质量管理", self.open_quality_manager, 'Success.TButton'),
            ("📈 批次管理", self.open_batch_manager, 'Primary.TButton'),
            ("📤 导入主系统", self.import_to_main, 'Warning.TButton'),
            ("🔗 系统集成", self.open_system_integration, 'Info.TButton')
        ]

        for text, command, style in quality_buttons:
            ttk.Button(quality_section, text=text, command=command, style=style).pack(fill=tk.X, pady=1)

        # 分隔线
        ttk.Separator(advanced_frame, orient='horizontal').pack(fill=tk.X, pady=8)

        # 安全防护
        security_section = ttk.Frame(advanced_frame)
        security_section.pack(fill=tk.X)

        ttk.Label(security_section, text="🛡️ 安全防护:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        security_buttons = [
            ("🛡️ 反垃圾管理", self.open_anti_spam, 'Warning.TButton'),
            ("🆘 应急管理", self.open_emergency_manager, 'Danger.TButton'),
            ("🔍 重复检测", self.open_duplicate_detection, 'Indigo.TButton')
        ]

        for text, command, style in security_buttons:
            ttk.Button(security_section, text=text, command=command, style=style).pack(fill=tk.X, pady=1)



    def create_deep_coordination(self, parent):
        """创建深度协调区域"""
        coord_frame = ttk.LabelFrame(parent, text="🧠 深度协调",
                                    style='Modern.TLabelframe', padding="10")
        coord_frame.pack(fill=tk.X, pady=(0, 10))

        # 协调状态
        status_frame = ttk.Frame(coord_frame)
        status_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(status_frame, text="协调状态:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        self.coordination_status = ttk.Label(status_frame, text="🔧 未激活",
                                           font=('Microsoft YaHei UI', 8),
                                           foreground=self.colors['warning'])
        self.coordination_status.pack(anchor=tk.W, pady=(2, 0))

        # 协调控制
        coord_buttons = [
            ("🚀 激活协调", self.activate_deep_coordination, 'Purple.TButton'),
            ("⏹️ 停止协调", self.deactivate_deep_coordination, 'Danger.TButton'),
            ("⚙️ 协调设置", self.configure_coordination, 'Primary.TButton')
        ]

        for text, command, style in coord_buttons:
            btn = ttk.Button(coord_frame, text=text, command=command, style=style)
            btn.pack(fill=tk.X, pady=2)

            if "激活协调" in text:
                self.activate_coord_button = btn
            elif "停止协调" in text:
                self.deactivate_coord_button = btn
                btn.configure(state='disabled')

    def create_status_section(self, parent):
        """创建状态显示区域"""
        status_frame = ttk.LabelFrame(parent, text="📊 系统状态",
                                     style='Modern.TLabelframe', padding="10")
        status_frame.pack(fill=tk.X, pady=(0, 10))

        # 发送统计
        stats_frame = ttk.Frame(status_frame)
        stats_frame.pack(fill=tk.X, pady=(0, 8))

        ttk.Label(stats_frame, text="发送统计:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        self.stats_labels = {}
        stats_items = [
            ("已发送", "0"),
            ("成功率", "0%"),
            ("队列任务", "0"),
            ("监控状态", "未启动")
        ]

        for label, value in stats_items:
            item_frame = ttk.Frame(stats_frame)
            item_frame.pack(fill=tk.X, pady=1)
            ttk.Label(item_frame, text=f"{label}:",
                     font=('Microsoft YaHei UI', 8)).pack(side=tk.LEFT)
            self.stats_labels[label] = ttk.Label(item_frame, text=value,
                                               font=('Microsoft YaHei UI', 8, 'bold'),
                                               foreground=self.colors['primary'])
            self.stats_labels[label].pack(side=tk.RIGHT)

        # 系统状态
        system_frame = ttk.Frame(status_frame)
        system_frame.pack(fill=tk.X, pady=(8, 0))

        ttk.Label(system_frame, text="系统状态:",
                 font=('Microsoft YaHei UI', 9, 'bold')).pack(anchor=tk.W)

        self.system_status = ttk.Label(system_frame, text="🔧 正在初始化...",
                                      font=('Microsoft YaHei UI', 8),
                                      foreground=self.colors['warning'])
        self.system_status.pack(anchor=tk.W, pady=(2, 0))

    def draw_monitor_decoration(self):
        """绘制监控装饰 - 带动画效果"""
        try:
            canvas = self.monitor_canvas
            cx, cy = 150, 60  # 中心点

            # 清空画布
            canvas.delete("all")

            # 绘制装饰性监控界面
            # 外圈 - 固定
            canvas.create_oval(cx-50, cy-35, cx+50, cy+35,
                              outline='#DAA520', width=2, fill='#8B4513')

            # 内圈 - 固定
            canvas.create_oval(cx-35, cy-25, cx+35, cy+25,
                              outline='#CD853F', width=2, fill='#A0522D')

            # 中心区域 - 固定
            canvas.create_oval(cx-20, cy-15, cx+20, cy+15,
                              outline='#DEB887', width=1, fill='#CD853F')

            # 标题
            canvas.create_text(cx, 15, text='系统监控',
                              font=('Microsoft YaHei UI', 9, 'bold'), fill='#DAA520')

            # 旋转的状态指示点 - 动画效果
            for i in range(8):  # 增加到8个点
                angle = (i * 45 + self.monitor_angle) * math.pi / 180
                x = cx + 30 * math.cos(angle)
                y = cy + 20 * math.sin(angle)

                # 根据位置调整颜色和大小
                brightness = (math.sin(angle) + 1) / 2  # 0-1之间
                if brightness > 0.7:
                    color = '#FFD700'  # 金色
                    size = 3
                elif brightness > 0.4:
                    color = '#FFA500'  # 橙色
                    size = 2
                else:
                    color = '#CD853F'  # 棕色
                    size = 1

                canvas.create_oval(x-size, y-size, x+size, y+size,
                                  fill=color, outline=color, width=1)

            # 旋转的指针
            pointer_angle = self.monitor_angle * math.pi / 180
            pointer_x = cx + 15 * math.cos(pointer_angle)
            pointer_y = cy + 10 * math.sin(pointer_angle)
            canvas.create_line(cx, cy, pointer_x, pointer_y,
                              fill='#FFD700', width=2)

            # 中心文字
            canvas.create_text(cx, cy, text='运行中',
                              font=('Microsoft YaHei UI', 7, 'bold'), fill='#DAA520')

            # 底部状态 - 动态显示
            status_text = f'监控系统正常运行 {"●" * (int(self.monitor_angle/60) % 4 + 1)}'
            canvas.create_text(cx, 105, text=status_text,
                              font=('Microsoft YaHei UI', 7), fill='#CD853F')

            # 更新角度
            if self.monitor_animation_active:
                self.monitor_angle = (self.monitor_angle + 5) % 360

            # 继续动画
            if self.monitor_animation_active:
                self.root.after(100, self.draw_monitor_decoration)

        except Exception as e:
            print(f"绘制监控装饰错误: {e}")

    def start_monitor_animation(self):
        """启动监控动画"""
        self.monitor_animation_active = True
        self.draw_monitor_decoration()

    def stop_monitor_animation(self):
        """停止监控动画"""
        self.monitor_animation_active = False

    def setup_events(self):
        """设置事件绑定"""
        # 鼠标滚轮事件
        def on_mousewheel(event):
            try:
                # 简单的滚动处理
                if hasattr(self, 'log_text'):
                    # 只在日志区域滚动
                    pass
            except:
                pass

        self.root.bind_all("<MouseWheel>", on_mousewheel)

        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    # ==================== 功能方法实现 ====================

    def initialize_system(self):
        """初始化系统"""
        try:
            self.log_message("🚀 邮件系统v3.0启动完成")
            self.log_message("✨ 完整功能版 - 包含所有2.0功能")
            self.log_message("📐 四栏优化布局已加载")
            self.log_message("🔧 所有功能模块已就绪")

            # 更新系统状态
            if hasattr(self, 'system_status'):
                self.system_status.configure(text="✅ 系统就绪",
                                           foreground=self.colors['success'])

            # 更新状态指示器
            if hasattr(self, 'status_indicator'):
                self.status_indicator.configure(text="🟢 系统就绪",
                                              foreground=self.colors['success'])

            # 初始化附件信息
            self.update_attachment_info()

            # 初始化历史统计
            self.update_history_stats()

            # 启动监控动画
            self.start_monitor_animation()

            # 初始化队列列表显示
            if hasattr(self, 'queue_list_text'):
                self.update_queue_list_display()

            self.log_message("✅ 系统初始化完成，所有功能已激活")
            self.log_message("🎯 新增功能：撤回、监控、质量数据库、应急管理")
            self.log_message("🎨 监控动画已启动")
            self.log_message("📋 队列滚动列表已初始化")

        except Exception as e:
            self.log_message(f"❌ 系统初始化失败: {str(e)}")
            print(f"初始化错误: {e}")  # 调试用

    def log_message(self, message):
        """添加日志消息"""
        try:
            timestamp = datetime.datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            if hasattr(self, 'log_text'):
                self.log_text.insert(tk.END, log_entry)
                self.log_text.see(tk.END)  # 自动滚动到底部
            else:
                print(f"日志: {log_entry.strip()}")  # 如果日志组件未就绪，打印到控制台
        except Exception as e:
            print(f"日志错误: {e}")

    # ==================== 邮件发送功能 ====================

    def send_email(self):
        """发送邮件"""
        try:
            self.log_message("🚀 点击发送邮件按钮")

            sender = self.sender_email.get().strip()
            recipients = self.recipient_emails.get(1.0, tk.END).strip()
            subject = self.subject.get().strip()
            body = self.body.get(1.0, tk.END).strip()

            # 输入验证
            if not sender or sender == "@qq.com":
                messagebox.showwarning("提示", "请输入发送者邮箱")
                self.log_message("⚠️ 发送者邮箱为空")
                return

            if not recipients:
                messagebox.showwarning("提示", "请输入收件人邮箱")
                self.log_message("⚠️ 收件人邮箱为空")
                return

            if not subject:
                messagebox.showwarning("提示", "请输入邮件主题")
                self.log_message("⚠️ 邮件主题为空")
                return

            # 检查授权码
            if not self.check_auth_code(sender):
                return

            self.log_message("📧 开始发送邮件...")
            self.log_message(f"📤 发送者: {sender}")

            # 解析收件人
            recipient_list = self.parse_recipients(recipients)
            self.log_message(f"📬 收件人数量: {len(recipient_list)}")
            self.log_message(f"📝 主题: {subject}")
            self.log_message(f"📎 附件数量: {len(self.attachments)}")

            # 模拟发送过程并记录
            success_count = 0
            for recipient in recipient_list:
                # 记录发送历史
                send_record = {
                    'sender': sender,
                    'recipient': recipient,
                    'subject': subject,
                    'body': body,
                    'send_time': datetime.datetime.now().isoformat(),
                    'attachments': self.attachments.copy(),
                    'success': True  # 模拟成功
                }
                self.sent_emails.append(send_record)
                success_count += 1

            # 启用撤回按钮
            if hasattr(self, 'recall_button'):
                self.recall_button.configure(state='normal')

            # 更新统计
            self.update_send_stats(success_count, 0)

            # 自动启动监控
            if self.auto_start_reply_monitoring.get():
                self.log_message("📡 自动启动回复监控")
                self.start_reply_monitoring()

            # 自动开启队列系统
            if self.auto_start_queue_system.get():
                self.log_message("📬 自动开启队列系统")
                self.auto_start_queue_after_send()

            self.log_message(f"✅ 邮件发送完成，成功 {success_count} 封")

            # 根据是否启用自动队列系统显示不同的提示
            if self.auto_start_queue_system.get():
                messagebox.showinfo("发送完成", f"邮件发送完成！\n成功发送: {success_count} 封\n\n✅ 撤回功能已激活\n📬 队列系统已自动开启")
            else:
                messagebox.showinfo("发送完成", f"邮件发送完成！\n成功发送: {success_count} 封\n\n撤回功能已激活")

        except Exception as e:
            error_msg = f"发送邮件失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)
            print(f"发送邮件错误: {e}")  # 调试用

    def check_auth_code(self, sender_email):
        """检查授权码"""
        auth_code = self.auth_code_entry.get().strip()
        if not auth_code:
            # 检查是否有保存的授权码
            if sender_email in self.auth_codes:
                return True
            else:
                messagebox.showwarning("提示", "请输入邮箱授权码")
                self.log_message("⚠️ 授权码为空")
                return False
        return True

    def parse_recipients(self, recipients_text):
        """解析收件人列表"""
        # 支持多种分隔符
        recipients = recipients_text.replace(',', '\n').replace(';', '\n').replace(' ', '\n')
        recipient_list = [email.strip() for email in recipients.split('\n') if email.strip()]
        return recipient_list

    def update_send_stats(self, success, failed):
        """更新发送统计"""
        try:
            if hasattr(self, 'stats_labels'):
                total_sent = len(self.sent_emails)
                success_rate = f"{(success/(success+failed)*100):.1f}%" if (success+failed) > 0 else "0%"

                self.stats_labels["已发送"].configure(text=str(total_sent))
                self.stats_labels["成功率"].configure(text=success_rate)
        except Exception as e:
            print(f"更新统计错误: {e}")

    # ==================== 撤回功能实现 ====================

    def send_recall_email(self):
        """发送撤回邮件"""
        if not self.sent_emails:
            messagebox.showwarning("提示", "没有已发送的邮件可以撤回")
            return

        # 创建撤回邮件选择窗口
        recall_window = tk.Toplevel(self.root)
        recall_window.title("发送撤回邮件")
        recall_window.geometry("700x600")
        recall_window.transient(self.root)
        recall_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(recall_window, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="📤 邮件撤回功能",
                 font=('Microsoft YaHei UI', 16, 'bold')).pack(pady=(0, 15))

        # 已发送邮件列表
        list_frame = ttk.LabelFrame(main_frame, text="已发送邮件列表", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建树形视图
        columns = ('序号', '时间', '收件人', '主题', '状态')
        tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120)

        # 添加滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 填充数据
        for i, record in enumerate(self.sent_emails, 1):
            send_time = record['send_time'][:19].replace('T', ' ')  # 格式化时间
            tree.insert('', 'end', values=(
                i, send_time, record['recipient'],
                record['subject'][:30] + '...' if len(record['subject']) > 30 else record['subject'],
                '已发送'
            ))

        # 撤回邮件内容
        content_frame = ttk.LabelFrame(main_frame, text="撤回邮件内容", padding="10")
        content_frame.pack(fill=tk.X, pady=(0, 15))

        # 撤回主题
        subject_frame = ttk.Frame(content_frame)
        subject_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(subject_frame, text="撤回主题:").pack(side=tk.LEFT)
        recall_subject = ttk.Entry(subject_frame, font=('Microsoft YaHei UI', 10))
        recall_subject.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))
        recall_subject.insert(0, "邮件撤回通知")

        # 撤回内容
        ttk.Label(content_frame, text="撤回内容:").pack(anchor=tk.W)
        recall_body = scrolledtext.ScrolledText(content_frame, width=60, height=6,
                                               font=('Microsoft YaHei UI', 10))
        recall_body.insert(1.0, """尊敬的收件人：

我之前发送给您的邮件需要撤回，请忽略该邮件内容。

如有任何疑问，请联系我。

谢谢！""")
        recall_body.pack(fill=tk.X, pady=(5, 0))

        # 按钮框架
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X)

        def send_recall_to_selected():
            """发送撤回邮件给选中的收件人"""
            selected_items = tree.selection()
            if not selected_items:
                messagebox.showwarning("提示", "请选择要撤回的邮件")
                return

            subject = recall_subject.get().strip()
            body = recall_body.get(1.0, tk.END).strip()

            if not subject or not body:
                messagebox.showerror("错误", "请输入撤回邮件主题和内容")
                return

            # 获取选中的邮件
            selected_emails = []
            for item in selected_items:
                values = tree.item(item)['values']
                recipient = values[2]
                selected_emails.append(recipient)

            # 确认发送
            if messagebox.askyesno("确认撤回",
                                 f"确定要向 {len(selected_emails)} 个收件人发送撤回邮件吗？"):
                self._send_recall_emails(selected_emails, subject, body)
                recall_window.destroy()

        def send_recall_to_all():
            """发送撤回邮件给所有收件人"""
            subject = recall_subject.get().strip()
            body = recall_body.get(1.0, tk.END).strip()

            if not subject or not body:
                messagebox.showerror("错误", "请输入撤回邮件主题和内容")
                return

            all_emails = list(set([record['recipient'] for record in self.sent_emails]))

            if messagebox.askyesno("确认撤回",
                                 f"确定要向所有 {len(all_emails)} 个收件人发送撤回邮件吗？"):
                self._send_recall_emails(all_emails, subject, body)
                recall_window.destroy()

        # 按钮
        ttk.Button(btn_frame, text="撤回选中邮件", command=send_recall_to_selected,
                  style='Purple.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="一键撤回全部", command=send_recall_to_all,
                  style='Danger.TButton').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(btn_frame, text="取消", command=recall_window.destroy,
                  style='Primary.TButton').pack(side=tk.RIGHT)

    def _send_recall_emails(self, recipients, subject, body):
        """实际发送撤回邮件"""
        try:
            sender_email = self.sender_email.get().strip()
            if not sender_email:
                messagebox.showerror("错误", "请先设置发送者邮箱")
                return

            # 检查授权码
            auth_code = self.auth_code_entry.get().strip()
            if not auth_code:
                messagebox.showerror("错误", "请先输入授权码")
                return

            success_count = 0
            failed_count = 0

            self.log_message(f"📤 开始发送撤回邮件给 {len(recipients)} 个收件人...")

            # 创建邮件发送器实例
            try:
                from email_sender import EmailSender
                email_sender = EmailSender(sender_email)

                # 设置授权码
                email_sender.smtp_config['password'] = auth_code

                self.log_message(f"✅ 邮件发送器初始化成功")

            except Exception as e:
                error_msg = f"初始化邮件发送器失败：{str(e)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("错误", error_msg)
                return

            for i, email in enumerate(recipients, 1):
                self.log_message(f"正在发送撤回邮件 {i}/{len(recipients)} 给: {email}")

                try:
                    # 真实发送撤回邮件
                    success = email_sender.send_email([email], subject, body)

                    if success:
                        success_count += 1
                        self.log_message(f"✓ 撤回邮件发送成功: {email}")
                    else:
                        failed_count += 1
                        self.log_message(f"✗ 撤回邮件发送失败: {email}")

                except Exception as e:
                    failed_count += 1
                    self.log_message(f"✗ 撤回邮件发送异常: {email} - {str(e)}")

                # 添加延迟
                if i < len(recipients):
                    time.sleep(0.5)  # 撤回邮件使用较短间隔

            self.log_message(f"📤 撤回邮件发送完成: 成功 {success_count} 封, 失败 {failed_count} 封")
            messagebox.showinfo("撤回完成",
                               f"撤回邮件发送完成！\n\n成功: {success_count} 封\n失败: {failed_count} 封")

        except Exception as e:
            error_msg = f"发送撤回邮件失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def show_send_history(self):
        """显示发送记录"""
        try:
            self.log_message("📋 打开发送记录窗口")

            if not self.sent_emails:
                messagebox.showinfo("提示", "暂无发送记录")
                return

            # 创建历史记录窗口
            history_window = tk.Toplevel(self.root)
            history_window.title("发送记录")
            history_window.geometry("800x600")
            history_window.transient(self.root)

            # 主框架
            main_frame = ttk.Frame(history_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            ttk.Label(main_frame, text="📋 邮件发送记录",
                     font=('Microsoft YaHei UI', 16, 'bold')).pack(pady=(0, 15))

            # 统计信息
            stats_frame = ttk.Frame(main_frame)
            stats_frame.pack(fill=tk.X, pady=(0, 15))

            total_count = len(self.sent_emails)
            unique_recipients = len(set([record['recipient'] for record in self.sent_emails]))

            ttk.Label(stats_frame, text=f"📊 总发送数: {total_count} 封  |  📬 收件人数: {unique_recipients} 个",
                     font=('Microsoft YaHei UI', 12)).pack()

            # 记录列表
            list_frame = ttk.Frame(main_frame)
            list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            columns = ('序号', '发送时间', '收件人', '主题', '状态')
            tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

            for col in columns:
                tree.heading(col, text=col)
                if col == '主题':
                    tree.column(col, width=200)
                elif col == '收件人':
                    tree.column(col, width=180)
                else:
                    tree.column(col, width=100)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)

            tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 填充数据
            for i, record in enumerate(self.sent_emails, 1):
                send_time = record['send_time'][:19].replace('T', ' ')
                tree.insert('', 'end', values=(
                    i, send_time, record['recipient'],
                    record['subject'][:40] + '...' if len(record['subject']) > 40 else record['subject'],
                    '成功' if record.get('success', True) else '失败'
                ))

            # 按钮框架
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(fill=tk.X)

            def export_history():
                """导出历史记录"""
                try:
                    filename = filedialog.asksaveasfilename(
                        title="导出发送记录",
                        defaultextension=".csv",
                        filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
                    )

                    if filename:
                        import csv
                        with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                            writer = csv.writer(f)
                            writer.writerow(['序号', '发送时间', '发送者', '收件人', '主题', '状态'])

                            for i, record in enumerate(self.sent_emails, 1):
                                writer.writerow([
                                    i, record['send_time'], record['sender'],
                                    record['recipient'], record['subject'],
                                    '成功' if record.get('success', True) else '失败'
                                ])

                        self.log_message(f"📤 发送记录已导出到: {filename}")
                        messagebox.showinfo("导出成功", f"发送记录已导出到:\n{filename}")

                except Exception as e:
                    error_msg = f"导出失败：{str(e)}"
                    self.log_message(f"❌ {error_msg}")
                    messagebox.showerror("错误", error_msg)

            ttk.Button(btn_frame, text="📤 导出记录", command=export_history,
                      style='Success.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="🔄 刷新", command=lambda: self.refresh_history(tree),
                      style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="关闭", command=history_window.destroy,
                      style='Primary.TButton').pack(side=tk.RIGHT)

        except Exception as e:
            error_msg = f"显示发送记录失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def clear_send_history(self):
        """清空发送记录"""
        try:
            self.log_message("🗑️ 点击清空发送记录按钮")

            if not self.sent_emails:
                messagebox.showinfo("提示", "没有发送记录需要清空")
                return

            if messagebox.askyesno("确认清空",
                                 f"确定要清空所有 {len(self.sent_emails)} 条发送记录吗？\n\n此操作不可恢复！"):
                count = len(self.sent_emails)
                self.sent_emails.clear()

                # 禁用撤回按钮
                if hasattr(self, 'recall_button'):
                    self.recall_button.configure(state='disabled')

                # 更新统计
                self.update_send_stats(0, 0)
                self.update_history_stats()

                self.log_message(f"🗑️ 已清空 {count} 条发送记录")
                messagebox.showinfo("清空完成", f"已清空 {count} 条发送记录")
            else:
                self.log_message("🗑️ 用户取消清空发送记录")

        except Exception as e:
            error_msg = f"清空发送记录失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def refresh_history(self, tree):
        """刷新历史记录显示"""
        try:
            # 清空现有项目
            for item in tree.get_children():
                tree.delete(item)

            # 重新填充数据
            for i, record in enumerate(self.sent_emails, 1):
                send_time = record['send_time'][:19].replace('T', ' ')
                tree.insert('', 'end', values=(
                    i, send_time, record['recipient'],
                    record['subject'][:40] + '...' if len(record['subject']) > 40 else record['subject'],
                    '成功' if record.get('success', True) else '失败'
                ))

            self.log_message("🔄 发送记录已刷新")

        except Exception as e:
            self.log_message(f"❌ 刷新记录失败: {str(e)}")

    def update_history_stats(self):
        """更新历史统计"""
        try:
            if hasattr(self, 'history_stats'):
                total = len(self.sent_emails)
                self.history_stats.configure(text=f"📊 总计: {total} 封")
        except Exception as e:
            print(f"更新历史统计错误: {e}")

    # ==================== 授权码管理功能 ====================

    def load_auth_codes(self):
        """从文件加载授权码"""
        try:
            import json
            import os

            if os.path.exists(self.auth_codes_file):
                with open(self.auth_codes_file, 'r', encoding='utf-8') as f:
                    self.auth_codes = json.load(f)
                self.log_message(f"✅ 已加载 {len(self.auth_codes)} 个保存的授权码")

                # 自动填充第一个授权码到界面
                if self.auth_codes and hasattr(self, 'auth_code_entry'):
                    first_email = list(self.auth_codes.keys())[0]
                    first_auth = self.auth_codes[first_email]['auth_code']
                    self.auth_code_entry.delete(0, tk.END)
                    self.auth_code_entry.insert(0, first_auth)

                    # 如果发送者邮箱为空，也填充
                    if hasattr(self, 'sender_email') and not self.sender_email.get().strip():
                        self.sender_email.delete(0, tk.END)
                        self.sender_email.insert(0, first_email)

            else:
                self.log_message("📝 未找到保存的授权码文件")

        except Exception as e:
            self.log_message(f"❌ 加载授权码失败: {str(e)}")
            print(f"加载授权码错误: {e}")

    def save_auth_codes_to_file(self):
        """保存授权码到文件"""
        try:
            import json

            with open(self.auth_codes_file, 'w', encoding='utf-8') as f:
                json.dump(self.auth_codes, f, ensure_ascii=False, indent=2)
            self.log_message(f"💾 授权码已保存到文件: {self.auth_codes_file}")

        except Exception as e:
            self.log_message(f"❌ 保存授权码到文件失败: {str(e)}")
            print(f"保存授权码错误: {e}")

    def save_auth_code(self):
        """保存授权码"""
        try:
            self.log_message("💾 点击保存授权码按钮")

            sender_email = self.sender_email.get().strip()
            auth_code = self.auth_code_entry.get().strip()

            if not sender_email or sender_email == "@qq.com":
                messagebox.showwarning("提示", "请先输入发送者邮箱")
                return

            if not auth_code:
                messagebox.showwarning("提示", "请输入授权码")
                return

            # 保存授权码到内存
            self.auth_codes[sender_email] = {
                'auth_code': auth_code,
                'save_time': datetime.datetime.now().isoformat()
            }

            # 保存到文件
            self.save_auth_codes_to_file()

            self.log_message(f"💾 授权码已保存: {sender_email}")
            messagebox.showinfo("保存成功", f"授权码已保存并持久化\n邮箱: {sender_email}\n下次启动时自动加载")

        except Exception as e:
            error_msg = f"保存授权码失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def manage_auth_codes(self):
        """管理授权码"""
        try:
            self.log_message("🔑 打开授权码管理窗口")

            # 创建管理窗口
            manage_window = tk.Toplevel(self.root)
            manage_window.title("授权码管理")
            manage_window.geometry("600x400")
            manage_window.transient(self.root)

            # 主框架
            main_frame = ttk.Frame(manage_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            ttk.Label(main_frame, text="🔑 授权码管理",
                     font=('Microsoft YaHei UI', 16, 'bold')).pack(pady=(0, 15))

            if not self.auth_codes:
                ttk.Label(main_frame, text="暂无保存的授权码",
                         font=('Microsoft YaHei UI', 12)).pack(expand=True)
                ttk.Button(main_frame, text="关闭", command=manage_window.destroy,
                          style='Primary.TButton').pack()
                return

            # 授权码列表
            list_frame = ttk.Frame(main_frame)
            list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            columns = ('邮箱', '保存时间', '状态')
            tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

            for col in columns:
                tree.heading(col, text=col)
                if col == '邮箱':
                    tree.column(col, width=200)
                else:
                    tree.column(col, width=150)

            tree.pack(fill=tk.BOTH, expand=True)

            # 填充数据
            for email, info in self.auth_codes.items():
                save_time = info.get('save_time', '')[:19].replace('T', ' ')
                tree.insert('', 'end', values=(email, save_time, '已保存'))

            # 按钮框架
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(fill=tk.X)

            def delete_selected():
                """删除选中的授权码"""
                selected = tree.selection()
                if not selected:
                    messagebox.showwarning("提示", "请选择要删除的授权码")
                    return

                item = selected[0]
                email = tree.item(item)['values'][0]

                if messagebox.askyesno("确认删除", f"确定要删除 {email} 的授权码吗？"):
                    del self.auth_codes[email]
                    tree.delete(item)
                    self.log_message(f"🗑️ 已删除授权码: {email}")

            ttk.Button(btn_frame, text="🗑️ 删除选中", command=delete_selected,
                      style='Danger.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="关闭", command=manage_window.destroy,
                      style='Primary.TButton').pack(side=tk.RIGHT)

        except Exception as e:
            error_msg = f"管理授权码失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    # ==================== 附件管理功能 ====================

    def add_attachment(self):
        """添加附件"""
        try:
            self.log_message("📁 点击添加附件按钮")

            filename = filedialog.askopenfilename(
                title="选择附件",
                filetypes=[
                    ("常用文件", "*.txt *.doc *.docx *.pdf *.xls *.xlsx"),
                    ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp"),
                    ("压缩文件", "*.zip *.rar *.7z"),
                    ("所有文件", "*.*")
                ]
            )

            if filename:
                # 检查文件是否已存在
                basename = os.path.basename(filename)
                if filename in self.attachments:
                    messagebox.showwarning("提示", f"附件 {basename} 已存在")
                    self.log_message(f"⚠️ 附件已存在: {basename}")
                    return

                # 添加到列表
                self.attachments.append(filename)
                self.attachment_listbox.insert(tk.END, basename)
                self.update_attachment_info()

                self.log_message(f"📁 添加附件成功: {basename}")
                messagebox.showinfo("成功", f"附件添加成功：{basename}")

            else:
                self.log_message("📁 用户取消选择附件")

        except Exception as e:
            error_msg = f"添加附件失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def remove_attachment(self):
        """删除附件"""
        try:
            self.log_message("🗑️ 点击删除附件按钮")

            selection = self.attachment_listbox.curselection()
            if selection:
                index = selection[0]
                filename = self.attachment_listbox.get(index)

                # 从列表中删除
                self.attachment_listbox.delete(index)
                if index < len(self.attachments):
                    removed_file = self.attachments.pop(index)
                    self.log_message(f"🗑️ 删除附件: {filename}")

                self.update_attachment_info()
                messagebox.showinfo("成功", f"附件删除成功：{filename}")

            else:
                messagebox.showwarning("提示", "请先选择要删除的附件")
                self.log_message("⚠️ 未选择要删除的附件")

        except Exception as e:
            error_msg = f"删除附件失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def clear_attachments(self):
        """清空附件"""
        try:
            self.log_message("🧹 点击清空附件按钮")

            if len(self.attachments) > 0:
                if messagebox.askyesno("确认", "确定要清空所有附件吗？"):
                    count = len(self.attachments)
                    self.attachment_listbox.delete(0, tk.END)
                    self.attachments.clear()
                    self.update_attachment_info()

                    self.log_message(f"🧹 清空所有附件，共 {count} 个")
                    messagebox.showinfo("成功", f"已清空 {count} 个附件")
                else:
                    self.log_message("🧹 用户取消清空附件")
            else:
                messagebox.showinfo("提示", "没有附件需要清空")
                self.log_message("ℹ️ 没有附件需要清空")

        except Exception as e:
            error_msg = f"清空附件失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def update_attachment_info(self):
        """更新附件信息显示"""
        try:
            count = len(self.attachments)
            if hasattr(self, 'attachment_info'):
                self.attachment_info.configure(text=f"📊 附件: {count} 个")
            self.log_message(f"📊 更新附件信息: {count} 个")
        except Exception as e:
            print(f"更新附件信息错误: {e}")  # 调试用

    # ==================== 高级功能实现 ====================

    def start_reply_monitoring(self):
        """开始自动回复监控"""
        try:
            self.log_message("📡 点击开始监控按钮")

            if self.monitoring_active:
                messagebox.showinfo("提示", "监控已在运行中")
                return

            sender_email = self.sender_email.get().strip()
            if not sender_email or sender_email == "@qq.com":
                messagebox.showwarning("提示", "请先设置发送者邮箱")
                return

            # 检查授权码
            if not self.check_auth_code(sender_email):
                return

            self.monitoring_active = True

            # 更新按钮状态
            if hasattr(self, 'start_monitor_button'):
                self.start_monitor_button.configure(state='disabled')
            if hasattr(self, 'stop_monitor_button'):
                self.stop_monitor_button.configure(state='normal')

            # 更新统计
            if hasattr(self, 'stats_labels'):
                self.stats_labels["监控状态"].configure(text="运行中")

            self.log_message("📡 自动回复监控已启动")
            messagebox.showinfo("监控启动", "自动回复监控已启动！\n\n将持续监控收件人的自动回复")

        except Exception as e:
            error_msg = f"启动监控失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def stop_reply_monitoring(self):
        """停止自动回复监控"""
        try:
            self.log_message("⏹️ 点击停止监控按钮")

            if not self.monitoring_active:
                messagebox.showinfo("提示", "监控未在运行")
                return

            self.monitoring_active = False

            # 更新按钮状态
            if hasattr(self, 'start_monitor_button'):
                self.start_monitor_button.configure(state='normal')
            if hasattr(self, 'stop_monitor_button'):
                self.stop_monitor_button.configure(state='disabled')

            # 更新统计
            if hasattr(self, 'stats_labels'):
                self.stats_labels["监控状态"].configure(text="已停止")

            self.log_message("⏹️ 自动回复监控已停止")
            messagebox.showinfo("监控停止", "自动回复监控已停止")

        except Exception as e:
            error_msg = f"停止监控失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def configure_monitoring(self):
        """配置监控设置"""
        try:
            self.log_message("📊 打开监控设置")

            # 创建监控设置窗口
            settings_window = tk.Toplevel(self.root)
            settings_window.title("📊 监控设置")
            settings_window.geometry("500x400")
            settings_window.resizable(False, False)

            # 主框架
            main_frame = ttk.Frame(settings_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建监控设置界面
            self._create_monitoring_settings_interface(main_frame)

            self.log_message("✅ 监控设置界面已打开")

        except Exception as e:
            error_msg = f"打开监控设置失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def open_reply_monitor(self):
        """打开自动回复监控管理"""
        try:
            self.log_message("📬 打开自动回复监控管理")

            # 导入邮件接收器
            from email_receiver import EmailReceiver

            # 创建监控管理窗口
            monitor_window = tk.Toplevel(self.root)
            monitor_window.title("📬 自动回复监控管理")
            monitor_window.geometry("800x600")
            monitor_window.resizable(True, True)

            # 主框架
            main_frame = ttk.Frame(monitor_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建监控管理界面
            self._create_monitor_interface(main_frame)

            self.log_message("✅ 自动回复监控管理器已打开")

        except Exception as e:
            error_msg = f"打开自动回复监控失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def open_quality_manager(self):
        """打开质量数据库管理"""
        try:
            self.log_message("📊 打开质量数据库管理")

            # 导入质量管理器
            from recipient_quality_manager import RecipientQualityManager

            # 创建质量管理窗口
            quality_window = tk.Toplevel(self.root)
            quality_window.title("📊 收件人质量数据库")
            quality_window.geometry("1000x700")
            quality_window.resizable(True, True)

            # 创建质量管理器实例
            quality_manager = RecipientQualityManager()

            # 主框架
            main_frame = ttk.Frame(quality_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建质量管理界面
            self._create_quality_manager_interface(main_frame, quality_manager)

            self.log_message("✅ 质量数据库管理界面已打开")

        except Exception as e:
            error_msg = f"打开质量管理失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def open_batch_manager(self):
        """打开批次管理"""
        try:
            self.log_message("📈 打开批次管理")

            # 导入批次管理器
            from batch_manager import BatchManager

            # 创建批次管理窗口
            batch_window = tk.Toplevel(self.root)
            batch_window.title("📈 智能批次管理")
            batch_window.geometry("900x600")
            batch_window.resizable(True, True)

            # 创建批次管理器实例
            batch_manager = BatchManager()

            # 主框架
            main_frame = ttk.Frame(batch_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建批次管理界面
            self._create_batch_manager_interface(main_frame, batch_manager)

            self.log_message("✅ 批次管理界面已打开")

        except Exception as e:
            error_msg = f"打开批次管理失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def import_to_main(self):
        """导入到主系统"""
        try:
            self.log_message("📤 导入到主系统")

            # 创建导入选择窗口
            import_window = tk.Toplevel(self.root)
            import_window.title("📤 导入到主系统")
            import_window.geometry("600x400")
            import_window.resizable(False, False)

            # 主框架
            main_frame = ttk.Frame(import_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建导入界面
            self._create_import_interface(main_frame)

            self.log_message("✅ 导入界面已打开")

        except Exception as e:
            error_msg = f"打开导入功能失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def open_anti_spam(self):
        """打开反垃圾邮件管理"""
        try:
            self.log_message("🛡️ 打开反垃圾邮件管理")

            # 导入反垃圾邮件管理器
            from anti_spam_manager import AntiSpamManager

            # 创建反垃圾邮件管理窗口
            anti_spam_window = tk.Toplevel(self.root)
            anti_spam_window.title("🛡️ 反垃圾邮件管理")
            anti_spam_window.geometry("800x600")
            anti_spam_window.resizable(True, True)

            # 创建反垃圾邮件管理器实例
            anti_spam_manager = AntiSpamManager()

            # 主框架
            main_frame = ttk.Frame(anti_spam_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建反垃圾邮件管理界面
            self._create_anti_spam_interface(main_frame, anti_spam_manager)

            self.log_message("✅ 反垃圾邮件管理界面已打开")

        except Exception as e:
            error_msg = f"打开反垃圾邮件管理失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def open_emergency_manager(self):
        """打开应急管理"""
        try:
            self.log_message("🆘 打开应急管理系统")

            # 导入QQ应急管理器
            from qq_email_anti_spam import QQEmailAntiSpamManager

            # 创建应急管理窗口
            emergency_window = tk.Toplevel(self.root)
            emergency_window.title("🆘 QQ邮箱应急管理")
            emergency_window.geometry("700x500")
            emergency_window.resizable(True, True)

            # 创建应急管理器实例
            emergency_manager = QQEmailAntiSpamManager()

            # 主框架
            main_frame = ttk.Frame(emergency_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建应急管理界面
            self._create_emergency_interface(main_frame, emergency_manager)

            self.log_message("✅ 应急管理界面已打开")

        except Exception as e:
            error_msg = f"打开应急管理失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def open_duplicate_detection(self):
        """打开重复检测"""
        try:
            self.log_message("🔍 打开重复检测")

            # 创建重复检测窗口
            detection_window = tk.Toplevel(self.root)
            detection_window.title("🔍 重复内容检测")
            detection_window.geometry("900x700")
            detection_window.resizable(True, True)

            # 主框架
            main_frame = ttk.Frame(detection_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建重复检测界面（使用RAG搜索引擎作为检测器）
            from rag_search_engine import RAGSearchEngine
            detector = RAGSearchEngine()
            self._create_duplicate_detection_interface(main_frame, detector)

            self.log_message("✅ 重复检测界面已打开")

        except Exception as e:
            error_msg = f"打开重复检测失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def activate_deep_coordination(self):
        """激活深度协调"""
        try:
            self.log_message("🚀 激活深度协调系统")

            if self.deep_coordination_active:
                messagebox.showinfo("提示", "深度协调已激活")
                return

            self.deep_coordination_active = True

            # 更新按钮状态
            if hasattr(self, 'activate_coord_button'):
                self.activate_coord_button.configure(state='disabled')
            if hasattr(self, 'deactivate_coord_button'):
                self.deactivate_coord_button.configure(state='normal')

            # 更新状态显示
            if hasattr(self, 'coordination_status'):
                self.coordination_status.configure(text="🧠 已激活",
                                                 foreground=self.colors['success'])

            self.log_message("🧠 深度协调系统已激活")
            messagebox.showinfo("深度协调", "深度协调系统已激活！\n\n系统将智能协调各功能模块")

        except Exception as e:
            error_msg = f"激活深度协调失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def deactivate_deep_coordination(self):
        """停止深度协调"""
        try:
            self.log_message("⏹️ 停止深度协调系统")

            if not self.deep_coordination_active:
                messagebox.showinfo("提示", "深度协调未激活")
                return

            self.deep_coordination_active = False

            # 更新按钮状态
            if hasattr(self, 'activate_coord_button'):
                self.activate_coord_button.configure(state='normal')
            if hasattr(self, 'deactivate_coord_button'):
                self.deactivate_coord_button.configure(state='disabled')

            # 更新状态显示
            if hasattr(self, 'coordination_status'):
                self.coordination_status.configure(text="🔧 未激活",
                                                 foreground=self.colors['warning'])

            self.log_message("⏹️ 深度协调系统已停止")
            messagebox.showinfo("深度协调", "深度协调系统已停止")

        except Exception as e:
            error_msg = f"停止深度协调失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def configure_coordination(self):
        """配置深度协调"""
        try:
            self.log_message("⚙️ 打开协调设置")

            # 创建协调设置窗口
            coord_settings_window = tk.Toplevel(self.root)
            coord_settings_window.title("⚙️ 深度协调设置")
            coord_settings_window.geometry("600x500")
            coord_settings_window.resizable(False, False)

            # 主框架
            main_frame = ttk.Frame(coord_settings_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建协调设置界面
            self._create_coordination_settings_interface(main_frame)

            self.log_message("✅ 深度协调设置界面已打开")

        except Exception as e:
            error_msg = f"打开协调设置失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    # ==================== 其他功能实现 ====================

    # 这里可以添加其他功能的实现，如队列管理、历史分析等
    # 由于篇幅限制，这些功能可以在后续版本中完善

    def pause_sending(self):
        """暂停发送"""
        try:
            self.log_message("⏸️ 点击暂停发送按钮")
            messagebox.showinfo("暂停", "发送已暂停")
            self.log_message("⏸️ 发送已暂停")
        except Exception as e:
            print(f"暂停发送错误: {e}")

    def stop_sending(self):
        """停止发送"""
        try:
            self.log_message("⏹️ 点击停止发送按钮")
            if messagebox.askyesno("确认", "确定要停止发送吗？"):
                self.log_message("⏹️ 发送已停止")
                messagebox.showinfo("停止", "发送已停止")
            else:
                self.log_message("⏹️ 用户取消停止发送")
        except Exception as e:
            print(f"停止发送错误: {e}")

    def resume_sending(self):
        """恢复发送"""
        try:
            self.log_message("▶️ 点击恢复发送按钮")
            self.log_message("▶️ 发送已恢复")
            messagebox.showinfo("恢复", "发送已恢复")
        except Exception as e:
            print(f"恢复发送错误: {e}")

    def continue_sending(self):
        """断点继续"""
        try:
            self.log_message("🔄 点击断点继续按钮")
            self.log_message("🔄 从断点继续发送")
            messagebox.showinfo("继续", "从断点继续发送")
        except Exception as e:
            print(f"断点继续错误: {e}")

    def test_connection(self):
        """测试连接"""
        try:
            self.log_message("🔧 点击测试连接按钮")
            self.log_message("🔧 开始测试邮件服务器连接...")

            # 模拟测试过程
            self.root.after(1000, lambda: self.log_message("✅ 连接测试完成"))
            messagebox.showinfo("测试", "正在测试连接，请查看日志")
        except Exception as e:
            print(f"测试连接错误: {e}")

    def validate_emails(self):
        """验证邮箱"""
        try:
            self.log_message("✅ 点击验证邮箱按钮")

            recipients = self.recipient_emails.get(1.0, tk.END).strip()
            if not recipients:
                messagebox.showwarning("提示", "请先输入收件人邮箱")
                self.log_message("⚠️ 收件人邮箱为空")
                return

            self.log_message("✅ 开始验证邮箱地址...")

            # 简单的邮箱格式验证
            email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
            recipient_list = self.parse_recipients(recipients)
            valid_count = 0
            invalid_count = 0

            for email in recipient_list:
                if re.match(email_pattern, email):
                    valid_count += 1
                else:
                    invalid_count += 1
                    self.log_message(f"❌ 无效邮箱: {email}")

            self.log_message(f"✅ 验证完成: 有效 {valid_count} 个，无效 {invalid_count} 个")
            messagebox.showinfo("验证结果", f"有效邮箱: {valid_count} 个\n无效邮箱: {invalid_count} 个")

        except Exception as e:
            error_msg = f"验证邮箱失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def clear_form(self):
        """清空表单"""
        try:
            self.log_message("🧹 点击清空表单按钮")

            if messagebox.askyesno("确认", "确定要清空所有表单内容吗？"):
                self.sender_email.delete(0, tk.END)
                self.sender_email.insert(0, "@qq.com")
                self.recipient_emails.delete(1.0, tk.END)
                self.subject.delete(0, tk.END)
                self.body.delete(1.0, tk.END)
                self.auth_code_entry.delete(0, tk.END)

                self.log_message("🧹 表单已清空")
                messagebox.showinfo("成功", "表单内容已清空")
            else:
                self.log_message("🧹 用户取消清空表单")

        except Exception as e:
            error_msg = f"清空表单失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    # 队列系统功能实现
    def add_to_queue(self):
        """添加到队列"""
        try:
            self.log_message("📋 添加邮件到队列")

            # 验证必填字段
            sender_email = self.sender_email.get().strip()
            recipients = self.recipient_emails.get(1.0, tk.END).strip()
            subject = self.subject.get().strip()
            body = self.body.get(1.0, tk.END).strip()

            if not all([sender_email, recipients, subject, body]):
                messagebox.showwarning("提示", "请填写完整的邮件信息")
                return

            # 创建队列任务
            task = {
                'sender_email': sender_email,
                'recipient_emails': recipients,
                'subject': subject,
                'body': body,
                'attachments': list(self.attachment_listbox.get(0, tk.END)),
                'created_time': datetime.datetime.now().isoformat(),
                'status': 'pending'
            }

            # 添加到队列
            if not hasattr(self, 'email_queue'):
                self.email_queue = []

            self.email_queue.append(task)

            # 更新队列显示
            self.update_queue_status()

            self.log_message(f"✅ 邮件已添加到队列，当前队列: {len(self.email_queue)} 个任务")
            messagebox.showinfo("成功", f"邮件已添加到队列\n当前队列任务数: {len(self.email_queue)}")

        except Exception as e:
            error_msg = f"添加到队列失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def open_queue_system(self):
        """打开队列系统"""
        try:
            self.log_message("📋 打开队列系统管理器")

            # 导入队列系统
            from queue_system import QueueSystemWindow

            # 创建队列系统实例
            queue_system = QueueSystemWindow(self)

            self.log_message("✅ 队列系统管理器已打开")

        except Exception as e:
            error_msg = f"打开队列系统失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def start_queue_sending(self):
        """开始队列发送"""
        try:
            self.log_message("🚀 开始队列发送")

            if not hasattr(self, 'email_queue') or not self.email_queue:
                messagebox.showwarning("提示", "队列为空，请先添加邮件到队列")
                return

            pending_tasks = [task for task in self.email_queue if task.get('status') == 'pending']
            if not pending_tasks:
                messagebox.showwarning("提示", "没有待发送的任务")
                return

            # 确认开始发送
            confirm_msg = f"确定要开始队列发送吗？\n\n待发送任务: {len(pending_tasks)} 个"
            if not messagebox.askyesno("确认发送", confirm_msg):
                return

            # 开始队列发送
            self.queue_mode = True
            self.should_stop = False

            self.log_message(f"🚀 开始队列发送: {len(pending_tasks)} 个任务")

            # 在新线程中执行队列发送
            import threading
            send_thread = threading.Thread(target=self._execute_queue_sending)
            send_thread.daemon = True
            send_thread.start()

        except Exception as e:
            error_msg = f"开始队列发送失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def pause_queue_sending(self):
        """暂停队列发送"""
        try:
            self.log_message("⏸️ 暂停队列发送")
            self.should_pause = True

            # 更新按钮状态
            if hasattr(self, 'pause_queue_button'):
                self.pause_queue_button.configure(state='disabled')
            if hasattr(self, 'resume_queue_button'):
                self.resume_queue_button.configure(state='normal')

            messagebox.showinfo("暂停", "队列发送已暂停")
        except Exception as e:
            print(f"暂停队列发送错误: {e}")

    def resume_queue_sending(self):
        """恢复队列发送"""
        try:
            self.log_message("▶️ 恢复队列发送")
            self.should_pause = False

            # 更新按钮状态
            if hasattr(self, 'pause_queue_button'):
                self.pause_queue_button.configure(state='normal')
            if hasattr(self, 'resume_queue_button'):
                self.resume_queue_button.configure(state='disabled')

            # 如果有队列任务且不在发送中，重新启动发送
            if (hasattr(self, 'email_queue') and self.email_queue and
                not getattr(self, 'queue_sending_active', False)):
                self.start_queue_sending()
            else:
                messagebox.showinfo("恢复", "队列发送已恢复")

        except Exception as e:
            error_msg = f"恢复队列发送失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def clear_queue(self):
        """清空队列"""
        try:
            self.log_message("🗑️ 清空邮件队列")

            if not hasattr(self, 'email_queue') or not self.email_queue:
                messagebox.showinfo("提示", "队列已经为空")
                return

            if messagebox.askyesno("确认清空", f"确定要清空队列中的 {len(self.email_queue)} 个任务吗？"):
                self.email_queue.clear()
                self.update_queue_status()
                self.log_message("✅ 队列已清空")
                messagebox.showinfo("成功", "队列已清空")

        except Exception as e:
            error_msg = f"清空队列失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def on_auto_queue_changed(self):
        """自动队列模式变化处理"""
        try:
            if hasattr(self, 'auto_queue_var') and self.auto_queue_var.get():
                self.log_message("🔄 自动队列模式已启用")
            else:
                self.log_message("🔄 自动队列模式已禁用")
        except Exception as e:
            print(f"自动队列模式变化错误: {e}")

    def view_email_history(self):
        """查看邮件历史"""
        try:
            self.log_message("📋 打开邮件历史管理器")

            # 导入历史管理器
            from email_history_manager import EmailHistoryManager

            # 创建历史管理窗口
            history_window = tk.Toplevel(self.root)
            history_window.title("📋 邮件历史管理")
            history_window.geometry("1000x700")
            history_window.resizable(True, True)

            # 创建历史管理器实例
            history_manager = EmailHistoryManager()

            # 主框架
            main_frame = ttk.Frame(history_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建历史管理界面
            self._create_history_interface(main_frame, history_manager)

            self.log_message("✅ 邮件历史管理器已打开")

        except Exception as e:
            error_msg = f"打开邮件历史失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def show_statistics(self):
        """显示统计信息"""
        try:
            self.log_message("📊 显示发送统计信息")

            # 导入历史管理器获取统计数据
            from email_history_manager import EmailHistoryManager
            history_manager = EmailHistoryManager()

            # 获取统计数据
            sender_email = self.sender_email.get().strip()
            if not sender_email:
                messagebox.showwarning("提示", "请先输入发送者邮箱")
                return

            stats = history_manager.get_statistics(sender_email)

            # 显示统计信息
            stats_msg = f"""📊 邮件发送统计

📧 总发送数量: {stats.get('total_sent', 0)} 封
✅ 成功发送: {stats.get('successful_sent', 0)} 封
❌ 发送失败: {stats.get('failed_sent', 0)} 封
📈 成功率: {stats.get('success_rate', 0):.1f}%

📅 最近发送: {stats.get('last_send_time', '无')}
📊 今日发送: {stats.get('today_sent', 0)} 封
📊 本周发送: {stats.get('week_sent', 0)} 封
📊 本月发送: {stats.get('month_sent', 0)} 封"""

            messagebox.showinfo("发送统计", stats_msg)
            self.log_message("✅ 统计信息已显示")

        except Exception as e:
            error_msg = f"显示统计信息失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def export_history(self):
        """导出历史记录"""
        try:
            self.log_message("📤 导出历史记录")

            # 导入历史管理器
            from email_history_manager import EmailHistoryManager
            from tkinter import filedialog

            history_manager = EmailHistoryManager()
            sender_email = self.sender_email.get().strip()

            # 选择保存路径
            filepath = filedialog.asksaveasfilename(
                title="导出历史记录",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if filepath:
                history_manager.export_history(filepath, sender_email)
                self.log_message(f"✅ 历史记录已导出到: {filepath}")
                messagebox.showinfo("导出成功", f"历史记录已导出到:\n{filepath}")

        except Exception as e:
            error_msg = f"导出历史记录失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def open_smart_search(self):
        """打开智能搜索"""
        try:
            self.log_message("🔍 打开智能搜索引擎")

            # 导入RAG搜索引擎
            from rag_search_engine import RAGSearchEngine

            # 创建搜索窗口
            search_window = tk.Toplevel(self.root)
            search_window.title("🔍 智能邮件搜索")
            search_window.geometry("900x600")
            search_window.resizable(True, True)

            # 创建搜索引擎实例
            search_engine = RAGSearchEngine()

            # 主框架
            main_frame = ttk.Frame(search_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建搜索界面
            self._create_search_interface(main_frame, search_engine)

            self.log_message("✅ 智能搜索引擎已打开")

        except Exception as e:
            error_msg = f"打开智能搜索失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def test_monitor(self):
        """测试监控功能"""
        try:
            self.log_message("🔧 测试自动回复监控功能")

            # 导入邮件接收器
            from email_receiver import EmailReceiver

            sender_email = self.sender_email.get().strip()
            auth_code = self.auth_code_entry.get().strip()

            if not sender_email or not auth_code:
                messagebox.showwarning("提示", "请先填写发送者邮箱和授权码")
                return

            # 创建接收器实例并测试连接
            receiver = EmailReceiver(sender_email, auth_code)

            # 测试IMAP连接
            self.log_message("🔧 正在测试IMAP连接...")
            try:
                mail = receiver.connect_imap()
                mail.logout()
                self.log_message("✅ IMAP连接测试成功")
                messagebox.showinfo("测试成功", "自动回复监控功能测试成功！\nIMAP连接正常")
            except Exception as e:
                self.log_message(f"❌ IMAP连接测试失败: {str(e)}")
                messagebox.showerror("测试失败", f"IMAP连接测试失败:\n{str(e)}")

        except Exception as e:
            error_msg = f"测试监控功能失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def reset_monitor(self):
        """重置监控"""
        try:
            self.log_message("🔄 重置自动回复监控")

            # 停止当前监控
            if hasattr(self, 'monitoring_active') and self.monitoring_active:
                self.stop_reply_monitoring()

            # 重置监控状态
            self.monitoring_active = False

            # 更新界面状态
            if hasattr(self, 'monitor_status_label'):
                self.monitor_status_label.configure(text="📡 监控未启动",
                                                  foreground=self.colors['warning'])

            self.log_message("✅ 监控已重置")
            messagebox.showinfo("重置成功", "自动回复监控已重置")

        except Exception as e:
            error_msg = f"重置监控失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def clear_log(self):
        """清空日志"""
        try:
            self.log_message("🗑️ 清空系统日志")

            if messagebox.askyesno("确认清空", "确定要清空所有日志内容吗？"):
                self.log_text.delete(1.0, tk.END)
                self.log_message("✅ 日志已清空")

        except Exception as e:
            error_msg = f"清空日志失败：{str(e)}"
            print(f"❌ {error_msg}")

    def save_log(self):
        """保存日志"""
        try:
            self.log_message("💾 保存系统日志")

            from tkinter import filedialog
            import datetime

            # 选择保存路径
            default_filename = f"email_system_log_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            filepath = filedialog.asksaveasfilename(
                title="保存日志文件",
                defaultextension=".txt",
                initialvalue=default_filename,
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filepath:
                # 获取日志内容
                log_content = self.log_text.get(1.0, tk.END)

                # 保存到文件
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(log_content)

                self.log_message(f"✅ 日志已保存到: {filepath}")
                messagebox.showinfo("保存成功", f"日志已保存到:\n{filepath}")

        except Exception as e:
            error_msg = f"保存日志失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)





    # ==================== 辅助界面创建方法 ====================

    def _create_history_interface(self, parent, history_manager):
        """创建历史记录管理界面"""
        try:
            # 搜索框架
            search_frame = ttk.LabelFrame(parent, text="🔍 搜索历史记录", padding="10")
            search_frame.pack(fill=tk.X, pady=(0, 10))

            # 搜索输入
            ttk.Label(search_frame, text="搜索关键词:").pack(anchor=tk.W)
            search_entry = ttk.Entry(search_frame, width=50)
            search_entry.pack(fill=tk.X, pady=(5, 10))

            # 搜索按钮
            search_button = ttk.Button(search_frame, text="🔍 搜索",
                                     command=lambda: self._search_history(history_manager, search_entry.get(), results_tree))
            search_button.pack(anchor=tk.W)

            # 结果显示框架
            results_frame = ttk.LabelFrame(parent, text="📋 搜索结果", padding="10")
            results_frame.pack(fill=tk.BOTH, expand=True)

            # 创建树形视图显示结果
            columns = ('时间', '收件人', '主题', '状态')
            results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

            # 设置列标题
            for col in columns:
                results_tree.heading(col, text=col)
                results_tree.column(col, width=150)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=results_tree.yview)
            results_tree.configure(yscrollcommand=scrollbar.set)

            # 布局
            results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 操作按钮框架
            button_frame = ttk.Frame(parent)
            button_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(button_frame, text="📊 显示统计",
                      command=lambda: self._show_history_stats(history_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="📤 导出记录",
                      command=lambda: self._export_selected_history(history_manager, results_tree)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🗑️ 清理旧记录",
                      command=lambda: self._cleanup_old_records(history_manager)).pack(side=tk.LEFT)

            # 初始加载最近记录
            self._search_history(history_manager, "", results_tree)

        except Exception as e:
            print(f"创建历史界面错误: {e}")

    def _create_search_interface(self, parent, search_engine):
        """创建智能搜索界面"""
        try:
            # 搜索框架
            search_frame = ttk.LabelFrame(parent, text="🔍 智能搜索", padding="10")
            search_frame.pack(fill=tk.X, pady=(0, 10))

            # 搜索输入
            ttk.Label(search_frame, text="搜索查询:").pack(anchor=tk.W)
            search_entry = ttk.Entry(search_frame, width=60)
            search_entry.pack(fill=tk.X, pady=(5, 10))

            # 搜索选项
            options_frame = ttk.Frame(search_frame)
            options_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(options_frame, text="搜索范围:").pack(side=tk.LEFT)
            search_scope = ttk.Combobox(options_frame, values=["所有邮件", "仅当前发件人"], state="readonly", width=15)
            search_scope.set("仅当前发件人")
            search_scope.pack(side=tk.LEFT, padx=(10, 20))

            ttk.Label(options_frame, text="结果数量:").pack(side=tk.LEFT)
            result_limit = ttk.Spinbox(options_frame, from_=5, to=100, value=20, width=10)
            result_limit.pack(side=tk.LEFT, padx=(10, 0))

            # 搜索按钮
            search_button = ttk.Button(search_frame, text="🔍 智能搜索",
                                     command=lambda: self._perform_smart_search(search_engine, search_entry.get(),
                                                                               search_scope.get(), int(result_limit.get()), results_text))
            search_button.pack(anchor=tk.W)

            # 结果显示
            results_frame = ttk.LabelFrame(parent, text="📋 搜索结果", padding="10")
            results_frame.pack(fill=tk.BOTH, expand=True)

            results_text = tk.Text(results_frame, wrap=tk.WORD, height=20)
            results_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=results_text.yview)
            results_text.configure(yscrollcommand=results_scrollbar.set)

            results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            print(f"创建搜索界面错误: {e}")

    def _search_history(self, history_manager, query, results_tree):
        """搜索历史记录"""
        try:
            # 清空现有结果
            for item in results_tree.get_children():
                results_tree.delete(item)

            # 获取发件人邮箱
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else None

            # 搜索记录
            if query.strip():
                records = history_manager.search_email_history(query, sender_email, 100)
            else:
                records = history_manager.get_recent_emails(sender_email, 50)

            # 显示结果
            for record in records:
                status = "✅ 成功" if record.get('success', False) else "❌ 失败"
                send_time = record.get('send_time', '')[:19] if record.get('send_time') else ''

                results_tree.insert('', tk.END, values=(
                    send_time,
                    record.get('recipient_email', ''),
                    record.get('subject', '')[:50],
                    status
                ))

        except Exception as e:
            print(f"搜索历史记录错误: {e}")

    def _perform_smart_search(self, search_engine, query, scope, limit, results_text):
        """执行智能搜索"""
        try:
            # 清空结果
            results_text.delete(1.0, tk.END)

            if not query.strip():
                results_text.insert(tk.END, "请输入搜索查询")
                return

            # 获取发件人邮箱
            sender_email = None
            if scope == "仅当前发件人" and hasattr(self, 'sender_email'):
                sender_email = self.sender_email.get().strip()

            self.log_message(f"🔍 开始智能搜索: {query}")
            results_text.insert(tk.END, "🔍 正在搜索，请稍候...\n\n")

            # 执行搜索
            results = search_engine.semantic_search(query, sender_email, limit)

            # 清空并显示结果
            results_text.delete(1.0, tk.END)

            if results:
                results_text.insert(tk.END, f"🔍 找到 {len(results)} 个相关结果:\n")
                results_text.insert(tk.END, f"搜索范围: {scope}\n")
                results_text.insert(tk.END, f"查询内容: {query}\n")
                results_text.insert(tk.END, "=" * 60 + "\n\n")

                for i, result in enumerate(results, 1):
                    relevance = result.get('relevance_score', 0)
                    results_text.insert(tk.END, f"📧 结果 {i} (相关度: {relevance:.3f})\n")
                    results_text.insert(tk.END, f"时间: {result.get('send_time', '')[:19]}\n")
                    results_text.insert(tk.END, f"发件人: {result.get('sender_email', '')}\n")
                    results_text.insert(tk.END, f"收件人: {result.get('recipient_email', '')}\n")
                    results_text.insert(tk.END, f"主题: {result.get('subject', '')}\n")

                    # 显示正文预览
                    body = result.get('body', '')
                    if len(body) > 100:
                        body_preview = body[:100] + "..."
                    else:
                        body_preview = body
                    results_text.insert(tk.END, f"正文: {body_preview}\n")

                    # 显示匹配关键词
                    if 'matched_terms' in result:
                        results_text.insert(tk.END, f"匹配词: {', '.join(result['matched_terms'])}\n")

                    results_text.insert(tk.END, "-" * 60 + "\n\n")

                self.log_message(f"✅ 智能搜索完成，找到 {len(results)} 个结果")
            else:
                results_text.insert(tk.END, "❌ 未找到相关结果\n\n")
                results_text.insert(tk.END, "建议:\n")
                results_text.insert(tk.END, "• 尝试使用不同的关键词\n")
                results_text.insert(tk.END, "• 检查搜索范围设置\n")
                results_text.insert(tk.END, "• 确保数据库中有相关邮件记录\n")
                self.log_message("⚠️ 智能搜索未找到结果")

        except Exception as e:
            error_msg = f"搜索出错: {str(e)}"
            results_text.delete(1.0, tk.END)
            results_text.insert(tk.END, f"❌ {error_msg}\n\n")
            results_text.insert(tk.END, "可能的原因:\n")
            results_text.insert(tk.END, "• 数据库连接问题\n")
            results_text.insert(tk.END, "• 搜索引擎初始化失败\n")
            results_text.insert(tk.END, "• 查询参数错误\n")
            self.log_message(f"❌ 智能搜索失败: {str(e)}")
            print(f"智能搜索错误: {e}")  # 调试用

    def _create_monitor_interface(self, parent):
        """创建监控管理界面"""
        try:
            # 监控状态框架
            status_frame = ttk.LabelFrame(parent, text="📡 监控状态", padding="10")
            status_frame.pack(fill=tk.X, pady=(0, 10))

            # 状态显示
            self.monitor_status_display = ttk.Label(status_frame, text="📡 监控未启动",
                                                   font=('Microsoft YaHei UI', 12, 'bold'),
                                                   foreground=self.colors['warning'])
            self.monitor_status_display.pack(anchor=tk.W)

            # 监控控制按钮
            control_frame = ttk.Frame(status_frame)
            control_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(control_frame, text="📡 开始监控",
                      command=self.start_reply_monitoring, style='Success.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(control_frame, text="⏹️ 停止监控",
                      command=self.stop_reply_monitoring, style='Danger.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(control_frame, text="🔧 测试连接",
                      command=self.test_monitor, style='Primary.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(control_frame, text="🔄 重置监控",
                      command=self.reset_monitor, style='Warning.TButton').pack(side=tk.LEFT)

            # 监控设置框架
            settings_frame = ttk.LabelFrame(parent, text="⚙️ 监控设置", padding="10")
            settings_frame.pack(fill=tk.X, pady=(0, 10))

            # 检查间隔设置
            interval_frame = ttk.Frame(settings_frame)
            interval_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(interval_frame, text="检查间隔(分钟):").pack(side=tk.LEFT)
            self.check_interval = ttk.Spinbox(interval_frame, from_=1, to=60, value=5, width=10)
            self.check_interval.pack(side=tk.LEFT, padx=(10, 20))

            ttk.Label(interval_frame, text="最大检查次数:").pack(side=tk.LEFT)
            self.max_checks = ttk.Spinbox(interval_frame, from_=1, to=100, value=24, width=10)
            self.max_checks.pack(side=tk.LEFT, padx=(10, 0))

            # 监控结果显示 - 使用Notebook分页
            results_frame = ttk.LabelFrame(parent, text="📋 监控结果", padding="10")
            results_frame.pack(fill=tk.BOTH, expand=True)

            # 创建分页控件
            self.monitor_notebook = ttk.Notebook(results_frame)
            self.monitor_notebook.pack(fill=tk.BOTH, expand=True)

            # 第一页：监控历史
            history_frame = ttk.Frame(self.monitor_notebook)
            self.monitor_notebook.add(history_frame, text="📊 监控历史")

            # 创建树形视图显示监控结果
            columns = ('时间', '收件人', '回复类型', '状态')
            self.monitor_results_tree = ttk.Treeview(history_frame, columns=columns, show='headings', height=10)

            # 设置列标题
            for col in columns:
                self.monitor_results_tree.heading(col, text=col)
                self.monitor_results_tree.column(col, width=150)

            # 添加滚动条
            monitor_scrollbar = ttk.Scrollbar(history_frame, orient=tk.VERTICAL, command=self.monitor_results_tree.yview)
            self.monitor_results_tree.configure(yscrollcommand=monitor_scrollbar.set)

            # 布局
            self.monitor_results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            monitor_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 第二页：有效收件人
            valid_frame = ttk.Frame(self.monitor_notebook)
            self.monitor_notebook.add(valid_frame, text="✅ 有效收件人")

            # 有效收件人列表
            valid_list_frame = ttk.Frame(valid_frame)
            valid_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            self.valid_recipients_text = scrolledtext.ScrolledText(
                valid_list_frame, height=8, wrap=tk.WORD,
                font=('Consolas', 9)
            )
            self.valid_recipients_text.pack(fill=tk.BOTH, expand=True)

            # 有效收件人操作按钮
            valid_buttons_frame = ttk.Frame(valid_frame)
            valid_buttons_frame.pack(fill=tk.X, pady=(5, 0))

            ttk.Button(valid_buttons_frame, text="🔄 刷新列表",
                      command=self.refresh_valid_recipients_list).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(valid_buttons_frame, text="📋 复制列表",
                      command=self.copy_valid_recipients_list).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(valid_buttons_frame, text="📤 导入主系统",
                      command=self.import_valid_to_main_system).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(valid_buttons_frame, text="📊 导入质量数据库",
                      command=self.import_valid_to_quality_database,
                      style='Success.TButton').pack(side=tk.LEFT)

            # 第三页：无效收件人
            invalid_frame = ttk.Frame(self.monitor_notebook)
            self.monitor_notebook.add(invalid_frame, text="❌ 无效收件人")

            # 无效收件人列表
            invalid_list_frame = ttk.Frame(invalid_frame)
            invalid_list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

            self.invalid_recipients_text = scrolledtext.ScrolledText(
                invalid_list_frame, height=8, wrap=tk.WORD,
                font=('Consolas', 9)
            )
            self.invalid_recipients_text.pack(fill=tk.BOTH, expand=True)

            # 无效收件人操作按钮
            invalid_buttons_frame = ttk.Frame(invalid_frame)
            invalid_buttons_frame.pack(fill=tk.X, pady=(5, 0))

            ttk.Button(invalid_buttons_frame, text="🔄 刷新列表",
                      command=self.refresh_invalid_recipients_list).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(invalid_buttons_frame, text="🗑️ 清理无效",
                      command=self.clean_invalid_recipients).pack(side=tk.LEFT, padx=(0, 5))
            ttk.Button(invalid_buttons_frame, text="📊 分析原因",
                      command=self.analyze_invalid_reasons).pack(side=tk.LEFT)

            # 初始化加载数据
            self.refresh_valid_recipients_list()
            self.refresh_invalid_recipients_list()

        except Exception as e:
            print(f"创建监控界面错误: {e}")

    def _create_quality_manager_interface(self, parent, quality_manager):
        """创建质量管理器界面"""
        try:
            # 收件人管理框架
            recipient_frame = ttk.LabelFrame(parent, text="📊 收件人质量管理", padding="10")
            recipient_frame.pack(fill=tk.X, pady=(0, 10))

            # 操作按钮
            button_frame = ttk.Frame(recipient_frame)
            button_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Button(button_frame, text="📊 分析质量",
                      command=lambda: self._analyze_quality(quality_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="📈 生成报告",
                      command=lambda: self._generate_quality_report(quality_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🔄 刷新数据",
                      command=lambda: self._refresh_quality_data(quality_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="☑️ 全选",
                      command=self._select_all_quality_records).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🗑️ 删除选中",
                      command=lambda: self._delete_selected_quality_records(quality_manager),
                      style='Danger.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🧹 清空数据库",
                      command=lambda: self._clear_all_quality_data(quality_manager),
                      style='Danger.TButton').pack(side=tk.LEFT)

            # 质量数据显示
            data_frame = ttk.LabelFrame(parent, text="📋 质量数据", padding="10")
            data_frame.pack(fill=tk.BOTH, expand=True)

            # 创建树形视图
            columns = ('邮箱', '质量分数', '发送次数', '回复次数', '状态')
            self.quality_tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=15)

            for col in columns:
                self.quality_tree.heading(col, text=col)
                self.quality_tree.column(col, width=120)

            # 滚动条
            quality_scrollbar = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.quality_tree.yview)
            self.quality_tree.configure(yscrollcommand=quality_scrollbar.set)

            self.quality_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            quality_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            print(f"创建质量管理界面错误: {e}")

    def _create_anti_spam_interface(self, parent, anti_spam_manager):
        """创建反垃圾邮件界面"""
        try:
            # 风险评估框架
            risk_frame = ttk.LabelFrame(parent, text="🛡️ 风险评估", padding="10")
            risk_frame.pack(fill=tk.X, pady=(0, 10))

            # 风险指标显示
            self.risk_level_label = ttk.Label(risk_frame, text="🟡 风险等级: 中等",
                                            font=('Microsoft YaHei UI', 12, 'bold'))
            self.risk_level_label.pack(anchor=tk.W, pady=(0, 10))

            # 评估按钮
            eval_frame = ttk.Frame(risk_frame)
            eval_frame.pack(fill=tk.X)

            ttk.Button(eval_frame, text="🔍 评估风险",
                      command=lambda: self._evaluate_spam_risk(anti_spam_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(eval_frame, text="⚙️ 调整策略",
                      command=lambda: self._adjust_anti_spam_strategy(anti_spam_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(eval_frame, text="📊 查看报告",
                      command=lambda: self._show_anti_spam_report(anti_spam_manager)).pack(side=tk.LEFT)

            # 策略设置框架
            strategy_frame = ttk.LabelFrame(parent, text="⚙️ 防护策略", padding="10")
            strategy_frame.pack(fill=tk.BOTH, expand=True)

            # 策略选项
            self.spam_protection_level = tk.StringVar(value="medium")
            ttk.Label(strategy_frame, text="防护等级:").pack(anchor=tk.W)

            level_frame = ttk.Frame(strategy_frame)
            level_frame.pack(fill=tk.X, pady=(5, 10))

            ttk.Radiobutton(level_frame, text="🟢 低", variable=self.spam_protection_level, value="low").pack(side=tk.LEFT, padx=(0, 20))
            ttk.Radiobutton(level_frame, text="🟡 中", variable=self.spam_protection_level, value="medium").pack(side=tk.LEFT, padx=(0, 20))
            ttk.Radiobutton(level_frame, text="🔴 高", variable=self.spam_protection_level, value="high").pack(side=tk.LEFT)

        except Exception as e:
            print(f"创建反垃圾界面错误: {e}")

    def _create_emergency_interface(self, parent, emergency_manager):
        """创建应急管理界面"""
        try:
            # 应急状态框架
            status_frame = ttk.LabelFrame(parent, text="🆘 应急状态", padding="10")
            status_frame.pack(fill=tk.X, pady=(0, 10))

            # 状态显示
            self.emergency_status_label = ttk.Label(status_frame, text="🟢 系统正常",
                                                   font=('Microsoft YaHei UI', 12, 'bold'),
                                                   foreground=self.colors['success'])
            self.emergency_status_label.pack(anchor=tk.W, pady=(0, 10))

            # 应急控制按钮
            control_frame = ttk.Frame(status_frame)
            control_frame.pack(fill=tk.X)

            ttk.Button(control_frame, text="🆘 激活应急",
                      command=lambda: self._activate_emergency(emergency_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(control_frame, text="🔄 重置系统",
                      command=lambda: self._reset_emergency(emergency_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(control_frame, text="📊 应急报告",
                      command=lambda: self._show_emergency_report(emergency_manager)).pack(side=tk.LEFT)

            # 应急设置框架
            settings_frame = ttk.LabelFrame(parent, text="⚙️ 应急设置", padding="10")
            settings_frame.pack(fill=tk.BOTH, expand=True)

            # 触发条件设置
            ttk.Label(settings_frame, text="无回复邮件阈值:").pack(anchor=tk.W)
            self.no_reply_threshold = ttk.Spinbox(settings_frame, from_=1, to=20, value=5, width=10)
            self.no_reply_threshold.pack(anchor=tk.W, pady=(5, 10))

            ttk.Label(settings_frame, text="检查时间间隔(分钟):").pack(anchor=tk.W)
            self.check_interval_emergency = ttk.Spinbox(settings_frame, from_=1, to=60, value=10, width=10)
            self.check_interval_emergency.pack(anchor=tk.W, pady=(5, 0))

        except Exception as e:
            print(f"创建应急界面错误: {e}")

    def _create_batch_manager_interface(self, parent, batch_manager):
        """创建批次管理界面"""
        try:
            # 批次设置框架
            batch_frame = ttk.LabelFrame(parent, text="📈 批次设置", padding="10")
            batch_frame.pack(fill=tk.X, pady=(0, 10))

            # 批次大小设置
            size_frame = ttk.Frame(batch_frame)
            size_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(size_frame, text="批次大小:").pack(side=tk.LEFT)
            self.batch_size = ttk.Spinbox(size_frame, from_=1, to=100, value=10, width=10)
            self.batch_size.pack(side=tk.LEFT, padx=(10, 20))

            ttk.Label(size_frame, text="批次间隔(秒):").pack(side=tk.LEFT)
            self.batch_interval = ttk.Spinbox(size_frame, from_=1, to=300, value=30, width=10)
            self.batch_interval.pack(side=tk.LEFT, padx=(10, 0))

            # 批次操作按钮
            button_frame = ttk.Frame(batch_frame)
            button_frame.pack(fill=tk.X)

            ttk.Button(button_frame, text="📊 创建批次",
                      command=lambda: self._create_batches(batch_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🔄 重新分批",
                      command=lambda: self._recreate_batches(batch_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="📋 查看批次",
                      command=lambda: self._view_batches(batch_manager)).pack(side=tk.LEFT)

            # 批次列表显示
            list_frame = ttk.LabelFrame(parent, text="📋 批次列表", padding="10")
            list_frame.pack(fill=tk.BOTH, expand=True)

            # 创建批次列表
            columns = ('批次号', '邮件数量', '状态', '创建时间')
            self.batch_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=12)

            for col in columns:
                self.batch_tree.heading(col, text=col)
                self.batch_tree.column(col, width=120)

            # 滚动条
            batch_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.batch_tree.yview)
            self.batch_tree.configure(yscrollcommand=batch_scrollbar.set)

            self.batch_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            batch_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            print(f"创建批次管理界面错误: {e}")

    def _create_import_interface(self, parent):
        """创建导入功能界面"""
        try:
            # 导入选项框架
            import_frame = ttk.LabelFrame(parent, text="📤 导入选项", padding="10")
            import_frame.pack(fill=tk.X, pady=(0, 10))

            # 导入类型选择
            type_frame = ttk.Frame(import_frame)
            type_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(type_frame, text="导入类型:").pack(anchor=tk.W)

            self.import_type = tk.StringVar(value="quality")
            type_options = ttk.Frame(type_frame)
            type_options.pack(fill=tk.X, pady=(5, 0))

            ttk.Radiobutton(type_options, text="📊 质量数据", variable=self.import_type, value="quality").pack(side=tk.LEFT, padx=(0, 20))
            ttk.Radiobutton(type_options, text="📋 收件人列表", variable=self.import_type, value="recipients").pack(side=tk.LEFT, padx=(0, 20))
            ttk.Radiobutton(type_options, text="📈 批次数据", variable=self.import_type, value="batches").pack(side=tk.LEFT)

            # 导入操作按钮
            button_frame = ttk.Frame(import_frame)
            button_frame.pack(fill=tk.X)

            ttk.Button(button_frame, text="📁 选择文件",
                      command=self._select_import_file).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="📤 开始导入",
                      command=self._start_import).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="📋 导入到主系统",
                      command=self._import_to_main_system).pack(side=tk.LEFT)

            # 导入状态显示
            status_frame = ttk.LabelFrame(parent, text="📊 导入状态", padding="10")
            status_frame.pack(fill=tk.BOTH, expand=True)

            self.import_status_text = tk.Text(status_frame, height=15, wrap=tk.WORD)
            import_scrollbar = ttk.Scrollbar(status_frame, orient=tk.VERTICAL, command=self.import_status_text.yview)
            self.import_status_text.configure(yscrollcommand=import_scrollbar.set)

            self.import_status_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            import_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            print(f"创建导入界面错误: {e}")

    def _create_duplicate_detection_interface(self, parent, detector):
        """创建重复检测界面"""
        try:
            # 检测设置框架
            settings_frame = ttk.LabelFrame(parent, text="🔍 检测设置", padding="10")
            settings_frame.pack(fill=tk.X, pady=(0, 10))

            # 相似度阈值设置
            threshold_frame = ttk.Frame(settings_frame)
            threshold_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(threshold_frame, text="相似度阈值:").pack(side=tk.LEFT)
            self.similarity_threshold = ttk.Scale(threshold_frame, from_=0.1, to=1.0, value=0.8, orient=tk.HORIZONTAL, length=200)
            self.similarity_threshold.pack(side=tk.LEFT, padx=(10, 10))

            self.threshold_label = ttk.Label(threshold_frame, text="0.8")
            self.threshold_label.pack(side=tk.LEFT)

            # 绑定阈值变化事件
            self.similarity_threshold.configure(command=lambda v: self.threshold_label.configure(text=f"{float(v):.2f}"))

            # 检测操作按钮
            button_frame = ttk.Frame(settings_frame)
            button_frame.pack(fill=tk.X)

            ttk.Button(button_frame, text="🔍 开始检测",
                      command=lambda: self._start_duplicate_detection(detector)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="📊 查看报告",
                      command=lambda: self._show_duplicate_report(detector)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🗑️ 清理重复",
                      command=lambda: self._clean_duplicates(detector)).pack(side=tk.LEFT)

            # 检测结果显示
            results_frame = ttk.LabelFrame(parent, text="📋 检测结果", padding="10")
            results_frame.pack(fill=tk.BOTH, expand=True)

            # 创建结果树形视图
            columns = ('邮件1', '邮件2', '相似度', '类型', '建议')
            self.duplicate_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

            for col in columns:
                self.duplicate_tree.heading(col, text=col)
                self.duplicate_tree.column(col, width=150)

            # 滚动条
            duplicate_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.duplicate_tree.yview)
            self.duplicate_tree.configure(yscrollcommand=duplicate_scrollbar.set)

            self.duplicate_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            duplicate_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        except Exception as e:
            print(f"创建重复检测界面错误: {e}")

    def _create_monitoring_settings_interface(self, parent):
        """创建监控设置界面"""
        try:
            # 基本设置框架
            basic_frame = ttk.LabelFrame(parent, text="📊 基本设置", padding="10")
            basic_frame.pack(fill=tk.X, pady=(0, 10))

            # 检查间隔设置
            interval_frame = ttk.Frame(basic_frame)
            interval_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(interval_frame, text="检查间隔(分钟):").pack(side=tk.LEFT)
            self.monitor_interval = ttk.Spinbox(interval_frame, from_=1, to=60, value=5, width=10)
            self.monitor_interval.pack(side=tk.LEFT, padx=(10, 20))

            ttk.Label(interval_frame, text="最大检查次数:").pack(side=tk.LEFT)
            self.monitor_max_checks = ttk.Spinbox(interval_frame, from_=1, to=100, value=24, width=10)
            self.monitor_max_checks.pack(side=tk.LEFT, padx=(10, 0))

            # 高级设置框架
            advanced_frame = ttk.LabelFrame(parent, text="⚙️ 高级设置", padding="10")
            advanced_frame.pack(fill=tk.X, pady=(0, 10))

            # 自动启动监控
            self.auto_start_monitoring = tk.BooleanVar(value=False)
            ttk.Checkbutton(advanced_frame, text="发送后自动启动监控",
                           variable=self.auto_start_monitoring).pack(anchor=tk.W, pady=(0, 10))

            # 监控超时设置
            timeout_frame = ttk.Frame(advanced_frame)
            timeout_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(timeout_frame, text="连接超时(秒):").pack(side=tk.LEFT)
            self.monitor_timeout = ttk.Spinbox(timeout_frame, from_=10, to=300, value=30, width=10)
            self.monitor_timeout.pack(side=tk.LEFT, padx=(10, 0))

            # 操作按钮
            button_frame = ttk.Frame(parent)
            button_frame.pack(fill=tk.X, pady=(20, 0))

            ttk.Button(button_frame, text="💾 保存设置",
                      command=self._save_monitoring_settings).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🔄 重置默认",
                      command=self._reset_monitoring_settings).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🔧 测试设置",
                      command=self._test_monitoring_settings).pack(side=tk.LEFT)

        except Exception as e:
            print(f"创建监控设置界面错误: {e}")

    def _create_coordination_settings_interface(self, parent):
        """创建协调设置界面"""
        try:
            # 协调模式设置
            mode_frame = ttk.LabelFrame(parent, text="🧠 协调模式", padding="10")
            mode_frame.pack(fill=tk.X, pady=(0, 10))

            self.coordination_mode = tk.StringVar(value="auto")
            ttk.Label(mode_frame, text="协调模式:").pack(anchor=tk.W)

            mode_options = ttk.Frame(mode_frame)
            mode_options.pack(fill=tk.X, pady=(5, 10))

            ttk.Radiobutton(mode_options, text="🤖 自动协调", variable=self.coordination_mode, value="auto").pack(side=tk.LEFT, padx=(0, 20))
            ttk.Radiobutton(mode_options, text="📋 手动协调", variable=self.coordination_mode, value="manual").pack(side=tk.LEFT, padx=(0, 20))
            ttk.Radiobutton(mode_options, text="🎯 智能协调", variable=self.coordination_mode, value="smart").pack(side=tk.LEFT)

            # 协调功能选择
            features_frame = ttk.LabelFrame(parent, text="🔧 协调功能", padding="10")
            features_frame.pack(fill=tk.X, pady=(0, 10))

            # 功能复选框
            self.coord_quality = tk.BooleanVar(value=True)
            self.coord_anti_spam = tk.BooleanVar(value=True)
            self.coord_emergency = tk.BooleanVar(value=True)
            self.coord_monitoring = tk.BooleanVar(value=True)

            ttk.Checkbutton(features_frame, text="📊 质量数据库协调", variable=self.coord_quality).pack(anchor=tk.W, pady=2)
            ttk.Checkbutton(features_frame, text="🛡️ 反垃圾邮件协调", variable=self.coord_anti_spam).pack(anchor=tk.W, pady=2)
            ttk.Checkbutton(features_frame, text="🆘 应急系统协调", variable=self.coord_emergency).pack(anchor=tk.W, pady=2)
            ttk.Checkbutton(features_frame, text="📬 监控系统协调", variable=self.coord_monitoring).pack(anchor=tk.W, pady=2)

            # 协调参数设置
            params_frame = ttk.LabelFrame(parent, text="⚙️ 协调参数", padding="10")
            params_frame.pack(fill=tk.X, pady=(0, 10))

            # 协调频率
            freq_frame = ttk.Frame(params_frame)
            freq_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Label(freq_frame, text="协调频率(分钟):").pack(side=tk.LEFT)
            self.coord_frequency = ttk.Spinbox(freq_frame, from_=1, to=60, value=10, width=10)
            self.coord_frequency.pack(side=tk.LEFT, padx=(10, 20))

            ttk.Label(freq_frame, text="风险阈值:").pack(side=tk.LEFT)
            self.coord_risk_threshold = ttk.Scale(freq_frame, from_=0.1, to=1.0, value=0.7, orient=tk.HORIZONTAL, length=150)
            self.coord_risk_threshold.pack(side=tk.LEFT, padx=(10, 0))

            # 操作按钮
            button_frame = ttk.Frame(parent)
            button_frame.pack(fill=tk.X, pady=(20, 0))

            ttk.Button(button_frame, text="💾 保存设置",
                      command=self._save_coordination_settings).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🔄 重置默认",
                      command=self._reset_coordination_settings).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(button_frame, text="🧪 测试协调",
                      command=self._test_coordination_settings).pack(side=tk.LEFT)

        except Exception as e:
            print(f"创建协调设置界面错误: {e}")

    def _show_history_stats(self, history_manager):
        """显示历史统计"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else None
            stats = history_manager.get_statistics(sender_email)

            stats_msg = f"""📊 邮件发送统计

📧 总发送数量: {stats.get('total_sent', 0)} 封
✅ 成功发送: {stats.get('successful_sent', 0)} 封
❌ 发送失败: {stats.get('failed_sent', 0)} 封
📈 成功率: {stats.get('success_rate', 0):.1f}%

📅 最近发送: {stats.get('last_send_time', '无')}
📊 今日发送: {stats.get('today_sent', 0)} 封
📊 本周发送: {stats.get('week_sent', 0)} 封
📊 本月发送: {stats.get('month_sent', 0)} 封"""

            messagebox.showinfo("发送统计", stats_msg)

        except Exception as e:
            messagebox.showerror("错误", f"获取统计信息失败: {str(e)}")

    def _export_selected_history(self, history_manager, results_tree):
        """导出选中的历史记录"""
        try:
            from tkinter import filedialog

            # 选择保存路径
            filepath = filedialog.asksaveasfilename(
                title="导出历史记录",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
            )

            if filepath:
                sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else None
                history_manager.export_history(filepath, sender_email)
                messagebox.showinfo("导出成功", f"历史记录已导出到:\n{filepath}")

        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {str(e)}")

    def _cleanup_old_records(self, history_manager):
        """清理旧记录"""
        try:
            import tkinter.simpledialog as simpledialog

            # 询问保留天数
            days = simpledialog.askinteger("清理旧记录", "请输入要保留的天数:", initialvalue=365, minvalue=1, maxvalue=3650)
            if days is None:
                return

            if messagebox.askyesno("确认清理", f"确定删除 {days} 天前的记录吗？"):
                deleted_count = history_manager.cleanup_old_records(days)
                messagebox.showinfo("清理完成", f"已删除 {deleted_count} 条旧记录")

        except Exception as e:
            messagebox.showerror("错误", f"清理失败: {str(e)}")

    def update_queue_status(self):
        """更新队列状态显示"""
        try:
            if hasattr(self, 'email_queue'):
                queue_count = len(self.email_queue)
                pending_count = len([task for task in self.email_queue if task.get('status') == 'pending'])
                completed_count = len([task for task in self.email_queue if task.get('status') == 'completed'])
                failed_count = len([task for task in self.email_queue if task.get('status') == 'failed'])
                sending_count = len([task for task in self.email_queue if task.get('status') == 'sending'])

                # 更新状态标签
                if hasattr(self, 'queue_task_count'):
                    self.queue_task_count.configure(text=f"📊 任务: {queue_count}个")
                if hasattr(self, 'queue_progress'):
                    progress = (completed_count + failed_count) / queue_count * 100 if queue_count > 0 else 0
                    self.queue_progress.configure(text=f"📈 进度: {progress:.1f}%")
                if hasattr(self, 'queue_speed'):
                    self.queue_speed.configure(text=f"⚡ 速度: {sending_count}封/分")

                # 更新队列列表显示
                self.update_queue_list_display()

        except Exception as e:
            print(f"更新队列状态错误: {e}")

    def update_queue_list_display(self):
        """更新队列列表显示 - 像操作日志一样的滚动显示"""
        if not hasattr(self, 'queue_list_text'):
            return

        try:
            # 清空现有内容
            self.queue_list_text.config(state=tk.NORMAL)
            self.queue_list_text.delete(1.0, tk.END)

            if not hasattr(self, 'email_queue') or not self.email_queue:
                self.queue_list_text.insert(tk.END, "📭 队列为空，暂无任务\n")
                self.queue_list_text.insert(tk.END, "💡 提示：点击'添加任务'按钮添加邮件发送任务\n")
                self.queue_list_text.insert(tk.END, "\n" + "="*50 + "\n")
                self.queue_list_text.insert(tk.END, "🖱️ 滚动功能说明：\n")
                self.queue_list_text.insert(tk.END, "• 使用鼠标滚轮可以上下滚动\n")
                self.queue_list_text.insert(tk.END, "• 使用右侧滚动条可以拖拽滚动\n")
                self.queue_list_text.insert(tk.END, "• 添加任务后会显示实际队列内容\n")
                self.queue_list_text.insert(tk.END, "• 当内容超过显示区域时会自动出现滚动条\n")
                self.queue_list_text.insert(tk.END, "\n📝 操作说明：\n")
                self.queue_list_text.insert(tk.END, "1. 在左侧填写邮件信息\n")
                self.queue_list_text.insert(tk.END, "2. 点击'添加任务'按钮\n")
                self.queue_list_text.insert(tk.END, "3. 任务会显示在此列表中\n")
                self.queue_list_text.insert(tk.END, "4. 可以实时查看任务状态变化\n")
                self.queue_list_text.insert(tk.END, "\n🎯 状态图标说明：\n")
                self.queue_list_text.insert(tk.END, "⏳ 待发送  📤 发送中  ✅ 已完成  ❌ 失败\n")
            else:
                # 添加队列标题
                self.queue_list_text.insert(tk.END, f"📋 队列任务列表 (共 {len(self.email_queue)} 个任务)\n")
                self.queue_list_text.insert(tk.END, "=" * 50 + "\n")

                # 显示每个任务
                for i, task in enumerate(self.email_queue, 1):
                    # 状态图标
                    status_icons = {
                        'pending': '⏳',
                        'sending': '📤',
                        'completed': '✅',
                        'failed': '❌'
                    }
                    status_icon = status_icons.get(task.get('status'), '❓')

                    # 计算收件人数量
                    recipients = task.get('recipient_emails', '')
                    if recipients:
                        recipient_count = len([email.strip() for email in
                                              recipients.replace(',', '\n').replace(';', '\n').split('\n')
                                              if email.strip()])
                    else:
                        recipient_count = 0

                    # 格式化主题（限制长度）
                    subject = task.get('subject', '无主题')
                    subject = subject[:25] + '...' if len(subject) > 25 else subject

                    # 发送模式
                    mode_text = {
                        'fast': '快速',
                        'standard': '标准',
                        'safe': '安全'
                    }.get(task.get('send_mode'), '标准')

                    # 创建时间
                    created_time = task.get('created_time', '')
                    if hasattr(created_time, 'strftime'):
                        time_str = created_time.strftime("%H:%M:%S")
                    elif isinstance(created_time, str):
                        time_str = created_time[:8] if len(created_time) >= 8 else created_time
                    else:
                        time_str = "未知"

                    # 插入任务信息
                    task_line = f"{status_icon} #{i:02d} | {subject:<27} | {recipient_count:2d}人 | {mode_text:4s} | {time_str}\n"
                    self.queue_list_text.insert(tk.END, task_line)

                # 添加统计信息
                pending_count = len([task for task in self.email_queue if task.get('status') == 'pending'])
                completed_count = len([task for task in self.email_queue if task.get('status') == 'completed'])
                failed_count = len([task for task in self.email_queue if task.get('status') == 'failed'])
                sending_count = len([task for task in self.email_queue if task.get('status') == 'sending'])

                self.queue_list_text.insert(tk.END, "=" * 50 + "\n")
                self.queue_list_text.insert(tk.END, f"📊 统计: 待发送 {pending_count} | 发送中 {sending_count} | 已完成 {completed_count} | 失败 {failed_count}\n")

                # 添加操作提示
                self.queue_list_text.insert(tk.END, "\n💡 操作提示：\n")
                self.queue_list_text.insert(tk.END, "• 使用鼠标滚轮或滚动条查看更多任务\n")
                self.queue_list_text.insert(tk.END, "• 点击'队列管理'按钮可以编辑任务\n")

            # 自动滚动到底部
            self.queue_list_text.see(tk.END)
            # 不设置为DISABLED，保持滚动功能可用

        except Exception as e:
            print(f"更新队列列表显示错误: {e}")

    def _execute_queue_sending(self):
        """执行队列发送（后台线程）"""
        try:
            import time

            if not hasattr(self, 'email_queue'):
                return

            pending_tasks = [task for task in self.email_queue if task.get('status') == 'pending']

            for i, task in enumerate(pending_tasks):
                if getattr(self, 'should_stop', False):
                    break

                while getattr(self, 'should_pause', False) and not getattr(self, 'should_stop', False):
                    time.sleep(1)

                if getattr(self, 'should_stop', False):
                    break

                # 执行发送任务
                self.root.after(0, lambda t=task: self._send_queue_task(t))

                # 等待发送完成
                time.sleep(2)

            # 发送完成
            self.root.after(0, lambda: self.log_message("✅ 队列发送完成"))

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"❌ 队列发送出错: {str(e)}"))

    def _send_queue_task(self, task):
        """发送队列任务"""
        try:
            self.log_message(f"📧 正在发送队列任务: {task.get('subject', '')}")

            # 更新任务状态
            task['status'] = 'sending'

            # 这里应该调用实际的发送逻辑
            # 为了演示，我们模拟发送成功
            task['status'] = 'completed'
            task['sent_time'] = datetime.datetime.now().isoformat()

            self.update_queue_status()
            self.log_message(f"✅ 队列任务发送完成: {task.get('subject', '')}")

        except Exception as e:
            task['status'] = 'failed'
            task['error'] = str(e)
            self.log_message(f"❌ 队列任务发送失败: {str(e)}")

    # ==================== 辅助功能方法 ====================

    def _analyze_quality(self, quality_manager):
        """分析收件人质量"""
        try:
            messagebox.showinfo("质量分析", "正在分析收件人质量，请稍候...")
            # 这里可以调用实际的质量分析逻辑
        except Exception as e:
            messagebox.showerror("错误", f"质量分析失败: {str(e)}")

    def _generate_quality_report(self, quality_manager):
        """生成质量报告"""
        try:
            messagebox.showinfo("质量报告", "质量报告生成功能正在完善中")
        except Exception as e:
            messagebox.showerror("错误", f"生成报告失败: {str(e)}")

    def _refresh_quality_data(self, quality_manager):
        """刷新质量数据"""
        try:
            messagebox.showinfo("刷新数据", "质量数据已刷新")
        except Exception as e:
            messagebox.showerror("错误", f"刷新数据失败: {str(e)}")

    def _evaluate_spam_risk(self, anti_spam_manager):
        """评估垃圾邮件风险"""
        try:
            messagebox.showinfo("风险评估", "正在评估发送风险...")
        except Exception as e:
            messagebox.showerror("错误", f"风险评估失败: {str(e)}")

    def _adjust_anti_spam_strategy(self, anti_spam_manager):
        """调整反垃圾策略"""
        try:
            messagebox.showinfo("策略调整", "反垃圾策略已调整")
        except Exception as e:
            messagebox.showerror("错误", f"策略调整失败: {str(e)}")

    def _show_anti_spam_report(self, anti_spam_manager):
        """显示反垃圾报告"""
        try:
            messagebox.showinfo("反垃圾报告", "反垃圾邮件报告功能正在完善中")
        except Exception as e:
            messagebox.showerror("错误", f"显示报告失败: {str(e)}")

    def _activate_emergency(self, emergency_manager):
        """激活应急模式"""
        try:
            if messagebox.askyesno("激活应急", "确定要激活应急模式吗？"):
                messagebox.showinfo("应急激活", "应急模式已激活")
        except Exception as e:
            messagebox.showerror("错误", f"激活应急失败: {str(e)}")

    def _reset_emergency(self, emergency_manager):
        """重置应急系统"""
        try:
            if messagebox.askyesno("重置系统", "确定要重置应急系统吗？"):
                messagebox.showinfo("系统重置", "应急系统已重置")
        except Exception as e:
            messagebox.showerror("错误", f"重置系统失败: {str(e)}")

    def _show_emergency_report(self, emergency_manager):
        """显示应急报告"""
        try:
            messagebox.showinfo("应急报告", "应急系统报告功能正在完善中")
        except Exception as e:
            messagebox.showerror("错误", f"显示报告失败: {str(e)}")

    def _create_batches(self, batch_manager):
        """创建批次"""
        try:
            batch_size = int(self.batch_size.get())
            messagebox.showinfo("创建批次", f"已创建批次，每批 {batch_size} 个收件人")
        except Exception as e:
            messagebox.showerror("错误", f"创建批次失败: {str(e)}")

    def _recreate_batches(self, batch_manager):
        """重新创建批次"""
        try:
            messagebox.showinfo("重新分批", "批次已重新创建")
        except Exception as e:
            messagebox.showerror("错误", f"重新分批失败: {str(e)}")

    def _view_batches(self, batch_manager):
        """查看批次"""
        try:
            messagebox.showinfo("查看批次", "批次查看功能正在完善中")
        except Exception as e:
            messagebox.showerror("错误", f"查看批次失败: {str(e)}")

    def _select_import_file(self):
        """选择导入文件"""
        try:
            from tkinter import filedialog
            filepath = filedialog.askopenfilename(
                title="选择导入文件",
                filetypes=[("Excel文件", "*.xlsx"), ("CSV文件", "*.csv"), ("JSON文件", "*.json"), ("所有文件", "*.*")]
            )
            if filepath:
                messagebox.showinfo("文件选择", f"已选择文件: {filepath}")
        except Exception as e:
            messagebox.showerror("错误", f"选择文件失败: {str(e)}")

    def _start_import(self):
        """开始导入"""
        try:
            import_type = self.import_type.get()
            messagebox.showinfo("开始导入", f"正在导入 {import_type} 数据...")
        except Exception as e:
            messagebox.showerror("错误", f"导入失败: {str(e)}")

    def _import_to_main_system(self):
        """导入到主系统 - 真正的集成功能"""
        try:
            self.log_message("📤 开始导入数据到主系统...")

            # 获取发件人邮箱
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            # 导入自动回复监控识别的有效收件人
            success_count = self._import_valid_recipients_from_monitor(sender_email)

            # 导入质量数据库中的优质收件人
            quality_count = self._import_quality_recipients(sender_email)

            # 更新主界面收件人列表
            total_imported = success_count + quality_count

            if total_imported > 0:
                result_msg = f"""✅ 数据导入成功！

📊 导入统计：
• 自动回复监控有效收件人：{success_count} 个
• 质量数据库优质收件人：{quality_count} 个
• 总计导入：{total_imported} 个

💡 已自动去重并更新到主界面收件人列表"""

                messagebox.showinfo("导入成功", result_msg)
                self.log_message(f"✅ 成功导入 {total_imported} 个收件人到主系统")
            else:
                messagebox.showinfo("提示", "没有找到可导入的收件人数据")

        except Exception as e:
            error_msg = f"导入主系统失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _import_valid_recipients_from_monitor(self, sender_email: str) -> int:
        """从自动回复监控导入有效收件人"""
        try:
            from email_receiver import EmailReceiver

            # 创建邮件接收器实例
            receiver = EmailReceiver(sender_email, "")  # 密码暂时为空，只用于查询

            # 获取有效收件人列表
            valid_recipients = receiver.get_valid_recipients(sender_email)

            if valid_recipients:
                # 添加到主界面收件人列表
                current_text = self.recipient_emails.get(1.0, tk.END).strip() if hasattr(self, 'recipient_emails') else ""
                current_recipients = set(self.parse_recipients(current_text))

                # 合并并去重
                all_recipients = current_recipients.union(set(valid_recipients))

                # 更新界面
                if hasattr(self, 'recipient_emails'):
                    self.recipient_emails.delete(1.0, tk.END)
                    self.recipient_emails.insert(1.0, '\n'.join(sorted(all_recipients)))

                self.log_message(f"📬 从自动回复监控导入 {len(valid_recipients)} 个有效收件人")
                return len(valid_recipients)

            return 0

        except Exception as e:
            self.log_message(f"❌ 从监控系统导入失败: {str(e)}")
            return 0

    def _import_quality_recipients(self, sender_email: str) -> int:
        """从质量数据库导入优质收件人"""
        try:
            from recipient_quality_manager import RecipientQualityManager

            # 创建质量管理器实例
            quality_manager = RecipientQualityManager()

            # 获取优质收件人（评分>70分的）
            quality_recipients = quality_manager.get_high_quality_recipients(
                sender_email=sender_email,
                min_score=70.0
            )

            if quality_recipients:
                # 提取邮箱地址
                recipient_emails = [r.email for r in quality_recipients]

                # 添加到主界面收件人列表
                current_text = self.recipient_emails.get(1.0, tk.END).strip() if hasattr(self, 'recipient_emails') else ""
                current_recipients = set(self.parse_recipients(current_text))

                # 合并并去重
                all_recipients = current_recipients.union(set(recipient_emails))

                # 更新界面
                if hasattr(self, 'recipient_emails'):
                    self.recipient_emails.delete(1.0, tk.END)
                    self.recipient_emails.insert(1.0, '\n'.join(sorted(all_recipients)))

                self.log_message(f"📊 从质量数据库导入 {len(recipient_emails)} 个优质收件人")
                return len(recipient_emails)

            return 0

        except Exception as e:
            self.log_message(f"❌ 从质量数据库导入失败: {str(e)}")
            return 0

    def open_system_integration(self):
        """打开系统集成管理"""
        try:
            self.log_message("🔗 打开系统集成管理")

            # 导入系统集成管理器
            from system_integration_manager import SystemIntegrationManager

            # 创建集成管理窗口
            integration_window = tk.Toplevel(self.root)
            integration_window.title("🔗 系统集成管理")
            integration_window.geometry("1000x700")
            integration_window.resizable(True, True)

            # 创建集成管理器实例
            integration_manager = SystemIntegrationManager()

            # 主框架
            main_frame = ttk.Frame(integration_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 创建集成管理界面
            self._create_integration_interface(main_frame, integration_manager)

            self.log_message("✅ 系统集成管理界面已打开")

        except Exception as e:
            error_msg = f"打开系统集成管理失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _create_integration_interface(self, parent, integration_manager):
        """创建系统集成界面"""
        try:
            # 标题
            title_label = ttk.Label(parent, text="🔗 系统集成管理中心",
                                   font=('Microsoft YaHei UI', 16, 'bold'))
            title_label.pack(pady=(0, 15))

            # 说明文本
            info_text = """
🎯 系统集成功能说明：
• 自动同步自动回复监控识别的有效收件人到质量数据库
• 根据质量数据库评分自动触发应急系统
• 实现各功能模块的深度协调和数据共享
• 解决系统相互独立不互联的问题
            """

            info_frame = ttk.LabelFrame(parent, text="📋 功能说明", padding="10")
            info_frame.pack(fill=tk.X, pady=(0, 15))

            ttk.Label(info_frame, text=info_text, font=('Microsoft YaHei UI', 9)).pack(anchor=tk.W)

            # 集成操作区域
            operation_frame = ttk.LabelFrame(parent, text="🔧 集成操作", padding="10")
            operation_frame.pack(fill=tk.X, pady=(0, 15))

            # 操作按钮
            btn_frame = ttk.Frame(operation_frame)
            btn_frame.pack(fill=tk.X, pady=(0, 10))

            ttk.Button(btn_frame, text="🔄 同步监控→质量库",
                      command=lambda: self._sync_monitor_to_quality(integration_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="⚡ 同步质量→应急",
                      command=lambda: self._sync_quality_to_emergency(integration_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="🎯 一键完整集成",
                      command=lambda: self._full_system_integration(integration_manager)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="📊 查看集成状态",
                      command=lambda: self._show_integration_status(integration_manager)).pack(side=tk.LEFT)

            # 集成状态显示区域
            status_frame = ttk.LabelFrame(parent, text="📊 集成状态", padding="10")
            status_frame.pack(fill=tk.BOTH, expand=True)

            # 创建状态显示文本框
            self.integration_status_text = scrolledtext.ScrolledText(
                status_frame, height=20, wrap=tk.WORD,
                font=('Consolas', 9)
            )
            self.integration_status_text.pack(fill=tk.BOTH, expand=True)

            # 显示初始状态
            self._display_integration_welcome()

        except Exception as e:
            print(f"创建系统集成界面错误: {e}")

    def _display_integration_welcome(self):
        """显示集成欢迎信息"""
        welcome_text = f"""🔗 系统集成管理中心
{'='*60}
初始化时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 集成功能：
• 自动回复监控 ↔ 质量数据库
• 质量数据库 ↔ 应急系统
• 应急系统 ↔ 反垃圾邮件
• 系统协调器统一管理

💡 使用说明：
1. 点击"同步监控→质量库"将自动回复监控的有效收件人同步到质量数据库
2. 点击"同步质量→应急"根据质量评分触发应急机制
3. 点击"一键完整集成"执行所有集成步骤
4. 点击"查看集成状态"查看详细的集成历史和状态

🔧 准备就绪，请选择集成操作...
"""

        if hasattr(self, 'integration_status_text'):
            self.integration_status_text.delete(1.0, tk.END)
            self.integration_status_text.insert(1.0, welcome_text)

    def _sync_monitor_to_quality(self, integration_manager):
        """同步监控数据到质量数据库"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            self._update_integration_status("🔄 开始同步自动回复监控数据到质量数据库...")

            # 执行同步
            result = integration_manager.sync_auto_reply_to_quality_db(sender_email)

            if result.get('success'):
                status_text = f"""✅ 同步完成！

📊 同步结果：
• 有效收件人：{result.get('valid_count', 0)} 个
• 无效收件人：{result.get('invalid_count', 0)} 个
• 总计同步：{result.get('total_synced', 0)} 个
• 同步时间：{result.get('sync_time', '')}

💡 所有数据已成功同步到质量数据库"""

                messagebox.showinfo("同步成功", f"成功同步 {result.get('total_synced', 0)} 个收件人到质量数据库")
            else:
                status_text = f"❌ 同步失败：{result.get('error', '未知错误')}"
                messagebox.showerror("同步失败", result.get('error', '未知错误'))

            self._update_integration_status(status_text)

        except Exception as e:
            error_msg = f"同步操作失败：{str(e)}"
            self._update_integration_status(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _sync_quality_to_emergency(self, integration_manager):
        """同步质量数据到应急系统"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            self._update_integration_status("⚡ 开始同步质量数据库到应急系统...")

            # 执行同步
            result = integration_manager.sync_quality_to_emergency_system(sender_email)

            if result.get('success'):
                status_text = f"""✅ 同步完成！

📊 同步结果：
• 低质量收件人：{result.get('low_quality_count', 0)} 个
• 应急系统触发：{'是' if result.get('emergency_triggered') else '否'}
• 同步时间：{result.get('sync_time', '')}

💡 质量数据已成功同步到应急系统"""

                if result.get('emergency_triggered'):
                    messagebox.showwarning("应急触发", f"检测到 {result.get('low_quality_count', 0)} 个低质量收件人，已触发应急机制")
                else:
                    messagebox.showinfo("同步成功", "质量数据同步完成，系统状态正常")
            else:
                status_text = f"❌ 同步失败：{result.get('error', '未知错误')}"
                messagebox.showerror("同步失败", result.get('error', '未知错误'))

            self._update_integration_status(status_text)

        except Exception as e:
            error_msg = f"同步操作失败：{str(e)}"
            self._update_integration_status(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _full_system_integration(self, integration_manager):
        """完整系统集成"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            # 确认操作
            confirm_msg = """🎯 即将执行完整系统集成，包括：

1. 自动回复监控 → 质量数据库
2. 质量数据库 → 应急系统
3. 更新系统协调状态

此操作将同步所有系统数据，确定继续吗？"""

            if not messagebox.askyesno("确认集成", confirm_msg):
                return

            self._update_integration_status("🎯 开始完整系统集成...")

            # 执行完整集成
            result = integration_manager.full_system_integration(sender_email)

            if result.get('success'):
                status_text = f"""✅ 完整系统集成成功！

📊 集成报告：
发件人：{result.get('sender_email', '')}
开始时间：{result.get('start_time', '')}
结束时间：{result.get('end_time', '')}

🔧 执行步骤："""

                for step in result.get('steps', []):
                    step_result = step.get('result', {})
                    status_icon = "✅" if step_result.get('success') else "❌"
                    status_text += f"\n{step.get('step')}. {step.get('name')} {status_icon}"

                    if step_result.get('success'):
                        if 'total_synced' in step_result:
                            status_text += f" (同步 {step_result['total_synced']} 个)"
                        elif 'low_quality_count' in step_result:
                            status_text += f" (检测 {step_result['low_quality_count']} 个低质量)"

                status_text += "\n\n💡 所有系统已完成深度集成，数据互联互通！"

                messagebox.showinfo("集成成功", "完整系统集成成功！所有功能模块已实现深度协调。")
            else:
                status_text = f"❌ 完整集成失败：{result.get('error', '未知错误')}"
                messagebox.showerror("集成失败", result.get('error', '未知错误'))

            self._update_integration_status(status_text)

        except Exception as e:
            error_msg = f"完整集成失败：{str(e)}"
            self._update_integration_status(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _show_integration_status(self, integration_manager):
        """显示集成状态"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            self._update_integration_status("📊 正在获取集成状态...")

            # 获取集成状态
            status = integration_manager.get_integration_status(sender_email)

            if 'error' not in status:
                status_text = f"""📊 系统集成状态报告
{'='*60}
发件人：{status.get('sender_email', '')}
检查时间：{status.get('last_check', '')}

📈 统计信息：
• 总同步次数：{status.get('total_syncs', 0)} 次
• 成功同步：{status.get('successful_syncs', 0)} 次
• 成功率：{status.get('success_rate', 0):.1f}%

📋 最近同步记录："""

                for sync in status.get('recent_syncs', [])[:5]:  # 显示最近5次
                    status_icon = "✅" if sync.get('success') else "❌"
                    status_text += f"""
{status_icon} {sync.get('sync_type', '')}
   {sync.get('source_system', '')} → {sync.get('target_system', '')}
   数据量：{sync.get('data_count', 0)} 个
   时间：{sync.get('sync_time', '')[:19]}"""

                    if not sync.get('success') and sync.get('error_message'):
                        status_text += f"\n   错误：{sync.get('error_message', '')}"

                if not status.get('recent_syncs'):
                    status_text += "\n暂无同步记录"

                status_text += "\n\n💡 系统集成状态正常，各模块数据互联互通。"

            else:
                status_text = f"❌ 获取集成状态失败：{status.get('error', '未知错误')}"

            self._update_integration_status(status_text)

        except Exception as e:
            error_msg = f"获取集成状态失败：{str(e)}"
            self._update_integration_status(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _update_integration_status(self, message: str):
        """更新集成状态显示"""
        try:
            if hasattr(self, 'integration_status_text'):
                current_time = datetime.datetime.now().strftime('%H:%M:%S')
                status_line = f"[{current_time}] {message}\n"

                self.integration_status_text.insert(tk.END, status_line)
                self.integration_status_text.see(tk.END)

                # 更新主界面日志
                self.log_message(message)

        except Exception as e:
            print(f"更新集成状态显示错误: {e}")

    def refresh_valid_recipients_list(self):
        """刷新有效收件人列表"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                if hasattr(self, 'valid_recipients_text'):
                    self.valid_recipients_text.delete(1.0, tk.END)
                    self.valid_recipients_text.insert(1.0, "请先在主界面填写发件人邮箱")
                return

            from email_receiver import EmailReceiver
            receiver = EmailReceiver(sender_email, "")
            valid_recipients = receiver.get_valid_recipients(sender_email)

            if hasattr(self, 'valid_recipients_text'):
                self.valid_recipients_text.delete(1.0, tk.END)
                if valid_recipients:
                    content = f"📬 有效收件人 ({len(valid_recipients)} 个)\n"
                    content += "=" * 50 + "\n\n"
                    for i, email in enumerate(valid_recipients, 1):
                        content += f"{i:3d}. {email}\n"
                    content += f"\n💡 这些收件人已发送自动回复，可以导入到质量数据库进行管理"
                else:
                    content = "暂无有效收件人\n\n💡 发送邮件后，收到自动回复的收件人将显示在这里"

                self.valid_recipients_text.insert(1.0, content)

            self.log_message(f"✅ 刷新有效收件人列表: {len(valid_recipients)} 个")

        except Exception as e:
            error_msg = f"刷新有效收件人列表失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            if hasattr(self, 'valid_recipients_text'):
                self.valid_recipients_text.delete(1.0, tk.END)
                self.valid_recipients_text.insert(1.0, f"❌ 加载失败: {error_msg}")

    def refresh_invalid_recipients_list(self):
        """刷新无效收件人列表"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                if hasattr(self, 'invalid_recipients_text'):
                    self.invalid_recipients_text.delete(1.0, tk.END)
                    self.invalid_recipients_text.insert(1.0, "请先在主界面填写发件人邮箱")
                return

            from email_receiver import EmailReceiver
            receiver = EmailReceiver(sender_email, "")
            invalid_recipients = receiver.get_invalid_recipients(sender_email)

            if hasattr(self, 'invalid_recipients_text'):
                self.invalid_recipients_text.delete(1.0, tk.END)
                if invalid_recipients:
                    content = f"❌ 无效收件人 ({len(invalid_recipients)} 个)\n"
                    content += "=" * 50 + "\n\n"
                    for i, email in enumerate(invalid_recipients, 1):
                        content += f"{i:3d}. {email}\n"
                    content += f"\n⚠️ 这些收件人邮箱可能有问题或已停用，建议从发送列表中移除"
                else:
                    content = "暂无无效收件人\n\n✅ 所有邮件都正常发送，没有退信"

                self.invalid_recipients_text.insert(1.0, content)

            self.log_message(f"✅ 刷新无效收件人列表: {len(invalid_recipients)} 个")

        except Exception as e:
            error_msg = f"刷新无效收件人列表失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            if hasattr(self, 'invalid_recipients_text'):
                self.invalid_recipients_text.delete(1.0, tk.END)
                self.invalid_recipients_text.insert(1.0, f"❌ 加载失败: {error_msg}")

    def copy_valid_recipients_list(self):
        """复制有效收件人列表"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            from email_receiver import EmailReceiver
            receiver = EmailReceiver(sender_email, "")
            valid_recipients = receiver.get_valid_recipients(sender_email)

            if valid_recipients:
                # 复制到剪贴板
                recipients_text = '\n'.join(valid_recipients)
                self.root.clipboard_clear()
                self.root.clipboard_append(recipients_text)

                messagebox.showinfo("复制成功", f"已复制 {len(valid_recipients)} 个有效收件人到剪贴板")
                self.log_message(f"📋 已复制 {len(valid_recipients)} 个有效收件人")
            else:
                messagebox.showinfo("提示", "暂无有效收件人可复制")

        except Exception as e:
            error_msg = f"复制有效收件人失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def import_valid_to_main_system(self):
        """导入有效收件人到主系统"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            from email_receiver import EmailReceiver
            receiver = EmailReceiver(sender_email, "")
            valid_recipients = receiver.get_valid_recipients(sender_email)

            if valid_recipients:
                # 获取当前主界面收件人
                current_text = self.recipient_emails.get(1.0, tk.END).strip() if hasattr(self, 'recipient_emails') else ""
                current_recipients = set(self.parse_recipients(current_text))

                # 合并并去重
                all_recipients = current_recipients.union(set(valid_recipients))

                # 更新主界面
                if hasattr(self, 'recipient_emails'):
                    self.recipient_emails.delete(1.0, tk.END)
                    self.recipient_emails.insert(1.0, '\n'.join(sorted(all_recipients)))

                new_count = len(all_recipients) - len(current_recipients)
                messagebox.showinfo("导入成功", f"成功导入 {len(valid_recipients)} 个有效收件人到主系统\n新增收件人: {new_count} 个")
                self.log_message(f"📤 导入 {len(valid_recipients)} 个有效收件人到主系统")
            else:
                messagebox.showinfo("提示", "暂无有效收件人可导入")

        except Exception as e:
            error_msg = f"导入有效收件人到主系统失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def import_valid_to_quality_database(self):
        """导入有效收件人到质量数据库"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            # 确认操作
            confirm_msg = """📊 即将导入有效收件人到质量数据库

🎯 操作说明：
• 将自动回复监控识别的有效收件人导入质量数据库
• 为这些收件人设置较高的质量评分
• 便于后续的质量管理和批次优化

确定继续吗？"""

            if not messagebox.askyesno("确认导入", confirm_msg):
                return

            # 执行导入
            from system_integration_manager import SystemIntegrationManager
            integration_manager = SystemIntegrationManager()

            result = integration_manager.sync_auto_reply_to_quality_db(sender_email)

            if result.get('success'):
                success_msg = f"""✅ 导入成功！

📊 导入统计：
• 有效收件人：{result.get('valid_count', 0)} 个
• 无效收件人：{result.get('invalid_count', 0)} 个
• 总计导入：{result.get('total_synced', 0)} 个

💡 数据已成功同步到质量数据库，可以在质量管理中查看"""

                messagebox.showinfo("导入成功", success_msg)
                self.log_message(f"📊 成功导入 {result.get('total_synced', 0)} 个收件人到质量数据库")
            else:
                messagebox.showerror("导入失败", result.get('error', '未知错误'))

        except Exception as e:
            error_msg = f"导入有效收件人到质量数据库失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def clean_invalid_recipients(self):
        """清理无效收件人"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            from email_receiver import EmailReceiver
            receiver = EmailReceiver(sender_email, "")
            invalid_recipients = receiver.get_invalid_recipients(sender_email)

            if not invalid_recipients:
                messagebox.showinfo("提示", "暂无无效收件人需要清理")
                return

            # 确认清理
            confirm_msg = f"""🗑️ 即将清理无效收件人

📊 清理统计：
• 无效收件人数量：{len(invalid_recipients)} 个
• 清理操作：从主系统收件人列表中移除这些邮箱

⚠️ 注意：此操作不可撤销，确定继续吗？"""

            if not messagebox.askyesno("确认清理", confirm_msg):
                return

            # 从主界面收件人列表中移除无效收件人
            if hasattr(self, 'recipient_emails'):
                current_text = self.recipient_emails.get(1.0, tk.END).strip()
                current_recipients = set(self.parse_recipients(current_text))

                # 移除无效收件人
                cleaned_recipients = current_recipients - set(invalid_recipients)
                removed_count = len(current_recipients) - len(cleaned_recipients)

                # 更新界面
                self.recipient_emails.delete(1.0, tk.END)
                self.recipient_emails.insert(1.0, '\n'.join(sorted(cleaned_recipients)))

                messagebox.showinfo("清理完成", f"成功清理 {removed_count} 个无效收件人")
                self.log_message(f"🗑️ 清理了 {removed_count} 个无效收件人")

                # 刷新无效收件人列表
                self.refresh_invalid_recipients_list()
            else:
                messagebox.showwarning("提示", "无法访问主界面收件人列表")

        except Exception as e:
            error_msg = f"清理无效收件人失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def analyze_invalid_reasons(self):
        """分析无效收件人原因"""
        try:
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            if not sender_email:
                messagebox.showwarning("提示", "请先在主界面填写发件人邮箱")
                return

            from email_receiver import EmailReceiver
            receiver = EmailReceiver(sender_email, "")
            invalid_recipients = receiver.get_invalid_recipients(sender_email)

            if not invalid_recipients:
                messagebox.showinfo("分析结果", "✅ 暂无无效收件人，所有邮件发送正常")
                return

            # 分析无效原因
            analysis = {
                'domain_issues': [],
                'format_issues': [],
                'common_domains': {},
                'total_count': len(invalid_recipients)
            }

            for email in invalid_recipients:
                # 检查邮箱格式
                if '@' not in email or '.' not in email.split('@')[-1]:
                    analysis['format_issues'].append(email)
                    continue

                # 统计域名
                domain = email.split('@')[-1].lower()
                analysis['common_domains'][domain] = analysis['common_domains'].get(domain, 0) + 1

            # 生成分析报告
            report = f"""📊 无效收件人分析报告

📈 总体统计：
• 无效收件人总数：{analysis['total_count']} 个
• 格式错误邮箱：{len(analysis['format_issues'])} 个
• 域名分布：{len(analysis['common_domains'])} 个不同域名

🔍 详细分析："""

            if analysis['format_issues']:
                report += f"\n\n❌ 格式错误邮箱 ({len(analysis['format_issues'])} 个)："
                for email in analysis['format_issues'][:5]:  # 只显示前5个
                    report += f"\n  • {email}"
                if len(analysis['format_issues']) > 5:
                    report += f"\n  • ... 还有 {len(analysis['format_issues']) - 5} 个"

            if analysis['common_domains']:
                report += f"\n\n📊 域名分布 (前5名)："
                sorted_domains = sorted(analysis['common_domains'].items(), key=lambda x: x[1], reverse=True)
                for domain, count in sorted_domains[:5]:
                    report += f"\n  • {domain}: {count} 个"

            report += f"\n\n💡 建议：\n• 检查并修正格式错误的邮箱\n• 验证高频域名的有效性\n• 考虑从发送列表中移除这些邮箱"

            messagebox.showinfo("分析报告", report)
            self.log_message(f"📊 完成无效收件人分析: {analysis['total_count']} 个")

        except Exception as e:
            error_msg = f"分析无效收件人失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _select_all_quality_records(self):
        """全选质量数据库记录"""
        try:
            if hasattr(self, 'quality_tree'):
                # 获取所有项目
                all_items = self.quality_tree.get_children()

                # 选中所有项目
                for item in all_items:
                    self.quality_tree.selection_add(item)

                self.log_message(f"☑️ 已全选 {len(all_items)} 条质量记录")
                messagebox.showinfo("全选完成", f"已选中 {len(all_items)} 条记录")
            else:
                messagebox.showwarning("提示", "质量数据表格未初始化")

        except Exception as e:
            error_msg = f"全选质量记录失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _delete_selected_quality_records(self, quality_manager):
        """删除选中的质量记录"""
        try:
            if not hasattr(self, 'quality_tree'):
                messagebox.showwarning("提示", "质量数据表格未初始化")
                return

            # 获取选中的项目
            selected_items = self.quality_tree.selection()

            if not selected_items:
                messagebox.showwarning("提示", "请先选择要删除的记录")
                return

            # 获取选中记录的邮箱地址
            selected_emails = []
            for item in selected_items:
                values = self.quality_tree.item(item, 'values')
                if values and len(values) > 0:
                    selected_emails.append(values[0])  # 第一列是邮箱

            if not selected_emails:
                messagebox.showwarning("提示", "无法获取选中记录的邮箱信息")
                return

            # 确认删除
            confirm_msg = f"""🗑️ 即将删除选中的质量记录

📊 删除统计：
• 选中记录数：{len(selected_emails)} 条
• 操作类型：从质量数据库中永久删除

⚠️ 注意：此操作不可撤销，确定继续吗？

删除的邮箱包括：
{chr(10).join(selected_emails[:5])}
{'...' if len(selected_emails) > 5 else ''}"""

            if not messagebox.askyesno("确认删除", confirm_msg):
                return

            # 执行删除
            deleted_count = 0
            for email in selected_emails:
                try:
                    if quality_manager.remove_recipient(email):
                        deleted_count += 1
                except Exception as e:
                    self.log_message(f"❌ 删除 {email} 失败: {str(e)}")

            # 刷新数据显示
            self._refresh_quality_data(quality_manager)

            messagebox.showinfo("删除完成", f"成功删除 {deleted_count} 条质量记录")
            self.log_message(f"🗑️ 删除了 {deleted_count} 条质量记录")

        except Exception as e:
            error_msg = f"删除质量记录失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _clear_all_quality_data(self, quality_manager):
        """清空所有质量数据"""
        try:
            # 获取当前记录数
            if hasattr(self, 'quality_tree'):
                all_items = self.quality_tree.get_children()
                total_count = len(all_items)
            else:
                total_count = 0

            if total_count == 0:
                messagebox.showinfo("提示", "质量数据库已经是空的")
                return

            # 严格确认
            confirm_msg = f"""🚨 危险操作：清空质量数据库

📊 影响范围：
• 将删除所有 {total_count} 条质量记录
• 包括所有收件人的质量评分、发送历史等数据
• 此操作不可撤销且无法恢复

⚠️ 强烈建议：
• 在清空前先导出备份数据
• 确认您真的需要清空所有数据

🔴 最后确认：您确定要清空整个质量数据库吗？"""

            if not messagebox.askyesno("危险操作确认", confirm_msg):
                return

            # 二次确认
            second_confirm = messagebox.askyesno(
                "最终确认",
                f"⚠️ 最后一次确认\n\n即将永久删除 {total_count} 条质量记录\n\n确定继续吗？"
            )

            if not second_confirm:
                return

            # 执行清空
            try:
                # 调用质量管理器的清空方法
                if hasattr(quality_manager, 'clear_all_data'):
                    quality_manager.clear_all_data()
                else:
                    # 如果没有专门的清空方法，逐个删除
                    all_emails = []
                    for item in all_items:
                        values = self.quality_tree.item(item, 'values')
                        if values and len(values) > 0:
                            all_emails.append(values[0])

                    for email in all_emails:
                        quality_manager.remove_recipient(email)

                # 刷新显示
                self._refresh_quality_data(quality_manager)

                messagebox.showinfo("清空完成", f"✅ 成功清空质量数据库\n删除了 {total_count} 条记录")
                self.log_message(f"🧹 清空了质量数据库，删除 {total_count} 条记录")

            except Exception as e:
                error_msg = f"清空数据库失败: {str(e)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("清空失败", error_msg)

        except Exception as e:
            error_msg = f"清空质量数据失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _start_duplicate_detection(self, detector):
        """开始重复检测"""
        try:
            # 获取当前邮件信息
            sender_email = self.sender_email.get().strip() if hasattr(self, 'sender_email') else ""
            subject = self.subject.get().strip() if hasattr(self, 'subject') else ""
            body = self.body.get(1.0, tk.END).strip() if hasattr(self, 'body') else ""
            recipients_text = self.recipient_emails.get(1.0, tk.END).strip() if hasattr(self, 'recipient_emails') else ""

            if not all([sender_email, subject, body, recipients_text]):
                messagebox.showwarning("提示", "请先在主界面填写完整的邮件信息（发件人、主题、正文、收件人）")
                return

            # 解析收件人列表
            recipient_list = self.parse_recipients(recipients_text)
            if not recipient_list:
                messagebox.showwarning("提示", "请输入有效的收件人邮箱")
                return

            threshold = self.similarity_threshold.get()
            self.log_message(f"🔍 开始重复检测，相似度阈值: {threshold:.2f}")

            # 清空现有结果
            for item in self.duplicate_tree.get_children():
                self.duplicate_tree.delete(item)

            # 执行重复检测
            result = detector.advanced_duplicate_detection(
                subject, body, recipient_list, sender_email, threshold
            )

            # 显示检测结果
            self._display_duplicate_results(result)

            # 显示总结信息
            total = result.get('total_recipients', 0)
            exact = len(result.get('exact_matches', []))
            similar = len(result.get('similar_matches', []))
            safe = len(result.get('safe_recipients', []))

            summary_msg = f"""重复检测完成！

📊 检测统计:
• 总收件人: {total} 个
• 完全重复: {exact} 个
• 相似重复: {similar} 个
• 安全发送: {safe} 个

💡 建议: {result.get('recommendation', '无特殊建议')}"""

            messagebox.showinfo("检测完成", summary_msg)
            self.log_message(f"✅ 重复检测完成: 总计{total}个，安全{safe}个，重复{exact+similar}个")

        except Exception as e:
            error_msg = f"重复检测失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _display_duplicate_results(self, result):
        """显示重复检测结果"""
        try:
            # 显示完全重复的邮件
            for match in result.get('exact_matches', []):
                self.duplicate_tree.insert('', 'end', values=(
                    f"当前邮件 → {match['recipient']}",
                    f"历史邮件 ({match['send_time'][:10]})",
                    "1.000",
                    "完全重复",
                    "❌ 不建议发送"
                ))

            # 显示相似重复的邮件
            for match in result.get('similar_matches', []):
                self.duplicate_tree.insert('', 'end', values=(
                    f"当前邮件 → {match['recipient']}",
                    f"历史邮件 ({match['send_time'][:10]})",
                    f"{match['similarity']:.3f}",
                    "相似重复",
                    "⚠️ 谨慎发送"
                ))

            # 显示安全收件人
            for recipient in result.get('safe_recipients', []):
                self.duplicate_tree.insert('', 'end', values=(
                    f"当前邮件 → {recipient}",
                    "无历史记录",
                    "0.000",
                    "安全发送",
                    "✅ 可以发送"
                ))

        except Exception as e:
            print(f"显示重复检测结果错误: {e}")

    def _show_duplicate_report(self, detector):
        """显示重复检测报告"""
        try:
            # 获取树形视图中的数据
            items = self.duplicate_tree.get_children()
            if not items:
                messagebox.showinfo("提示", "请先执行重复检测")
                return

            # 创建报告窗口
            report_window = tk.Toplevel(self.root)
            report_window.title("📊 重复检测报告")
            report_window.geometry("800x600")
            report_window.resizable(True, True)

            # 主框架
            main_frame = ttk.Frame(report_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 报告内容
            report_text = scrolledtext.ScrolledText(main_frame, width=80, height=30,
                                                   font=('Consolas', 10))
            report_text.pack(fill=tk.BOTH, expand=True)

            # 生成报告内容
            report_content = self._generate_duplicate_report()
            report_text.insert(1.0, report_content)

            # 按钮框架
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(fill=tk.X, pady=(10, 0))

            ttk.Button(btn_frame, text="📤 导出报告",
                      command=lambda: self._export_duplicate_report(report_content)).pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="关闭",
                      command=report_window.destroy).pack(side=tk.RIGHT)

        except Exception as e:
            messagebox.showerror("错误", f"显示报告失败: {str(e)}")

    def _generate_duplicate_report(self):
        """生成重复检测报告"""
        try:
            import datetime

            report = f"""📊 邮件重复检测报告
{'='*60}
生成时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
检测系统: 智能邮件系统 v3.0

📋 检测结果详情:
{'='*60}
"""

            items = self.duplicate_tree.get_children()
            exact_count = 0
            similar_count = 0
            safe_count = 0

            for item in items:
                values = self.duplicate_tree.item(item)['values']
                mail_info = values[0]
                history_info = values[1]
                similarity = values[2]
                type_info = values[3]
                suggestion = values[4]

                if type_info == "完全重复":
                    exact_count += 1
                elif type_info == "相似重复":
                    similar_count += 1
                elif type_info == "安全发送":
                    safe_count += 1

                report += f"""
📧 {mail_info}
   历史记录: {history_info}
   相似度: {similarity}
   类型: {type_info}
   建议: {suggestion}
{'-'*40}"""

            report += f"""

📊 统计汇总:
{'='*60}
• 完全重复: {exact_count} 个
• 相似重复: {similar_count} 个
• 安全发送: {safe_count} 个
• 总计: {exact_count + similar_count + safe_count} 个

💡 建议:
• 完全重复的邮件不建议再次发送
• 相似重复的邮件需要谨慎考虑
• 安全发送的邮件可以正常发送

🔍 检测说明:
• 相似度阈值越高，检测越严格
• 建议定期清理重复的邮件记录
• 使用智能搜索功能查看历史邮件详情
"""
            return report

        except Exception as e:
            return f"生成报告失败: {str(e)}"

    def _export_duplicate_report(self, content):
        """导出重复检测报告"""
        try:
            from tkinter import filedialog
            import datetime

            filename = filedialog.asksaveasfilename(
                title="导出重复检测报告",
                defaultextension=".txt",
                filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                initialname=f"重复检测报告_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            )

            if filename:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                messagebox.showinfo("导出成功", f"报告已导出到:\n{filename}")
                self.log_message(f"📤 重复检测报告已导出: {filename}")

        except Exception as e:
            messagebox.showerror("错误", f"导出报告失败: {str(e)}")

    def _clean_duplicates(self, detector):
        """清理重复内容"""
        try:
            items = self.duplicate_tree.get_children()
            if not items:
                messagebox.showinfo("提示", "请先执行重复检测")
                return

            # 统计重复项
            duplicate_items = []
            for item in items:
                values = self.duplicate_tree.item(item)['values']
                type_info = values[3]
                if type_info in ["完全重复", "相似重复"]:
                    duplicate_items.append(values)

            if not duplicate_items:
                messagebox.showinfo("提示", "没有检测到重复内容")
                return

            # 确认清理
            confirm_msg = f"检测到 {len(duplicate_items)} 个重复项，确定要清理吗？\n\n注意：此操作将从收件人列表中移除重复的邮箱地址。"
            if not messagebox.askyesno("确认清理", confirm_msg):
                return

            # 执行清理
            cleaned_count = self._remove_duplicate_recipients(duplicate_items)

            if cleaned_count > 0:
                messagebox.showinfo("清理完成", f"已清理 {cleaned_count} 个重复收件人\n请重新检查收件人列表")
                self.log_message(f"🗑️ 已清理 {cleaned_count} 个重复收件人")

                # 清空检测结果
                for item in self.duplicate_tree.get_children():
                    self.duplicate_tree.delete(item)
            else:
                messagebox.showinfo("提示", "没有需要清理的内容")

        except Exception as e:
            messagebox.showerror("错误", f"清理重复失败: {str(e)}")

    def _remove_duplicate_recipients(self, duplicate_items):
        """从收件人列表中移除重复的邮箱"""
        try:
            if not hasattr(self, 'recipient_emails'):
                return 0

            current_text = self.recipient_emails.get(1.0, tk.END).strip()
            current_recipients = self.parse_recipients(current_text)

            # 提取需要移除的收件人
            recipients_to_remove = set()
            for item in duplicate_items:
                mail_info = item[0]  # 格式: "当前邮件 → <EMAIL>"
                if " → " in mail_info:
                    recipient = mail_info.split(" → ")[1]
                    recipients_to_remove.add(recipient)

            # 过滤掉重复的收件人
            cleaned_recipients = [r for r in current_recipients if r not in recipients_to_remove]

            # 更新收件人列表
            self.recipient_emails.delete(1.0, tk.END)
            if cleaned_recipients:
                self.recipient_emails.insert(1.0, '\n'.join(cleaned_recipients))

            return len(recipients_to_remove)

        except Exception as e:
            print(f"移除重复收件人错误: {e}")
            return 0

    def _save_monitoring_settings(self):
        """保存监控设置"""
        try:
            settings = {
                'interval': int(self.monitor_interval.get()),
                'max_checks': int(self.monitor_max_checks.get()),
                'auto_start': self.auto_start_monitoring.get(),
                'timeout': int(self.monitor_timeout.get())
            }

            # 保存到配置文件
            import json
            with open('monitor_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("保存成功", "监控设置已保存")

        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {str(e)}")

    def _reset_monitoring_settings(self):
        """重置监控设置"""
        try:
            self.monitor_interval.set(5)
            self.monitor_max_checks.set(24)
            self.auto_start_monitoring.set(False)
            self.monitor_timeout.set(30)
            messagebox.showinfo("重置成功", "监控设置已重置为默认值")
        except Exception as e:
            messagebox.showerror("错误", f"重置设置失败: {str(e)}")

    def _test_monitoring_settings(self):
        """测试监控设置"""
        try:
            interval = int(self.monitor_interval.get())
            max_checks = int(self.monitor_max_checks.get())
            timeout = int(self.monitor_timeout.get())

            test_msg = f"""监控设置测试：

检查间隔: {interval} 分钟
最大检查次数: {max_checks} 次
连接超时: {timeout} 秒
自动启动: {'是' if self.auto_start_monitoring.get() else '否'}

设置有效，可以正常使用！"""

            messagebox.showinfo("测试结果", test_msg)

        except Exception as e:
            messagebox.showerror("错误", f"测试设置失败: {str(e)}")

    def _save_coordination_settings(self):
        """保存协调设置"""
        try:
            settings = {
                'mode': self.coordination_mode.get(),
                'features': {
                    'quality': self.coord_quality.get(),
                    'anti_spam': self.coord_anti_spam.get(),
                    'emergency': self.coord_emergency.get(),
                    'monitoring': self.coord_monitoring.get()
                },
                'frequency': int(self.coord_frequency.get()),
                'risk_threshold': float(self.coord_risk_threshold.get())
            }

            # 保存到配置文件
            import json
            with open('coordination_settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            messagebox.showinfo("保存成功", "协调设置已保存")

        except Exception as e:
            messagebox.showerror("错误", f"保存设置失败: {str(e)}")

    def _reset_coordination_settings(self):
        """重置协调设置"""
        try:
            self.coordination_mode.set("auto")
            self.coord_quality.set(True)
            self.coord_anti_spam.set(True)
            self.coord_emergency.set(True)
            self.coord_monitoring.set(True)
            self.coord_frequency.set(10)
            self.coord_risk_threshold.set(0.7)
            messagebox.showinfo("重置成功", "协调设置已重置为默认值")
        except Exception as e:
            messagebox.showerror("错误", f"重置设置失败: {str(e)}")

    def _test_coordination_settings(self):
        """测试协调设置"""
        try:
            mode = self.coordination_mode.get()
            frequency = int(self.coord_frequency.get())
            risk_threshold = float(self.coord_risk_threshold.get())

            enabled_features = []
            if self.coord_quality.get():
                enabled_features.append("质量数据库")
            if self.coord_anti_spam.get():
                enabled_features.append("反垃圾邮件")
            if self.coord_emergency.get():
                enabled_features.append("应急系统")
            if self.coord_monitoring.get():
                enabled_features.append("监控系统")

            test_msg = f"""协调设置测试：

协调模式: {mode}
协调频率: {frequency} 分钟
风险阈值: {risk_threshold:.2f}
启用功能: {', '.join(enabled_features)}

设置有效，协调系统可以正常工作！"""

            messagebox.showinfo("测试结果", test_msg)

        except Exception as e:
            messagebox.showerror("错误", f"测试设置失败: {str(e)}")

    # ==================== 重复检测功能 ====================

    def check_duplicates(self):
        """检查重复邮件发送 - 从2.0系统移植的完整功能"""
        try:
            # 获取当前输入
            sender_email = self.sender_email.get().strip()
            subject = self.subject.get().strip()
            body = self.body.get(1.0, tk.END).strip()
            recipient_emails_text = self.recipient_emails.get(1.0, tk.END).strip()

            if not all([sender_email, subject, body, recipient_emails_text]):
                messagebox.showwarning("提示", "请先填写完整的邮件信息")
                return

            # 解析收件人列表
            recipient_list = self.parse_recipients(recipient_emails_text)
            if not recipient_list:
                messagebox.showwarning("提示", "请输入有效的收件人邮箱")
                return

            self.log_message("🔍 开始检查重复邮件...")

            # 初始化RAG搜索引擎
            from rag_search_engine import RAGSearchEngine
            rag_search = RAGSearchEngine()

            # 使用RAG搜索引擎进行高级重复检测
            result = rag_search.advanced_duplicate_detection(
                subject, body, recipient_list, sender_email
            )

            # 显示检测结果
            self._show_duplicate_detection_result(result)

        except Exception as e:
            error_msg = f"重复检测失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def _show_duplicate_detection_result(self, result):
        """显示重复检测结果"""
        try:
            # 创建结果窗口
            result_window = tk.Toplevel(self.root)
            result_window.title("🔍 重复检测结果")
            result_window.geometry("900x700")
            result_window.resizable(True, True)

            # 主框架
            main_frame = ttk.Frame(result_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = ttk.Label(main_frame, text="🔍 重复邮件检测结果",
                                   font=('Microsoft YaHei UI', 16, 'bold'))
            title_label.pack(pady=(0, 15))

            # 统计信息框架
            stats_frame = ttk.LabelFrame(main_frame, text="📊 检测统计", padding="10")
            stats_frame.pack(fill=tk.X, pady=(0, 15))

            total_recipients = result.get('total_recipients', 0)
            safe_recipients = result.get('safe_recipients', [])
            exact_matches = result.get('exact_matches', [])
            similar_matches = result.get('similar_matches', [])

            stats_text = f"""
📊 总收件人: {total_recipients} 个
✅ 安全发送: {len(safe_recipients)} 个
❌ 完全重复: {len(exact_matches)} 个
⚠️ 相似重复: {len(similar_matches)} 个
📈 安全率: {(len(safe_recipients)/total_recipients*100):.1f}% (如果总数>0)
"""

            ttk.Label(stats_frame, text=stats_text, font=('Microsoft YaHei UI', 10)).pack(anchor=tk.W)

            # 建议信息
            recommendation = result.get('recommendation', '无特殊建议')
            ttk.Label(stats_frame, text=f"💡 建议: {recommendation}",
                     font=('Microsoft YaHei UI', 10, 'bold'),
                     foreground='#2563eb').pack(anchor=tk.W, pady=(10, 0))

            # 详细结果框架
            details_frame = ttk.LabelFrame(main_frame, text="📋 详细结果", padding="10")
            details_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            # 创建Notebook来分类显示结果
            notebook = ttk.Notebook(details_frame)
            notebook.pack(fill=tk.BOTH, expand=True)

            # 安全收件人标签页
            safe_frame = ttk.Frame(notebook)
            notebook.add(safe_frame, text=f"✅ 安全发送 ({len(safe_recipients)})")

            safe_text = scrolledtext.ScrolledText(safe_frame, width=80, height=15,
                                                 font=('Consolas', 10))
            safe_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            if safe_recipients:
                safe_text.insert(tk.END, "✅ 以下收件人可以安全发送:\n\n")
                for i, recipient in enumerate(safe_recipients, 1):
                    safe_text.insert(tk.END, f"{i:3d}. {recipient}\n")
            else:
                safe_text.insert(tk.END, "❌ 没有安全的收件人")

            # 完全重复标签页
            exact_frame = ttk.Frame(notebook)
            notebook.add(exact_frame, text=f"❌ 完全重复 ({len(exact_matches)})")

            exact_text = scrolledtext.ScrolledText(exact_frame, width=80, height=15,
                                                  font=('Consolas', 10))
            exact_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            if exact_matches:
                exact_text.insert(tk.END, "❌ 以下收件人已收到完全相同的邮件:\n\n")
                for i, match in enumerate(exact_matches, 1):
                    exact_text.insert(tk.END, f"{i:3d}. {match['recipient']}\n")
                    exact_text.insert(tk.END, f"     发送时间: {match['send_time']}\n")
                    exact_text.insert(tk.END, f"     主题: {match['subject']}\n")
                    exact_text.insert(tk.END, f"     相似度: {match['similarity']:.3f}\n\n")
            else:
                exact_text.insert(tk.END, "✅ 没有完全重复的收件人")

            # 相似重复标签页
            similar_frame = ttk.Frame(notebook)
            notebook.add(similar_frame, text=f"⚠️ 相似重复 ({len(similar_matches)})")

            similar_text = scrolledtext.ScrolledText(similar_frame, width=80, height=15,
                                                    font=('Consolas', 10))
            similar_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

            if similar_matches:
                similar_text.insert(tk.END, "⚠️ 以下收件人已收到相似的邮件:\n\n")
                for i, match in enumerate(similar_matches, 1):
                    similar_text.insert(tk.END, f"{i:3d}. {match['recipient']}\n")
                    similar_text.insert(tk.END, f"     发送时间: {match['send_time']}\n")
                    similar_text.insert(tk.END, f"     主题: {match['subject']}\n")
                    similar_text.insert(tk.END, f"     相似度: {match['similarity']:.3f}\n\n")
            else:
                similar_text.insert(tk.END, "✅ 没有相似重复的收件人")

            # 按钮框架
            btn_frame = ttk.Frame(main_frame)
            btn_frame.pack(fill=tk.X)

            # 操作按钮
            def remove_duplicates():
                """移除重复收件人"""
                try:
                    duplicates = exact_matches + similar_matches
                    if not duplicates:
                        messagebox.showinfo("提示", "没有重复的收件人需要移除")
                        return

                    if messagebox.askyesno("确认移除", f"确定要从收件人列表中移除 {len(duplicates)} 个重复的收件人吗？"):
                        # 获取当前收件人列表
                        current_recipients = self.parse_recipients(self.recipient_emails.get(1.0, tk.END))

                        # 移除重复的收件人
                        duplicate_emails = {match['recipient'] for match in duplicates}
                        filtered_recipients = [r for r in current_recipients if r not in duplicate_emails]

                        # 更新收件人列表
                        self.recipient_emails.delete(1.0, tk.END)
                        if filtered_recipients:
                            self.recipient_emails.insert(1.0, '\n'.join(filtered_recipients))

                        messagebox.showinfo("移除完成", f"已移除 {len(duplicates)} 个重复收件人")
                        self.log_message(f"🗑️ 已移除 {len(duplicates)} 个重复收件人")
                        result_window.destroy()

                except Exception as e:
                    messagebox.showerror("错误", f"移除重复收件人失败: {str(e)}")

            def export_result():
                """导出检测结果"""
                try:
                    from tkinter import filedialog
                    import datetime

                    filename = filedialog.asksaveasfilename(
                        title="导出重复检测结果",
                        defaultextension=".txt",
                        filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")],
                        initialname=f"重复检测结果_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
                    )

                    if filename:
                        with open(filename, 'w', encoding='utf-8') as f:
                            f.write("🔍 重复邮件检测结果\n")
                            f.write("=" * 50 + "\n\n")
                            f.write(f"检测时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            f.write(f"总收件人: {total_recipients} 个\n")
                            f.write(f"安全发送: {len(safe_recipients)} 个\n")
                            f.write(f"完全重复: {len(exact_matches)} 个\n")
                            f.write(f"相似重复: {len(similar_matches)} 个\n\n")
                            f.write(f"建议: {recommendation}\n\n")

                            if safe_recipients:
                                f.write("✅ 安全收件人:\n")
                                for recipient in safe_recipients:
                                    f.write(f"  - {recipient}\n")
                                f.write("\n")

                            if exact_matches:
                                f.write("❌ 完全重复:\n")
                                for match in exact_matches:
                                    f.write(f"  - {match['recipient']} (发送时间: {match['send_time']})\n")
                                f.write("\n")

                            if similar_matches:
                                f.write("⚠️ 相似重复:\n")
                                for match in similar_matches:
                                    f.write(f"  - {match['recipient']} (相似度: {match['similarity']:.3f})\n")

                        messagebox.showinfo("导出成功", f"检测结果已导出到:\n{filename}")
                        self.log_message(f"📤 重复检测结果已导出: {filename}")

                except Exception as e:
                    messagebox.showerror("错误", f"导出结果失败: {str(e)}")

            # 按钮
            ttk.Button(btn_frame, text="🗑️ 移除重复", command=remove_duplicates,
                      style='Warning.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="📤 导出结果", command=export_result,
                      style='Success.TButton').pack(side=tk.LEFT, padx=(0, 10))
            ttk.Button(btn_frame, text="关闭", command=result_window.destroy,
                      style='Primary.TButton').pack(side=tk.RIGHT)

            # 记录日志
            self.log_message(f"✅ 重复检测完成: 总计{total_recipients}个，安全{len(safe_recipients)}个，重复{len(exact_matches)+len(similar_matches)}个")

        except Exception as e:
            error_msg = f"显示重复检测结果失败：{str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    # ==================== 自动队列系统功能 ====================

    def on_auto_queue_system_changed(self):
        """自动队列系统选项变化处理"""
        try:
            if self.auto_start_queue_system.get():
                self.log_message("📬 自动队列系统已启用")
                # 更新说明标签颜色
                if hasattr(self, 'auto_queue_system_label'):
                    self.auto_queue_system_label.configure(foreground=self.colors['success'])
            else:
                self.log_message("📬 自动队列系统已禁用")
                # 更新说明标签颜色
                if hasattr(self, 'auto_queue_system_label'):
                    self.auto_queue_system_label.configure(foreground=self.colors['dark'])
        except Exception as e:
            self.log_message(f"❌ 自动队列系统设置失败: {str(e)}")

    def auto_start_queue_after_send(self):
        """发送完成后自动启动队列系统"""
        try:
            # 检查是否有队列任务
            if not hasattr(self, 'email_queue') or not self.email_queue:
                self.log_message("📬 队列为空，无需启动队列系统")
                return

            pending_tasks = [task for task in self.email_queue if task.get('status') == 'pending']
            if not pending_tasks:
                self.log_message("📬 没有待发送的队列任务")
                return

            self.log_message(f"📬 检测到 {len(pending_tasks)} 个待发送队列任务")
            self.log_message("🚀 自动启动队列发送...")

            # 启用队列发送按钮
            if hasattr(self, 'start_queue_button'):
                self.start_queue_button.configure(state='normal')

            # 延迟3秒后自动启动队列发送
            self.root.after(3000, self._auto_execute_queue_sending)

        except Exception as e:
            self.log_message(f"❌ 自动启动队列系统失败: {str(e)}")

    def _auto_execute_queue_sending(self):
        """自动执行队列发送（延迟执行）"""
        try:
            self.log_message("🚀 开始自动队列发送...")

            # 检查队列状态
            if not hasattr(self, 'email_queue') or not self.email_queue:
                self.log_message("⚠️ 队列为空，取消自动发送")
                return

            pending_tasks = [task for task in self.email_queue if task.get('status') == 'pending']
            if not pending_tasks:
                self.log_message("⚠️ 没有待发送任务，取消自动发送")
                return

            # 开始队列发送
            self.queue_mode = True
            self.should_stop = False

            self.log_message(f"📬 自动队列发送启动: {len(pending_tasks)} 个任务")

            # 在新线程中执行队列发送
            import threading
            send_thread = threading.Thread(target=self._execute_queue_sending)
            send_thread.daemon = True
            send_thread.start()

            # 更新按钮状态
            if hasattr(self, 'start_queue_button'):
                self.start_queue_button.configure(state='disabled')
            if hasattr(self, 'pause_queue_button'):
                self.pause_queue_button.configure(state='normal')

        except Exception as e:
            self.log_message(f"❌ 自动执行队列发送失败: {str(e)}")

    def on_closing(self):
        """窗口关闭处理"""
        try:
            if messagebox.askokcancel("退出", "确定要退出邮件系统吗？"):
                self.log_message("👋 邮件系统正在关闭...")

                # 保存授权码
                if self.auth_codes:
                    self.save_auth_codes_to_file()
                    self.log_message("💾 授权码已自动保存")

                self.root.destroy()
        except Exception as e:
            print(f"关闭窗口错误: {e}")
            self.root.destroy()  # 强制关闭



def main():
    """主函数"""
    try:
        print("🚀 启动完整功能版邮件系统v3.0...")
        root = tk.Tk()
        app = EmailSenderGUI(root)
        print("✅ 系统启动成功，开始运行...")
        root.mainloop()
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
