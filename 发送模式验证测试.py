# -*- coding: utf-8 -*-
"""
发送模式验证测试程序
专门测试发送模式是否正确应用
"""

import tkinter as tk
from tkinter import ttk, messagebox
import random
import time
import threading

class SendModeTest:
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 发送模式验证测试")
        self.root.geometry("800x600")
        
        # 发送模式变量
        self.send_mode = tk.StringVar(value="standard")
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔍 发送模式验证测试",
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # 发送模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="发送模式选择", padding="10")
        mode_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Label(mode_frame, text="选择发送模式:", font=('Arial', 10)).pack(anchor=tk.W, pady=5)
        
        radio_frame = ttk.Frame(mode_frame)
        radio_frame.pack(fill=tk.X, pady=5)
        
        ttk.Radiobutton(radio_frame, text="标准发送（1-2分钟间隔）",
                       variable=self.send_mode, value="standard").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(radio_frame, text="快速发送（30-60秒间隔）",
                       variable=self.send_mode, value="fast").pack(side=tk.LEFT, padx=5)
        ttk.Radiobutton(radio_frame, text="安全发送（3-5分钟间隔）",
                       variable=self.send_mode, value="safe").pack(side=tk.LEFT, padx=5)
        
        # 测试按钮
        btn_frame = ttk.Frame(mode_frame)
        btn_frame.pack(fill=tk.X, pady=10)
        
        ttk.Button(btn_frame, text="🧪 测试发送模式获取",
                  command=self.test_mode_get).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🚀 模拟发送流程",
                  command=self.simulate_send).pack(side=tk.LEFT, padx=5)
        ttk.Button(btn_frame, text="🔄 清空日志",
                  command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="5")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = tk.Text(log_frame, width=80, height=20, font=('Consolas', 9))
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 初始化日志
        self.log("=" * 60)
        self.log("🔍 发送模式验证测试程序")
        self.log("=" * 60)
        self.log("此程序专门测试发送模式是否正确应用")
        self.log("请选择不同的发送模式并点击测试按钮")
        self.log("")
        
    def log(self, message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # 同时输出到控制台
        print(f"[{timestamp}] {message}")
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.log("日志已清空")
        
    def test_mode_get(self):
        """测试发送模式获取"""
        self.log("")
        self.log("🧪 测试发送模式获取")
        self.log("-" * 40)
        
        # 获取当前选择的发送模式
        current_mode = self.send_mode.get()
        self.log(f"📋 当前选择的发送模式: '{current_mode}'")
        
        # 测试延迟配置获取
        delay_ranges = {
            "fast": (30, 60),      # 快速发送：30-60秒
            "standard": (60, 120), # 标准发送：1-2分钟
            "safe": (180, 300)     # 安全发送：3-5分钟
        }
        
        # 模拟主程序的获取逻辑
        delay_range = delay_ranges.get(current_mode, (60, 120))
        self.log(f"📊 获取到的延迟范围: {delay_range}")
        
        # 生成随机延迟
        delay = random.uniform(delay_range[0], delay_range[1])
        minutes = delay // 60
        seconds = delay % 60
        self.log(f"⏱️ 生成的随机延迟: {delay:.1f}秒 ({minutes:.0f}分{seconds:.1f}秒)")
        
        # 模式名称获取
        mode_names = {
            "fast": "快速发送",
            "standard": "标准发送", 
            "safe": "安全发送"
        }
        mode_name = mode_names.get(current_mode, "标准发送")
        self.log(f"📝 模式名称: {mode_name}")
        
        # 验证逻辑
        self.log("")
        self.log("✅ 验证结果:")
        if current_mode == "fast" and delay_range == (30, 60):
            self.log("✅ 快速发送模式 - 延迟范围正确")
        elif current_mode == "standard" and delay_range == (60, 120):
            self.log("✅ 标准发送模式 - 延迟范围正确")
        elif current_mode == "safe" and delay_range == (180, 300):
            self.log("✅ 安全发送模式 - 延迟范围正确")
        else:
            self.log("❌ 发送模式配置有问题！")
            
    def simulate_send(self):
        """模拟发送流程"""
        self.log("")
        self.log("🚀 模拟发送流程")
        self.log("-" * 40)
        
        def send_thread():
            try:
                # 模拟收件人列表
                recipients = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
                
                # 获取发送模式
                current_mode = self.send_mode.get()
                self.root.after(0, lambda: self.log(f"🔍 当前选择的发送模式: '{current_mode}'"))
                
                # 延迟配置
                delay_ranges = {
                    "fast": (30, 60),
                    "standard": (60, 120),
                    "safe": (180, 300)
                }
                delay_range = delay_ranges.get(current_mode, (60, 120))
                self.root.after(0, lambda: self.log(f"🔍 获取到的延迟范围: {delay_range}"))
                
                # 模式名称
                mode_names = {
                    "fast": "快速发送",
                    "standard": "标准发送",
                    "safe": "安全发送"
                }
                mode_name = mode_names.get(current_mode, "标准发送")
                
                self.root.after(0, lambda: self.log(f"开始{mode_name}模式，共 {len(recipients)} 个收件人"))
                
                for i, email in enumerate(recipients, 1):
                    self.root.after(0, lambda i=i, email=email: 
                                   self.log(f"📤 模拟发送第 {i}/{len(recipients)} 封邮件给: {email}"))
                    
                    # 模拟发送延迟（实际中这里会调用真实的发送函数）
                    time.sleep(1)  # 模拟发送时间
                    
                    self.root.after(0, lambda email=email: self.log(f"✅ 模拟发送成功: {email}"))
                    
                    # 添加间隔延迟
                    if i < len(recipients):
                        next_delay = random.uniform(delay_range[0], delay_range[1])
                        minutes = next_delay // 60
                        seconds = next_delay % 60
                        
                        self.root.after(0, lambda d=next_delay, m=minutes, s=seconds:
                                       self.log(f"🔍 生成延迟 {d:.1f}秒 ({m:.0f}分{s:.1f}秒)，范围 {delay_range}"))
                        self.root.after(0, lambda d=next_delay: self.log(f"⏳ 等待 {d:.1f} 秒后发送下一封..."))
                        
                        # 实际延迟（为了演示，我们缩短到1/10）
                        demo_delay = next_delay / 10  # 演示用，实际延迟缩短
                        self.root.after(0, lambda d=demo_delay: self.log(f"📝 演示模式：实际等待 {d:.1f} 秒（已缩短）"))
                        time.sleep(demo_delay)
                
                self.root.after(0, lambda: self.log(""))
                self.root.after(0, lambda: self.log("🎉 模拟发送完成！"))
                self.root.after(0, lambda: self.log("如果您看到正确的延迟时间，说明发送模式工作正常"))
                
            except Exception as e:
                self.root.after(0, lambda: self.log(f"❌ 模拟发送出错: {str(e)}"))
        
        # 在新线程中执行
        threading.Thread(target=send_thread, daemon=True).start()

def main():
    """主函数"""
    root = tk.Tk()
    app = SendModeTest(root)
    
    print("发送模式验证测试程序已启动")
    print("请在GUI界面中选择不同的发送模式并测试")
    
    root.mainloop()

if __name__ == "__main__":
    main()
