# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
"""

import tkinter as tk
from gui_main import EmailSenderGUI

def test_auth_code_format():
    """测试授权码格式兼容性"""
    print("测试授权码格式兼容性...")
    
    # 模拟新格式授权码
    new_format = {
        '<EMAIL>': {
            'auth_code': 'abcdefghijklmnop',
            'add_time': '2024-01-01 12:00:00'
        }
    }
    
    # 模拟旧格式授权码
    old_format = {
        '<EMAIL>': 'abcdefghijklmnop'
    }
    
    # 测试新格式提取
    auth_info = new_format.get('<EMAIL>')
    if isinstance(auth_info, dict):
        auth_code = auth_info.get('auth_code', '')
        print(f"✓ 新格式授权码提取成功: {auth_code}")
    
    # 测试旧格式提取
    auth_info = old_format.get('<EMAIL>')
    if isinstance(auth_info, dict):
        auth_code = auth_info.get('auth_code', '')
    else:
        auth_code = auth_info  # 兼容旧格式
    print(f"✓ 旧格式授权码提取成功: {auth_code}")

def test_placeholder_text():
    """测试占位符文本处理"""
    print("\n测试占位符文本处理...")
    
    placeholder_text = "请在此输入邮件正文内容...\n\n支持功能：\n• 支持中文输入\n• 支持Emoji表情 😊\n• 支持多行文本\n• 右键菜单快速插入表情"
    
    # 测试占位符文本识别
    test_input = placeholder_text.strip()
    if test_input == placeholder_text.strip():
        body = ""
        print("✓ 占位符文本识别成功，转换为空正文")
    
    # 测试实际内容
    test_input = "这是真实的邮件内容"
    if test_input != placeholder_text.strip():
        body = test_input
        print(f"✓ 实际内容识别成功: {body}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("    邮件系统修复效果测试")
    print("=" * 50)
    
    # 运行测试
    test_auth_code_format()
    test_placeholder_text()
    
    print("\n" + "=" * 50)
    print("    测试完成！")
    print("=" * 50)
    
    # 启动GUI进行手动测试
    print("\n启动GUI进行手动测试...")
    print("请测试以下功能：")
    print("1. 邮件正文输入和占位符处理")
    print("2. 添加到队列功能")
    print("3. 队列管理窗口")
    print("4. 授权码管理")
    print("5. 队列发送功能")
    
    root = tk.Tk()
    app = EmailSenderGUI(root)
    
    # 添加测试数据
    app.log_message("🧪 测试模式启动")
    app.log_message("✅ 授权码获取逻辑已修复")
    app.log_message("✅ 邮件正文占位符处理已优化")
    app.log_message("✅ 队列系统集成已完善")
    app.log_message("✅ 界面布局已美化")
    
    root.mainloop()

if __name__ == "__main__":
    main()
