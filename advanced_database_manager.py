#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级数据库管理器 - 彻底解决数据库锁定问题
"""

import sqlite3
import threading
import queue
import time
import logging
from typing import Dict, Any, Optional, Callable, List
from contextlib import contextmanager
import os
import datetime

class AdvancedDatabaseManager:
    """高级数据库管理器 - 使用连接池和队列机制避免锁定"""
    
    def __init__(self, db_path: str, max_connections: int = 3):
        self.db_path = db_path
        self.max_connections = max_connections
        self.connection_pool = queue.Queue(maxsize=max_connections)
        self.operation_queue = queue.Queue()
        self.result_queues = {}
        self.worker_thread = None
        self.running = False
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        
        # 确保数据库目录存在
        os.makedirs(os.path.dirname(db_path) if os.path.dirname(db_path) else '.', exist_ok=True)
        
        # 初始化连接池
        self._init_connection_pool()
        
        # 启动工作线程
        self._start_worker()
    
    def _init_connection_pool(self):
        """初始化连接池"""
        for _ in range(self.max_connections):
            conn = self._create_connection()
            if conn:
                self.connection_pool.put(conn)
    
    def _create_connection(self) -> Optional[sqlite3.Connection]:
        """创建优化的数据库连接"""
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=60.0,
                check_same_thread=False,
                isolation_level=None  # 自动提交模式
            )
            
            # 优化设置 - 彻底解决锁定问题
            conn.execute("PRAGMA journal_mode=WAL")  # WAL模式减少锁定
            conn.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全
            conn.execute("PRAGMA busy_timeout=60000")  # 60秒忙等待
            conn.execute("PRAGMA temp_store=MEMORY")  # 临时存储在内存
            conn.execute("PRAGMA cache_size=10000")  # 增大缓存
            conn.execute("PRAGMA wal_autocheckpoint=1000")  # WAL检查点
            conn.execute("PRAGMA optimize")  # 优化查询
            
            return conn
        except Exception as e:
            self.logger.error(f"创建数据库连接失败: {str(e)}")
            return None
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            # 从连接池获取连接
            conn = self.connection_pool.get(timeout=30)
            yield conn
        except queue.Empty:
            self.logger.warning("连接池超时，创建临时连接")
            conn = self._create_connection()
            if conn:
                yield conn
            else:
                raise Exception("无法创建数据库连接")
        except Exception as e:
            self.logger.error(f"数据库连接错误: {str(e)}")
            raise
        finally:
            if conn:
                try:
                    # 归还连接到池中
                    self.connection_pool.put_nowait(conn)
                except queue.Full:
                    # 连接池满了，关闭连接
                    conn.close()
    
    def _start_worker(self):
        """启动工作线程"""
        self.running = True
        self.worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self.worker_thread.start()
    
    def _worker_loop(self):
        """工作线程循环 - 串行化所有数据库操作"""
        while self.running:
            try:
                # 获取操作请求
                operation = self.operation_queue.get(timeout=1)
                if operation is None:
                    break
                
                operation_id, func, args, kwargs = operation
                
                try:
                    # 执行数据库操作
                    with self.get_connection() as conn:
                        result = func(conn, *args, **kwargs)
                    
                    # 返回结果
                    if operation_id in self.result_queues:
                        self.result_queues[operation_id].put(('success', result))
                
                except Exception as e:
                    # 返回错误
                    if operation_id in self.result_queues:
                        self.result_queues[operation_id].put(('error', str(e)))
                
                finally:
                    self.operation_queue.task_done()
            
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"工作线程错误: {str(e)}")
    
    def execute_operation(self, func: Callable, *args, **kwargs) -> Any:
        """执行数据库操作（通过队列串行化）"""
        operation_id = id(threading.current_thread()) + int(time.time() * 1000000)
        
        # 创建结果队列
        result_queue = queue.Queue()
        self.result_queues[operation_id] = result_queue
        
        try:
            # 提交操作到队列
            self.operation_queue.put((operation_id, func, args, kwargs))
            
            # 等待结果
            status, result = result_queue.get(timeout=60)
            
            if status == 'success':
                return result
            else:
                raise Exception(result)
        
        finally:
            # 清理结果队列
            if operation_id in self.result_queues:
                del self.result_queues[operation_id]
    
    def close(self):
        """关闭数据库管理器"""
        self.running = False
        
        # 停止工作线程
        if self.worker_thread and self.worker_thread.is_alive():
            self.operation_queue.put(None)
            self.worker_thread.join(timeout=5)
        
        # 关闭所有连接
        while not self.connection_pool.empty():
            try:
                conn = self.connection_pool.get_nowait()
                conn.close()
            except queue.Empty:
                break
            except Exception as e:
                self.logger.error(f"关闭连接失败: {str(e)}")

# 全局数据库管理器实例
_db_managers = {}
_db_lock = threading.Lock()

def get_advanced_db_manager(db_path: str) -> AdvancedDatabaseManager:
    """获取高级数据库管理器实例（单例模式）"""
    with _db_lock:
        if db_path not in _db_managers:
            _db_managers[db_path] = AdvancedDatabaseManager(db_path)
        return _db_managers[db_path]

def close_all_advanced_db_managers():
    """关闭所有高级数据库管理器"""
    with _db_lock:
        for manager in _db_managers.values():
            manager.close()
        _db_managers.clear()

# 数据库操作函数
def update_recipient_status_op(conn: sqlite3.Connection, recipient_email: str, sender_email: str, status: str):
    """更新收件人状态的数据库操作"""
    cursor = conn.cursor()
    
    # 确保表存在
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS recipient_status (
            recipient_email TEXT,
            sender_email TEXT,
            last_reply_time TEXT,
            reply_count INTEGER DEFAULT 0,
            bounce_count INTEGER DEFAULT 0,
            status TEXT,
            updated_at TEXT,
            PRIMARY KEY (recipient_email, sender_email)
        )
    ''')
    
    # 获取当前状态
    cursor.execute('''
        SELECT reply_count, bounce_count FROM recipient_status 
        WHERE recipient_email = ? AND sender_email = ?
    ''', (recipient_email, sender_email))
    
    result = cursor.fetchone()
    if result:
        reply_count, bounce_count = result
    else:
        reply_count, bounce_count = 0, 0
    
    # 更新计数
    if status == 'auto_reply':
        reply_count += 1
    elif status == 'bounce':
        bounce_count += 1
    
    # 插入或更新记录
    cursor.execute('''
        INSERT OR REPLACE INTO recipient_status 
        (recipient_email, sender_email, last_reply_time, reply_count, bounce_count, status, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (recipient_email, sender_email, datetime.datetime.now().isoformat(),
              reply_count, bounce_count, status, datetime.datetime.now().isoformat()))
    
    return True

def save_auto_reply_op(conn: sqlite3.Connection, reply_info: Dict[str, Any]):
    """保存自动回复的数据库操作"""
    cursor = conn.cursor()
    
    # 确保表存在
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS auto_replies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            recipient_email TEXT,
            sender_email TEXT,
            reply_type TEXT,
            reply_time TEXT,
            subject TEXT,
            body TEXT,
            created_at TEXT,
            UNIQUE(recipient_email, sender_email, reply_time)
        )
    ''')
    
    cursor.execute('''
        INSERT OR REPLACE INTO auto_replies 
        (recipient_email, sender_email, reply_type, reply_time, subject, body, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (
        reply_info['recipient_email'],
        reply_info['sender_email'],
        reply_info['reply_type'],
        reply_info['reply_time'],
        reply_info['subject'],
        reply_info['body'],
        datetime.datetime.now().isoformat()
    ))
    
    return True

def get_recent_replies_op(conn: sqlite3.Connection, sender_email: str, hours: int, target_recipients: List[str] = None):
    """获取最近回复的数据库操作"""
    cursor = conn.cursor()
    
    # 确保表存在
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS auto_replies (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            recipient_email TEXT,
            sender_email TEXT,
            reply_type TEXT,
            reply_time TEXT,
            subject TEXT,
            body TEXT,
            created_at TEXT,
            UNIQUE(recipient_email, sender_email, reply_time)
        )
    ''')
    
    # 构建查询
    if target_recipients:
        placeholders = ','.join(['?' for _ in target_recipients])
        query = f'''
            SELECT recipient_email, reply_type, reply_time, subject, body
            FROM auto_replies 
            WHERE sender_email = ? 
            AND recipient_email IN ({placeholders})
            AND datetime(reply_time) > datetime('now', '-{hours} hours')
            ORDER BY reply_time DESC
        '''
        params = [sender_email] + target_recipients
    else:
        query = '''
            SELECT recipient_email, reply_type, reply_time, subject, body
            FROM auto_replies 
            WHERE sender_email = ? 
            AND datetime(reply_time) > datetime('now', '-{} hours')
            ORDER BY reply_time DESC
        '''.format(hours)
        params = [sender_email]
    
    cursor.execute(query, params)
    results = cursor.fetchall()
    
    replies = []
    for row in results:
        replies.append({
            'recipient_email': row[0],
            'reply_type': row[1],
            'reply_time': row[2],
            'subject': row[3],
            'body': row[4],
            'sender_email': sender_email
        })
    
    return replies
