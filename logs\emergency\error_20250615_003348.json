{"timestamp": "2025-06-15T00:33:48.872978", "type": "AttributeError", "message": "'IntelligentErrorHandler' object has no attribute 'emergency_system_cleanup'", "traceback": "  File \"E:\\自动发邮件\\智能错误处理与应急恢复系统.py\", line 861, in <module>\n    main()\n  File \"E:\\自动发邮件\\智能错误处理与应急恢复系统.py\", line 822, in main\n    handler = IntelligentErrorHandler()\n              ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"E:\\自动发邮件\\智能错误处理与应急恢复系统.py\", line 40, in __init__\n    self.setup_error_handling()\n  File \"E:\\自动发邮件\\智能错误处理与应急恢复系统.py\", line 136, in setup_error_handling\n    'emergency_cleanup': self.emergency_system_cleanup,\n                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "severity": "medium", "error_type": "unknown", "auto_fix": false, "strategy": "manual_intervention"}