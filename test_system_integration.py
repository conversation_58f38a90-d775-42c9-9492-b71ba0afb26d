#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试系统集成功能
验证自动回复监控、质量数据库、应急系统的深度集成
"""

import sys
import os
import datetime
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('system_integration_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_system_integration():
    """测试系统集成功能"""
    print("🔗 开始测试系统集成功能...")
    print("="*60)
    
    try:
        # 导入系统集成管理器
        from system_integration_manager import SystemIntegrationManager
        
        # 创建集成管理器实例
        integration_manager = SystemIntegrationManager()
        print("✅ 系统集成管理器初始化成功")
        
        # 测试邮箱
        test_email = "<EMAIL>"
        
        print(f"\n📧 测试邮箱: {test_email}")
        print("-"*40)
        
        # 测试1: 同步自动回复监控到质量数据库
        print("\n🔄 测试1: 同步自动回复监控到质量数据库")
        result1 = integration_manager.sync_auto_reply_to_quality_db(test_email)
        
        if result1.get('success'):
            print(f"✅ 同步成功:")
            print(f"   有效收件人: {result1.get('valid_count', 0)} 个")
            print(f"   无效收件人: {result1.get('invalid_count', 0)} 个")
            print(f"   总计同步: {result1.get('total_synced', 0)} 个")
        else:
            print(f"❌ 同步失败: {result1.get('error', '未知错误')}")
        
        # 测试2: 同步质量数据库到应急系统
        print("\n⚡ 测试2: 同步质量数据库到应急系统")
        result2 = integration_manager.sync_quality_to_emergency_system(test_email)
        
        if result2.get('success'):
            print(f"✅ 同步成功:")
            print(f"   低质量收件人: {result2.get('low_quality_count', 0)} 个")
            print(f"   应急系统触发: {'是' if result2.get('emergency_triggered') else '否'}")
        else:
            print(f"❌ 同步失败: {result2.get('error', '未知错误')}")
        
        # 测试3: 完整系统集成
        print("\n🎯 测试3: 完整系统集成")
        result3 = integration_manager.full_system_integration(test_email)

        if result3.get('success'):
            print(f"✅ 完整集成成功:")
            print(f"   开始时间: {result3.get('start_time', '')}")
            print(f"   结束时间: {result3.get('end_time', '')}")
            print(f"   执行步骤: {len(result3.get('steps', []))} 个")

            for step in result3.get('steps', []):
                step_result = step.get('result', {})
                status_icon = "✅" if step_result.get('success') else "❌"
                print(f"   {step.get('step')}. {step.get('name')} {status_icon}")
        else:
            print(f"❌ 完整集成失败: {result3.get('error', '未知错误')}")
            # 如果失败，显示详细的步骤信息用于调试
            if 'steps' in result3:
                print("   详细步骤信息:")
                for step in result3.get('steps', []):
                    step_result = step.get('result', {})
                    status_icon = "✅" if step_result.get('success') else "❌"
                    print(f"   {step.get('step')}. {step.get('name')} {status_icon}")
                    if not step_result.get('success') and 'error' in step_result:
                        print(f"      错误: {step_result['error']}")
        
        # 测试4: 获取集成状态
        print("\n📊 测试4: 获取集成状态")
        status = integration_manager.get_integration_status(test_email)
        
        if 'error' not in status:
            print(f"✅ 状态获取成功:")
            print(f"   总同步次数: {status.get('total_syncs', 0)} 次")
            print(f"   成功同步: {status.get('successful_syncs', 0)} 次")
            print(f"   成功率: {status.get('success_rate', 0):.1f}%")
            print(f"   最近同步记录: {len(status.get('recent_syncs', []))} 条")
        else:
            print(f"❌ 状态获取失败: {status.get('error', '未知错误')}")
        
        print("\n"+"="*60)
        print("🎉 系统集成功能测试完成!")
        
        # 生成测试报告
        generate_test_report(result1, result2, result3, status)
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
        print("请确保所有必要的模块都已正确安装")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logging.error(f"系统集成测试失败: {str(e)}")

def generate_test_report(result1, result2, result3, status):
    """生成测试报告"""
    try:
        report_content = f"""🔗 系统集成功能测试报告
{'='*60}
测试时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试系统: 邮件系统 v3.0

📋 测试结果汇总:
{'='*60}

1. 自动回复监控 → 质量数据库同步
   状态: {'✅ 成功' if result1.get('success') else '❌ 失败'}
   详情: {result1.get('total_synced', 0) if result1.get('success') else result1.get('error', '未知错误')}

2. 质量数据库 → 应急系统同步  
   状态: {'✅ 成功' if result2.get('success') else '❌ 失败'}
   详情: {result2.get('low_quality_count', 0) if result2.get('success') else result2.get('error', '未知错误')}

3. 完整系统集成
   状态: {'✅ 成功' if result3.get('success') else '❌ 失败'}
   详情: {len(result3.get('steps', [])) if result3.get('success') else result3.get('error', '未知错误')}

4. 集成状态查询
   状态: {'✅ 成功' if 'error' not in status else '❌ 失败'}
   详情: {f"成功率 {status.get('success_rate', 0):.1f}%" if 'error' not in status else status.get('error', '未知错误')}

📊 总体评估:
{'='*60}
• 功能完整性: {'✅ 完整' if all([result1.get('success'), result2.get('success'), result3.get('success')]) else '⚠️ 部分功能异常'}
• 数据集成: {'✅ 正常' if 'error' not in status else '❌ 异常'}
• 系统协调: {'✅ 良好' if result3.get('success') else '❌ 需要改进'}

💡 建议:
• 定期执行系统集成测试
• 监控各模块间的数据同步状态
• 及时处理集成过程中的异常情况

🔧 技术说明:
• 系统集成解决了各功能模块相互独立的问题
• 实现了自动回复监控与质量数据库的数据共享
• 建立了质量评分与应急系统的联动机制
• 提供了统一的集成状态监控和管理界面
"""
        
        # 保存报告
        report_filename = f"system_integration_report_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"\n📄 测试报告已保存: {report_filename}")
        
    except Exception as e:
        print(f"❌ 生成测试报告失败: {str(e)}")

def test_qq_emergency_fix():
    """测试QQ应急状态修复"""
    print("\n🆘 测试QQ应急状态修复...")
    print("-"*40)
    
    try:
        from qq_email_anti_spam import QQEmailAntiSpamManager
        
        # 创建QQ应急管理器
        qq_manager = QQEmailAntiSpamManager()
        print("✅ QQ应急管理器初始化成功")
        
        # 测试邮箱
        test_email = "<EMAIL>"
        test_recipient = "<EMAIL>"
        
        # 测试更新回复状态（修复REPLY_RECEIVED错误）
        print(f"\n📧 测试更新回复状态: {test_recipient}")
        
        # 测试正常回复
        result1 = qq_manager.update_qq_reply_status(
            sender_email=test_email,
            recipient_email=test_recipient,
            has_reply=True,
            reply_type="REPLY_RECEIVED"  # 测试修复后的处理
        )
        
        if result1:
            print("✅ 正常回复状态更新成功")
        else:
            print("❌ 正常回复状态更新失败")
        
        # 测试无回复
        result2 = qq_manager.update_qq_reply_status(
            sender_email=test_email,
            recipient_email=test_recipient,
            has_reply=False,
            reply_type=""
        )
        
        if result2:
            print("✅ 无回复状态更新成功")
        else:
            print("❌ 无回复状态更新失败")
        
        # 获取应急状态
        emergency_status = qq_manager.get_qq_emergency_status(test_email)
        
        if emergency_status:
            print(f"✅ 应急状态获取成功")
            print(f"   应急模式: {'激活' if emergency_status.get('emergency', {}).get('is_active') else '未激活'}")
        else:
            print("❌ 应急状态获取失败")
        
        print("✅ QQ应急状态修复测试完成")
        
    except Exception as e:
        print(f"❌ QQ应急状态测试失败: {str(e)}")

if __name__ == "__main__":
    print("🚀 启动系统集成功能测试")
    print("="*60)
    
    # 测试系统集成
    test_system_integration()
    
    # 测试QQ应急状态修复
    test_qq_emergency_fix()
    
    print("\n🎯 所有测试完成!")
    print("="*60)
