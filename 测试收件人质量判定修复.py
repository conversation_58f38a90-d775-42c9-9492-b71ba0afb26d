#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试收件人质量判定修复
验证修复后的收件人质量判定逻辑是否正确工作
"""

import os
import sys
import logging
import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('recipient_quality_fix_test.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

def test_recipient_quality_manager():
    """测试收件人质量管理器的修复效果"""
    try:
        print("🔧 测试收件人质量管理器修复效果...")
        
        from recipient_quality_manager import RecipientQualityManager
        
        # 创建质量管理器
        quality_manager = RecipientQualityManager()
        
        # 1. 测试添加测试收件人
        print("\n📝 步骤1: 添加测试收件人")
        test_recipients = [
            {"email": "<EMAIL>", "score": 85, "has_reply": True},
            {"email": "<EMAIL>", "score": 55, "has_reply": False},
            {"email": "<EMAIL>", "score": 25, "has_reply": False},
            {"email": "<EMAIL>", "score": 5, "has_reply": False}
        ]
        
        sender_email = "<EMAIL>"
        
        for recipient in test_recipients:
            quality_manager.add_recipient(
                email=recipient["email"],
                sender_email=sender_email,
                initial_score=recipient["score"],
                source="测试数据"
            )
            
            # 模拟发送和回复数据
            quality_manager.update_recipient_quality(
                email=recipient["email"],
                sender_email=sender_email,
                subject="测试邮件",
                body="测试内容",
                success=True,
                reply_received=recipient["has_reply"],
                reply_type="auto" if recipient["has_reply"] else ""
            )
        
        print(f"✅ 添加了 {len(test_recipients)} 个测试收件人")
        
        # 2. 测试获取高质量收件人（有效收件人/安全收件人）
        print("\n📊 步骤2: 测试获取高质量收件人（有效收件人/安全收件人）")
        high_quality_recipients = quality_manager.get_quality_recipients(
            min_quality_score=60.0,
            limit=100
        )
        
        print(f"✅ 高质量收件人（有效收件人）: {len(high_quality_recipients)} 个")
        for recipient in high_quality_recipients:
            print(f"   📧 {recipient.email}: 质量分 {recipient.quality_score:.1f}, 状态 {recipient.status}")
        
        # 3. 测试获取低质量收件人（无效收件人）
        print("\n📊 步骤3: 测试获取低质量收件人（无效收件人）")
        low_quality_recipients = quality_manager.get_low_quality_recipients(
            sender_email=sender_email,
            max_quality_score=40.0
        )
        
        print(f"⚠️ 低质量收件人（无效收件人）: {len(low_quality_recipients)} 个")
        for recipient in low_quality_recipients:
            print(f"   📧 {recipient.email}: 质量分 {recipient.quality_score:.1f}, 状态 {recipient.status}")
        
        # 4. 测试自动清理低质量收件人的逻辑
        print("\n🧹 步骤4: 测试自动清理低质量收件人逻辑")
        
        # 模拟GUI中的自动清理逻辑
        if low_quality_recipients:
            marked_count = 0
            for recipient in low_quality_recipients:
                # 将质量分数设为0，自动标记为invalid状态
                if quality_manager.update_recipient_score(
                    email=recipient.email,
                    new_score=0.0,
                    sender_email=sender_email
                ):
                    marked_count += 1
            
            print(f"✅ 成功标记 {marked_count} 个低质量收件人为无效")
        
        # 5. 验证清理后的状态
        print("\n🔍 步骤5: 验证清理后的状态")
        
        # 重新获取低质量收件人
        updated_low_quality = quality_manager.get_low_quality_recipients(
            sender_email=sender_email,
            max_quality_score=40.0
        )
        
        print(f"📊 清理后的低质量收件人: {len(updated_low_quality)} 个")
        for recipient in updated_low_quality:
            print(f"   📧 {recipient.email}: 质量分 {recipient.quality_score:.1f}, 状态 {recipient.status}")
        
        # 6. 测试收件人质量判定逻辑的正确性
        print("\n✅ 步骤6: 验证收件人质量判定逻辑")
        
        print("\n📋 收件人质量判定标准:")
        print("🟢 有效收件人（安全收件人）:")
        print("   • 有自动回复的邮箱")
        print("   • 质量评分高的邮箱（≥60分）")
        print("   • 状态为 excellent, good, fair")
        
        print("\n🔴 无效收件人（低质量收件人）:")
        print("   • 没有自动回复的邮箱")
        print("   • 有退信记录的邮箱")
        print("   • 质量评分低的邮箱（<40分）")
        print("   • 状态为 poor, invalid")
        
        # 7. 生成质量分析报告
        print("\n📈 步骤7: 生成质量分析报告")
        analytics = quality_manager.get_quality_analytics()
        
        if analytics:
            overview = analytics.get('overview', {})
            distribution = analytics.get('quality_distribution', {})
            
            print(f"📊 质量概览:")
            print(f"   总收件人: {overview.get('total_recipients', 0)} 个")
            print(f"   平均质量分: {overview.get('avg_quality_score', 0):.1f}")
            print(f"   平均回复率: {overview.get('avg_response_rate', 0):.1f}%")
            
            print(f"\n📈 质量分布:")
            for status, count in distribution.items():
                print(f"   {status}: {count} 个")
        
        print("\n✅ 收件人质量判定修复测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        logging.error(f"测试收件人质量管理器失败: {str(e)}")
        return False

def test_method_call_fix():
    """测试方法调用修复"""
    try:
        print("\n🔧 测试方法调用修复...")
        
        from recipient_quality_manager import RecipientQualityManager
        
        quality_manager = RecipientQualityManager()
        
        # 测试正确的方法调用
        print("✅ 测试 get_quality_recipients 方法（获取高质量收件人）")
        high_quality = quality_manager.get_quality_recipients(
            min_quality_score=60.0,  # 正确参数名
            limit=10
        )
        print(f"   获取到 {len(high_quality)} 个高质量收件人")
        
        print("✅ 测试 get_low_quality_recipients 方法（获取低质量收件人）")
        low_quality = quality_manager.get_low_quality_recipients(
            max_quality_score=30.0,  # 正确参数名
            sender_email="<EMAIL>"
        )
        print(f"   获取到 {len(low_quality)} 个低质量收件人")
        
        print("✅ 方法调用修复测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 方法调用测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始收件人质量判定修复测试")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # 测试1: 收件人质量管理器
    if test_recipient_quality_manager():
        success_count += 1
    
    # 测试2: 方法调用修复
    if test_method_call_fix():
        success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 个测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！收件人质量判定修复成功！")
        print("\n📋 修复总结:")
        print("✅ 修复了 get_quality_recipients 方法调用错误")
        print("✅ 明确了有效收件人和无效收件人的判定标准")
        print("✅ 修复了自动清理低质量收件人的逻辑")
        print("✅ 确保了收件人质量判定逻辑的正确性")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return success_count == total_tests

if __name__ == "__main__":
    main()
