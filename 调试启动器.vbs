' ================================================================
' 调试启动器 v4.0 - 带详细错误记录
' 功能: 记录所有执行步骤，找出闪退原因
' 日期: 2025-06-12
' ================================================================

Option Explicit

' 声明变量
Dim objShell, objFSO, currentDir, scriptPath, command, result
Dim pythonCmd, errorMsg, logFile, logContent

' 初始化日志
logContent = "=== 启动器调试日志 ===" & vbCrLf
logContent = logContent & "启动时间: " & Now() & vbCrLf & vbCrLf

' 步骤1: 初始化对象
logContent = logContent & "步骤1: 初始化对象..." & vbCrLf
On Error Resume Next
Set objShell = CreateObject("WScript.Shell")
If Err.Number <> 0 Then
    logContent = logContent & "错误: 无法创建WScript.Shell对象 - " & Err.Description & vbCrLf
    WriteLog
    MsgBox "致命错误: 无法创建Shell对象!" & vbCrLf & Err.Description, vbCritical, "初始化失败"
    WScript.Quit 1
End If

Set objFSO = CreateObject("Scripting.FileSystemObject")
If Err.Number <> 0 Then
    logContent = logContent & "错误: 无法创建FileSystemObject对象 - " & Err.Description & vbCrLf
    WriteLog
    MsgBox "致命错误: 无法创建文件系统对象!" & vbCrLf & Err.Description, vbCritical, "初始化失败"
    WScript.Quit 1
End If
On Error GoTo 0
logContent = logContent & "成功: 对象初始化完成" & vbCrLf & vbCrLf

' 步骤2: 获取当前目录
logContent = logContent & "步骤2: 获取当前目录..." & vbCrLf
On Error Resume Next
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)
If Err.Number <> 0 Then
    logContent = logContent & "错误: 无法获取当前目录 - " & Err.Description & vbCrLf
    WriteLog
    MsgBox "错误: 无法获取当前目录!" & vbCrLf & Err.Description, vbCritical, "目录错误"
    WScript.Quit 1
End If
On Error GoTo 0
logContent = logContent & "成功: 当前目录 = " & currentDir & vbCrLf & vbCrLf

' 步骤3: 检查主脚本文件
logContent = logContent & "步骤3: 检查主脚本文件..." & vbCrLf
scriptPath = currentDir & "\gui_main.py"
logContent = logContent & "查找文件: " & scriptPath & vbCrLf

On Error Resume Next
If Not objFSO.FileExists(scriptPath) Then
    logContent = logContent & "错误: gui_main.py文件不存在!" & vbCrLf
    WriteLog
    errorMsg = "错误: 找不到主程序文件!" & vbCrLf & vbCrLf & _
               "缺少文件: gui_main.py" & vbCrLf & _
               "当前目录: " & currentDir & vbCrLf & vbCrLf & _
               "请确保gui_main.py文件在同一目录中。"
    MsgBox errorMsg, vbCritical + vbOKOnly, "文件缺失"
    WScript.Quit 1
End If
On Error GoTo 0
logContent = logContent & "成功: gui_main.py文件存在" & vbCrLf & vbCrLf

' 步骤4: 检测Python环境
logContent = logContent & "步骤4: 检测Python环境..." & vbCrLf
pythonCmd = ""

' 测试python命令
logContent = logContent & "测试python命令..." & vbCrLf
On Error Resume Next
Err.Clear
result = objShell.Run("python --version", 0, True)
logContent = logContent & "python --version 返回码: " & result & vbCrLf
If Err.Number <> 0 Then
    logContent = logContent & "python命令错误: " & Err.Description & vbCrLf
Else
    If result = 0 Then
        pythonCmd = "python"
        logContent = logContent & "成功: python命令可用" & vbCrLf
    Else
        logContent = logContent & "python命令返回非零值: " & result & vbCrLf
    End If
End If

' 测试python3命令
If pythonCmd = "" Then
    logContent = logContent & "测试python3命令..." & vbCrLf
    Err.Clear
    result = objShell.Run("python3 --version", 0, True)
    logContent = logContent & "python3 --version 返回码: " & result & vbCrLf
    If Err.Number <> 0 Then
        logContent = logContent & "python3命令错误: " & Err.Description & vbCrLf
    Else
        If result = 0 Then
            pythonCmd = "python3"
            logContent = logContent & "成功: python3命令可用" & vbCrLf
        Else
            logContent = logContent & "python3命令返回非零值: " & result & vbCrLf
        End If
    End If
End If

' 测试py命令
If pythonCmd = "" Then
    logContent = logContent & "测试py命令..." & vbCrLf
    Err.Clear
    result = objShell.Run("py --version", 0, True)
    logContent = logContent & "py --version 返回码: " & result & vbCrLf
    If Err.Number <> 0 Then
        logContent = logContent & "py命令错误: " & Err.Description & vbCrLf
    Else
        If result = 0 Then
            pythonCmd = "py"
            logContent = logContent & "成功: py命令可用" & vbCrLf
        Else
            logContent = logContent & "py命令返回非零值: " & result & vbCrLf
        End If
    End If
End If

On Error GoTo 0

' 检查Python检测结果
If pythonCmd = "" Then
    logContent = logContent & "错误: 没有找到可用的Python命令!" & vbCrLf
    WriteLog
    errorMsg = "错误: 未找到Python环境!" & vbCrLf & vbCrLf & _
               "请安装Python 3.6或更高版本。" & vbCrLf & vbCrLf & _
               "安装后确保Python已添加到系统PATH环境变量。" & vbCrLf & vbCrLf & _
               "详细日志已保存到: 启动器调试日志.txt"
    MsgBox errorMsg, vbCritical + vbOKOnly, "Python环境错误"
    WScript.Quit 2
Else
    logContent = logContent & "最终选择的Python命令: " & pythonCmd & vbCrLf & vbCrLf
End If

' 步骤5: 构建启动命令
logContent = logContent & "步骤5: 构建启动命令..." & vbCrLf
command = "cmd.exe /c ""cd /d """ & currentDir & """ && " & pythonCmd & " gui_main.py"""
logContent = logContent & "启动命令: " & command & vbCrLf & vbCrLf

' 步骤6: 启动程序
logContent = logContent & "步骤6: 启动程序..." & vbCrLf
On Error Resume Next
Err.Clear
result = objShell.Run(command, 1, False)
logContent = logContent & "启动命令返回码: " & result & vbCrLf

If Err.Number <> 0 Then
    logContent = logContent & "启动错误: " & Err.Description & " (错误号: " & Err.Number & ")" & vbCrLf
    WriteLog
    errorMsg = "启动失败!" & vbCrLf & vbCrLf & _
               "错误代码: " & Err.Number & vbCrLf & _
               "错误描述: " & Err.Description & vbCrLf & vbCrLf & _
               "启动命令: " & command & vbCrLf & vbCrLf & _
               "详细日志已保存到: 启动器调试日志.txt" & vbCrLf & vbCrLf & _
               "请尝试手动运行: " & pythonCmd & " gui_main.py"
    MsgBox errorMsg, vbCritical + vbOKOnly, "启动错误"
    WScript.Quit 3
Else
    logContent = logContent & "成功: 程序启动命令已执行" & vbCrLf
    logContent = logContent & "注意: 这不代表GUI一定启动成功，只是命令执行了" & vbCrLf
End If

On Error GoTo 0

' 步骤7: 完成
logContent = logContent & vbCrLf & "步骤7: 启动器执行完成" & vbCrLf
logContent = logContent & "完成时间: " & Now() & vbCrLf
logContent = logContent & "=== 日志结束 ===" & vbCrLf

' 写入日志
WriteLog

' 显示完成信息（可选）
' MsgBox "启动器执行完成!" & vbCrLf & "详细日志: 启动器调试日志.txt", vbInformation, "执行完成"

' 正常退出
WScript.Quit 0

' 写入日志的子程序
Sub WriteLog()
    On Error Resume Next
    Dim logFilePath
    logFilePath = currentDir & "\启动器调试日志.txt"
    
    ' 如果objFSO不存在，尝试重新创建
    If objFSO Is Nothing Then
        Set objFSO = CreateObject("Scripting.FileSystemObject")
    End If
    
    If Not objFSO Is Nothing Then
        Dim logFileObj
        Set logFileObj = objFSO.CreateTextFile(logFilePath, True)
        If Not logFileObj Is Nothing Then
            logFileObj.Write logContent
            logFileObj.Close
        End If
    End If
    On Error GoTo 0
End Sub
