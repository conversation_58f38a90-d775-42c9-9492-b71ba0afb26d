# -*- coding: utf-8 -*-
"""
邮箱有效性检测工具
通过实际发送测试来验证邮箱是否存在和有效
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from config import SMTP_CONFIG

def test_email_validity(sender_email, test_emails):
    """
    测试邮箱有效性
    
    Args:
        sender_email: 发送者邮箱
        test_emails: 要测试的邮箱列表
    
    Returns:
        dict: 测试结果
    """
    results = {
        'valid': [],      # 有效邮箱
        'invalid': [],    # 无效邮箱
        'unknown': [],    # 无法确定
        'errors': {}      # 错误详情
    }
    
    print(f"开始测试 {len(test_emails)} 个邮箱的有效性...")
    print("=" * 50)
    
    for i, email in enumerate(test_emails, 1):
        print(f"\n测试 {i}/{len(test_emails)}: {email}")
        print("-" * 30)
        
        try:
            # 创建测试邮件（不实际发送，只测试收件人验证）
            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = email
            msg['Subject'] = "邮箱有效性测试"
            
            # 连接SMTP服务器
            server = smtplib.SMTP(SMTP_CONFIG['server'], SMTP_CONFIG['port'])
            server.starttls()
            server.login(sender_email, SMTP_CONFIG['password'])
            
            # 尝试验证收件人（不实际发送）
            try:
                # 使用RCPT TO命令验证收件人
                server.mail(sender_email)
                code, message = server.rcpt(email)
                
                if code == 250:
                    print(f"✓ 有效邮箱: {email}")
                    results['valid'].append(email)
                elif code in [550, 551, 553]:
                    print(f"✗ 无效邮箱: {email} (错误码: {code})")
                    results['invalid'].append(email)
                    results['errors'][email] = f"SMTP错误码 {code}: {message.decode() if isinstance(message, bytes) else message}"
                else:
                    print(f"? 无法确定: {email} (错误码: {code})")
                    results['unknown'].append(email)
                    results['errors'][email] = f"SMTP错误码 {code}: {message.decode() if isinstance(message, bytes) else message}"
                    
            except smtplib.SMTPRecipientsRefused:
                print(f"✗ 无效邮箱: {email} (收件人被拒绝)")
                results['invalid'].append(email)
                results['errors'][email] = "收件人地址被拒绝"
                
            except smtplib.SMTPResponseException as e:
                if e.smtp_code in [550, 551, 553]:
                    print(f"✗ 无效邮箱: {email} (错误码: {e.smtp_code})")
                    results['invalid'].append(email)
                else:
                    print(f"? 无法确定: {email} (错误码: {e.smtp_code})")
                    results['unknown'].append(email)
                results['errors'][email] = f"SMTP错误码 {e.smtp_code}: {str(e)}"
            
            server.quit()
            
        except smtplib.SMTPAuthenticationError:
            print(f"❌ 认证失败，无法继续测试")
            break
            
        except Exception as e:
            print(f"? 测试出错: {email} - {str(e)}")
            results['unknown'].append(email)
            results['errors'][email] = str(e)
    
    return results

def parse_email_list(email_input):
    """解析邮箱列表"""
    # 支持分号、逗号、换行分隔
    if ';' in email_input or '；' in email_input:
        email_input = email_input.replace('；', ';')
        emails = [email.strip() for email in email_input.split(';')]
    elif '\n' in email_input:
        emails = [email.strip() for email in email_input.split('\n')]
    else:
        email_input = email_input.replace('，', ',')
        emails = [email.strip() for email in email_input.split(',')]
    
    # 过滤空邮箱
    return [email for email in emails if email and '@' in email]

def main():
    """主函数"""
    print("邮箱有效性检测工具")
    print("=" * 50)
    print("注意：此工具通过SMTP验证邮箱是否存在")
    print("某些邮件服务器可能不支持此验证方式")
    print("=" * 50)
    
    # 获取发送者邮箱
    sender_email = input("\n请输入您的QQ邮箱地址: ").strip()
    if not sender_email.endswith('@qq.com'):
        print("❌ 请输入有效的QQ邮箱地址")
        return
    
    # 获取要测试的邮箱
    print("\n请输入要测试的邮箱地址（支持分号、逗号、换行分隔）:")
    email_input = input().strip()
    
    if not email_input:
        print("❌ 请输入要测试的邮箱地址")
        return
    
    # 解析邮箱列表
    test_emails = parse_email_list(email_input)
    
    if not test_emails:
        print("❌ 没有找到有效的邮箱地址")
        return
    
    print(f"\n解析到 {len(test_emails)} 个邮箱地址:")
    for email in test_emails:
        print(f"  - {email}")
    
    # 确认测试
    confirm = input(f"\n确认测试这 {len(test_emails)} 个邮箱吗？(y/n): ").strip().lower()
    if confirm != 'y':
        print("测试已取消")
        return
    
    # 开始测试
    results = test_email_validity(sender_email, test_emails)
    
    # 显示结果
    print("\n" + "=" * 50)
    print("测试结果汇总")
    print("=" * 50)
    
    print(f"\n✓ 有效邮箱 ({len(results['valid'])} 个):")
    for email in results['valid']:
        print(f"  ✓ {email}")
    
    print(f"\n✗ 无效邮箱 ({len(results['invalid'])} 个):")
    for email in results['invalid']:
        error = results['errors'].get(email, '未知错误')
        print(f"  ✗ {email} - {error}")
    
    if results['unknown']:
        print(f"\n? 无法确定 ({len(results['unknown'])} 个):")
        for email in results['unknown']:
            error = results['errors'].get(email, '未知错误')
            print(f"  ? {email} - {error}")
    
    # 生成建议
    print(f"\n📋 建议:")
    if results['valid']:
        print(f"- 可以正常发送给 {len(results['valid'])} 个有效邮箱")
    if results['invalid']:
        print(f"- 建议从列表中移除 {len(results['invalid'])} 个无效邮箱")
    if results['unknown']:
        print(f"- {len(results['unknown'])} 个邮箱无法确定，建议实际测试")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n程序出错: {str(e)}")
    
    input("\n按回车键退出...")
