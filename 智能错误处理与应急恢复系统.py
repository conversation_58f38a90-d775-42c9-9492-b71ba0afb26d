#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能错误处理与应急恢复系统 v1.0
功能：
1. 实时错误监控和处理
2. 自动应急响应机制
3. 智能恢复和修复
4. 预防性检查和维护
5. 系统健康状态监控
"""

import os
import sys
import json
import time
import shutil
import logging
import traceback
import threading
import subprocess
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
import ast
import re

class IntelligentErrorHandler:
    """智能错误处理器"""
    
    def __init__(self):
        self.setup_logging()
        self.error_count = 0
        self.recovery_attempts = 0
        self.emergency_mode = False
        self.monitoring_active = False
        self.error_patterns = {}
        self.recovery_strategies = {}
        self.system_baseline = {}
        self.setup_error_handling()
        
    def setup_logging(self):
        """设置多级日志系统"""
        os.makedirs('logs/emergency', exist_ok=True)
        
        # 主日志
        self.main_logger = logging.getLogger('main')
        self.main_logger.setLevel(logging.INFO)
        
        # 错误日志
        self.error_logger = logging.getLogger('error')
        self.error_logger.setLevel(logging.ERROR)
        
        # 应急日志
        self.emergency_logger = logging.getLogger('emergency')
        self.emergency_logger.setLevel(logging.CRITICAL)
        
        # 配置处理器
        handlers = [
            ('logs/intelligent_error_handler.log', self.main_logger),
            ('logs/emergency/error_details.log', self.error_logger),
            ('logs/emergency/emergency_actions.log', self.emergency_logger)
        ]
        
        for log_file, logger in handlers:
            handler = logging.FileHandler(log_file, encoding='utf-8')
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
            # 同时输出到控制台
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
    
    def setup_error_handling(self):
        """设置错误处理机制"""
        # 注册全局异常处理器
        sys.excepthook = self.global_exception_handler
        
        # 设置错误模式识别
        self.error_patterns = {
            'syntax_error': {
                'pattern': r'SyntaxError|invalid syntax',
                'severity': 'critical',
                'auto_fix': True,
                'strategy': 'syntax_repair'
            },
            'import_error': {
                'pattern': r'ImportError|ModuleNotFoundError',
                'severity': 'high',
                'auto_fix': True,
                'strategy': 'dependency_install'
            },
            'file_not_found': {
                'pattern': r'FileNotFoundError|No such file',
                'severity': 'high',
                'auto_fix': True,
                'strategy': 'file_recovery'
            },
            'database_error': {
                'pattern': r'sqlite3\.Error|database.*locked',
                'severity': 'high',
                'auto_fix': True,
                'strategy': 'database_repair'
            },
            'permission_error': {
                'pattern': r'PermissionError|Access.*denied',
                'severity': 'medium',
                'auto_fix': True,
                'strategy': 'permission_fix'
            },
            'memory_error': {
                'pattern': r'MemoryError|out of memory',
                'severity': 'critical',
                'auto_fix': False,
                'strategy': 'emergency_cleanup'
            },
            'network_error': {
                'pattern': r'ConnectionError|timeout|network',
                'severity': 'medium',
                'auto_fix': True,
                'strategy': 'network_recovery'
            }
        }
        
        # 设置恢复策略
        self.recovery_strategies = {
            'syntax_repair': self.repair_syntax_errors,
            'dependency_install': self.install_missing_dependencies,
            'file_recovery': self.recover_missing_files,
            'database_repair': self.repair_database_issues,
            'permission_fix': self.fix_permission_issues,
            'emergency_cleanup': self.emergency_system_cleanup,
            'network_recovery': self.recover_network_issues
        }
    
    def global_exception_handler(self, exc_type, exc_value, exc_traceback):
        """全局异常处理器"""
        try:
            error_info = {
                'timestamp': datetime.now().isoformat(),
                'type': exc_type.__name__,
                'message': str(exc_value),
                'traceback': ''.join(traceback.format_tb(exc_traceback)),
                'severity': 'unknown'
            }
            
            print(f"\n🚨 检测到系统错误: {error_info['type']}")
            print(f"📝 错误信息: {error_info['message']}")
            
            # 分析错误类型和严重程度
            error_analysis = self.analyze_error(error_info)
            error_info.update(error_analysis)
            
            # 记录错误
            self.log_error(error_info)
            
            # 自动处理错误
            if error_analysis.get('auto_fix', False):
                print(f"🔧 尝试自动修复错误...")
                success = self.auto_fix_error(error_info)
                if success:
                    print(f"✅ 错误已自动修复")
                    return
                else:
                    print(f"❌ 自动修复失败")
            
            # 启动应急机制
            if error_analysis.get('severity') in ['critical', 'high']:
                print(f"🚨 启动应急机制...")
                self.activate_emergency_mode(error_info)
            
            # 如果是关键错误，尝试系统恢复
            if error_analysis.get('severity') == 'critical':
                print(f"🔄 尝试系统恢复...")
                self.attempt_system_recovery(error_info)
            
        except Exception as e:
            # 错误处理器本身出错时的最后防线
            print(f"💥 错误处理器异常: {str(e)}")
            self.emergency_logger.critical(f"错误处理器异常: {str(e)}")
    
    def analyze_error(self, error_info):
        """分析错误类型和严重程度"""
        error_text = f"{error_info['type']} {error_info['message']} {error_info['traceback']}"
        
        for error_type, config in self.error_patterns.items():
            if re.search(config['pattern'], error_text, re.IGNORECASE):
                return {
                    'error_type': error_type,
                    'severity': config['severity'],
                    'auto_fix': config['auto_fix'],
                    'strategy': config['strategy']
                }
        
        # 未知错误类型
        return {
            'error_type': 'unknown',
            'severity': 'medium',
            'auto_fix': False,
            'strategy': 'manual_intervention'
        }
    
    def log_error(self, error_info):
        """记录错误信息"""
        self.error_count += 1
        
        # 保存详细错误信息
        error_file = f"logs/emergency/error_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(error_file, 'w', encoding='utf-8') as f:
            json.dump(error_info, f, ensure_ascii=False, indent=2)
        
        # 记录到错误日志
        self.error_logger.error(
            f"错误#{self.error_count} - {error_info['type']}: {error_info['message']}"
        )
        
        # 更新错误统计
        self.update_error_statistics(error_info)
    
    def auto_fix_error(self, error_info):
        """自动修复错误"""
        try:
            strategy = error_info.get('strategy')
            if strategy and strategy in self.recovery_strategies:
                print(f"  🔧 执行修复策略: {strategy}")
                return self.recovery_strategies[strategy](error_info)
            return False
        except Exception as e:
            self.error_logger.error(f"自动修复失败: {str(e)}")
            return False
    
    def repair_syntax_errors(self, error_info):
        """修复语法错误"""
        try:
            print("  🔍 扫描语法错误...")
            
            # 运行语法修复工具
            if os.path.exists('批量修复语法错误.py'):
                result = subprocess.run([
                    sys.executable, '批量修复语法错误.py'
                ], capture_output=True, text=True, timeout=300)
                
                if result.returncode == 0:
                    print("  ✅ 语法错误修复成功")
                    return True
            
            # 手动语法检查和修复
            return self.manual_syntax_check()
            
        except Exception as e:
            self.error_logger.error(f"语法修复失败: {str(e)}")
            return False
    
    def manual_syntax_check(self):
        """手动语法检查"""
        try:
            critical_files = [
                'gui_main.py', 'email_sender.py', 'email_history_manager.py'
            ]
            
            for file_path in critical_files:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    try:
                        ast.parse(content)
                    except SyntaxError as e:
                        print(f"  🔧 修复文件: {file_path}")
                        if self.fix_common_syntax_issues(file_path, content):
                            print(f"    ✅ {file_path} 修复成功")
                        else:
                            print(f"    ❌ {file_path} 修复失败")
                            return False
            
            return True
            
        except Exception as e:
            self.error_logger.error(f"手动语法检查失败: {str(e)}")
            return False
    
    def fix_common_syntax_issues(self, file_path, content):
        """修复常见语法问题"""
        try:
            # 备份原文件
            backup_path = f"{file_path}.backup_{int(time.time())}"
            shutil.copy2(file_path, backup_path)
            
            # 修复常见问题
            fixed_content = content
            
            # 修复被注释的参数
            fixes = [
                (r'# pad_y=([^)]+)', r'pady=\1'),
                (r'# pad_x=([^)]+)', r'padx=\1'),
                (r'# text_variable=([^)]+)', r'textvariable=\1'),
                (r'# y_scroll_command=([^)]+)', r'yscrollcommand=\1'),
                (r'# scroll_region=([^)]+)', r'scrollregion=\1')
            ]
            
            for pattern, replacement in fixes:
                fixed_content = re.sub(pattern, replacement, fixed_content)
            
            # 写入修复后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            
            # 验证语法
            try:
                ast.parse(fixed_content)
                return True
            except SyntaxError:
                # 恢复备份
                shutil.copy2(backup_path, file_path)
                return False
                
        except Exception as e:
            self.error_logger.error(f"修复语法问题失败: {str(e)}")
            return False
    
    def install_missing_dependencies(self, error_info):
        """安装缺失的依赖包"""
        try:
            print("  📦 检查并安装缺失的依赖包...")
            
            required_packages = ['jieba', 'psutil', 'requests']
            installed_count = 0
            
            for package in required_packages:
                try:
                    __import__(package)
                except ImportError:
                    print(f"    📦 安装 {package}...")
                    result = subprocess.run([
                        sys.executable, '-m', 'pip', 'install', package
                    ], capture_output=True, text=True, timeout=300)
                    
                    if result.returncode == 0:
                        print(f"    ✅ {package} 安装成功")
                        installed_count += 1
                    else:
                        print(f"    ❌ {package} 安装失败")
            
            return installed_count > 0
            
        except Exception as e:
            self.error_logger.error(f"依赖安装失败: {str(e)}")
            return False
    
    def recover_missing_files(self, error_info):
        """恢复缺失的文件"""
        try:
            print("  📁 恢复缺失的文件...")
            
            # 检查关键文件
            critical_files = {
                'gui_main.py': '主界面文件',
                'email_sender.py': '邮件发送器',
                'email_history_manager.py': '历史管理器'
            }
            
            recovered_count = 0
            
            for file_path, description in critical_files.items():
                if not os.path.exists(file_path):
                    print(f"    🔍 尝试恢复 {description}: {file_path}")
                    
                    # 尝试从备份恢复
                    backup_pattern = f"{file_path}.backup_*"
                    import glob
                    backups = glob.glob(backup_pattern)
                    
                    if backups:
                        latest_backup = max(backups, key=os.path.getmtime)
                        shutil.copy2(latest_backup, file_path)
                        print(f"    ✅ 从备份恢复: {file_path}")
                        recovered_count += 1
                    else:
                        print(f"    ❌ 无法恢复: {file_path}")
            
            # 创建缺失的目录
            required_dirs = ['logs', 'user_data', 'backups', 'temp']
            for dir_name in required_dirs:
                if not os.path.exists(dir_name):
                    os.makedirs(dir_name, exist_ok=True)
                    print(f"    ✅ 创建目录: {dir_name}")
                    recovered_count += 1
            
            return recovered_count > 0
            
        except Exception as e:
            self.error_logger.error(f"文件恢复失败: {str(e)}")
            return False
    
    def repair_database_issues(self, error_info):
        """修复数据库问题"""
        try:
            print("  🗄️ 修复数据库问题...")
            
            databases = [
                'email_history.db', 'recipient_quality.db', 'anti_spam.db',
                'qq_anti_spam.db', 'system_integration.db'
            ]
            
            repaired_count = 0
            
            for db_path in databases:
                if os.path.exists(db_path):
                    try:
                        # 测试数据库连接
                        conn = sqlite3.connect(db_path, timeout=5)
                        conn.execute("SELECT 1")
                        conn.close()
                    except sqlite3.Error:
                        print(f"    🔧 修复数据库: {db_path}")
                        
                        # 备份损坏的数据库
                        backup_path = f"{db_path}.corrupted_{int(time.time())}"
                        shutil.move(db_path, backup_path)
                        
                        # 重新创建数据库
                        self.create_database(db_path)
                        repaired_count += 1
                        print(f"    ✅ 数据库重建成功: {db_path}")
                else:
                    # 创建缺失的数据库
                    print(f"    🔧 创建缺失的数据库: {db_path}")
                    self.create_database(db_path)
                    repaired_count += 1
                    print(f"    ✅ 数据库创建成功: {db_path}")
            
            return repaired_count > 0
            
        except Exception as e:
            self.error_logger.error(f"数据库修复失败: {str(e)}")
            return False
    
    def create_database(self, db_path):
        """创建数据库"""
        conn = sqlite3.connect(db_path)
        
        if 'email_history.db' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sent_emails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_email TEXT,
                    recipient_email TEXT,
                    subject TEXT,
                    body TEXT,
                    sent_time TEXT,
                    status TEXT
                )
            ''')
        elif 'recipient_quality.db' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS recipient_quality (
                    email TEXT PRIMARY KEY,
                    quality_score REAL,
                    send_count INTEGER,
                    reply_count INTEGER,
                    last_reply_time TEXT,
                    status TEXT
                )
            ''')
        # 添加其他数据库的创建逻辑...
        
        conn.commit()
        conn.close()
    
    def fix_permission_issues(self, error_info):
        """修复权限问题"""
        try:
            print("  🔐 修复权限问题...")
            
            # 测试文件写入权限
            test_file = 'temp/permission_test.txt'
            try:
                os.makedirs('temp', exist_ok=True)
                with open(test_file, 'w') as f:
                    f.write('test')
                os.remove(test_file)
                print("    ✅ 文件权限正常")
                return True
            except Exception as e:
                print(f"    ❌ 权限问题: {str(e)}")
                return False
                
        except Exception as e:
            self.error_logger.error(f"权限修复失败: {str(e)}")
            return False

    def start_continuous_monitoring(self):
        """启动持续监控"""
        if self.monitoring_active:
            return

        self.monitoring_active = True
        print("🔍 启动持续系统监控...")

        # 启动监控线程
        monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        monitor_thread.start()

        self.main_logger.info("持续监控已启动")

    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_active:
            try:
                # 执行定期检查
                self.perform_health_check()

                # 检查系统资源
                self.monitor_system_resources()

                # 检查文件完整性
                self.monitor_file_integrity()

                # 预防性维护
                self.preventive_maintenance()

                # 等待下次检查
                time.sleep(60)  # 每分钟检查一次

            except Exception as e:
                self.error_logger.error(f"监控循环异常: {str(e)}")
                time.sleep(60)

    def perform_health_check(self):
        """执行健康检查"""
        try:
            health_status = {
                'timestamp': datetime.now().isoformat(),
                'checks': {}
            }

            # 检查关键文件
            health_status['checks']['files'] = self.check_file_integrity()

            # 检查依赖包
            health_status['checks']['dependencies'] = self.check_dependencies()

            # 检查数据库
            health_status['checks']['databases'] = self.check_databases()

            # 检查磁盘空间
            health_status['checks']['disk_space'] = self.check_disk_space()

            # 检查内存使用
            health_status['checks']['memory'] = self.check_memory_usage()

            # 记录健康状态
            if not all(health_status['checks'].values()):
                self.main_logger.warning(f"健康检查发现问题: {health_status}")

                # 尝试自动修复
                self.auto_fix_health_issues(health_status)

        except Exception as e:
            self.error_logger.error(f"健康检查失败: {str(e)}")

    def monitor_system_resources(self):
        """监控系统资源"""
        try:
            import psutil

            # 检查CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 90:
                self.main_logger.warning(f"CPU使用率过高: {cpu_percent}%")

            # 检查内存使用
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                self.main_logger.warning(f"内存使用率过高: {memory.percent}%")

            # 检查磁盘空间
            disk = psutil.disk_usage('.')
            free_percent = (disk.free / disk.total) * 100
            if free_percent < 10:
                self.main_logger.warning(f"磁盘空间不足: {free_percent:.1f}%")

        except Exception as e:
            self.error_logger.error(f"资源监控失败: {str(e)}")

    def monitor_file_integrity(self):
        """监控文件完整性"""
        try:
            critical_files = [
                'gui_main.py', 'email_sender.py', 'email_history_manager.py'
            ]

            for file_path in critical_files:
                if not os.path.exists(file_path):
                    self.main_logger.error(f"关键文件缺失: {file_path}")

                    # 尝试从备份恢复
                    self.recover_missing_files({'message': f'文件缺失: {file_path}'})

        except Exception as e:
            self.error_logger.error(f"文件完整性监控失败: {str(e)}")

    def preventive_maintenance(self):
        """预防性维护"""
        try:
            # 清理过期日志
            self.cleanup_old_logs()

            # 清理临时文件
            self.cleanup_temp_files()

            # 备份关键数据
            self.backup_critical_data()

            # 检查语法错误
            self.check_syntax_errors()

        except Exception as e:
            self.error_logger.error(f"预防性维护失败: {str(e)}")

    def cleanup_old_logs(self):
        """清理过期日志"""
        try:
            logs_dir = 'logs'
            if not os.path.exists(logs_dir):
                return

            cutoff_time = time.time() - (7 * 24 * 3600)  # 7天前
            cleaned_count = 0

            for file_name in os.listdir(logs_dir):
                if file_name.endswith('.log.old'):
                    file_path = os.path.join(logs_dir, file_name)
                    try:
                        if os.path.getmtime(file_path) < cutoff_time:
                            os.remove(file_path)
                            cleaned_count += 1
                    except:
                        pass

            if cleaned_count > 0:
                self.main_logger.info(f"清理了 {cleaned_count} 个过期日志文件")

        except Exception as e:
            self.error_logger.error(f"清理日志失败: {str(e)}")

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_dir = 'temp'
            if not os.path.exists(temp_dir):
                return

            cutoff_time = time.time() - (24 * 3600)  # 24小时前
            cleaned_count = 0

            for file_name in os.listdir(temp_dir):
                if file_name.endswith('.tmp') or file_name.endswith('.temp'):
                    file_path = os.path.join(temp_dir, file_name)
                    try:
                        if os.path.getmtime(file_path) < cutoff_time:
                            os.remove(file_path)
                            cleaned_count += 1
                    except:
                        pass

            if cleaned_count > 0:
                self.main_logger.info(f"清理了 {cleaned_count} 个临时文件")

        except Exception as e:
            self.error_logger.error(f"清理临时文件失败: {str(e)}")

    def backup_critical_data(self):
        """备份关键数据"""
        try:
            # 每小时备份一次
            current_hour = datetime.now().hour
            backup_flag_file = f'temp/backup_hour_{current_hour}.flag'

            if os.path.exists(backup_flag_file):
                return  # 本小时已备份

            # 创建备份
            backup_dir = f"backups/auto_backup_{datetime.now().strftime('%Y%m%d_%H')}"
            os.makedirs(backup_dir, exist_ok=True)

            # 备份用户数据
            if os.path.exists('user_data'):
                shutil.copytree('user_data', os.path.join(backup_dir, 'user_data'), dirs_exist_ok=True)

            # 备份数据库
            databases = ['email_history.db', 'recipient_quality.db']
            for db_path in databases:
                if os.path.exists(db_path):
                    shutil.copy2(db_path, backup_dir)

            # 创建备份标志
            with open(backup_flag_file, 'w') as f:
                f.write(datetime.now().isoformat())

            self.main_logger.info(f"自动备份完成: {backup_dir}")

        except Exception as e:
            self.error_logger.error(f"自动备份失败: {str(e)}")

    def check_syntax_errors(self):
        """检查语法错误"""
        try:
            critical_files = ['gui_main.py', 'email_sender.py']

            for file_path in critical_files:
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    try:
                        ast.parse(content)
                    except SyntaxError as e:
                        self.main_logger.error(f"发现语法错误: {file_path} - {str(e)}")

                        # 自动修复语法错误
                        self.repair_syntax_errors({'message': f'语法错误: {file_path}'})

        except Exception as e:
            self.error_logger.error(f"语法检查失败: {str(e)}")

    def save_emergency_event(self, event):
        """保存应急事件"""
        try:
            os.makedirs('logs/emergency', exist_ok=True)
            event_file = f"logs/emergency/emergency_event_{int(time.time())}.json"

            with open(event_file, 'w', encoding='utf-8') as f:
                json.dump(event, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.error_logger.error(f"保存应急事件失败: {str(e)}")

    def update_error_statistics(self, error_info):
        """更新错误统计"""
        try:
            stats_file = 'logs/error_statistics.json'

            if os.path.exists(stats_file):
                with open(stats_file, 'r', encoding='utf-8') as f:
                    stats = json.load(f)
            else:
                stats = {'total_errors': 0, 'error_types': {}, 'daily_stats': {}}

            # 更新统计
            stats['total_errors'] += 1
            error_type = error_info.get('error_type', 'unknown')
            stats['error_types'][error_type] = stats['error_types'].get(error_type, 0) + 1

            # 更新日统计
            today = datetime.now().strftime('%Y-%m-%d')
            if today not in stats['daily_stats']:
                stats['daily_stats'][today] = 0
            stats['daily_stats'][today] += 1

            # 保存统计
            with open(stats_file, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.error_logger.error(f"更新错误统计失败: {str(e)}")

    def check_disk_space(self):
        """检查磁盘空间"""
        try:
            import psutil
            disk_usage = psutil.disk_usage('.')
            free_space_mb = disk_usage.free / (1024 * 1024)
            return free_space_mb > 100  # 至少100MB
        except:
            return True

    def check_memory_usage(self):
        """检查内存使用"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return memory.percent < 90  # 内存使用率小于90%
        except:
            return True

    def auto_fix_health_issues(self, health_status):
        """自动修复健康问题"""
        try:
            for check_name, status in health_status['checks'].items():
                if not status:
                    print(f"🔧 修复健康问题: {check_name}")

                    if check_name == 'files':
                        self.recover_missing_files({'message': '文件完整性检查失败'})
                    elif check_name == 'dependencies':
                        self.install_missing_dependencies({'message': '依赖检查失败'})
                    elif check_name == 'databases':
                        self.repair_database_issues({'message': '数据库检查失败'})
                    elif check_name == 'disk_space':
                        self.emergency_system_cleanup({'message': '磁盘空间不足'})

        except Exception as e:
            self.error_logger.error(f"自动修复健康问题失败: {str(e)}")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='智能错误处理与应急恢复系统')
    parser.add_argument('--start-monitoring', action='store_true', help='启动持续监控')
    parser.add_argument('--health-check', action='store_true', help='执行健康检查')
    parser.add_argument('--emergency-test', action='store_true', help='测试应急机制')
    parser.add_argument('--install', action='store_true', help='安装错误处理器')

    args = parser.parse_args()

    handler = IntelligentErrorHandler()

    if args.install:
        print("🛡️ 安装智能错误处理器...")
        handler.start_continuous_monitoring()
        print("✅ 错误处理器已安装并启动监控")

        # 保持运行
        try:
            while True:
                time.sleep(60)
        except KeyboardInterrupt:
            print("🛑 错误处理器已停止")
            handler.monitoring_active = False

    elif args.start_monitoring:
        handler.start_continuous_monitoring()
        print("🔍 持续监控已启动")

    elif args.health_check:
        print("🏥 执行系统健康检查...")
        handler.perform_health_check()
        print("✅ 健康检查完成")

    elif args.emergency_test:
        print("🚨 测试应急机制...")
        test_error = {
            'type': 'TestError',
            'message': '应急机制测试',
            'severity': 'high'
        }
        handler.activate_emergency_mode(test_error)
        print("✅ 应急机制测试完成")

    else:
        print("智能错误处理与应急恢复系统")
        print("使用 --help 查看可用选项")

if __name__ == "__main__":
    main()
