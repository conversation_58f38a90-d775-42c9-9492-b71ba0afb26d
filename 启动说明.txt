自动化邮件发送助手 - 启动说明
================================

🎯 推荐启动方式（已解决所有编码问题）
----------------------------------

1. 【最推荐】双击 "快速启动.vbs"
   - 直接启动图形界面版本
   - 完美支持中文
   - 自动检查Python环境
   - 一键启动，最简单

2. 【功能完整】双击 "启动.vbs"
   - 显示完整功能菜单
   - 支持所有功能选择
   - 完美支持中文
   - 用户友好的对话框界面

3. 【英文版本】双击 "start.vbs"
   - 英文界面版本
   - 功能与中文版相同
   - 适合英文环境使用

🔧 其他启动方式
---------------

4. 双击 "启动.bat" 或 "start.bat"
   - 会自动调用对应的VBS脚本
   - 简化的批处理启动

5. 运行 "python 启动器.py"
   - Python版本的启动菜单
   - 命令行界面
   - 需要在终端中运行

6. 直接运行Python脚本
   - python gui_main.py（图形界面）
   - python main.py（命令行单邮件）
   - python main.py --batch（批量邮件）

📋 文件说明
-----------

启动相关文件：
- 快速启动.vbs     ⭐ 一键启动图形界面（最推荐）
- 启动.vbs         ⭐ 完整功能菜单（中文）
- start.vbs        ⭐ 完整功能菜单（英文）
- 启动.bat         → 调用启动.vbs
- start.bat        → 调用start.vbs
- 启动器.py        → Python版本启动菜单

核心程序文件：
- gui_main.py      → 图形界面版本
- main.py          → 命令行版本
- email_sender.py  → 邮件发送核心
- config.py        → 配置文件
- test_email.py    → 功能测试
- setup.py         → 环境检查

文档文件：
- README.md        → 详细使用说明
- 使用指南.txt     → 简明使用指南
- 项目说明.md      → 项目概述
- 启动说明.txt     → 本文件

⚠️ 重要提示
-----------

1. 首次使用建议：
   - 双击 "快速启动.vbs" 直接体验
   - 或双击 "启动.vbs" 查看完整功能

2. 如果VBS文件无法运行：
   - 检查Windows是否禁用了VBS脚本
   - 可以右键选择"打开方式" → "Microsoft Windows Based Script Host"

3. 环境要求：
   - Python 3.6+ 已安装
   - 网络连接正常
   - QQ邮箱SMTP授权码已配置

4. 您的配置信息：
   - SMTP服务器: smtp.qq.com
   - 端口: 587 (TLS)
   - 授权码: vwpboqxircdudgfa

🆘 故障排除
-----------

问题1: VBS脚本提示Python环境错误
解决: 确保Python已正确安装并添加到系统PATH

问题2: 图形界面无法启动
解决: 检查是否安装了tkinter库（Python标准库）

问题3: 邮件发送失败
解决: 检查网络连接，确认QQ邮箱地址正确

问题4: 中文显示乱码
解决: 使用VBS启动器，已完美解决编码问题

================================
现在您可以愉快地使用邮件发送助手了！
推荐直接双击 "快速启动.vbs" 开始使用。
