#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件发送问题诊断工具
"""

import smtplib
import imaplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext

class EmailDiagnosticTool:
    def __init__(self, root):
        self.root = root
        self.root.title("📧 邮件发送诊断工具")
        self.root.geometry("800x700")
        
        self.create_widgets()
    
    def create_widgets(self):
        """创建界面组件"""
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="📧 邮件发送诊断工具", 
                               font=('Microsoft YaHei UI', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # 配置框架
        config_frame = ttk.LabelFrame(main_frame, text="📋 邮箱配置", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 邮箱配置
        ttk.Label(config_frame, text="发件人邮箱:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.sender_email_var = tk.StringVar(value="<EMAIL>")
        ttk.Entry(config_frame, textvariable=self.sender_email_var, width=30).grid(row=0, column=1, padx=(0, 20))
        
        ttk.Label(config_frame, text="授权码/密码:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.password_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=self.password_var, width=20, show="*").grid(row=0, column=3)
        
        ttk.Label(config_frame, text="收件人邮箱:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.recipient_var = tk.StringVar(value="<EMAIL>")
        ttk.Entry(config_frame, textvariable=self.recipient_var, width=30).grid(row=1, column=1, pady=(10, 0))
        
        # 诊断按钮
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=2, column=0, columnspan=4, pady=(15, 0))
        
        ttk.Button(button_frame, text="🔍 SMTP诊断", command=self.diagnose_smtp).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📬 IMAP诊断", command=self.diagnose_imap).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="📧 测试发送", command=self.test_send).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="🧹 清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=5)
        
        # 结果显示
        result_frame = ttk.LabelFrame(main_frame, text="📊 诊断结果", padding="10")
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, height=25, font=('Consolas', 10))
        self.result_text.pack(fill=tk.BOTH, expand=True)
        
        # 添加初始说明
        self.log("🔧 邮件发送诊断工具已启动")
        self.log("=" * 60)
        self.log("📋 使用说明:")
        self.log("1. 填写您的QQ邮箱和授权码")
        self.log("2. 点击'SMTP诊断'检查发送服务器连接")
        self.log("3. 点击'IMAP诊断'检查接收服务器连接")
        self.log("4. 点击'测试发送'发送测试邮件")
        self.log("=" * 60)
    
    def log(self, message):
        """添加日志"""
        self.result_text.insert(tk.END, f"{message}\n")
        self.result_text.see(tk.END)
        self.root.update()
    
    def clear_log(self):
        """清空日志"""
        self.result_text.delete(1.0, tk.END)
    
    def diagnose_smtp(self):
        """诊断SMTP连接"""
        self.log("\n🔍 开始SMTP诊断...")
        self.log("-" * 40)
        
        sender_email = self.sender_email_var.get().strip()
        password = self.password_var.get().strip()
        
        if not sender_email or not password:
            self.log("❌ 请填写邮箱和授权码")
            return
        
        try:
            # 检测邮箱类型并选择服务器
            if 'qq.com' in sender_email or 'foxmail.com' in sender_email:
                smtp_server = 'smtp.qq.com'
                smtp_port = 587
                self.log(f"📧 检测到QQ邮箱，使用服务器: {smtp_server}:{smtp_port}")
            elif '163.com' in sender_email:
                smtp_server = 'smtp.163.com'
                smtp_port = 587
                self.log(f"📧 检测到163邮箱，使用服务器: {smtp_server}:{smtp_port}")
            elif 'gmail.com' in sender_email:
                smtp_server = 'smtp.gmail.com'
                smtp_port = 587
                self.log(f"📧 检测到Gmail，使用服务器: {smtp_server}:{smtp_port}")
            else:
                smtp_server = 'smtp.qq.com'
                smtp_port = 587
                self.log(f"⚠️ 未识别邮箱类型，默认使用: {smtp_server}:{smtp_port}")
            
            # 测试连接
            self.log(f"🔗 正在连接SMTP服务器...")
            
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.set_debuglevel(0)  # 关闭调试输出
            
            self.log("✅ SMTP服务器连接成功")
            
            # 启动TLS
            self.log("🔐 启动TLS加密...")
            server.starttls()
            self.log("✅ TLS加密启动成功")
            
            # 登录测试
            self.log("🔑 正在验证登录...")
            server.login(sender_email, password)
            self.log("✅ SMTP登录验证成功")
            
            server.quit()
            self.log("🎉 SMTP诊断完成 - 所有检查通过！")
            
        except smtplib.SMTPAuthenticationError as e:
            self.log(f"❌ SMTP认证失败: {str(e)}")
            self.log("💡 可能的原因:")
            self.log("   1. 授权码错误")
            self.log("   2. 未开启SMTP服务")
            self.log("   3. 邮箱被锁定")
        except smtplib.SMTPConnectError as e:
            self.log(f"❌ SMTP连接失败: {str(e)}")
            self.log("💡 可能的原因:")
            self.log("   1. 网络连接问题")
            self.log("   2. 服务器地址错误")
            self.log("   3. 端口被阻塞")
        except Exception as e:
            self.log(f"❌ SMTP诊断失败: {str(e)}")
    
    def diagnose_imap(self):
        """诊断IMAP连接"""
        self.log("\n📬 开始IMAP诊断...")
        self.log("-" * 40)
        
        sender_email = self.sender_email_var.get().strip()
        password = self.password_var.get().strip()
        
        if not sender_email or not password:
            self.log("❌ 请填写邮箱和授权码")
            return
        
        try:
            # 检测邮箱类型并选择服务器
            if 'qq.com' in sender_email or 'foxmail.com' in sender_email:
                imap_server = 'imap.qq.com'
                imap_port = 993
                self.log(f"📧 检测到QQ邮箱，使用服务器: {imap_server}:{imap_port}")
            elif '163.com' in sender_email:
                imap_server = 'imap.163.com'
                imap_port = 993
                self.log(f"📧 检测到163邮箱，使用服务器: {imap_server}:{imap_port}")
            elif 'gmail.com' in sender_email:
                imap_server = 'imap.gmail.com'
                imap_port = 993
                self.log(f"📧 检测到Gmail，使用服务器: {imap_server}:{imap_port}")
            else:
                imap_server = 'imap.qq.com'
                imap_port = 993
                self.log(f"⚠️ 未识别邮箱类型，默认使用: {imap_server}:{imap_port}")
            
            # 测试连接
            self.log(f"🔗 正在连接IMAP服务器...")
            
            mail = imaplib.IMAP4_SSL(imap_server, imap_port)
            self.log("✅ IMAP服务器连接成功")
            
            # 登录测试
            self.log("🔑 正在验证登录...")
            mail.login(sender_email, password)
            self.log("✅ IMAP登录验证成功")
            
            # 测试邮箱访问
            self.log("📂 正在测试邮箱访问...")
            mail.select('INBOX')
            self.log("✅ 邮箱访问成功")
            
            mail.logout()
            self.log("🎉 IMAP诊断完成 - 所有检查通过！")
            
        except imaplib.IMAP4.error as e:
            self.log(f"❌ IMAP认证失败: {str(e)}")
            self.log("💡 可能的原因:")
            self.log("   1. 授权码错误")
            self.log("   2. 未开启IMAP服务")
            self.log("   3. 邮箱被锁定")
        except Exception as e:
            self.log(f"❌ IMAP诊断失败: {str(e)}")
    
    def test_send(self):
        """测试发送邮件"""
        self.log("\n📧 开始测试发送...")
        self.log("-" * 40)
        
        sender_email = self.sender_email_var.get().strip()
        password = self.password_var.get().strip()
        recipient = self.recipient_var.get().strip()
        
        if not sender_email or not password or not recipient:
            self.log("❌ 请填写完整的邮箱信息")
            return
        
        try:
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = recipient
            msg['Subject'] = "📧 邮件发送测试"
            
            body = f"""这是一封测试邮件。

发送时间: {tk.datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
发件人: {sender_email}
收件人: {recipient}

如果您收到这封邮件，说明邮件发送功能正常。

此邮件由邮件发送诊断工具自动发送。"""
            
            msg.attach(MIMEText(body, 'plain', 'utf-8'))
            
            # 发送邮件
            if 'qq.com' in sender_email or 'foxmail.com' in sender_email:
                smtp_server = 'smtp.qq.com'
                smtp_port = 587
            elif '163.com' in sender_email:
                smtp_server = 'smtp.163.com'
                smtp_port = 587
            elif 'gmail.com' in sender_email:
                smtp_server = 'smtp.gmail.com'
                smtp_port = 587
            else:
                smtp_server = 'smtp.qq.com'
                smtp_port = 587
            
            self.log(f"📤 正在发送测试邮件...")
            self.log(f"   发件人: {sender_email}")
            self.log(f"   收件人: {recipient}")
            self.log(f"   服务器: {smtp_server}:{smtp_port}")
            
            server = smtplib.SMTP(smtp_server, smtp_port)
            server.starttls()
            server.login(sender_email, password)
            
            text = msg.as_string()
            server.sendmail(sender_email, recipient, text)
            server.quit()
            
            self.log("✅ 测试邮件发送成功！")
            self.log("💡 请检查收件箱确认邮件是否收到")
            
        except Exception as e:
            self.log(f"❌ 测试发送失败: {str(e)}")

def main():
    """主函数"""
    root = tk.Tk()
    app = EmailDiagnosticTool(root)
    root.mainloop()

if __name__ == "__main__":
    main()
