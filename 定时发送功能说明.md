# ⏰ 定时发送功能完整说明

## 🎯 功能概述

定时发送功能是邮件系统的重要增强功能，它允许用户在最佳时间点自动发送邮件，并集成了所有现有的系统功能，包括反垃圾策略、自动回复监控、质量数据库管理、应急系统等。

## ✨ 核心特性

### 🕐 智能定时调度
- **精确时间控制**：支持精确到分钟的定时发送
- **最佳时间推荐**：基于历史数据分析推荐最佳发送时间
- **自动重试机制**：发送失败时自动重试，可配置重试次数和间隔
- **长期数据保存**：所有任务和执行历史持久化存储，重启后数据不丢失

### 🛡️ 完整功能集成
- **反垃圾策略**：自动应用保守/适中/激进三种反垃圾策略
- **自动回复监控**：发送后自动启动回复监控，跟踪邮件效果
- **质量数据库**：自动更新收件人质量评分和历史记录
- **应急系统**：自动检查并激活QQ邮箱应急模式
- **深度协调**：应用系统深度协调功能，优化发送效果

### 📊 数据分析与优化
- **最佳时间分析**：分析不同时间段的发送效果
- **成功率统计**：跟踪任务执行成功率和失败原因
- **效果评估**：基于回复率和成功率评估发送效果
- **智能推荐**：根据历史数据推荐最佳发送策略

## 🚀 使用方法

### 1. 打开定时发送管理器
在主界面点击 **"⏰ 定时发送"** 按钮，打开定时发送管理器。

### 2. 创建定时任务
在 **"📝 创建定时任务"** 标签页中：

#### 基本信息
- **任务名称**：为任务起一个有意义的名称
- **发送者邮箱**：选择发送邮箱（自动填充当前设置）
- **收件人**：输入收件人邮箱地址（支持多个，每行一个）
- **邮件主题**：输入邮件主题
- **邮件内容**：输入邮件正文内容

#### 定时设置
- **指定具体时间**：手动设置发送日期和时间
- **使用最佳时间**：系统根据历史数据推荐最佳时间
- **快速设置**：点击 **"🕐 最佳时间"** 按钮自动设置推荐时间

#### 发送策略
- **发送模式**：选择快速/标准/安全模式
- **反垃圾策略**：选择保守/适中/激进策略

#### 功能开关
- **📡 自动回复监控**：发送后自动监控回复
- **📊 质量数据库**：自动更新收件人质量数据
- **🆘 应急系统**：自动检查应急状态
- **🔗 深度协调**：应用系统深度协调

### 3. 管理定时任务
在 **"📋 任务列表"** 标签页中：

- **查看任务**：查看所有定时任务的状态和详情
- **取消任务**：取消尚未执行的任务
- **删除任务**：删除不需要的任务
- **查看详情**：点击任务查看详细信息和执行结果

### 4. 查看统计信息
在 **"📊 统计信息"** 标签页中：

- **任务统计**：查看总任务数、成功率等统计信息
- **执行效率**：分析任务执行效率和成功率
- **系统状态**：查看调度器运行状态和配置信息

### 5. 最佳时间分析
在 **"🕐 最佳时间"** 标签页中：

- **时间效果分析**：查看不同时间段的发送效果
- **推荐时间**：获取基于历史数据的最佳发送时间推荐
- **详细数据**：查看每个时间段的详细发送数据

## 🔧 高级功能

### 自动重试机制
- 任务执行失败时自动重试
- 可配置最大重试次数（默认3次）
- 重试间隔可调整（默认5分钟）

### 数据持久化
- 所有任务数据存储在SQLite数据库中
- 系统重启后数据完全保留
- 支持数据导出和备份

### 最佳时间学习
- 系统自动记录每次发送的时间和效果
- 分析不同时间段的成功率和回复率
- 基于历史数据推荐最佳发送时间

### 批量管理
- 支持批量创建定时任务
- 批量取消或删除任务
- 批量导出任务数据

## 📋 任务状态说明

- **⏳ 待执行**：任务已创建，等待执行时间到达
- **🔄 执行中**：任务正在执行中
- **✅ 已完成**：任务执行成功
- **❌ 失败**：任务执行失败
- **⏸️ 已取消**：任务被用户取消

## 🛠️ 配置选项

### 调度器配置
- **检查间隔**：调度器检查任务的时间间隔（默认60秒）
- **最大并发**：同时执行的最大任务数（默认3个）
- **自动重试**：是否启用自动重试（默认启用）

### 数据管理
- **清理天数**：自动清理多少天前的旧任务（默认30天）
- **备份间隔**：数据备份的时间间隔（默认1小时）

## 💡 使用建议

### 最佳实践
1. **选择合适的发送时间**：
   - 工作日上午9-11点
   - 工作日下午2-4点
   - 避免周末和节假日
   - 避免深夜和凌晨

2. **合理设置功能开关**：
   - 重要邮件建议开启所有功能
   - 测试邮件可关闭部分功能
   - 大批量发送建议开启反垃圾策略

3. **定期查看统计信息**：
   - 关注任务成功率
   - 分析最佳发送时间
   - 优化发送策略

### 注意事项
1. **时间设置**：确保定时时间在未来
2. **邮箱配置**：确保发送邮箱已正确配置授权码
3. **收件人格式**：确保收件人邮箱格式正确
4. **系统资源**：大量定时任务可能占用系统资源

## 🔍 故障排除

### 常见问题
1. **任务不执行**：
   - 检查调度器是否运行
   - 确认任务时间设置正确
   - 查看系统日志

2. **发送失败**：
   - 检查邮箱授权码
   - 确认网络连接
   - 查看错误日志

3. **数据丢失**：
   - 检查数据库文件是否存在
   - 确认文件权限
   - 查看备份文件

### 日志文件
- **schedule_manager.log**：定时发送管理器日志
- **email_sender.log**：邮件发送日志
- **系统主日志**：在主界面日志区域查看

## 🎉 总结

定时发送功能提供了完整的邮件定时发送解决方案，集成了所有现有系统功能，具备智能分析和优化能力。通过合理使用这个功能，可以大大提高邮件发送的效率和效果，实现真正的自动化邮件营销。

---

**📞 技术支持**：如有问题请查看系统日志或联系技术支持。
