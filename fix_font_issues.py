#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 修复字体问题脚本
批量替换代码中的 Consolas 字体为 Courier New
"""

import re
import os

def fix_font_issues(file_path):
    """修复文件中的字体问题"""
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 记录原始内容
        original_content = content
        
        # 替换 Consolas 字体为 Courier New
        content = re.sub(r"font=\('Consolas',", "font=('Courier New',", content)
        
        # 检查是否有修改
        if content != original_content:
            # 备份原文件
            backup_path = file_path + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(original_content)
            print(f"✅ 已备份原文件: {backup_path}")
            
            # 写入修改后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 统计替换次数
            consolas_count = original_content.count("font=('Consolas',")
            courier_count = content.count("font=('Courier New',")
            
            print(f"✅ 已修复 {file_path}")
            print(f"   替换了 {consolas_count} 处 Consolas 字体")
            print(f"   现在有 {courier_count} 处 Courier New 字体")
            
            return True
        else:
            print(f"ℹ️ {file_path} 无需修复")
            return False
            
    except Exception as e:
        print(f"❌ 修复 {file_path} 失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🔧 字体问题修复工具")
    print("=" * 40)
    
    # 需要检查的文件列表
    files_to_check = [
        'gui_main.py',
        'schedule_manager.py',
        'test_schedule_manager.py',
        'test_gui_schedule.py'
    ]
    
    fixed_count = 0
    total_count = 0
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"\n🔍 检查文件: {file_path}")
            total_count += 1
            
            if fix_font_issues(file_path):
                fixed_count += 1
        else:
            print(f"⚠️ 文件不存在: {file_path}")
    
    print(f"\n📊 修复完成")
    print(f"检查文件: {total_count} 个")
    print(f"修复文件: {fixed_count} 个")
    
    if fixed_count > 0:
        print("\n💡 修复说明:")
        print("- 已将所有 'Consolas' 字体替换为 'Courier New'")
        print("- 原文件已备份为 .backup 文件")
        print("- Courier New 是系统内置字体，兼容性更好")
        print("- 如需恢复，可以使用备份文件")
    
    print("\n🎉 字体问题修复完成！")

if __name__ == "__main__":
    main()
