#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度系统协调实现 - 让所有功能深度融合，相互配合
"""

import json
import sqlite3
import datetime
import threading
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class SystemEvent(Enum):
    """系统事件枚举"""
    EMAIL_SENT = "email_sent"
    REPLY_RECEIVED = "reply_received"
    NO_REPLY_DETECTED = "no_reply_detected"
    BOUNCE_DETECTED = "bounce_detected"
    RISK_LEVEL_CHANGED = "risk_level_changed"
    EMERGENCY_ACTIVATED = "emergency_activated"
    EMERGENCY_DEACTIVATED = "emergency_deactivated"

@dataclass
class SystemState:
    """系统状态数据类"""
    sender_email: str
    total_sent: int = 0
    total_replies: int = 0
    total_bounces: int = 0
    consecutive_no_reply: int = 0
    risk_level: RiskLevel = RiskLevel.LOW
    emergency_active: bool = False
    last_send_time: Optional[datetime.datetime] = None
    last_reply_time: Optional[datetime.datetime] = None

class SystemDataCenter:
    """统一数据中心 - 管理所有系统数据"""
    
    def __init__(self):
        self.lock = threading.RLock()
        self.states: Dict[str, SystemState] = {}
        self.event_listeners: Dict[SystemEvent, List] = {event: [] for event in SystemEvent}
        
    def get_state(self, sender_email: str) -> SystemState:
        """获取发件人状态"""
        with self.lock:
            if sender_email not in self.states:
                self.states[sender_email] = SystemState(sender_email)
            return self.states[sender_email]
    
    def update_state(self, sender_email: str, **kwargs):
        """更新发件人状态"""
        with self.lock:
            state = self.get_state(sender_email)
            for key, value in kwargs.items():
                if hasattr(state, key):
                    setattr(state, key, value)
    
    def register_event_listener(self, event: SystemEvent, callback):
        """注册事件监听器"""
        self.event_listeners[event].append(callback)
    
    def emit_event(self, event, data: Dict[str, Any]):
        """触发事件"""
        # 支持字符串和枚举两种方式
        if isinstance(event, str):
            # 字符串转换为枚举
            try:
                event = SystemEvent(event)
            except ValueError:
                # 如果字符串不匹配，尝试按名称查找
                for e in SystemEvent:
                    if e.name == event or e.value == event:
                        event = e
                        break
                else:
                    print(f"❌ 未知事件类型: {event}")
                    return

        for callback in self.event_listeners[event]:
            try:
                callback(data)
            except Exception as e:
                print(f"❌ 事件处理失败: {event.value} - {str(e)}")

class IntelligentDecisionEngine:
    """智能决策引擎 - 基于多维度数据做出智能决策"""
    
    def __init__(self, data_center: SystemDataCenter):
        self.data_center = data_center
        
    def calculate_risk_level(self, sender_email: str) -> RiskLevel:
        """计算风险等级"""
        try:
            state = self.data_center.get_state(sender_email)
            
            # 获取详细数据
            recipient_data = self._get_recipient_quality_data(sender_email)
            sending_data = self._get_sending_history_data(sender_email)
            monitor_data = self._get_monitor_data(sender_email)
            
            # 计算各维度风险分数 (0-100)
            reply_risk = self._calculate_reply_risk(state, monitor_data)
            quality_risk = self._calculate_quality_risk(recipient_data)
            frequency_risk = self._calculate_frequency_risk(sending_data)
            pattern_risk = self._calculate_pattern_risk(sending_data)
            
            # 综合风险分数
            total_risk = (reply_risk * 0.4 + quality_risk * 0.3 + 
                         frequency_risk * 0.2 + pattern_risk * 0.1)
            
            # 确定风险等级
            if total_risk >= 80:
                return RiskLevel.CRITICAL
            elif total_risk >= 60:
                return RiskLevel.HIGH
            elif total_risk >= 40:
                return RiskLevel.MEDIUM
            else:
                return RiskLevel.LOW
                
        except Exception as e:
            print(f"❌ 计算风险等级失败: {str(e)}")
            return RiskLevel.MEDIUM
    
    def _calculate_reply_risk(self, state: SystemState, monitor_data: Dict) -> float:
        """计算回复风险分数"""
        if state.total_sent == 0:
            return 0
        
        reply_rate = state.total_replies / state.total_sent
        bounce_rate = state.total_bounces / state.total_sent
        
        # 回复率风险
        reply_risk = max(0, (0.3 - reply_rate) * 100 / 0.3)  # 30%以下回复率有风险
        
        # 退信率风险
        bounce_risk = bounce_rate * 500  # 退信率每1%增加5分风险
        
        # 连续无回复风险
        consecutive_risk = min(state.consecutive_no_reply * 10, 50)  # 每个无回复增加10分
        
        return min(reply_risk + bounce_risk + consecutive_risk, 100)
    
    def _calculate_quality_risk(self, recipient_data: Dict) -> float:
        """计算收件人质量风险分数"""
        if not recipient_data:
            return 50  # 无数据时中等风险
        
        total_recipients = recipient_data.get('total', 0)
        if total_recipients == 0:
            return 50
        
        invalid_count = recipient_data.get('invalid', 0)
        low_quality_count = recipient_data.get('low_quality', 0)
        
        invalid_rate = invalid_count / total_recipients
        low_quality_rate = low_quality_count / total_recipients
        
        return min((invalid_rate * 100 + low_quality_rate * 50), 100)
    
    def _calculate_frequency_risk(self, sending_data: Dict) -> float:
        """计算发送频率风险分数"""
        recent_sends = sending_data.get('recent_24h', 0)
        
        # 24小时内发送超过100封有风险
        if recent_sends > 100:
            return min((recent_sends - 100) * 2, 100)
        
        return 0
    
    def _calculate_pattern_risk(self, sending_data: Dict) -> float:
        """计算发送模式风险分数"""
        # 检查发送模式是否异常
        pattern_score = 0
        
        # 发送时间过于集中
        if sending_data.get('time_concentration', 0) > 0.8:
            pattern_score += 30
        
        # 内容重复度过高
        if sending_data.get('content_similarity', 0) > 0.9:
            pattern_score += 40
        
        # 收件人重复度过高
        if sending_data.get('recipient_overlap', 0) > 0.7:
            pattern_score += 30
        
        return min(pattern_score, 100)
    
    def _get_recipient_quality_data(self, sender_email: str) -> Dict:
        """获取收件人质量数据"""
        try:
            conn = sqlite3.connect('recipient_quality.db', timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'invalid' THEN 1 ELSE 0 END) as invalid,
                    SUM(CASE WHEN reply_count = 0 AND bounce_count = 0 THEN 1 ELSE 0 END) as low_quality
                FROM recipient_status 
                WHERE sender_email = ?
            """, (sender_email,))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return {
                    'total': result[0],
                    'invalid': result[1],
                    'low_quality': result[2]
                }
            
            return {}
            
        except Exception as e:
            print(f"❌ 获取收件人质量数据失败: {str(e)}")
            return {}
    
    def _get_sending_history_data(self, sender_email: str) -> Dict:
        """获取发送历史数据"""
        try:
            conn = sqlite3.connect('email_history.db', timeout=30.0)
            cursor = conn.cursor()

            # 确保表存在
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS email_records (
                    id TEXT PRIMARY KEY,
                    sender_email TEXT NOT NULL,
                    recipient_email TEXT NOT NULL,
                    subject TEXT NOT NULL,
                    body TEXT NOT NULL,
                    content_hash TEXT NOT NULL,
                    send_time TEXT NOT NULL,
                    success INTEGER NOT NULL,
                    batch_id TEXT,
                    attachments TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 获取24小时内发送数量
            cursor.execute("""
                SELECT COUNT(*)
                FROM email_records
                WHERE sender_email = ?
                AND datetime(send_time) > datetime('now', '-24 hours')
            """, (sender_email,))

            result = cursor.fetchone()
            recent_24h = result[0] if result else 0
            
            conn.close()
            
            return {
                'recent_24h': recent_24h,
                'time_concentration': 0.5,  # 简化实现
                'content_similarity': 0.3,  # 简化实现
                'recipient_overlap': 0.2    # 简化实现
            }
            
        except Exception as e:
            print(f"❌ 获取发送历史数据失败: {str(e)}")
            return {'recent_24h': 0}
    
    def _get_monitor_data(self, sender_email: str) -> Dict:
        """获取监控数据"""
        # 简化实现，实际应该从监控数据库获取
        return {}
    
    def recommend_sending_strategy(self, sender_email: str) -> Dict[str, Any]:
        """推荐发送策略"""
        risk_level = self.calculate_risk_level(sender_email)
        state = self.data_center.get_state(sender_email)
        
        strategy = {
            'risk_level': risk_level.value,
            'recommended_mode': 'safe',
            'max_batch_size': 10,
            'send_interval': 300,  # 秒
            'enable_monitoring': True,
            'emergency_threshold': 3,
            'recommendations': []
        }
        
        if risk_level == RiskLevel.LOW:
            strategy.update({
                'recommended_mode': 'standard',
                'max_batch_size': 50,
                'send_interval': 120,
                'emergency_threshold': 5
            })
            strategy['recommendations'].append("✅ 当前状态良好，可以正常发送")
            
        elif risk_level == RiskLevel.MEDIUM:
            strategy.update({
                'recommended_mode': 'safe',
                'max_batch_size': 20,
                'send_interval': 180,
                'emergency_threshold': 3
            })
            strategy['recommendations'].append("⚠️ 建议降低发送频率，加强监控")
            
        elif risk_level == RiskLevel.HIGH:
            strategy.update({
                'recommended_mode': 'safe',
                'max_batch_size': 10,
                'send_interval': 300,
                'emergency_threshold': 2
            })
            strategy['recommendations'].extend([
                "🚨 高风险状态，建议暂停发送",
                "🔍 检查收件人质量",
                "📝 优化邮件内容"
            ])
            
        elif risk_level == RiskLevel.CRITICAL:
            strategy.update({
                'recommended_mode': 'manual',
                'max_batch_size': 1,
                'send_interval': 600,
                'emergency_threshold': 1
            })
            strategy['recommendations'].extend([
                "🆘 严重风险，立即停止发送",
                "🔧 激活应急模式",
                "📞 联系技术支持"
            ])
        
        return strategy

class DeepSystemCoordinator:
    """深度系统协调器 - 实现所有功能的深度协调"""
    
    def __init__(self):
        self.data_center = SystemDataCenter()
        self.decision_engine = IntelligentDecisionEngine(self.data_center)
        self.setup_event_listeners()
        
    def setup_event_listeners(self):
        """设置事件监听器"""
        # 邮件发送事件
        self.data_center.register_event_listener(
            SystemEvent.EMAIL_SENT, 
            self.on_email_sent
        )
        
        # 回复接收事件
        self.data_center.register_event_listener(
            SystemEvent.REPLY_RECEIVED, 
            self.on_reply_received
        )
        
        # 无回复检测事件
        self.data_center.register_event_listener(
            SystemEvent.NO_REPLY_DETECTED, 
            self.on_no_reply_detected
        )
        
        # 风险等级变化事件
        self.data_center.register_event_listener(
            SystemEvent.RISK_LEVEL_CHANGED, 
            self.on_risk_level_changed
        )
    
    def on_email_sent(self, data: Dict[str, Any]):
        """处理邮件发送事件"""
        sender_email = data['sender_email']
        recipient_count = data.get('recipient_count', 1)
        
        # 更新状态
        state = self.data_center.get_state(sender_email)
        self.data_center.update_state(
            sender_email,
            total_sent=state.total_sent + recipient_count,
            last_send_time=datetime.datetime.now()
        )
        
        # 自动启动监控
        self._auto_start_monitoring(sender_email, data.get('recipients', []))
        
        # 检查风险等级
        self._check_and_update_risk_level(sender_email)
        
        print(f"📧 邮件发送事件处理完成: {sender_email} (+{recipient_count})")
    
    def on_reply_received(self, data: Dict[str, Any]):
        """处理回复接收事件"""
        sender_email = data['sender_email']
        recipient_email = data['recipient_email']
        
        # 更新状态
        state = self.data_center.get_state(sender_email)
        self.data_center.update_state(
            sender_email,
            total_replies=state.total_replies + 1,
            consecutive_no_reply=0,  # 重置连续无回复计数
            last_reply_time=datetime.datetime.now()
        )
        
        # 更新收件人质量数据库
        self._update_recipient_quality(sender_email, recipient_email, 'reply')
        
        # 检查应急恢复
        self._check_emergency_recovery(sender_email)
        
        print(f"✅ 回复接收事件处理完成: {sender_email} <- {recipient_email}")
    
    def on_no_reply_detected(self, data: Dict[str, Any]):
        """处理无回复检测事件"""
        sender_email = data['sender_email']
        recipient_email = data['recipient_email']
        
        # 更新状态
        state = self.data_center.get_state(sender_email)
        self.data_center.update_state(
            sender_email,
            consecutive_no_reply=state.consecutive_no_reply + 1
        )
        
        # 更新收件人质量数据库
        self._update_recipient_quality(sender_email, recipient_email, 'no_reply')
        
        # 检查应急激活
        self._check_emergency_activation(sender_email)
        
        print(f"❌ 无回复检测事件处理完成: {sender_email} -> {recipient_email}")
    
    def on_risk_level_changed(self, data: Dict[str, Any]):
        """处理风险等级变化事件"""
        sender_email = data['sender_email']
        old_level = data['old_level']
        new_level = data['new_level']
        
        # 自动调整发送策略
        strategy = self.decision_engine.recommend_sending_strategy(sender_email)
        self._apply_sending_strategy(sender_email, strategy)
        
        # 通知用户
        self._notify_risk_level_change(sender_email, old_level, new_level, strategy)
        
        print(f"⚠️ 风险等级变化: {sender_email} {old_level} -> {new_level}")
    
    def _auto_start_monitoring(self, sender_email: str, recipients: List[str]):
        """自动启动监控"""
        try:
            # 这里应该调用实际的监控启动函数
            print(f"📡 自动启动监控: {sender_email} ({len(recipients)} 个收件人)")
        except Exception as e:
            print(f"❌ 自动启动监控失败: {str(e)}")
    
    def _update_recipient_quality(self, sender_email: str, recipient_email: str, event_type: str):
        """更新收件人质量数据库"""
        try:
            # 这里应该调用实际的质量数据库更新函数
            print(f"📊 更新收件人质量: {recipient_email} ({event_type})")
        except Exception as e:
            print(f"❌ 更新收件人质量失败: {str(e)}")
    
    def _check_emergency_activation(self, sender_email: str):
        """检查应急激活条件"""
        state = self.data_center.get_state(sender_email)
        strategy = self.decision_engine.recommend_sending_strategy(sender_email)
        
        threshold = strategy.get('emergency_threshold', 5)
        
        if state.consecutive_no_reply >= threshold and not state.emergency_active:
            # 激活应急模式
            self.data_center.update_state(sender_email, emergency_active=True)
            self.data_center.emit_event(SystemEvent.EMERGENCY_ACTIVATED, {
                'sender_email': sender_email,
                'trigger_reason': f'连续{state.consecutive_no_reply}个无回复'
            })
            print(f"🆘 应急模式已激活: {sender_email}")
    
    def _check_emergency_recovery(self, sender_email: str):
        """检查应急恢复条件"""
        state = self.data_center.get_state(sender_email)
        
        if state.emergency_active and state.consecutive_no_reply == 0:
            # 检查是否满足恢复条件
            if self._is_recovery_conditions_met(sender_email):
                self.data_center.update_state(sender_email, emergency_active=False)
                self.data_center.emit_event(SystemEvent.EMERGENCY_DEACTIVATED, {
                    'sender_email': sender_email,
                    'recovery_reason': '收到回复，满足恢复条件'
                })
                print(f"✅ 应急模式已恢复: {sender_email}")
    
    def _is_recovery_conditions_met(self, sender_email: str) -> bool:
        """检查是否满足恢复条件"""
        # 简化实现，实际应该有更复杂的恢复条件检查
        return True
    
    def _check_and_update_risk_level(self, sender_email: str):
        """检查并更新风险等级"""
        old_level = self.data_center.get_state(sender_email).risk_level
        new_level = self.decision_engine.calculate_risk_level(sender_email)
        
        if old_level != new_level:
            self.data_center.update_state(sender_email, risk_level=new_level)
            self.data_center.emit_event(SystemEvent.RISK_LEVEL_CHANGED, {
                'sender_email': sender_email,
                'old_level': old_level.value,
                'new_level': new_level.value
            })
    
    def _apply_sending_strategy(self, sender_email: str, strategy: Dict[str, Any]):
        """应用发送策略"""
        try:
            # 这里应该调用实际的策略应用函数
            print(f"🔧 应用发送策略: {sender_email} - {strategy['recommended_mode']}")
        except Exception as e:
            print(f"❌ 应用发送策略失败: {str(e)}")
    
    def _notify_risk_level_change(self, sender_email: str, old_level: str, new_level: str, strategy: Dict[str, Any]):
        """通知风险等级变化"""
        try:
            # 这里应该调用实际的通知函数
            print(f"📢 风险等级变化通知: {sender_email} {old_level} -> {new_level}")
        except Exception as e:
            print(f"❌ 发送通知失败: {str(e)}")
    
    def get_system_status(self, sender_email: str) -> Dict[str, Any]:
        """获取系统状态"""
        state = self.data_center.get_state(sender_email)
        strategy = self.decision_engine.recommend_sending_strategy(sender_email)
        
        return {
            'sender_email': sender_email,
            'state': {
                'total_sent': state.total_sent,
                'total_replies': state.total_replies,
                'total_bounces': state.total_bounces,
                'consecutive_no_reply': state.consecutive_no_reply,
                'risk_level': state.risk_level.value,
                'emergency_active': state.emergency_active,
                'last_send_time': state.last_send_time.isoformat() if state.last_send_time else None,
                'last_reply_time': state.last_reply_time.isoformat() if state.last_reply_time else None
            },
            'strategy': strategy,
            'coordination_status': 'active'
        }

# 全局协调器实例
global_coordinator = DeepSystemCoordinator()

def get_coordinator() -> DeepSystemCoordinator:
    """获取全局协调器实例"""
    return global_coordinator

# 使用示例
if __name__ == "__main__":
    coordinator = get_coordinator()
    
    # 模拟邮件发送事件
    coordinator.data_center.emit_event(SystemEvent.EMAIL_SENT, {
        'sender_email': '<EMAIL>',
        'recipient_count': 10,
        'recipients': ['<EMAIL>', '<EMAIL>']
    })
    
    # 模拟回复接收事件
    coordinator.data_center.emit_event(SystemEvent.REPLY_RECEIVED, {
        'sender_email': '<EMAIL>',
        'recipient_email': '<EMAIL>'
    })
    
    # 获取系统状态
    status = coordinator.get_system_status('<EMAIL>')
    print(f"\n📊 系统状态:")
    print(json.dumps(status, ensure_ascii=False, indent=2))
