#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试一键启用全功能模式
"""

import tkinter as tk
from gui_main import EmailSenderGUI

def test_all_features():
    """测试一键启用全功能"""
    print("🚀 测试一键启用全功能模式")
    print("=" * 50)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建GUI实例
        app = EmailSenderGUI(root)
        
        print("✅ GUI创建成功")
        
        # 检查一键启用按钮是否存在
        if hasattr(app, 'enable_all_features'):
            print("✅ 一键启用功能方法存在")
        else:
            print("❌ 一键启用功能方法不存在")
            
        # 检查状态显示是否存在
        if hasattr(app, 'all_features_status_label'):
            print("✅ 全功能状态显示存在")
        else:
            print("❌ 全功能状态显示不存在")
            
        # 检查各个功能模块方法是否存在
        feature_methods = [
            '_init_all_components',
            '_enable_auto_reply_monitoring',
            '_enable_quality_database',
            '_enable_anti_spam',
            '_enable_qq_emergency',
            '_enable_smart_queue',
            '_enable_deep_coordination',
            '_finalize_all_features'
        ]
        
        print("\n🔧 检查功能模块方法:")
        for method in feature_methods:
            if hasattr(app, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        # 检查状态管理方法
        status_methods = [
            '_add_all_features_status_display',
            '_check_existing_all_features_config',
            '_update_all_features_status_display',
            '_restore_all_features'
        ]
        
        print("\n📊 检查状态管理方法:")
        for method in status_methods:
            if hasattr(app, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        # 检查界面显示方法
        ui_methods = [
            '_show_all_features_enabled',
            '_show_usage_guide'
        ]
        
        print("\n🎨 检查界面显示方法:")
        for method in ui_methods:
            if hasattr(app, method):
                print(f"  ✅ {method}")
            else:
                print(f"  ❌ {method}")
        
        # 测试QQ应急管理器
        print("\n🆘 测试QQ应急管理器:")
        try:
            from qq_email_anti_spam import QQEmailAntiSpamManager
            qq_manager = QQEmailAntiSpamManager()

            # 检查初始化方法
            if hasattr(qq_manager, 'initialize_qq_sender'):
                print("  ✅ initialize_qq_sender 方法存在")
            else:
                print("  ❌ initialize_qq_sender 方法不存在")

            # 检查应急检查方法
            if hasattr(qq_manager, '_check_emergency_status'):
                print("  ✅ _check_emergency_status 方法存在")
            else:
                print("  ❌ _check_emergency_status 方法不存在")

        except Exception as e:
            print(f"  ❌ QQ应急管理器测试失败: {str(e)}")

        # 测试深度协调系统
        print("\n🔧 测试深度协调系统:")
        try:
            from 深度系统协调实现 import get_coordinator
            coordinator = get_coordinator()

            # 检查协调器方法
            if hasattr(coordinator, 'get_system_status'):
                print("  ✅ get_system_status 方法存在")
            else:
                print("  ❌ get_system_status 方法不存在")

            if hasattr(coordinator, 'data_center'):
                print("  ✅ data_center 属性存在")
            else:
                print("  ❌ data_center 属性不存在")

        except Exception as e:
            print(f"  ❌ 深度协调系统测试失败: {str(e)}")

        print("\n🎉 测试完成！")
        print("✅ 修复内容:")
        print("  • QQ应急管理器：移除了不存在的 set_emergency_threshold 方法调用")
        print("  • 深度协调系统：修复了 activate_deep_coordination 方法调用问题")
        print("  • 应急模式会在连续5封邮件无回复时自动激活")
        print("  • 深度协调系统在初始化时已自动启动")
        print("\n提示：运行程序后可以点击'🚀 一键启用全功能'按钮测试完整功能")

        # 不启动主循环，只是测试
        root.destroy()
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_all_features()
