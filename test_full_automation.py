#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全自动模式测试脚本
测试全自动模式是否正常工作
"""

import json
import os
import datetime

def test_full_automation():
    """测试全自动模式"""
    print("🧪 全自动模式测试")
    print("=" * 50)
    
    # 测试配置文件
    config_files = [
        'all_features_config.json',
        'auto_reply_config.json', 
        'startup_config.json',
        'monitor_settings.json',
        'automation_workflow.json'
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ {config_file} 配置正确")
            except Exception as e:
                print(f"❌ {config_file} 配置错误: {str(e)}")
        else:
            print(f"⚠️ {config_file} 不存在")
    
    # 测试模块导入
    modules = [
        'email_sender',
        'email_receiver', 
        'recipient_quality_manager',
        'qq_email_anti_spam',
        'system_coordinator'
    ]
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ {module} 模块可正常导入")
        except ImportError as e:
            print(f"❌ {module} 模块导入失败: {str(e)}")
    
    print("\n🎯 测试完成")
    print("💡 如果所有项目都显示 ✅，说明全自动模式配置正确")

if __name__ == "__main__":
    test_full_automation()
