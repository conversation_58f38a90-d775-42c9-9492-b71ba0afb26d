#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库表修复
"""

import tkinter as tk
import sqlite3
import os
from gui_main import EmailSenderGUI

def test_database_tables():
    """测试数据库表是否正确创建"""
    print("🔧 测试数据库表修复")
    print("=" * 60)
    
    # 测试数据库文件
    databases = [
        ('email_history.db', ['email_records']),
        ('recipient_quality.db', ['recipient_quality', 'send_history', 'batch_management', 'batch_recipients', 'campaign_analytics', 'recipient_status']),
        ('auto_reply_monitor.db', ['recipient_status'])
    ]
    
    for db_file, expected_tables in databases:
        print(f"\n📁 检查数据库: {db_file}")
        
        if not os.path.exists(db_file):
            print(f"  ❌ 数据库文件不存在: {db_file}")
            continue
        
        try:
            conn = sqlite3.connect(db_file)
            cursor = conn.cursor()
            
            # 获取所有表名
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            existing_tables = [row[0] for row in cursor.fetchall()]
            
            print(f"  📊 现有表: {existing_tables}")
            
            # 检查期望的表
            for table in expected_tables:
                if table in existing_tables:
                    print(f"    ✅ {table} - 存在")
                    
                    # 检查表结构
                    cursor.execute(f"PRAGMA table_info({table})")
                    columns = cursor.fetchall()
                    print(f"      📋 列数: {len(columns)}")
                    
                    # 检查记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"      📈 记录数: {count}")
                    
                else:
                    print(f"    ❌ {table} - 不存在")
            
            conn.close()
            
        except Exception as e:
            print(f"  ❌ 检查数据库失败: {str(e)}")

def test_gui_initialization():
    """测试GUI初始化是否正常"""
    print("\n🖥️ 测试GUI初始化")
    print("=" * 60)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        
        # 创建GUI实例
        app = EmailSenderGUI(root)
        
        print("✅ GUI创建成功")
        
        # 测试邮箱输入
        test_email = "<EMAIL>"
        app.sender_email.delete(0, tk.END)
        app.sender_email.insert(0, test_email)
        print(f"✅ 设置测试邮箱: {test_email}")
        
        # 测试各个管理器初始化
        managers_to_test = [
            ('质量数据库管理器', 'quality_manager'),
            ('反垃圾邮件管理器', 'anti_spam_manager'),
            ('QQ应急管理器', 'qq_emergency_manager'),
            ('邮件历史管理器', 'history_manager'),
            ('RAG搜索引擎', 'rag_search_engine')
        ]
        
        for manager_name, attr_name in managers_to_test:
            if hasattr(app, attr_name):
                manager = getattr(app, attr_name)
                if manager:
                    print(f"  ✅ {manager_name} - 已初始化")
                else:
                    print(f"  ❌ {manager_name} - 未初始化")
            else:
                print(f"  ⚠️ {manager_name} - 属性不存在")
        
        # 测试功能状态
        print(f"\n📊 功能状态:")
        print(f"  全功能模式: {getattr(app, 'all_features_enabled', False)}")
        print(f"  功能状态: {getattr(app, 'feature_status', {})}")
        
        # 关闭窗口
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ GUI初始化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_database_operations():
    """测试数据库操作"""
    print("\n🗄️ 测试数据库操作")
    print("=" * 60)
    
    try:
        # 测试质量数据库
        from recipient_quality_manager import RecipientQualityManager
        quality_manager = RecipientQualityManager()
        print("✅ 质量数据库管理器创建成功")
        
        # 测试添加收件人
        test_email = "<EMAIL>"
        quality_manager.update_recipient_quality(
            email=test_email,
            sender_email="<EMAIL>",
            subject="测试邮件",
            body="测试内容",
            success=True
        )
        print(f"✅ 添加测试收件人: {test_email}")
        
        # 测试查询收件人
        recipients = quality_manager.get_quality_recipients(min_quality_score=0)
        print(f"✅ 查询收件人数量: {len(recipients)}")
        
        # 测试邮件历史管理器
        from email_history_manager import EmailHistoryManager
        history_manager = EmailHistoryManager()
        print("✅ 邮件历史管理器创建成功")
        
        # 测试添加邮件记录
        record_id = history_manager.add_email_record(
            sender_email="<EMAIL>",
            recipient_email=test_email,
            subject="测试邮件",
            body="测试内容",
            success=True
        )
        print(f"✅ 添加邮件记录: {record_id}")
        
        # 测试搜索
        search_results = history_manager.search_email_history("测试", "<EMAIL>")
        print(f"✅ 搜索结果数量: {len(search_results)}")
        
        # 测试自动回复监控
        from email_receiver import EmailReceiver
        receiver = EmailReceiver("<EMAIL>", "test_password")
        print("✅ 自动回复监控器创建成功")

        # 测试更新收件人状态
        receiver.update_recipient_status(test_email, "<EMAIL>", "auto_reply")
        print(f"✅ 更新收件人状态: {test_email}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinator_functions():
    """测试协调器功能"""
    print("\n🔧 测试协调器功能")
    print("=" * 60)
    
    try:
        # 测试系统协调器
        from system_coordinator import EmailSystemCoordinator
        coordinator = EmailSystemCoordinator()
        print("✅ 系统协调器创建成功")

        # 测试分析收件人状态
        analysis = coordinator.analyze_recipient_status("<EMAIL>")
        print(f"✅ 收件人状态分析: {len(analysis)} 项数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 协调器功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始数据库修复测试")
    print("=" * 80)
    
    # 测试数据库表
    test_database_tables()
    
    # 测试GUI初始化
    gui_success = test_gui_initialization()
    
    # 测试数据库操作
    db_success = test_database_operations()
    
    # 测试协调器功能
    coord_success = test_coordinator_functions()
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 测试结果总结:")
    print(f"  GUI初始化: {'✅ 成功' if gui_success else '❌ 失败'}")
    print(f"  数据库操作: {'✅ 成功' if db_success else '❌ 失败'}")
    print(f"  协调器功能: {'✅ 成功' if coord_success else '❌ 失败'}")
    
    if gui_success and db_success and coord_success:
        print("\n🎉 所有测试通过！数据库修复成功！")
        print("✅ 一键启用和重置功能现在应该可以正常工作")
        return True
    else:
        print("\n⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
