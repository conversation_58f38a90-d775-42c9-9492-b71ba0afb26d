2025-06-12 22:37:04,004 - INFO - 集成数据库初始化完成
2025-06-12 22:37:04,004 - INFO - 系统集成管理器初始化完成
2025-06-12 22:37:04,005 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:37:04,046 - INFO - 自动回复数据库初始化完成
2025-06-12 22:37:04,046 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:37:04,050 - INFO - 找到 79 个有效收件人
2025-06-12 22:37:04,053 - INFO - 找到 4 个无效收件人
2025-06-12 22:37:04,056 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:37:04,775 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:37:04,776 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:37:04,790 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:37:04,790 - ERROR - 同步质量数据到应急系统失败: 'RecipientQualityManager' object has no attribute 'get_low_quality_recipients'
2025-06-12 22:37:04,796 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:37:04,796 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:37:04,800 - INFO - 自动回复数据库初始化完成
2025-06-12 22:37:04,800 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:37:04,803 - INFO - 找到 79 个有效收件人
2025-06-12 22:37:04,807 - INFO - 找到 4 个无效收件人
2025-06-12 22:37:04,809 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:37:05,510 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:37:05,510 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:37:05,514 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:37:05,514 - ERROR - 同步质量数据到应急系统失败: 'RecipientQualityManager' object has no attribute 'get_low_quality_recipients'
2025-06-12 22:37:05,521 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:37:05,527 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:37:05,529 - ERROR - ❌ 更新QQ回复状态失败: near "ORDER": syntax error
2025-06-12 22:37:05,531 - ERROR - ❌ 更新QQ回复状态失败: near "ORDER": syntax error
2025-06-12 22:38:08,223 - INFO - 集成数据库初始化完成
2025-06-12 22:38:08,223 - INFO - 系统集成管理器初始化完成
2025-06-12 22:38:08,224 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:38:08,270 - INFO - 自动回复数据库初始化完成
2025-06-12 22:38:08,270 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:38:08,273 - INFO - 找到 79 个有效收件人
2025-06-12 22:38:08,276 - INFO - 找到 4 个无效收件人
2025-06-12 22:38:08,280 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:38:08,988 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:38:08,989 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:38:09,004 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:38:09,005 - ERROR - 获取低质量收件人失败: no such column: sender_email
2025-06-12 22:38:09,009 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:38:09,016 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:38:09,016 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:38:09,017 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:38:09,020 - INFO - 自动回复数据库初始化完成
2025-06-12 22:38:09,020 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:38:09,023 - INFO - 找到 79 个有效收件人
2025-06-12 22:38:09,026 - INFO - 找到 4 个无效收件人
2025-06-12 22:38:09,028 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:38:09,236 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:38:09,237 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:38:09,238 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:38:09,239 - ERROR - 获取低质量收件人失败: no such column: sender_email
2025-06-12 22:38:09,243 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:38:09,248 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:38:09,250 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:38:09,255 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:38:09,257 - ERROR - ❌ 更新QQ回复状态失败: no such table: qq_send_records
2025-06-12 22:38:09,259 - ERROR - ❌ 更新QQ回复状态失败: no such table: qq_send_records
2025-06-12 22:40:11,010 - INFO - 集成数据库初始化完成
2025-06-12 22:40:11,010 - INFO - 系统集成管理器初始化完成
2025-06-12 22:40:11,011 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:40:11,055 - INFO - 自动回复数据库初始化完成
2025-06-12 22:40:11,056 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:40:11,059 - INFO - 找到 79 个有效收件人
2025-06-12 22:40:11,061 - INFO - 找到 4 个无效收件人
2025-06-12 22:40:11,064 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:40:11,067 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,068 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,069 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,071 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,072 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,074 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,075 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,076 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,078 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,079 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,080 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,081 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,083 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,084 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,085 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,087 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,088 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,090 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,093 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,095 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,096 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,098 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,099 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,100 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,101 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,102 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,103 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,104 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,106 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,107 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,108 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,110 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,111 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,112 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,114 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,115 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,116 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,118 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,119 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,121 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,122 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,123 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,124 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,126 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,127 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,129 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,130 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,131 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,132 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,133 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,135 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,137 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,138 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,140 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,141 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,142 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,144 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,145 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,147 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,149 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,150 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,152 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,153 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,155 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,156 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,157 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,158 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,160 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,161 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,162 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,164 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,166 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,167 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,169 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,170 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,171 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,172 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,173 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,175 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,176 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,177 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,179 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,180 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,187 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:40:11,187 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:40:11,198 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:40:11,200 - ERROR - 获取低质量收件人失败: no such column: sender_email
2025-06-12 22:40:11,203 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:40:11,209 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:40:11,210 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:40:11,210 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:40:11,213 - INFO - 自动回复数据库初始化完成
2025-06-12 22:40:11,213 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:40:11,216 - INFO - 找到 79 个有效收件人
2025-06-12 22:40:11,220 - INFO - 找到 4 个无效收件人
2025-06-12 22:40:11,222 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:40:11,223 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,224 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,225 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,226 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,228 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,229 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,230 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,232 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,234 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,235 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,236 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,237 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,239 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,240 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,242 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,243 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,244 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,245 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,247 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,248 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,250 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,251 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,253 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,254 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,255 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,256 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,257 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,259 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,260 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,261 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,263 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,264 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,264 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,266 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,267 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,268 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,270 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,271 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,272 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,273 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,274 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,276 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,278 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,279 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,280 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,281 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,283 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,283 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,285 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,287 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,288 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,290 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,291 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,293 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,293 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,294 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,296 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,297 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,299 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,300 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,302 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,303 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,304 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,305 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,307 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,308 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,310 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,311 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,313 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,314 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,315 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,316 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,318 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,319 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,321 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,322 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,323 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,324 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,325 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,327 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,329 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,330 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,332 - ERROR - 更新收件人质量失败: table recipient_quality has no column named sender_email
2025-06-12 22:40:11,338 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:40:11,338 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:40:11,340 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:40:11,342 - ERROR - 获取低质量收件人失败: no such column: sender_email
2025-06-12 22:40:11,345 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:40:11,351 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:40:11,353 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:40:11,358 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:40:11,369 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> True (自动回复)
2025-06-12 22:40:11,378 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> False ()
2025-06-12 22:41:25,948 - INFO - 集成数据库初始化完成
2025-06-12 22:41:25,949 - INFO - 系统集成管理器初始化完成
2025-06-12 22:41:25,949 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:41:25,994 - INFO - 自动回复数据库初始化完成
2025-06-12 22:41:25,994 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:41:25,998 - INFO - 找到 79 个有效收件人
2025-06-12 22:41:26,002 - INFO - 找到 4 个无效收件人
2025-06-12 22:41:26,005 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:41:26,749 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:41:26,750 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:41:26,756 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:41:26,760 - ERROR - 获取低质量收件人失败: RecipientQuality.__init__() missing 1 required positional argument: 'last_reply_time'
2025-06-12 22:41:26,763 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:41:26,770 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:41:26,770 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:41:26,770 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:41:26,774 - INFO - 自动回复数据库初始化完成
2025-06-12 22:41:26,774 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:41:26,777 - INFO - 找到 79 个有效收件人
2025-06-12 22:41:26,780 - INFO - 找到 4 个无效收件人
2025-06-12 22:41:26,783 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:41:27,499 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:41:27,500 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:41:27,503 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:41:27,505 - ERROR - 获取低质量收件人失败: RecipientQuality.__init__() missing 1 required positional argument: 'last_reply_time'
2025-06-12 22:41:27,509 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:41:27,515 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:41:27,517 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:41:27,524 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:41:27,533 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> True (自动回复)
2025-06-12 22:41:27,543 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> False ()
2025-06-12 22:43:24,359 - INFO - 集成数据库初始化完成
2025-06-12 22:43:24,359 - INFO - 系统集成管理器初始化完成
2025-06-12 22:43:24,359 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:43:24,405 - INFO - 自动回复数据库初始化完成
2025-06-12 22:43:24,405 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:43:24,408 - INFO - 找到 79 个有效收件人
2025-06-12 22:43:24,411 - INFO - 找到 4 个无效收件人
2025-06-12 22:43:24,415 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:43:25,151 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:43:25,152 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:43:25,158 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:43:25,162 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:43:25,165 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:43:25,172 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:43:25,172 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:43:25,172 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:43:25,176 - INFO - 自动回复数据库初始化完成
2025-06-12 22:43:25,176 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:43:25,179 - INFO - 找到 79 个有效收件人
2025-06-12 22:43:25,181 - INFO - 找到 4 个无效收件人
2025-06-12 22:43:25,185 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:43:25,906 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:43:25,906 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:43:25,909 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:43:25,913 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:43:25,916 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:43:25,921 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:43:25,923 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:43:25,928 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:43:25,939 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> True (自动回复)
2025-06-12 22:43:25,948 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> False ()
2025-06-12 22:45:48,415 - INFO - 集成数据库初始化完成
2025-06-12 22:45:48,415 - INFO - 系统集成管理器初始化完成
2025-06-12 22:45:48,416 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:45:48,451 - INFO - 自动回复数据库初始化完成
2025-06-12 22:45:48,451 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:45:48,454 - INFO - 找到 79 个有效收件人
2025-06-12 22:45:48,458 - INFO - 找到 4 个无效收件人
2025-06-12 22:45:48,460 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:45:49,145 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:45:49,145 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:45:49,150 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:45:49,153 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:45:49,156 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:45:49,162 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:45:49,163 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:45:49,163 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:45:49,166 - INFO - 自动回复数据库初始化完成
2025-06-12 22:45:49,166 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:45:49,169 - INFO - 找到 79 个有效收件人
2025-06-12 22:45:49,172 - INFO - 找到 4 个无效收件人
2025-06-12 22:45:49,175 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:45:49,857 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:45:49,857 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:45:49,860 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:45:49,863 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:45:49,866 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:45:49,871 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:45:49,873 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:45:49,878 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:45:49,888 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> True (自动回复)
2025-06-12 22:45:49,897 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> False ()
2025-06-12 22:46:56,712 - INFO - 集成数据库初始化完成
2025-06-12 22:46:56,712 - INFO - 系统集成管理器初始化完成
2025-06-12 22:46:56,712 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:46:56,763 - INFO - 自动回复数据库初始化完成
2025-06-12 22:46:56,763 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:46:56,766 - INFO - 找到 79 个有效收件人
2025-06-12 22:46:56,771 - INFO - 找到 4 个无效收件人
2025-06-12 22:46:56,774 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:46:57,481 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:46:57,482 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:46:57,487 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:46:57,490 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:46:57,492 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:46:57,500 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:46:57,500 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:46:57,500 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:46:57,503 - INFO - 自动回复数据库初始化完成
2025-06-12 22:46:57,503 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:46:57,507 - INFO - 找到 79 个有效收件人
2025-06-12 22:46:57,510 - INFO - 找到 4 个无效收件人
2025-06-12 22:46:57,513 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:46:58,237 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:46:58,237 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:46:58,240 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:46:58,244 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:46:58,247 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:46:58,253 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:46:58,255 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:46:58,260 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:46:58,270 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> True (自动回复)
2025-06-12 22:46:58,280 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> False ()
2025-06-12 22:47:57,888 - INFO - 集成数据库初始化完成
2025-06-12 22:47:57,889 - INFO - 系统集成管理器初始化完成
2025-06-12 22:47:57,889 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:47:57,923 - INFO - 自动回复数据库初始化完成
2025-06-12 22:47:57,924 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:47:57,926 - INFO - 找到 79 个有效收件人
2025-06-12 22:47:57,930 - INFO - 找到 4 个无效收件人
2025-06-12 22:47:57,932 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:47:58,613 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:47:58,614 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:47:58,619 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:47:58,622 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:47:58,625 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:47:58,632 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:47:58,632 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:47:58,632 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:47:58,635 - INFO - 自动回复数据库初始化完成
2025-06-12 22:47:58,636 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:47:58,639 - INFO - 找到 79 个有效收件人
2025-06-12 22:47:58,642 - INFO - 找到 4 个无效收件人
2025-06-12 22:47:58,645 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:47:59,340 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:47:59,340 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:47:59,343 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:47:59,346 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:47:59,349 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:47:59,355 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:47:59,361 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:47:59,367 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:47:59,376 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> True (自动回复)
2025-06-12 22:47:59,385 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> False ()
2025-06-12 22:50:17,010 - INFO - 集成数据库初始化完成
2025-06-12 22:50:17,011 - INFO - 系统集成管理器初始化完成
2025-06-12 22:50:17,011 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:50:17,049 - INFO - 自动回复数据库初始化完成
2025-06-12 22:50:17,049 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:50:17,052 - INFO - 找到 79 个有效收件人
2025-06-12 22:50:17,055 - INFO - 找到 4 个无效收件人
2025-06-12 22:50:17,058 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:50:17,755 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:50:17,756 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:50:17,761 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:50:17,763 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:50:17,766 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:50:17,772 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:50:17,773 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:50:17,773 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:50:17,776 - INFO - 自动回复数据库初始化完成
2025-06-12 22:50:17,776 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:50:17,779 - INFO - 找到 79 个有效收件人
2025-06-12 22:50:17,781 - INFO - 找到 4 个无效收件人
2025-06-12 22:50:17,784 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:50:18,532 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:50:18,532 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:50:18,535 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:50:18,538 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:50:18,541 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:50:18,546 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:50:18,554 - INFO - 自动回复数据库初始化完成
2025-06-12 22:50:18,554 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:50:18,557 - INFO - 找到 79 个有效收件人
2025-06-12 22:50:18,559 - INFO - 找到 4 个无效收件人
2025-06-12 22:50:18,562 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:50:18,565 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:50:18,566 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:50:18,571 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:50:18,581 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> True (自动回复)
2025-06-12 22:50:18,591 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> False ()
2025-06-12 22:51:11,700 - INFO - 集成数据库初始化完成
2025-06-12 22:51:11,701 - INFO - 系统集成管理器初始化完成
2025-06-12 22:51:11,701 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:51:11,737 - INFO - 自动回复数据库初始化完成
2025-06-12 22:51:11,738 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:51:11,741 - INFO - 找到 79 个有效收件人
2025-06-12 22:51:11,744 - INFO - 找到 4 个无效收件人
2025-06-12 22:51:11,747 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:51:12,438 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:51:12,438 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:51:12,444 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:51:12,447 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:51:12,450 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:51:12,456 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:51:12,456 - INFO - 开始完整系统集成: <EMAIL>
2025-06-12 22:51:12,456 - INFO - 开始同步自动回复数据到质量数据库: <EMAIL>
2025-06-12 22:51:12,459 - INFO - 自动回复数据库初始化完成
2025-06-12 22:51:12,459 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:51:12,462 - INFO - 找到 79 个有效收件人
2025-06-12 22:51:12,465 - INFO - 找到 4 个无效收件人
2025-06-12 22:51:12,467 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:51:13,150 - INFO - ✅ 自动回复数据同步完成: 83 个收件人
2025-06-12 22:51:13,150 - INFO - 开始同步质量数据到应急系统: <EMAIL>
2025-06-12 22:51:13,153 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:51:13,157 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:51:13,160 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:51:13,165 - INFO - ✅ 质量数据同步到应急系统完成
2025-06-12 22:51:13,175 - INFO - 自动回复数据库初始化完成
2025-06-12 22:51:13,175 - INFO - 邮件接收器初始化完成 - 邮箱: <EMAIL>
2025-06-12 22:51:13,178 - INFO - 找到 79 个有效收件人
2025-06-12 22:51:13,180 - INFO - 找到 4 个无效收件人
2025-06-12 22:51:13,184 - INFO - 收件人质量数据库初始化完成
2025-06-12 22:51:13,187 - INFO - 获取到 4 个低质量收件人（评分≤30.0）
2025-06-12 22:51:13,188 - INFO - ✅ 完整系统集成完成: <EMAIL>
2025-06-12 22:51:13,193 - INFO - QQ邮箱反垃圾邮件数据库初始化完成
2025-06-12 22:51:13,203 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> True (自动回复)
2025-06-12 22:51:13,213 - INFO - ✅ 更新QQ回复状态成功: <EMAIL> -> False ()
