# -*- coding: utf-8 -*-
"""
测试暂停、恢复和断点继续功能
"""

import json
import os
import datetime

def create_test_progress_file():
    """创建测试用的进度文件"""
    progress_data = {
        'session_id': f'test_session_{datetime.datetime.now().strftime("%Y%m%d_%H%M%S")}',
        'send_mode': 'standard',
        'current_email_index': 5,
        'total_emails': 20,
        'success_count': 4,
        'failed_count': 1,
        'current_batch': 2,
        'total_batches': 4,
        'batch_size': 5,
        'start_time': datetime.datetime.now().isoformat(),
        'last_update': datetime.datetime.now().isoformat(),
        'email_list': [
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件1', 'body': '测试内容1'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件2', 'body': '测试内容2'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件3', 'body': '测试内容3'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件4', 'body': '测试内容4'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件5', 'body': '测试内容5'},
            # 剩余的邮件...
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件6', 'body': '测试内容6'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件7', 'body': '测试内容7'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件8', 'body': '测试内容8'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件9', 'body': '测试内容9'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件10', 'body': '测试内容10'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件11', 'body': '测试内容11'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件12', 'body': '测试内容12'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件13', 'body': '测试内容13'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件14', 'body': '测试内容14'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件15', 'body': '测试内容15'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件16', 'body': '测试内容16'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件17', 'body': '测试内容17'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件18', 'body': '测试内容18'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件19', 'body': '测试内容19'},
            {'to_emails': ['<EMAIL>'], 'subject': '测试邮件20', 'body': '测试内容20'},
        ]
    }

    with open('email_send_progress.json', 'w', encoding='utf-8') as f:
        json.dump(progress_data, f, ensure_ascii=False, indent=2)

    print("✅ 测试进度文件已创建: email_send_progress.json")
    print(f"📊 模拟进度: 已发送 {progress_data['current_email_index']}/{progress_data['total_emails']} 封邮件")
    print(f"📈 成功: {progress_data['success_count']}, 失败: {progress_data['failed_count']}")
    print(f"🔄 剩余: {progress_data['total_emails'] - progress_data['current_email_index']} 封邮件")

def check_progress_file():
    """检查进度文件"""
    if os.path.exists('email_send_progress.json'):
        with open('email_send_progress.json', 'r', encoding='utf-8') as f:
            data = json.load(f)

        print("📋 当前进度文件信息:")
        print(f"  会话ID: {data.get('session_id', 'unknown')}")
        print(f"  发送模式: {data.get('send_mode', 'unknown')}")
        print(f"  当前位置: {data.get('current_email_index', 0)}/{data.get('total_emails', 0)}")
        print(f"  成功数量: {data.get('success_count', 0)}")
        print(f"  失败数量: {data.get('failed_count', 0)}")
        print(f"  剩余邮件: {data.get('total_emails', 0) - data.get('current_email_index', 0)}")
    else:
        print("❌ 没有找到进度文件")

def clear_progress_file():
    """清除进度文件"""
    if os.path.exists('email_send_progress.json'):
        os.remove('email_send_progress.json')
        print("🗑️ 进度文件已删除")
    else:
        print("❌ 没有找到进度文件")

def main():
    """主函数"""
    print("=" * 50)
    print("    暂停、恢复和断点继续功能测试")
    print("=" * 50)

    while True:
        print("\n请选择操作:")
        print("1. 创建测试进度文件")
        print("2. 查看当前进度文件")
        print("3. 清除进度文件")
        print("4. 启动GUI程序测试")
        print("5. 退出")

        choice = input("\n请输入选项 (1-5): ").strip()

        if choice == '1':
            create_test_progress_file()
        elif choice == '2':
            check_progress_file()
        elif choice == '3':
            clear_progress_file()
        elif choice == '4':
            print("🚀 启动GUI程序...")
            print("💡 测试步骤:")
            print("  1. 程序启动后，如果有进度文件，会自动提示是否断点继续")
            print("  2. 在主界面可以看到'断点继续'按钮")
            print("  3. 在队列管理界面可以看到'暂停发送'、'恢复发送'、'断点继续'按钮")
            print("  4. 发送邮件时可以测试暂停和恢复功能")

            import subprocess
            subprocess.run(['python', 'gui_main.py'])
        elif choice == '5':
            print("再见！")
            break
        else:
            print("无效选项，请重新选择")

if __name__ == "__main__":
    main()
