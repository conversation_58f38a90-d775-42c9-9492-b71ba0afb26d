#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🛡️ 系统稳定性全面保障方案
确保2.0系统在各种情况下都能稳定运行
包括：系统重启、电脑重启、环境变化、依赖缺失等
"""

import os
import sys
import json
import sqlite3
import subprocess
import shutil
import time
from pathlib import Path
import logging
from datetime import datetime

class SystemStabilityGuard:
    """系统稳定性守护者"""
    
    def __init__(self):
        self.setup_logging()
        self.base_dir = Path(__file__).parent
        self.backup_dir = self.base_dir / "system_backup"
        self.config_dir = self.base_dir / "config_backup"
        
        # 确保备份目录存在
        self.backup_dir.mkdir(exist_ok=True)
        self.config_dir.mkdir(exist_ok=True)
        
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f"logs/stability_guard_{datetime.now().strftime('%Y%m%d')}.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def comprehensive_check(self):
        """全面系统检查"""
        self.logger.info("🔍 开始全面系统稳定性检查...")
        
        checks = [
            ("Python环境检查", self.check_python_environment),
            ("依赖包检查", self.check_dependencies),
            ("核心文件检查", self.check_core_files),
            ("数据库完整性检查", self.check_databases),
            ("配置文件检查", self.check_config_files),
            ("权限检查", self.check_permissions),
            ("磁盘空间检查", self.check_disk_space),
            ("网络连接检查", self.check_network),
            ("启动器检查", self.check_launchers),
            ("备份完整性检查", self.check_backups)
        ]
        
        results = {}
        for check_name, check_func in checks:
            try:
                self.logger.info(f"🔧 执行: {check_name}")
                result = check_func()
                results[check_name] = result
                if result['status'] == 'success':
                    self.logger.info(f"✅ {check_name} - 通过")
                else:
                    self.logger.warning(f"⚠️ {check_name} - {result['message']}")
            except Exception as e:
                self.logger.error(f"❌ {check_name} - 检查失败: {str(e)}")
                results[check_name] = {'status': 'error', 'message': str(e)}
        
        return results
    
    def check_python_environment(self):
        """检查Python环境"""
        try:
            # 检查Python版本
            if sys.version_info < (3, 6):
                return {'status': 'error', 'message': f'Python版本过低: {sys.version}'}
            
            # 检查Python路径
            python_path = sys.executable
            if not os.path.exists(python_path):
                return {'status': 'error', 'message': f'Python路径无效: {python_path}'}
            
            # 检查pip
            try:
                subprocess.run([python_path, '-m', 'pip', '--version'], 
                             check=True, capture_output=True)
            except subprocess.CalledProcessError:
                return {'status': 'warning', 'message': 'pip不可用，可能影响依赖安装'}
            
            return {'status': 'success', 'message': f'Python {sys.version.split()[0]} 环境正常'}
            
        except Exception as e:
            return {'status': 'error', 'message': f'Python环境检查失败: {str(e)}'}
    
    def check_dependencies(self):
        """检查依赖包"""
        required_packages = {
            'jieba': '中文分词库',
            'tkinter': 'GUI界面库',
            'sqlite3': '数据库库',
            'smtplib': '邮件发送库',
            'email': '邮件处理库'
        }
        
        missing_packages = []
        for package, description in required_packages.items():
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(f"{package} ({description})")
        
        if missing_packages:
            return {
                'status': 'error', 
                'message': f'缺少依赖包: {", ".join(missing_packages)}',
                'missing': missing_packages
            }
        
        return {'status': 'success', 'message': '所有依赖包完整'}
    
    def check_core_files(self):
        """检查核心文件"""
        core_files = [
            'gui_main.py',
            'email_sender.py',
            'email_history_manager.py',
            'rag_search_engine.py',
            '深度系统协调实现.py',
            'recipient_quality_manager.py',
            'anti_spam_manager.py',
            'qq_email_anti_spam.py',
            'system_coordinator.py',
            'batch_manager.py',
            'queue_system.py'
        ]
        
        missing_files = []
        for file_name in core_files:
            if not os.path.exists(file_name):
                missing_files.append(file_name)
        
        if missing_files:
            return {
                'status': 'error',
                'message': f'缺少核心文件: {", ".join(missing_files)}',
                'missing': missing_files
            }
        
        return {'status': 'success', 'message': '所有核心文件完整'}
    
    def check_databases(self):
        """检查数据库完整性"""
        databases = [
            'email_history.db',
            'recipient_quality.db',
            'anti_spam.db',
            'qq_anti_spam.db',
            'system_integration.db',
            'user_data/user_settings.db'
        ]
        
        issues = []
        for db_path in databases:
            if os.path.exists(db_path):
                try:
                    # 尝试连接数据库
                    conn = sqlite3.connect(db_path)
                    conn.execute("SELECT 1")
                    conn.close()
                except sqlite3.Error as e:
                    issues.append(f"{db_path}: {str(e)}")
            else:
                # 数据库不存在，尝试创建
                try:
                    self.create_missing_database(db_path)
                except Exception as e:
                    issues.append(f"无法创建 {db_path}: {str(e)}")
        
        if issues:
            return {'status': 'warning', 'message': f'数据库问题: {"; ".join(issues)}'}
        
        return {'status': 'success', 'message': '所有数据库正常'}
    
    def check_config_files(self):
        """检查配置文件"""
        config_files = [
            'all_features_config.json',
            'automation_workflow.json',
            'auth_codes.json',
            'monitor_settings.json',
            'startup_config.json'
        ]
        
        missing_configs = []
        for config_file in config_files:
            if not os.path.exists(config_file):
                missing_configs.append(config_file)
                # 尝试从备份恢复
                backup_path = self.config_dir / f"{config_file}.backup"
                if backup_path.exists():
                    try:
                        shutil.copy2(backup_path, config_file)
                        self.logger.info(f"✅ 从备份恢复配置文件: {config_file}")
                        missing_configs.remove(config_file)
                    except Exception as e:
                        self.logger.error(f"❌ 恢复配置文件失败 {config_file}: {str(e)}")
        
        if missing_configs:
            # 创建默认配置
            for config_file in missing_configs:
                try:
                    self.create_default_config(config_file)
                except Exception as e:
                    self.logger.error(f"❌ 创建默认配置失败 {config_file}: {str(e)}")
        
        return {'status': 'success', 'message': '配置文件检查完成'}
    
    def check_permissions(self):
        """检查文件权限"""
        try:
            # 检查当前目录写权限
            test_file = "permission_test.tmp"
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            
            # 检查日志目录权限
            log_dir = Path("logs")
            if not log_dir.exists():
                log_dir.mkdir()
            
            return {'status': 'success', 'message': '文件权限正常'}
            
        except Exception as e:
            return {'status': 'error', 'message': f'权限检查失败: {str(e)}'}
    
    def check_disk_space(self):
        """检查磁盘空间"""
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            free_mb = free // (1024 * 1024)
            
            if free_mb < 100:  # 少于100MB
                return {'status': 'warning', 'message': f'磁盘空间不足: {free_mb}MB'}
            
            return {'status': 'success', 'message': f'磁盘空间充足: {free_mb}MB'}
            
        except Exception as e:
            return {'status': 'error', 'message': f'磁盘空间检查失败: {str(e)}'}
    
    def check_network(self):
        """检查网络连接"""
        try:
            import socket
            # 测试DNS解析
            socket.gethostbyname('smtp.qq.com')
            
            # 测试SMTP端口连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('smtp.qq.com', 587))
            sock.close()
            
            if result == 0:
                return {'status': 'success', 'message': 'SMTP网络连接正常'}
            else:
                return {'status': 'warning', 'message': 'SMTP端口连接失败，可能影响邮件发送'}
                
        except Exception as e:
            return {'status': 'warning', 'message': f'网络检查失败: {str(e)}'}
    
    def check_launchers(self):
        """检查启动器"""
        launchers = [
            '快速启动.vbs',
            '启动.vbs',
            '一键启动.vbs'
        ]
        
        working_launchers = []
        for launcher in launchers:
            if os.path.exists(launcher):
                working_launchers.append(launcher)
        
        if not working_launchers:
            return {'status': 'error', 'message': '没有可用的启动器'}
        
        return {'status': 'success', 'message': f'可用启动器: {", ".join(working_launchers)}'}
    
    def check_backups(self):
        """检查备份完整性"""
        if not self.backup_dir.exists():
            return {'status': 'warning', 'message': '备份目录不存在'}
        
        backup_files = list(self.backup_dir.glob("*.backup_*"))
        if len(backup_files) < 5:
            return {'status': 'warning', 'message': f'备份文件较少: {len(backup_files)}个'}
        
        return {'status': 'success', 'message': f'备份文件正常: {len(backup_files)}个'}

    def create_missing_database(self, db_path):
        """创建缺失的数据库"""
        db_dir = os.path.dirname(db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)

        conn = sqlite3.connect(db_path)

        # 根据数据库类型创建基础表结构
        if 'email_history' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS email_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT,
                    sender_email TEXT,
                    recipient_email TEXT,
                    subject TEXT,
                    body TEXT,
                    status TEXT
                )
            ''')
        elif 'recipient_quality' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS recipient_quality (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT UNIQUE,
                    quality_score REAL,
                    success_count INTEGER DEFAULT 0,
                    failure_count INTEGER DEFAULT 0,
                    last_updated TEXT
                )
            ''')
        elif 'anti_spam' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS anti_spam_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_email TEXT,
                    timestamp TEXT,
                    action TEXT,
                    reason TEXT
                )
            ''')
        elif 'user_settings' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS user_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    updated_at TEXT
                )
            ''')

        conn.commit()
        conn.close()
        self.logger.info(f"✅ 创建数据库: {db_path}")

    def create_default_config(self, config_file):
        """创建默认配置文件"""
        default_configs = {
            'all_features_config.json': {
                "auto_reply_monitoring": True,
                "recipient_quality_management": True,
                "anti_spam_protection": True,
                "qq_emergency_management": True,
                "smart_queue_system": True,
                "deep_coordination": True,
                "enabled_features_count": 6
            },
            'automation_workflow.json': {
                "auto_start_monitoring": False,
                "auto_queue_mode": True,
                "smart_scheduling": True,
                "emergency_protection": True
            },
            'auth_codes.json': {},
            'monitor_settings.json': {
                "check_interval": 30,
                "auto_reply_detection": True,
                "bounce_detection": True,
                "spam_detection": True
            },
            'startup_config.json': {
                "auto_restore_settings": True,
                "show_startup_tips": True,
                "check_updates": True,
                "backup_on_startup": True
            }
        }

        if config_file in default_configs:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_configs[config_file], f, indent=2, ensure_ascii=False)
            self.logger.info(f"✅ 创建默认配置: {config_file}")

    def auto_repair(self):
        """自动修复系统问题"""
        self.logger.info("🔧 开始自动修复...")

        repair_actions = [
            ("安装缺失依赖", self.repair_dependencies),
            ("修复数据库", self.repair_databases),
            ("恢复配置文件", self.repair_configs),
            ("创建备份", self.create_system_backup),
            ("修复启动器", self.repair_launchers)
        ]

        repair_results = {}
        for action_name, action_func in repair_actions:
            try:
                self.logger.info(f"🔧 执行修复: {action_name}")
                result = action_func()
                repair_results[action_name] = result
                if result['status'] == 'success':
                    self.logger.info(f"✅ {action_name} - 修复成功")
                else:
                    self.logger.warning(f"⚠️ {action_name} - {result['message']}")
            except Exception as e:
                self.logger.error(f"❌ {action_name} - 修复失败: {str(e)}")
                repair_results[action_name] = {'status': 'error', 'message': str(e)}

        return repair_results

    def repair_dependencies(self):
        """修复依赖问题"""
        try:
            # 检查并安装jieba
            try:
                import jieba
            except ImportError:
                self.logger.info("📦 安装jieba依赖...")
                subprocess.run([sys.executable, '-m', 'pip', 'install', 'jieba'],
                             check=True, capture_output=True)
                self.logger.info("✅ jieba安装成功")

            return {'status': 'success', 'message': '依赖修复完成'}

        except Exception as e:
            return {'status': 'error', 'message': f'依赖修复失败: {str(e)}'}

    def repair_databases(self):
        """修复数据库问题"""
        try:
            databases = [
                'email_history.db',
                'recipient_quality.db',
                'anti_spam.db',
                'qq_anti_spam.db',
                'user_data/user_settings.db'
            ]

            for db_path in databases:
                if not os.path.exists(db_path):
                    self.create_missing_database(db_path)

            return {'status': 'success', 'message': '数据库修复完成'}

        except Exception as e:
            return {'status': 'error', 'message': f'数据库修复失败: {str(e)}'}

    def repair_configs(self):
        """修复配置文件"""
        try:
            config_files = [
                'all_features_config.json',
                'automation_workflow.json',
                'auth_codes.json',
                'monitor_settings.json',
                'startup_config.json'
            ]

            for config_file in config_files:
                if not os.path.exists(config_file):
                    self.create_default_config(config_file)

            return {'status': 'success', 'message': '配置文件修复完成'}

        except Exception as e:
            return {'status': 'error', 'message': f'配置文件修复失败: {str(e)}'}

    def create_system_backup(self):
        """创建系统备份"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # 备份数据库
            db_files = ['email_history.db', 'recipient_quality.db', 'anti_spam.db', 'qq_anti_spam.db']
            for db_file in db_files:
                if os.path.exists(db_file):
                    backup_path = self.backup_dir / f"{db_file}.backup_{timestamp}"
                    shutil.copy2(db_file, backup_path)

            # 备份配置文件
            config_files = ['all_features_config.json', 'auth_codes.json', 'monitor_settings.json']
            for config_file in config_files:
                if os.path.exists(config_file):
                    backup_path = self.config_dir / f"{config_file}.backup"
                    shutil.copy2(config_file, backup_path)

            return {'status': 'success', 'message': f'系统备份完成: {timestamp}'}

        except Exception as e:
            return {'status': 'error', 'message': f'系统备份失败: {str(e)}'}

    def repair_launchers(self):
        """修复启动器"""
        try:
            # 检查快速启动.vbs是否存在且功能完整
            if not os.path.exists('快速启动.vbs'):
                self.create_enhanced_launcher()

            return {'status': 'success', 'message': '启动器修复完成'}

        except Exception as e:
            return {'status': 'error', 'message': f'启动器修复失败: {str(e)}'}

    def create_enhanced_launcher(self):
        """创建增强版启动器"""
        launcher_content = '''
' ================================================================
' 🏷️ 邮件系统 2.0 版本 - 增强稳定启动器
' 版本: 2.3 (全面稳定版)
' 功能: 全面检查和自动修复启动问题
' ================================================================
Option Explicit

Dim objShell, objFSO, currentDir
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")
currentDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' 主启动流程
Main()

Sub Main()
    ' 1. 系统环境检查
    If Not CheckSystemEnvironment() Then
        Exit Sub
    End If

    ' 2. 依赖检查和修复
    If Not CheckAndFixDependencies() Then
        Exit Sub
    End If

    ' 3. 启动系统
    LaunchSystem()
End Sub

Function CheckSystemEnvironment()
    ' 检查Python环境
    Dim pythonCheck
    pythonCheck = objShell.Run("python --version", 0, True)

    If pythonCheck <> 0 Then
        MsgBox "Python环境检查失败！" & vbCrLf & vbCrLf & _
               "请确保Python已正确安装并添加到PATH环境变量。", vbCritical, "环境错误"
        CheckSystemEnvironment = False
        Exit Function
    End If

    ' 检查核心文件
    If Not objFSO.FileExists(currentDir & "\\gui_main.py") Then
        MsgBox "核心文件gui_main.py缺失！", vbCritical, "文件错误"
        CheckSystemEnvironment = False
        Exit Function
    End If

    CheckSystemEnvironment = True
End Function

Function CheckAndFixDependencies()
    ' 运行系统稳定性检查
    Dim checkCommand
    checkCommand = "cmd /c ""cd /d """ & currentDir & """ && python 系统稳定性全面保障方案.py --auto-repair"""

    Dim result
    result = objShell.Run(checkCommand, 1, True)

    If result = 0 Then
        CheckAndFixDependencies = True
    Else
        MsgBox "系统稳定性检查失败！请手动检查系统状态。", vbExclamation, "检查失败"
        CheckAndFixDependencies = False
    End If
End Function

Sub LaunchSystem()
    ' 启动主系统
    Dim command
    command = "cmd /c ""cd /d """ & currentDir & """ && python gui_main.py"""
    objShell.Run command, 1, False
End Sub
'''

        with open('快速启动.vbs', 'w', encoding='utf-8') as f:
            f.write(launcher_content)

        self.logger.info("✅ 创建增强版启动器")

def main():
    """主函数"""
    guard = SystemStabilityGuard()

    if len(sys.argv) > 1 and sys.argv[1] == '--auto-repair':
        # 自动修复模式
        print("🔧 自动修复模式")
        repair_results = guard.auto_repair()

        success_count = sum(1 for r in repair_results.values() if r['status'] == 'success')
        total_count = len(repair_results)

        if success_count == total_count:
            print("✅ 所有修复操作成功完成")
            sys.exit(0)
        else:
            print(f"⚠️ 部分修复失败 ({success_count}/{total_count})")
            sys.exit(1)
    else:
        # 检查模式
        print("🔍 系统稳定性全面检查")
        results = guard.comprehensive_check()

        success_count = sum(1 for r in results.values() if r['status'] == 'success')
        total_count = len(results)

        print(f"\n📊 检查结果: {success_count}/{total_count} 项通过")

        if success_count < total_count:
            print("\n🔧 建议运行自动修复:")
            print("python 系统稳定性全面保障方案.py --auto-repair")

if __name__ == "__main__":
    main()
