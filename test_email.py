# -*- coding: utf-8 -*-
"""
邮件发送功能测试脚本
"""

from email_sender import EmailSender
import os

def test_basic_email():
    """测试基本邮件发送功能"""
    print("=" * 50)
    print("    邮件发送功能测试")
    print("=" * 50)
    
    # 获取测试参数
    sender_email = input("请输入您的QQ邮箱地址: ").strip()
    test_recipient = input("请输入测试收件人邮箱: ").strip()
    
    # 创建邮件发送器
    sender = EmailSender(sender_email)
    
    # 测试1: 发送纯文本邮件
    print("\n测试1: 发送纯文本邮件...")
    success = sender.send_email(
        to_emails=[test_recipient],
        subject="测试邮件 - 纯文本",
        body="这是一封测试邮件，用于验证邮件发送功能是否正常。\n\n发送时间: " + 
             str(__import__('datetime').datetime.now())
    )
    
    if success:
        print("✓ 纯文本邮件发送成功")
    else:
        print("✗ 纯文本邮件发送失败")
        return False
    
    # 测试2: 创建测试附件并发送
    print("\n测试2: 发送带附件的邮件...")
    
    # 创建测试文件
    test_file = "test_attachment.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write("这是一个测试附件文件。\n")
        f.write("创建时间: " + str(__import__('datetime').datetime.now()) + "\n")
        f.write("用于测试邮件附件发送功能。")
    
    success = sender.send_email(
        to_emails=[test_recipient],
        subject="测试邮件 - 带附件",
        body="这是一封带附件的测试邮件。\n\n请查看附件内容。",
        attachments=[test_file]
    )
    
    if success:
        print("✓ 带附件邮件发送成功")
    else:
        print("✗ 带附件邮件发送失败")
    
    # 清理测试文件
    if os.path.exists(test_file):
        os.remove(test_file)
        print(f"已清理测试文件: {test_file}")
    
    return success

def test_multiple_recipients():
    """测试多收件人发送"""
    print("\n测试3: 多收件人邮件发送...")
    
    sender_email = input("请输入您的QQ邮箱地址: ").strip()
    recipients_str = input("请输入多个测试收件人邮箱（用逗号分隔）: ").strip()
    recipients = [email.strip() for email in recipients_str.split(',') if email.strip()]
    
    if len(recipients) < 2:
        print("需要至少2个收件人来测试多收件人功能")
        return False
    
    sender = EmailSender(sender_email)
    
    success = sender.send_email(
        to_emails=recipients,
        subject="测试邮件 - 多收件人",
        body="这是一封发送给多个收件人的测试邮件。\n\n" +
             f"收件人列表: {', '.join(recipients)}"
    )
    
    if success:
        print("✓ 多收件人邮件发送成功")
    else:
        print("✗ 多收件人邮件发送失败")
    
    return success

if __name__ == "__main__":
    try:
        print("开始邮件发送功能测试...\n")
        
        # 基本功能测试
        if test_basic_email():
            print("\n基本功能测试通过!")
        else:
            print("\n基本功能测试失败!")
            exit(1)
        
        # 询问是否进行多收件人测试
        print("\n是否进行多收件人测试？(y/n): ", end="")
        if input().strip().lower() in ['y', 'yes', '是']:
            if test_multiple_recipients():
                print("\n多收件人测试通过!")
            else:
                print("\n多收件人测试失败!")
        
        print("\n" + "=" * 50)
        print("    测试完成")
        print("=" * 50)
        print("请检查收件人邮箱是否收到测试邮件")
        print("如果没有收到，请检查垃圾邮件文件夹")
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
