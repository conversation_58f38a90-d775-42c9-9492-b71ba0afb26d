#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🕐 定时发送管理器
完整的定时发送功能，集成所有现有功能并具备长期数据保存能力
"""

import sqlite3
import json
import datetime
import threading
import time
import logging
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from contextlib import contextmanager

@dataclass
class ScheduledTask:
    """定时任务数据类"""
    id: str
    name: str
    sender_email: str
    recipient_emails: str
    subject: str
    body: str
    attachments: List[str]
    scheduled_time: str  # ISO格式时间字符串
    send_mode: str  # 'fast', 'standard', 'safe'
    anti_spam_strategy: str  # 'conservative', 'moderate', 'aggressive'
    enable_monitoring: bool
    enable_quality_db: bool
    enable_emergency: bool
    enable_coordination: bool
    status: str  # 'pending', 'running', 'completed', 'failed', 'cancelled'
    created_time: str
    executed_time: Optional[str] = None
    result: Optional[Dict] = None
    retry_count: int = 0
    max_retries: int = 3

class ScheduleManager:
    """定时发送管理器"""
    
    def __init__(self):
        self.db_path = "schedule_manager.db"
        self.config_path = "schedule_config.json"
        self.logger = self._setup_logger()
        self.scheduler_thread = None
        self.is_running = False
        self.tasks_cache = {}
        
        # 初始化数据库
        self._init_database()
        
        # 加载配置
        self.config = self._load_config()
        
        # 启动调度器
        self.start_scheduler()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger('ScheduleManager')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.FileHandler('schedule_manager.log', encoding='utf-8')
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _init_database(self):
        """初始化数据库"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 创建定时任务表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS scheduled_tasks (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        sender_email TEXT NOT NULL,
                        recipient_emails TEXT NOT NULL,
                        subject TEXT NOT NULL,
                        body TEXT NOT NULL,
                        attachments TEXT,  -- JSON格式
                        scheduled_time TEXT NOT NULL,
                        send_mode TEXT DEFAULT 'standard',
                        anti_spam_strategy TEXT DEFAULT 'moderate',
                        enable_monitoring BOOLEAN DEFAULT 1,
                        enable_quality_db BOOLEAN DEFAULT 1,
                        enable_emergency BOOLEAN DEFAULT 1,
                        enable_coordination BOOLEAN DEFAULT 1,
                        status TEXT DEFAULT 'pending',
                        created_time TEXT NOT NULL,
                        executed_time TEXT,
                        result TEXT,  -- JSON格式
                        retry_count INTEGER DEFAULT 0,
                        max_retries INTEGER DEFAULT 3
                    )
                ''')
                
                # 创建执行历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS execution_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_id TEXT NOT NULL,
                        execution_time TEXT NOT NULL,
                        status TEXT NOT NULL,
                        result TEXT,  -- JSON格式
                        error_message TEXT,
                        FOREIGN KEY (task_id) REFERENCES scheduled_tasks (id)
                    )
                ''')
                
                # 创建最佳时间分析表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS optimal_time_analysis (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sender_email TEXT NOT NULL,
                        hour INTEGER NOT NULL,
                        day_of_week INTEGER NOT NULL,
                        success_rate REAL DEFAULT 0.0,
                        reply_rate REAL DEFAULT 0.0,
                        total_sent INTEGER DEFAULT 0,
                        last_updated TEXT NOT NULL
                    )
                ''')
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {str(e)}")
            raise
    
    @contextmanager
    def _get_db_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            conn = sqlite3.connect(
                self.db_path,
                timeout=30.0,
                check_same_thread=False
            )
            # 优化设置
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA busy_timeout=30000")
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
    
    def _load_config(self) -> Dict:
        """加载配置"""
        default_config = {
            "check_interval": 60,  # 检查间隔（秒）
            "max_concurrent_tasks": 3,  # 最大并发任务数
            "auto_retry": True,  # 自动重试
            "retry_interval": 300,  # 重试间隔（秒）
            "enable_optimal_time": True,  # 启用最佳时间分析
            "backup_interval": 3600,  # 备份间隔（秒）
            "cleanup_days": 30,  # 清理天数
            "timezone": "Asia/Shanghai"
        }
        
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # 合并默认配置
                    default_config.update(config)
            
            # 保存配置
            self._save_config(default_config)
            return default_config
            
        except Exception as e:
            self.logger.error(f"加载配置失败: {str(e)}")
            return default_config
    
    def _save_config(self, config: Dict):
        """保存配置"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存配置失败: {str(e)}")
    
    def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            return
        
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._scheduler_loop, daemon=True)
        self.scheduler_thread.start()
        self.logger.info("定时调度器已启动")
    
    def stop_scheduler(self):
        """停止调度器"""
        self.is_running = False
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
        self.logger.info("定时调度器已停止")

    def add_scheduled_task(self, task: ScheduledTask) -> bool:
        """添加定时任务"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO scheduled_tasks (
                        id, name, sender_email, recipient_emails, subject, body,
                        attachments, scheduled_time, send_mode, anti_spam_strategy,
                        enable_monitoring, enable_quality_db, enable_emergency,
                        enable_coordination, status, created_time, retry_count, max_retries
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task.id, task.name, task.sender_email, task.recipient_emails,
                    task.subject, task.body, json.dumps(task.attachments),
                    task.scheduled_time, task.send_mode, task.anti_spam_strategy,
                    task.enable_monitoring, task.enable_quality_db,
                    task.enable_emergency, task.enable_coordination,
                    task.status, task.created_time, task.retry_count, task.max_retries
                ))

                conn.commit()
                self.logger.info(f"定时任务已添加: {task.name} ({task.id})")
                return True

        except Exception as e:
            self.logger.error(f"添加定时任务失败: {str(e)}")
            return False

    def get_all_tasks(self) -> List[ScheduledTask]:
        """获取所有任务"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM scheduled_tasks ORDER BY scheduled_time DESC')
                rows = cursor.fetchall()

                return [self._row_to_task(row) for row in rows]

        except Exception as e:
            self.logger.error(f"获取任务列表失败: {str(e)}")
            return []

    def get_pending_tasks(self) -> List[ScheduledTask]:
        """获取待执行任务"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM scheduled_tasks
                    WHERE status = 'pending'
                    ORDER BY scheduled_time ASC
                ''')
                rows = cursor.fetchall()

                return [self._row_to_task(row) for row in rows]

        except Exception as e:
            self.logger.error(f"获取待执行任务失败: {str(e)}")
            return []

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE scheduled_tasks
                    SET status = 'cancelled'
                    WHERE id = ? AND status = 'pending'
                ''', (task_id,))

                if cursor.rowcount > 0:
                    conn.commit()
                    self.logger.info(f"任务已取消: {task_id}")
                    return True
                else:
                    return False

        except Exception as e:
            self.logger.error(f"取消任务失败: {str(e)}")
            return False

    def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                # 删除执行历史
                cursor.execute('DELETE FROM execution_history WHERE task_id = ?', (task_id,))

                # 删除任务
                cursor.execute('DELETE FROM scheduled_tasks WHERE id = ?', (task_id,))

                if cursor.rowcount > 0:
                    conn.commit()
                    self.logger.info(f"任务已删除: {task_id}")
                    return True
                else:
                    return False

        except Exception as e:
            self.logger.error(f"删除任务失败: {str(e)}")
            return False

    def get_optimal_send_time(self, sender_email: str) -> datetime.datetime:
        """获取最佳发送时间"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                # 查询历史最佳时间
                cursor.execute('''
                    SELECT hour, day_of_week,
                           (success_rate * 0.6 + reply_rate * 0.4) as score
                    FROM optimal_time_analysis
                    WHERE sender_email = ? AND total_sent >= 5
                    ORDER BY score DESC
                    LIMIT 1
                ''', (sender_email,))

                result = cursor.fetchone()

                if result:
                    best_hour, best_day, _ = result

                    # 计算下一个最佳时间
                    now = datetime.datetime.now()
                    target_time = now.replace(hour=best_hour, minute=0, second=0, microsecond=0)

                    # 如果今天的时间已过，选择明天
                    if target_time <= now:
                        target_time += datetime.timedelta(days=1)

                    return target_time
                else:
                    # 没有历史数据，使用默认最佳时间（上午10点）
                    now = datetime.datetime.now()
                    target_time = now.replace(hour=10, minute=0, second=0, microsecond=0)

                    if target_time <= now:
                        target_time += datetime.timedelta(days=1)

                    return target_time

        except Exception as e:
            self.logger.error(f"获取最佳发送时间失败: {str(e)}")
            # 返回默认时间
            now = datetime.datetime.now()
            return now.replace(hour=10, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
    
    def _scheduler_loop(self):
        """调度器主循环"""
        while self.is_running:
            try:
                self._check_and_execute_tasks()
                time.sleep(self.config.get("check_interval", 60))
            except Exception as e:
                self.logger.error(f"调度器循环出错: {str(e)}")
                time.sleep(60)  # 出错时等待1分钟
    
    def _check_and_execute_tasks(self):
        """检查并执行到期任务"""
        try:
            now = datetime.datetime.now()
            
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # 查找到期的待执行任务
                cursor.execute('''
                    SELECT * FROM scheduled_tasks 
                    WHERE status = 'pending' 
                    AND datetime(scheduled_time) <= datetime(?)
                    ORDER BY scheduled_time ASC
                ''', (now.isoformat(),))
                
                due_tasks = cursor.fetchall()
                
                for task_row in due_tasks:
                    task = self._row_to_task(task_row)
                    self._execute_task(task)
                    
        except Exception as e:
            self.logger.error(f"检查任务失败: {str(e)}")
    
    def _row_to_task(self, row) -> ScheduledTask:
        """将数据库行转换为任务对象"""
        return ScheduledTask(
            id=row[0],
            name=row[1],
            sender_email=row[2],
            recipient_emails=row[3],
            subject=row[4],
            body=row[5],
            attachments=json.loads(row[6]) if row[6] else [],
            scheduled_time=row[7],
            send_mode=row[8],
            anti_spam_strategy=row[9],
            enable_monitoring=bool(row[10]),
            enable_quality_db=bool(row[11]),
            enable_emergency=bool(row[12]),
            enable_coordination=bool(row[13]),
            status=row[14],
            created_time=row[15],
            executed_time=row[16],
            result=json.loads(row[17]) if row[17] else None,
            retry_count=row[18],
            max_retries=row[19]
        )
    
    def _execute_task(self, task: ScheduledTask):
        """执行定时任务"""
        try:
            self.logger.info(f"开始执行定时任务: {task.name} ({task.id})")
            
            # 更新任务状态为运行中
            self._update_task_status(task.id, 'running')
            
            # 在新线程中执行任务，避免阻塞调度器
            execution_thread = threading.Thread(
                target=self._execute_task_in_thread,
                args=(task,),
                daemon=True
            )
            execution_thread.start()
            
        except Exception as e:
            self.logger.error(f"启动任务执行失败: {str(e)}")
            self._update_task_status(task.id, 'failed', {'error': str(e)})
    
    def _execute_task_in_thread(self, task: ScheduledTask):
        """在线程中执行任务"""
        try:
            # 这里将集成所有现有功能
            result = self._execute_full_email_workflow(task)
            
            # 更新任务状态
            if result.get('success', False):
                self._update_task_status(task.id, 'completed', result)
                self.logger.info(f"任务执行成功: {task.name}")
            else:
                # 检查是否需要重试
                if task.retry_count < task.max_retries:
                    self._schedule_retry(task)
                else:
                    self._update_task_status(task.id, 'failed', result)
                    self.logger.error(f"任务执行失败: {task.name}")
                    
        except Exception as e:
            self.logger.error(f"任务执行异常: {str(e)}")
            if task.retry_count < task.max_retries:
                self._schedule_retry(task)
            else:
                self._update_task_status(task.id, 'failed', {'error': str(e)})
    
    def _execute_full_email_workflow(self, task: ScheduledTask) -> Dict:
        """执行完整的邮件工作流程"""
        try:
            result = {
                'success': False,
                'sent_count': 0,
                'failed_count': 0,
                'monitoring_started': False,
                'quality_updated': False,
                'emergency_checked': False,
                'coordination_applied': False,
                'details': []
            }

            # 1. 应用反垃圾策略
            if task.anti_spam_strategy:
                self._apply_anti_spam_strategy(task)
                result['details'].append("✅ 反垃圾策略已应用")

            # 2. 发送邮件
            send_result = self._send_emails(task)
            result.update(send_result)

            if not send_result['success']:
                return result

            # 3. 启动自动回复监控
            if task.enable_monitoring:
                monitoring_result = self._start_monitoring(task)
                result['monitoring_started'] = monitoring_result
                if monitoring_result:
                    result['details'].append("✅ 自动回复监控已启动")

            # 4. 更新质量数据库
            if task.enable_quality_db:
                quality_result = self._update_quality_database(task)
                result['quality_updated'] = quality_result
                if quality_result:
                    result['details'].append("✅ 质量数据库已更新")

            # 5. 检查应急系统
            if task.enable_emergency:
                emergency_result = self._check_emergency_system(task)
                result['emergency_checked'] = emergency_result
                if emergency_result:
                    result['details'].append("✅ 应急系统已检查")

            # 6. 应用深度协调
            if task.enable_coordination:
                coordination_result = self._apply_coordination(task)
                result['coordination_applied'] = coordination_result
                if coordination_result:
                    result['details'].append("✅ 深度协调已应用")

            # 7. 记录最佳时间数据
            self._record_optimal_time_data(task, result)

            result['success'] = True
            result['details'].append(f"🎉 定时任务执行完成，共发送 {result['sent_count']} 封邮件")

            return result

        except Exception as e:
            self.logger.error(f"执行邮件工作流程失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'details': [f"❌ 执行失败: {str(e)}"]
            }

    def _apply_anti_spam_strategy(self, task: ScheduledTask):
        """应用反垃圾策略"""
        try:
            from anti_spam_manager import AntiSpamManager

            spam_manager = AntiSpamManager()

            # 根据策略调整发送参数
            if task.anti_spam_strategy == 'conservative':
                # 保守策略：低频率，小批次
                task.send_mode = 'safe'
            elif task.anti_spam_strategy == 'aggressive':
                # 激进策略：高频率，大批次
                task.send_mode = 'fast'
            else:
                # 适中策略：标准模式
                task.send_mode = 'standard'

            # 检查发送权限
            recipient_count = len(task.recipient_emails.split('\n'))
            permission = spam_manager.check_sending_permission(task.sender_email, recipient_count)

            if not permission.get('can_send', True):
                raise Exception(f"反垃圾检查失败: {permission.get('reason', '未知原因')}")

            self.logger.info(f"反垃圾策略应用成功: {task.anti_spam_strategy}")

        except Exception as e:
            self.logger.error(f"应用反垃圾策略失败: {str(e)}")
            raise

    def _send_emails(self, task: ScheduledTask) -> Dict:
        """发送邮件"""
        try:
            from email_sender import EmailSender

            sender = EmailSender(task.sender_email)

            # 解析收件人列表
            recipients = [email.strip() for email in task.recipient_emails.replace(',', '\n').split('\n') if email.strip()]

            if not recipients:
                raise Exception("没有有效的收件人")

            # 批量发送
            send_result = sender.send_batch_emails(
                email_list=[{
                    'to_emails': recipients,
                    'subject': task.subject,
                    'body': task.body,
                    'attachments': task.attachments
                }],
                send_mode=task.send_mode
            )

            return {
                'success': send_result.get('success', False),
                'sent_count': send_result.get('success_count', 0),
                'failed_count': send_result.get('failed_count', 0)
            }

        except Exception as e:
            self.logger.error(f"发送邮件失败: {str(e)}")
            return {
                'success': False,
                'sent_count': 0,
                'failed_count': len(task.recipient_emails.split('\n')),
                'error': str(e)
            }

    def _start_monitoring(self, task: ScheduledTask) -> bool:
        """启动自动回复监控"""
        try:
            from email_receiver import EmailReceiver

            # 获取授权码
            auth_codes = self._load_auth_codes()
            auth_info = auth_codes.get(task.sender_email)

            if not auth_info:
                self.logger.warning(f"未找到 {task.sender_email} 的授权码，跳过监控")
                return False

            auth_code = auth_info.get('auth_code', '') if isinstance(auth_info, dict) else auth_info

            if not auth_code:
                self.logger.warning(f"{task.sender_email} 的授权码为空，跳过监控")
                return False

            # 启动监控
            receiver = EmailReceiver(task.sender_email, auth_code)

            # 在后台线程中启动监控
            def monitor_thread():
                try:
                    # 监控2小时
                    receiver.monitor_replies_continuously(check_interval=600, max_checks=12)
                except Exception as e:
                    self.logger.error(f"监控过程中出错: {str(e)}")

            monitoring_thread = threading.Thread(target=monitor_thread, daemon=True)
            monitoring_thread.start()

            self.logger.info(f"自动回复监控已启动: {task.sender_email}")
            return True

        except Exception as e:
            self.logger.error(f"启动监控失败: {str(e)}")
            return False

    def _load_auth_codes(self) -> Dict:
        """加载授权码"""
        try:
            if os.path.exists('auth_codes.json'):
                with open('auth_codes.json', 'r', encoding='utf-8') as f:
                    return json.load(f)
            return {}
        except Exception as e:
            self.logger.error(f"加载授权码失败: {str(e)}")
            return {}

    def _update_quality_database(self, task: ScheduledTask) -> bool:
        """更新质量数据库"""
        try:
            from recipient_quality_manager import RecipientQualityManager

            quality_manager = RecipientQualityManager()

            # 解析收件人列表
            recipients = [email.strip() for email in task.recipient_emails.replace(',', '\n').split('\n') if email.strip()]

            # 为每个收件人添加发送记录
            for recipient in recipients:
                quality_manager.add_recipient(
                    email=recipient,
                    source=f"定时任务_{task.name}",
                    tags=[f"定时发送", task.send_mode, task.anti_spam_strategy]
                )

            self.logger.info(f"质量数据库已更新，添加 {len(recipients)} 个收件人")
            return True

        except Exception as e:
            self.logger.error(f"更新质量数据库失败: {str(e)}")
            return False

    def _check_emergency_system(self, task: ScheduledTask) -> bool:
        """检查应急系统"""
        try:
            from qq_email_anti_spam import QQEmailAntiSpamManager

            emergency_manager = QQEmailAntiSpamManager(task.sender_email)

            # 检查是否需要激活应急模式
            status = emergency_manager.check_emergency_status()

            if status.get('should_activate', False):
                emergency_manager.activate_emergency_mode()
                self.logger.info(f"应急模式已激活: {task.sender_email}")

            return True

        except Exception as e:
            self.logger.error(f"检查应急系统失败: {str(e)}")
            return False

    def _apply_coordination(self, task: ScheduledTask) -> bool:
        """应用深度协调"""
        try:
            # 尝试导入深度协调系统
            try:
                from 深度系统协调实现 import get_coordinator
                coordinator = get_coordinator()

                # 生成协调报告
                report = coordinator.generate_system_report(task.sender_email)

                if report:
                    self.logger.info(f"深度协调已应用: {task.sender_email}")
                    return True
                else:
                    self.logger.warning("深度协调报告生成失败")
                    return False

            except ImportError:
                self.logger.warning("深度协调系统不可用")
                return False

        except Exception as e:
            self.logger.error(f"应用深度协调失败: {str(e)}")
            return False

    def _record_optimal_time_data(self, task: ScheduledTask, result: Dict):
        """记录最佳时间数据"""
        try:
            executed_time = datetime.datetime.fromisoformat(task.scheduled_time)
            hour = executed_time.hour
            day_of_week = executed_time.weekday()

            success_rate = result['sent_count'] / (result['sent_count'] + result['failed_count']) if (result['sent_count'] + result['failed_count']) > 0 else 0

            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                # 检查是否已有记录
                cursor.execute('''
                    SELECT id, total_sent, success_rate FROM optimal_time_analysis
                    WHERE sender_email = ? AND hour = ? AND day_of_week = ?
                ''', (task.sender_email, hour, day_of_week))

                existing = cursor.fetchone()

                if existing:
                    # 更新现有记录
                    old_total = existing[1]
                    old_success_rate = existing[2]

                    new_total = old_total + result['sent_count']
                    new_success_rate = (old_success_rate * old_total + success_rate * result['sent_count']) / new_total

                    cursor.execute('''
                        UPDATE optimal_time_analysis
                        SET success_rate = ?, total_sent = ?, last_updated = ?
                        WHERE id = ?
                    ''', (new_success_rate, new_total, datetime.datetime.now().isoformat(), existing[0]))
                else:
                    # 插入新记录
                    cursor.execute('''
                        INSERT INTO optimal_time_analysis
                        (sender_email, hour, day_of_week, success_rate, reply_rate, total_sent, last_updated)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (task.sender_email, hour, day_of_week, success_rate, 0.0, result['sent_count'], datetime.datetime.now().isoformat()))

                conn.commit()
                self.logger.info(f"最佳时间数据已记录: {hour}时, 星期{day_of_week}")

        except Exception as e:
            self.logger.error(f"记录最佳时间数据失败: {str(e)}")

    def get_task_statistics(self) -> Dict:
        """获取任务统计信息"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                # 总任务数
                cursor.execute('SELECT COUNT(*) FROM scheduled_tasks')
                total_tasks = cursor.fetchone()[0]

                # 各状态任务数
                cursor.execute('''
                    SELECT status, COUNT(*) FROM scheduled_tasks
                    GROUP BY status
                ''')
                status_counts = dict(cursor.fetchall())

                # 今日执行任务数
                today = datetime.date.today().isoformat()
                cursor.execute('''
                    SELECT COUNT(*) FROM scheduled_tasks
                    WHERE DATE(executed_time) = ?
                ''', (today,))
                today_executed = cursor.fetchone()[0]

                # 成功率
                cursor.execute('''
                    SELECT
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COUNT(CASE WHEN status IN ('completed', 'failed') THEN 1 END) as total_finished
                    FROM scheduled_tasks
                ''')
                success_data = cursor.fetchone()
                success_rate = (success_data[0] / success_data[1] * 100) if success_data[1] > 0 else 0

                return {
                    'total_tasks': total_tasks,
                    'status_counts': status_counts,
                    'today_executed': today_executed,
                    'success_rate': round(success_rate, 2),
                    'pending': status_counts.get('pending', 0),
                    'running': status_counts.get('running', 0),
                    'completed': status_counts.get('completed', 0),
                    'failed': status_counts.get('failed', 0),
                    'cancelled': status_counts.get('cancelled', 0)
                }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {str(e)}")
            return {}

    def cleanup_old_tasks(self, days: int = None):
        """清理旧任务"""
        try:
            if days is None:
                days = self.config.get("cleanup_days", 30)

            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)

            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                # 删除旧的执行历史
                cursor.execute('''
                    DELETE FROM execution_history
                    WHERE datetime(execution_time) < datetime(?)
                ''', (cutoff_date.isoformat(),))

                history_deleted = cursor.rowcount

                # 删除旧的已完成/失败任务
                cursor.execute('''
                    DELETE FROM scheduled_tasks
                    WHERE status IN ('completed', 'failed', 'cancelled')
                    AND datetime(created_time) < datetime(?)
                ''', (cutoff_date.isoformat(),))

                tasks_deleted = cursor.rowcount

                conn.commit()

                self.logger.info(f"清理完成: 删除 {tasks_deleted} 个任务, {history_deleted} 条历史记录")

                return {
                    'tasks_deleted': tasks_deleted,
                    'history_deleted': history_deleted
                }

        except Exception as e:
            self.logger.error(f"清理旧任务失败: {str(e)}")
            return {'tasks_deleted': 0, 'history_deleted': 0}

    def export_tasks(self, status_filter: str = None) -> List[Dict]:
        """导出任务数据"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                if status_filter:
                    cursor.execute('''
                        SELECT * FROM scheduled_tasks
                        WHERE status = ?
                        ORDER BY created_time DESC
                    ''', (status_filter,))
                else:
                    cursor.execute('''
                        SELECT * FROM scheduled_tasks
                        ORDER BY created_time DESC
                    ''')

                rows = cursor.fetchall()
                tasks = [self._row_to_task(row) for row in rows]

                # 转换为字典格式
                return [asdict(task) for task in tasks]

        except Exception as e:
            self.logger.error(f"导出任务失败: {str(e)}")
            return []

    def get_execution_history(self, task_id: str = None) -> List[Dict]:
        """获取执行历史"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                if task_id:
                    cursor.execute('''
                        SELECT * FROM execution_history
                        WHERE task_id = ?
                        ORDER BY execution_time DESC
                    ''', (task_id,))
                else:
                    cursor.execute('''
                        SELECT * FROM execution_history
                        ORDER BY execution_time DESC
                        LIMIT 100
                    ''')

                rows = cursor.fetchall()

                return [{
                    'id': row[0],
                    'task_id': row[1],
                    'execution_time': row[2],
                    'status': row[3],
                    'result': json.loads(row[4]) if row[4] else None,
                    'error_message': row[5]
                } for row in rows]

        except Exception as e:
            self.logger.error(f"获取执行历史失败: {str(e)}")
            return []

    def _scheduler_loop(self):
        """调度器主循环"""
        while self.is_running:
            try:
                self._check_and_execute_tasks()
                time.sleep(self.config.get("check_interval", 60))
            except Exception as e:
                self.logger.error(f"调度器循环出错: {str(e)}")
                time.sleep(60)  # 出错时等待1分钟

    def _check_and_execute_tasks(self):
        """检查并执行到期任务"""
        try:
            now = datetime.datetime.now()

            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                # 查找到期的待执行任务
                cursor.execute('''
                    SELECT * FROM scheduled_tasks
                    WHERE status = 'pending'
                    AND datetime(scheduled_time) <= datetime(?)
                    ORDER BY scheduled_time ASC
                ''', (now.isoformat(),))

                due_tasks = cursor.fetchall()

                for task_row in due_tasks:
                    task = self._row_to_task(task_row)
                    self._execute_task(task)

        except Exception as e:
            self.logger.error(f"检查任务失败: {str(e)}")

    def _row_to_task(self, row) -> ScheduledTask:
        """将数据库行转换为任务对象"""
        return ScheduledTask(
            id=row[0],
            name=row[1],
            sender_email=row[2],
            recipient_emails=row[3],
            subject=row[4],
            body=row[5],
            attachments=json.loads(row[6]) if row[6] else [],
            scheduled_time=row[7],
            send_mode=row[8],
            anti_spam_strategy=row[9],
            enable_monitoring=bool(row[10]),
            enable_quality_db=bool(row[11]),
            enable_emergency=bool(row[12]),
            enable_coordination=bool(row[13]),
            status=row[14],
            created_time=row[15],
            executed_time=row[16],
            result=json.loads(row[17]) if row[17] else None,
            retry_count=row[18],
            max_retries=row[19]
        )

    def _execute_task(self, task: ScheduledTask):
        """执行定时任务"""
        try:
            self.logger.info(f"开始执行定时任务: {task.name} ({task.id})")

            # 更新任务状态为运行中
            self._update_task_status(task.id, 'running')

            # 在新线程中执行任务，避免阻塞调度器
            execution_thread = threading.Thread(
                target=self._execute_task_in_thread,
                args=(task,),
                daemon=True
            )
            execution_thread.start()

        except Exception as e:
            self.logger.error(f"启动任务执行失败: {str(e)}")
            self._update_task_status(task.id, 'failed', {'error': str(e)})

    def _execute_task_in_thread(self, task: ScheduledTask):
        """在线程中执行任务"""
        try:
            # 执行完整的邮件工作流程
            result = self._execute_full_email_workflow(task)

            # 更新任务状态
            if result.get('success', False):
                self._update_task_status(task.id, 'completed', result)
                self.logger.info(f"任务执行成功: {task.name}")
            else:
                # 检查是否需要重试
                if task.retry_count < task.max_retries:
                    self._schedule_retry(task)
                else:
                    self._update_task_status(task.id, 'failed', result)
                    self.logger.error(f"任务执行失败: {task.name}")

        except Exception as e:
            self.logger.error(f"任务执行异常: {str(e)}")
            if task.retry_count < task.max_retries:
                self._schedule_retry(task)
            else:
                self._update_task_status(task.id, 'failed', {'error': str(e)})

    def _update_task_status(self, task_id: str, status: str, result: Dict = None):
        """更新任务状态"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                executed_time = datetime.datetime.now().isoformat() if status in ['completed', 'failed'] else None
                result_json = json.dumps(result, ensure_ascii=False) if result else None

                cursor.execute('''
                    UPDATE scheduled_tasks
                    SET status = ?, executed_time = ?, result = ?
                    WHERE id = ?
                ''', (status, executed_time, result_json, task_id))

                # 记录执行历史
                if status in ['completed', 'failed']:
                    cursor.execute('''
                        INSERT INTO execution_history
                        (task_id, execution_time, status, result, error_message)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        task_id,
                        datetime.datetime.now().isoformat(),
                        status,
                        result_json,
                        result.get('error') if result else None
                    ))

                conn.commit()

        except Exception as e:
            self.logger.error(f"更新任务状态失败: {str(e)}")

    def _schedule_retry(self, task: ScheduledTask):
        """安排重试"""
        try:
            retry_time = datetime.datetime.now() + datetime.timedelta(
                seconds=self.config.get("retry_interval", 300)
            )

            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE scheduled_tasks
                    SET status = 'pending',
                        scheduled_time = ?,
                        retry_count = retry_count + 1
                    WHERE id = ?
                ''', (retry_time.isoformat(), task.id))
                conn.commit()

            self.logger.info(f"任务 {task.name} 已安排重试，时间: {retry_time}")

        except Exception as e:
            self.logger.error(f"安排重试失败: {str(e)}")

    def get_task_statistics(self) -> Dict:
        """获取任务统计信息"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                # 总任务数
                cursor.execute('SELECT COUNT(*) FROM scheduled_tasks')
                total_tasks = cursor.fetchone()[0]

                # 各状态任务数
                cursor.execute('''
                    SELECT status, COUNT(*) FROM scheduled_tasks
                    GROUP BY status
                ''')
                status_counts = dict(cursor.fetchall())

                # 今日执行任务数
                today = datetime.date.today().isoformat()
                cursor.execute('''
                    SELECT COUNT(*) FROM scheduled_tasks
                    WHERE DATE(executed_time) = ?
                ''', (today,))
                today_executed = cursor.fetchone()[0]

                # 成功率
                cursor.execute('''
                    SELECT
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COUNT(CASE WHEN status IN ('completed', 'failed') THEN 1 END) as total_finished
                    FROM scheduled_tasks
                ''')
                success_data = cursor.fetchone()
                success_rate = (success_data[0] / success_data[1] * 100) if success_data[1] > 0 else 0

                return {
                    'total_tasks': total_tasks,
                    'status_counts': status_counts,
                    'today_executed': today_executed,
                    'success_rate': round(success_rate, 2),
                    'pending': status_counts.get('pending', 0),
                    'running': status_counts.get('running', 0),
                    'completed': status_counts.get('completed', 0),
                    'failed': status_counts.get('failed', 0),
                    'cancelled': status_counts.get('cancelled', 0)
                }

        except Exception as e:
            self.logger.error(f"获取统计信息失败: {str(e)}")
            return {}

    def cleanup_old_tasks(self, days: int = None):
        """清理旧任务"""
        try:
            if days is None:
                days = self.config.get("cleanup_days", 30)

            cutoff_date = datetime.datetime.now() - datetime.timedelta(days=days)

            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                # 删除旧的执行历史
                cursor.execute('''
                    DELETE FROM execution_history
                    WHERE datetime(execution_time) < datetime(?)
                ''', (cutoff_date.isoformat(),))

                history_deleted = cursor.rowcount

                # 删除旧的已完成/失败任务
                cursor.execute('''
                    DELETE FROM scheduled_tasks
                    WHERE status IN ('completed', 'failed', 'cancelled')
                    AND datetime(created_time) < datetime(?)
                ''', (cutoff_date.isoformat(),))

                tasks_deleted = cursor.rowcount

                conn.commit()

                self.logger.info(f"清理完成: 删除 {tasks_deleted} 个任务, {history_deleted} 条历史记录")

                return {
                    'tasks_deleted': tasks_deleted,
                    'history_deleted': history_deleted
                }

        except Exception as e:
            self.logger.error(f"清理旧任务失败: {str(e)}")
            return {'tasks_deleted': 0, 'history_deleted': 0}

    def export_tasks(self, status_filter: str = None) -> List[Dict]:
        """导出任务数据"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                if status_filter:
                    cursor.execute('''
                        SELECT * FROM scheduled_tasks
                        WHERE status = ?
                        ORDER BY created_time DESC
                    ''', (status_filter,))
                else:
                    cursor.execute('''
                        SELECT * FROM scheduled_tasks
                        ORDER BY created_time DESC
                    ''')

                rows = cursor.fetchall()
                tasks = [self._row_to_task(row) for row in rows]

                # 转换为字典格式
                return [asdict(task) for task in tasks]

        except Exception as e:
            self.logger.error(f"导出任务失败: {str(e)}")
            return []

    def get_execution_history(self, task_id: str = None) -> List[Dict]:
        """获取执行历史"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()

                if task_id:
                    cursor.execute('''
                        SELECT * FROM execution_history
                        WHERE task_id = ?
                        ORDER BY execution_time DESC
                    ''', (task_id,))
                else:
                    cursor.execute('''
                        SELECT * FROM execution_history
                        ORDER BY execution_time DESC
                        LIMIT 100
                    ''')

                rows = cursor.fetchall()

                return [{
                    'id': row[0],
                    'task_id': row[1],
                    'execution_time': row[2],
                    'status': row[3],
                    'result': json.loads(row[4]) if row[4] else None,
                    'error_message': row[5]
                } for row in rows]

        except Exception as e:
            self.logger.error(f"获取执行历史失败: {str(e)}")
            return []

    def get_all_scheduled_tasks(self) -> List[ScheduledTask]:
        """获取所有定时任务 - 修复方法名称"""
        return self.get_all_tasks()

    def get_scheduled_tasks_by_status(self, status: str) -> List[ScheduledTask]:
        """根据状态获取定时任务"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT * FROM scheduled_tasks
                    WHERE status = ?
                    ORDER BY scheduled_time DESC
                ''', (status,))
                rows = cursor.fetchall()

                return [self._row_to_task(row) for row in rows]

        except Exception as e:
            self.logger.error(f"根据状态获取任务失败: {str(e)}")
            return []
    
    def _update_task_status(self, task_id: str, status: str, result: Dict = None):
        """更新任务状态"""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                executed_time = datetime.datetime.now().isoformat() if status in ['completed', 'failed'] else None
                result_json = json.dumps(result, ensure_ascii=False) if result else None
                
                cursor.execute('''
                    UPDATE scheduled_tasks 
                    SET status = ?, executed_time = ?, result = ?
                    WHERE id = ?
                ''', (status, executed_time, result_json, task_id))
                
                # 记录执行历史
                if status in ['completed', 'failed']:
                    cursor.execute('''
                        INSERT INTO execution_history 
                        (task_id, execution_time, status, result, error_message)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        task_id,
                        datetime.datetime.now().isoformat(),
                        status,
                        result_json,
                        result.get('error') if result else None
                    ))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"更新任务状态失败: {str(e)}")
    
    def _schedule_retry(self, task: ScheduledTask):
        """安排重试"""
        try:
            retry_time = datetime.datetime.now() + datetime.timedelta(
                seconds=self.config.get("retry_interval", 300)
            )
            
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE scheduled_tasks 
                    SET status = 'pending', 
                        scheduled_time = ?, 
                        retry_count = retry_count + 1
                    WHERE id = ?
                ''', (retry_time.isoformat(), task.id))
                conn.commit()
                
            self.logger.info(f"任务 {task.name} 已安排重试，时间: {retry_time}")
            
        except Exception as e:
            self.logger.error(f"安排重试失败: {str(e)}")
