# 📧 邮件撤回功能使用指南

## ✅ 新增功能概览

### 1. 发送控制功能
- ✅ **停止发送按钮** - 可随时停止正在进行的发送
- ✅ **暂停发送按钮** - 预留功能（开发中）
- ✅ **实时停止检查** - 每0.5秒检查停止指令

### 2. 邮件撤回功能
- ✅ **发送撤回邮件** - 向收件人发送撤回通知
- ✅ **选择性撤回** - 可选择特定邮件进行撤回
- ✅ **一键撤回全部** - 向所有收件人发送撤回通知
- ✅ **发送历史记录** - 查看所有已发送的邮件
- ✅ **导出发送记录** - 将发送历史导出为CSV文件

## 🔧 功能详细说明

### 邮件撤回的工作原理

**重要说明**：
- ❌ **无法真正删除已发送的邮件** - 这是邮件系统的基本特性
- ✅ **发送撤回通知邮件** - 向收件人发送新的撤回通知
- ✅ **告知收件人忽略** - 通过撤回邮件告知收件人忽略之前的邮件

### 撤回功能的使用场景

1. **内容错误** - 发现邮件内容有误
2. **收件人错误** - 发送给了错误的收件人
3. **时机不当** - 发送时机不合适
4. **信息过时** - 邮件信息已过时

## 📋 操作步骤

### 第一步：发送邮件后启用撤回功能
1. 发送邮件完成后，"发送撤回邮件"按钮会自动启用
2. 程序会自动记录所有已发送的邮件信息
3. 包括：收件人、主题、内容、发送时间、批次信息

### 第二步：选择撤回方式

#### 方式一：选择性撤回
1. 点击"发送撤回邮件"按钮
2. 在弹出的窗口中查看已发送的邮件列表
3. 选择需要撤回的特定邮件（可多选）
4. 编辑撤回邮件的主题和内容
5. 点击"撤回选中邮件"

#### 方式二：一键撤回全部
1. 点击"发送撤回邮件"按钮
2. 编辑撤回邮件的主题和内容
3. 点击"一键撤回全部"
4. 确认后向所有收件人发送撤回通知

### 第三步：查看撤回结果
1. 观察实时日志中的撤回发送状态
2. 系统会显示成功和失败的数量
3. 撤回邮件使用较短的发送间隔（2秒）

## 🎯 撤回邮件模板

### 默认撤回内容
```
主题：邮件撤回通知

内容：
尊敬的收件人：

我之前发送给您的邮件需要撤回，请忽略该邮件内容。

如有任何疑问，请联系我。

谢谢！
```

### 自定义撤回内容建议

**正式撤回**：
```
主题：关于XXX邮件的撤回通知

内容：
尊敬的XXX：

由于[具体原因]，我需要撤回之前发送的关于"[原邮件主题]"的邮件。

请忽略该邮件内容，正确信息我会另行通知。

给您带来的不便，深表歉意。

此致
敬礼！

[您的姓名]
[联系方式]
```

**紧急撤回**：
```
主题：紧急撤回 - 请忽略之前邮件

内容：
紧急通知：

请立即忽略我刚才发送的邮件，该邮件包含错误信息。

正确信息将稍后发送。

如有疑问请立即联系我：[电话号码]

谢谢配合！
```

## 📊 发送历史管理

### 查看发送历史
1. 点击"查看发送记录"按钮
2. 查看所有已发送邮件的详细信息
3. 包括序号、时间、收件人、主题、批次

### 导出发送记录
1. 在发送历史窗口中点击"导出历史"
2. 选择保存位置和文件名
3. 生成CSV格式的发送记录文件
4. 可用Excel等软件打开查看

### 清空发送记录
1. 点击"清空发送记录"按钮
2. 确认后清空所有发送历史
3. 注意：此操作不可恢复

## ⚠️ 重要注意事项

### 撤回的局限性
1. **无法真正删除** - 已发送的邮件无法从收件人邮箱中删除
2. **依赖收件人配合** - 需要收件人看到撤回通知并配合忽略
3. **时效性要求** - 撤回通知应尽快发送
4. **可能增加困扰** - 频繁撤回可能给收件人造成困扰

### 最佳实践建议
1. **发送前仔细检查** - 减少撤回的需要
2. **及时撤回** - 发现问题后立即撤回
3. **说明原因** - 在撤回邮件中说明撤回原因
4. **后续跟进** - 必要时通过其他方式确认收件人已知晓

## 🔧 故障排除

### 问题1：撤回按钮不可用
**原因**：没有已发送的邮件记录
**解决**：先发送邮件，完成后撤回按钮会自动启用

### 问题2：撤回邮件发送失败
**原因**：网络问题或邮箱设置问题
**解决**：
1. 检查网络连接
2. 确认邮箱授权码正确
3. 查看实时日志了解具体错误

### 问题3：收件人没有收到撤回邮件
**原因**：可能进入垃圾箱或邮箱问题
**解决**：
1. 提醒收件人检查垃圾箱
2. 通过其他方式（电话、微信等）通知
3. 使用不同的邮件主题重新发送

## 💡 使用技巧

### 预防性措施
1. **测试发送** - 重要邮件先发给自己测试
2. **分批发送** - 大批量邮件分批发送，便于控制
3. **内容检查** - 发送前多次检查内容和收件人
4. **时间选择** - 选择合适的发送时间

### 高效撤回
1. **快速响应** - 发现问题立即停止发送
2. **批量撤回** - 使用一键撤回功能提高效率
3. **记录管理** - 定期查看和管理发送记录
4. **备份记录** - 重要发送记录及时导出备份

## 📞 技术支持

### 功能相关问题
- 查看程序实时日志
- 参考"立即行动指南.md"
- 使用停止发送功能及时止损

### 邮件发送问题
- 检查邮箱授权码设置
- 验证网络连接状态
- 参考"批量邮件发送真实解决方案.md"

## 🎯 总结

撤回功能虽然无法真正删除已发送的邮件，但提供了一种补救机制：

✅ **及时通知** - 快速向收件人发送撤回通知
✅ **灵活选择** - 可选择特定邮件或全部撤回
✅ **记录管理** - 完整的发送历史记录
✅ **操作简便** - 一键操作，简单高效

**记住**：最好的撤回是不需要撤回 - 发送前仔细检查是最重要的！ 🚀
