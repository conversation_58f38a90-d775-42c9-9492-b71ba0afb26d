#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔄 系统重启恢复机制
确保系统重启或电脑重启后能够自动恢复到之前的状态
包括：配置恢复、数据恢复、环境检查、自动修复等
"""

import os
import sys
import json
import sqlite3
import shutil
import time
from pathlib import Path
import logging
from datetime import datetime, timedelta
import winreg
import subprocess

class SystemRecoveryManager:
    """系统恢复管理器"""
    
    def __init__(self):
        self.setup_logging()
        self.base_dir = Path(__file__).parent
        self.recovery_dir = self.base_dir / "recovery_data"
        self.state_file = self.recovery_dir / "system_state.json"
        
        # 确保恢复目录存在
        self.recovery_dir.mkdir(exist_ok=True)
        
    def setup_logging(self):
        """设置日志"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(f"logs/recovery_{datetime.now().strftime('%Y%m%d')}.log", encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def save_system_state(self):
        """保存系统状态"""
        try:
            self.logger.info("💾 保存系统状态...")
            
            state = {
                "timestamp": datetime.now().isoformat(),
                "python_path": sys.executable,
                "working_directory": str(self.base_dir),
                "installed_packages": self.get_installed_packages(),
                "config_files": self.get_config_files_state(),
                "database_files": self.get_database_files_state(),
                "user_settings": self.get_user_settings(),
                "system_info": self.get_system_info()
            }
            
            with open(self.state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
            
            self.logger.info("✅ 系统状态保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 保存系统状态失败: {str(e)}")
            return False
    
    def load_system_state(self):
        """加载系统状态"""
        try:
            if not self.state_file.exists():
                self.logger.warning("⚠️ 系统状态文件不存在")
                return None
            
            with open(self.state_file, 'r', encoding='utf-8') as f:
                state = json.load(f)
            
            self.logger.info("✅ 系统状态加载成功")
            return state
            
        except Exception as e:
            self.logger.error(f"❌ 加载系统状态失败: {str(e)}")
            return None
    
    def check_system_changes(self):
        """检查系统变化"""
        try:
            self.logger.info("🔍 检查系统变化...")
            
            previous_state = self.load_system_state()
            if not previous_state:
                return {"status": "no_previous_state"}
            
            current_state = {
                "python_path": sys.executable,
                "working_directory": str(self.base_dir),
                "installed_packages": self.get_installed_packages(),
                "config_files": self.get_config_files_state(),
                "database_files": self.get_database_files_state()
            }
            
            changes = {}
            
            # 检查Python路径变化
            if previous_state["python_path"] != current_state["python_path"]:
                changes["python_path"] = {
                    "old": previous_state["python_path"],
                    "new": current_state["python_path"]
                }
            
            # 检查工作目录变化
            if previous_state["working_directory"] != current_state["working_directory"]:
                changes["working_directory"] = {
                    "old": previous_state["working_directory"],
                    "new": current_state["working_directory"]
                }
            
            # 检查依赖包变化
            old_packages = set(previous_state.get("installed_packages", []))
            new_packages = set(current_state["installed_packages"])
            if old_packages != new_packages:
                changes["packages"] = {
                    "removed": list(old_packages - new_packages),
                    "added": list(new_packages - old_packages)
                }
            
            # 检查配置文件变化
            old_configs = previous_state.get("config_files", {})
            new_configs = current_state["config_files"]
            config_changes = {}
            for file_name in set(old_configs.keys()) | set(new_configs.keys()):
                if file_name not in old_configs:
                    config_changes[file_name] = "added"
                elif file_name not in new_configs:
                    config_changes[file_name] = "removed"
                elif old_configs[file_name] != new_configs[file_name]:
                    config_changes[file_name] = "modified"
            
            if config_changes:
                changes["config_files"] = config_changes
            
            return {"status": "success", "changes": changes}
            
        except Exception as e:
            self.logger.error(f"❌ 检查系统变化失败: {str(e)}")
            return {"status": "error", "message": str(e)}
    
    def auto_recovery(self):
        """自动恢复系统"""
        try:
            self.logger.info("🔄 开始自动恢复系统...")
            
            # 1. 检查系统变化
            change_result = self.check_system_changes()
            if change_result["status"] == "error":
                return False
            
            changes = change_result.get("changes", {})
            if not changes:
                self.logger.info("✅ 系统无变化，无需恢复")
                return True
            
            # 2. 处理各种变化
            recovery_actions = []
            
            if "python_path" in changes:
                recovery_actions.append(("Python路径变化", self.recover_python_environment))
            
            if "packages" in changes:
                recovery_actions.append(("依赖包变化", lambda: self.recover_packages(changes["packages"])))
            
            if "config_files" in changes:
                recovery_actions.append(("配置文件变化", lambda: self.recover_config_files(changes["config_files"])))
            
            # 3. 执行恢复操作
            success_count = 0
            for action_name, action_func in recovery_actions:
                try:
                    self.logger.info(f"🔧 执行恢复: {action_name}")
                    if action_func():
                        self.logger.info(f"✅ {action_name} - 恢复成功")
                        success_count += 1
                    else:
                        self.logger.warning(f"⚠️ {action_name} - 恢复失败")
                except Exception as e:
                    self.logger.error(f"❌ {action_name} - 恢复异常: {str(e)}")
            
            # 4. 更新系统状态
            self.save_system_state()
            
            return success_count == len(recovery_actions)
            
        except Exception as e:
            self.logger.error(f"❌ 自动恢复失败: {str(e)}")
            return False
    
    def get_installed_packages(self):
        """获取已安装的包列表"""
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'list', '--format=freeze'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                return result.stdout.strip().split('\n')
            return []
        except:
            return []
    
    def get_config_files_state(self):
        """获取配置文件状态"""
        config_files = [
            'all_features_config.json',
            'automation_workflow.json',
            'auth_codes.json',
            'monitor_settings.json',
            'startup_config.json'
        ]
        
        state = {}
        for config_file in config_files:
            if os.path.exists(config_file):
                stat = os.stat(config_file)
                state[config_file] = {
                    "exists": True,
                    "size": stat.st_size,
                    "modified": stat.st_mtime
                }
            else:
                state[config_file] = {"exists": False}
        
        return state
    
    def get_database_files_state(self):
        """获取数据库文件状态"""
        db_files = [
            'email_history.db',
            'recipient_quality.db',
            'anti_spam.db',
            'qq_anti_spam.db',
            'user_data/user_settings.db'
        ]
        
        state = {}
        for db_file in db_files:
            if os.path.exists(db_file):
                stat = os.stat(db_file)
                state[db_file] = {
                    "exists": True,
                    "size": stat.st_size,
                    "modified": stat.st_mtime
                }
            else:
                state[db_file] = {"exists": False}
        
        return state
    
    def get_user_settings(self):
        """获取用户设置"""
        try:
            if os.path.exists('user_data/user_settings.db'):
                conn = sqlite3.connect('user_data/user_settings.db')
                cursor = conn.cursor()
                cursor.execute("SELECT key, value FROM user_settings")
                settings = dict(cursor.fetchall())
                conn.close()
                return settings
            return {}
        except:
            return {}
    
    def get_system_info(self):
        """获取系统信息"""
        import platform
        return {
            "platform": platform.platform(),
            "python_version": platform.python_version(),
            "architecture": platform.architecture()[0]
        }
    
    def recover_python_environment(self):
        """恢复Python环境"""
        try:
            # 检查当前Python环境是否可用
            result = subprocess.run([sys.executable, '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                self.logger.info("✅ Python环境正常")
                return True
            else:
                self.logger.error("❌ Python环境异常")
                return False
        except Exception as e:
            self.logger.error(f"❌ Python环境恢复失败: {str(e)}")
            return False
    
    def recover_packages(self, package_changes):
        """恢复依赖包"""
        try:
            # 重新安装缺失的关键包
            critical_packages = ['jieba']
            
            for package in critical_packages:
                if package in package_changes.get("removed", []):
                    self.logger.info(f"📦 重新安装关键包: {package}")
                    result = subprocess.run([sys.executable, '-m', 'pip', 'install', package], 
                                          capture_output=True, text=True)
                    if result.returncode != 0:
                        self.logger.error(f"❌ 安装 {package} 失败")
                        return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 恢复依赖包失败: {str(e)}")
            return False
    
    def recover_config_files(self, config_changes):
        """恢复配置文件"""
        try:
            # 从备份恢复缺失的配置文件
            backup_dir = Path("config_backup")
            
            for config_file, change_type in config_changes.items():
                if change_type == "removed":
                    backup_file = backup_dir / f"{config_file}.backup"
                    if backup_file.exists():
                        shutil.copy2(backup_file, config_file)
                        self.logger.info(f"✅ 从备份恢复: {config_file}")
                    else:
                        # 创建默认配置
                        self.create_default_config(config_file)
                        self.logger.info(f"✅ 创建默认配置: {config_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 恢复配置文件失败: {str(e)}")
            return False
    
    def create_default_config(self, config_file):
        """创建默认配置文件"""
        default_configs = {
            'all_features_config.json': {
                "auto_reply_monitoring": True,
                "recipient_quality_management": True,
                "anti_spam_protection": True,
                "qq_emergency_management": True,
                "smart_queue_system": True,
                "deep_coordination": True,
                "enabled_features_count": 6
            },
            'automation_workflow.json': {
                "auto_start_monitoring": False,
                "auto_queue_mode": True,
                "smart_scheduling": True,
                "emergency_protection": True
            },
            'auth_codes.json': {},
            'monitor_settings.json': {
                "check_interval": 30,
                "auto_reply_detection": True,
                "bounce_detection": True,
                "spam_detection": True
            },
            'startup_config.json': {
                "auto_restore_settings": True,
                "show_startup_tips": True,
                "check_updates": True,
                "backup_on_startup": True
            }
        }
        
        if config_file in default_configs:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_configs[config_file], f, indent=2, ensure_ascii=False)
    
    def setup_auto_recovery_service(self):
        """设置自动恢复服务"""
        try:
            # 创建Windows任务计划，在系统启动时运行恢复检查
            script_path = str(self.base_dir / "系统重启恢复机制.py")
            
            # 创建批处理文件
            bat_content = f'''@echo off
cd /d "{self.base_dir}"
python "{script_path}" --auto-recovery
'''
            
            bat_file = self.base_dir / "auto_recovery.bat"
            with open(bat_file, 'w', encoding='gbk') as f:
                f.write(bat_content)
            
            self.logger.info("✅ 自动恢复服务设置完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 设置自动恢复服务失败: {str(e)}")
            return False

def main():
    """主函数"""
    recovery_manager = SystemRecoveryManager()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--save-state':
            # 保存系统状态
            print("💾 保存系统状态...")
            success = recovery_manager.save_system_state()
            sys.exit(0 if success else 1)
            
        elif sys.argv[1] == '--auto-recovery':
            # 自动恢复
            print("🔄 自动恢复系统...")
            success = recovery_manager.auto_recovery()
            sys.exit(0 if success else 1)
            
        elif sys.argv[1] == '--setup-service':
            # 设置自动恢复服务
            print("⚙️ 设置自动恢复服务...")
            success = recovery_manager.setup_auto_recovery_service()
            sys.exit(0 if success else 1)
    
    else:
        # 交互模式
        print("🔄 系统重启恢复机制")
        print("1. 保存当前系统状态")
        print("2. 检查系统变化")
        print("3. 执行自动恢复")
        print("4. 设置自动恢复服务")
        
        choice = input("\n请选择操作 (1-4): ").strip()
        
        if choice == '1':
            recovery_manager.save_system_state()
        elif choice == '2':
            result = recovery_manager.check_system_changes()
            print(f"检查结果: {result}")
        elif choice == '3':
            recovery_manager.auto_recovery()
        elif choice == '4':
            recovery_manager.setup_auto_recovery_service()

if __name__ == "__main__":
    main()
