# -*- coding: utf-8 -*-
"""
QQ邮箱专项测试 - 针对QQ邮箱的特殊发送测试
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from config import SMTP_CONFIG

def test_qq_to_qq_simple():
    """测试QQ邮箱到QQ邮箱的简单发送"""
    print("QQ邮箱到QQ邮箱简单发送测试")
    print("=" * 50)
    
    sender = input("请输入发送者QQ邮箱: ").strip()
    recipient = input("请输入接收者QQ邮箱: ").strip()
    
    if not (sender.endswith('@qq.com') and recipient.endswith('@qq.com')):
        print("❌ 请确保两个邮箱都是QQ邮箱")
        return
    
    try:
        print(f"\n测试发送: {sender} -> {recipient}")
        print("-" * 50)
        
        # 创建最简单的邮件
        msg = MIMEText("这是一封测试邮件", 'plain', 'utf-8')
        msg['From'] = sender
        msg['To'] = recipient
        msg['Subject'] = "测试邮件"
        
        # 连接并发送
        print("1. 连接SMTP服务器...")
        server = smtplib.SMTP('smtp.qq.com', 587)
        
        print("2. 启用TLS...")
        server.starttls()
        
        print("3. 登录...")
        server.login(sender, SMTP_CONFIG['password'])
        
        print("4. 发送邮件...")
        result = server.sendmail(sender, [recipient], msg.as_string())
        
        server.quit()
        
        if result:
            print(f"❌ 发送失败: {result}")
        else:
            print("✅ 发送成功!")
            print("请检查收件人邮箱（包括垃圾邮件文件夹）")
            
    except smtplib.SMTPRecipientsRefused as e:
        print(f"❌ 收件人被拒绝: {e}")
        for email, (code, msg) in e.recipients.items():
            print(f"  邮箱: {email}")
            print(f"  错误码: {code}")
            print(f"  错误信息: {msg.decode() if isinstance(msg, bytes) else msg}")
            
    except Exception as e:
        print(f"❌ 发送失败: {e}")

def test_different_content():
    """测试不同内容的邮件"""
    print("\n" + "=" * 50)
    print("测试不同内容的邮件")
    print("=" * 50)
    
    sender = input("请输入发送者QQ邮箱: ").strip()
    recipient = input("请输入接收者QQ邮箱: ").strip()
    
    test_cases = [
        {
            'subject': '测试',
            'body': '你好',
            'description': '最简单的中文邮件'
        },
        {
            'subject': 'Test',
            'body': 'Hello',
            'description': '简单的英文邮件'
        },
        {
            'subject': '工作邮件',
            'body': '这是一封正常的工作邮件，内容完全正常。',
            'description': '正常工作邮件'
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['description']}")
        print(f"主题: {test_case['subject']}")
        print(f"内容: {test_case['body']}")
        
        try:
            msg = MIMEText(test_case['body'], 'plain', 'utf-8')
            msg['From'] = sender
            msg['To'] = recipient
            msg['Subject'] = test_case['subject']
            
            server = smtplib.SMTP('smtp.qq.com', 587)
            server.starttls()
            server.login(sender, SMTP_CONFIG['password'])
            
            result = server.sendmail(sender, [recipient], msg.as_string())
            server.quit()
            
            if result:
                print(f"❌ 失败: {result}")
            else:
                print("✅ 成功")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        if i < len(test_cases):
            input("按回车继续下一个测试...")

def check_qq_mail_restrictions():
    """检查QQ邮箱可能的限制"""
    print("\n" + "=" * 50)
    print("QQ邮箱可能的限制和解决方案")
    print("=" * 50)
    
    print("\n🔍 可能的问题:")
    print("1. 收件人邮箱设置了拒收外部邮件")
    print("2. 反垃圾邮件系统误判")
    print("3. 发送频率过高被限制")
    print("4. 邮件内容被过滤")
    print("5. IP地址被列入黑名单")
    
    print("\n💡 解决方案:")
    print("1. 让收件人检查邮箱设置:")
    print("   - 登录QQ邮箱")
    print("   - 设置 -> 反垃圾 -> 设置白名单")
    print("   - 将发送者邮箱加入白名单")
    
    print("\n2. 优化邮件内容:")
    print("   - 使用简单的主题")
    print("   - 避免敏感词汇")
    print("   - 减少链接和图片")
    
    print("\n3. 检查发送环境:")
    print("   - 确保网络环境正常")
    print("   - 避免使用VPN或代理")
    print("   - 检查是否被ISP限制")
    
    print("\n4. 联系收件人确认:")
    print("   - 确认邮箱地址正确")
    print("   - 确认邮箱正常使用")
    print("   - 检查垃圾邮件文件夹")

def main():
    """主函数"""
    print("QQ邮箱专项测试工具")
    print("=" * 50)
    print("专门针对QQ邮箱之间的发送问题进行测试")
    print("=" * 50)
    
    while True:
        print("\n请选择测试类型:")
        print("1. 简单QQ到QQ发送测试")
        print("2. 不同内容邮件测试")
        print("3. QQ邮箱限制说明")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == '1':
            test_qq_to_qq_simple()
        elif choice == '2':
            test_different_content()
        elif choice == '3':
            check_qq_mail_restrictions()
        elif choice == '4':
            print("测试结束")
            break
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n测试已取消")
    except Exception as e:
        print(f"\n程序出错: {str(e)}")
    
    input("\n按回车键退出...")
