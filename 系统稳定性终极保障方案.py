#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2.0系统稳定性终极保障方案
确保系统在任何情况下都能稳定运行，包括：
- 系统重启后的自动恢复
- 电脑重启后的环境适应
- 依赖包缺失的自动修复
- 数据库损坏的自动重建
- 配置文件丢失的自动恢复
- 网络问题的智能处理
- 权限问题的自动解决
- 资源不足的预警和处理
"""

import os
import sys
import json
import sqlite3
import subprocess
import threading
import time
import shutil
import logging
import traceback
from datetime import datetime, timedelta
from pathlib import Path
import psutil
import socket

class SystemStabilityGuardian:
    """系统稳定性守护者"""
    
    def __init__(self):
        self.setup_logging()
        self.system_state = {}
        self.recovery_actions = []
        self.critical_files = [
            'gui_main.py', 'email_sender.py', 'email_history_manager.py',
            'rag_search_engine.py', 'recipient_quality_manager.py',
            'anti_spam_manager.py', 'qq_email_anti_spam.py',
            'system_coordinator.py', 'batch_manager.py', 'queue_system.py',
            'database_manager.py'
        ]
        self.critical_databases = [
            'email_history.db', 'recipient_quality.db', 'anti_spam.db',
            'qq_anti_spam.db', 'system_integration.db'
        ]
        
    def setup_logging(self):
        """设置日志系统"""
        os.makedirs('logs', exist_ok=True)
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/system_stability.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def comprehensive_system_check(self):
        """全面系统检查"""
        print("🔍 开始全面系统稳定性检查...")
        self.logger.info("开始全面系统稳定性检查")
        
        checks = [
            ("Python环境", self.check_python_environment),
            ("关键依赖包", self.check_dependencies),
            ("核心文件完整性", self.check_core_files),
            ("数据库完整性", self.check_databases),
            ("配置文件状态", self.check_config_files),
            ("目录结构", self.check_directory_structure),
            ("文件权限", self.check_file_permissions),
            ("磁盘空间", self.check_disk_space),
            ("内存使用", self.check_memory_usage),
            ("网络连接", self.check_network_connectivity),
            ("进程状态", self.check_process_status),
            ("系统资源", self.check_system_resources)
        ]
        
        results = {}
        for check_name, check_func in checks:
            try:
                print(f"  🔍 检查 {check_name}...")
                result = check_func()
                results[check_name] = result
                
                if result['status'] == 'error':
                    print(f"    ❌ {result['message']}")
                elif result['status'] == 'warning':
                    print(f"    ⚠️ {result['message']}")
                else:
                    print(f"    ✅ {result['message']}")
                    
            except Exception as e:
                error_msg = f"检查失败: {str(e)}"
                print(f"    💥 {error_msg}")
                results[check_name] = {'status': 'error', 'message': error_msg}
                self.logger.error(f"{check_name}检查失败: {str(e)}")
        
        return results
    
    def check_python_environment(self):
        """检查Python环境"""
        try:
            python_version = sys.version
            python_path = sys.executable
            pip_available = subprocess.run([sys.executable, '-m', 'pip', '--version'], 
                                         capture_output=True, text=True).returncode == 0
            
            if not pip_available:
                return {'status': 'error', 'message': 'pip不可用，无法安装依赖包'}
            
            return {
                'status': 'success',
                'message': f'Python环境正常 (版本: {python_version.split()[0]}, 路径: {python_path})'
            }
        except Exception as e:
            return {'status': 'error', 'message': f'Python环境检查失败: {str(e)}'}
    
    def check_dependencies(self):
        """检查关键依赖包"""
        required_packages = ['jieba', 'psutil']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            return {
                'status': 'error',
                'message': f'缺少依赖包: {", ".join(missing_packages)}',
                'missing': missing_packages
            }
        
        return {'status': 'success', 'message': '所有关键依赖包已安装'}
    
    def check_core_files(self):
        """检查核心文件完整性"""
        missing_files = []
        corrupted_files = []
        
        for file_path in self.critical_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
            else:
                # 检查文件是否可读且不为空
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read(100)  # 读取前100个字符
                        if not content.strip():
                            corrupted_files.append(file_path)
                except Exception:
                    corrupted_files.append(file_path)
        
        issues = []
        if missing_files:
            issues.append(f"缺少文件: {', '.join(missing_files)}")
        if corrupted_files:
            issues.append(f"损坏文件: {', '.join(corrupted_files)}")
        
        if issues:
            return {'status': 'error', 'message': '; '.join(issues)}
        
        return {'status': 'success', 'message': f'所有{len(self.critical_files)}个核心文件完整'}
    
    def check_databases(self):
        """检查数据库完整性"""
        issues = []
        
        for db_path in self.critical_databases:
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path, timeout=5)
                    conn.execute("SELECT 1")
                    conn.close()
                except sqlite3.Error as e:
                    issues.append(f"{db_path}: {str(e)}")
            else:
                # 数据库不存在，标记为需要创建
                issues.append(f"{db_path}: 不存在")
        
        if issues:
            return {'status': 'warning', 'message': f'数据库问题: {"; ".join(issues)}'}
        
        return {'status': 'success', 'message': f'所有{len(self.critical_databases)}个数据库正常'}
    
    def check_config_files(self):
        """检查配置文件状态"""
        config_files = [
            'user_data/user_settings.json',
            'user_data/email_content.json',
            'user_data/send_config.json',
            'user_data/monitoring_settings.json'
        ]
        
        missing_configs = []
        for config_file in config_files:
            if not os.path.exists(config_file):
                missing_configs.append(config_file)
        
        if missing_configs:
            return {
                'status': 'warning',
                'message': f'缺少配置文件: {", ".join(missing_configs)}'
            }
        
        return {'status': 'success', 'message': '所有配置文件存在'}
    
    def check_directory_structure(self):
        """检查目录结构"""
        required_dirs = ['logs', 'user_data', 'backups', 'temp']
        missing_dirs = []
        
        for dir_name in required_dirs:
            if not os.path.exists(dir_name):
                missing_dirs.append(dir_name)
        
        if missing_dirs:
            return {
                'status': 'warning',
                'message': f'缺少目录: {", ".join(missing_dirs)}',
                'missing': missing_dirs
            }
        
        return {'status': 'success', 'message': '目录结构完整'}
    
    def check_file_permissions(self):
        """检查文件权限"""
        test_file = 'temp/permission_test.txt'
        try:
            os.makedirs('temp', exist_ok=True)
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            return {'status': 'success', 'message': '文件读写权限正常'}
        except Exception as e:
            return {'status': 'error', 'message': f'文件权限问题: {str(e)}'}
    
    def check_disk_space(self):
        """检查磁盘空间"""
        try:
            disk_usage = psutil.disk_usage('.')
            free_space_mb = disk_usage.free / (1024 * 1024)
            
            if free_space_mb < 100:
                return {'status': 'error', 'message': f'磁盘空间不足: {free_space_mb:.1f}MB'}
            elif free_space_mb < 500:
                return {'status': 'warning', 'message': f'磁盘空间较少: {free_space_mb:.1f}MB'}
            
            return {'status': 'success', 'message': f'磁盘空间充足: {free_space_mb:.1f}MB'}
        except Exception as e:
            return {'status': 'warning', 'message': f'无法检查磁盘空间: {str(e)}'}
    
    def check_memory_usage(self):
        """检查内存使用"""
        try:
            memory = psutil.virtual_memory()
            available_mb = memory.available / (1024 * 1024)
            
            if available_mb < 100:
                return {'status': 'warning', 'message': f'可用内存较少: {available_mb:.1f}MB'}
            
            return {'status': 'success', 'message': f'内存充足: {available_mb:.1f}MB'}
        except Exception as e:
            return {'status': 'warning', 'message': f'无法检查内存: {str(e)}'}
    
    def check_network_connectivity(self):
        """检查网络连接"""
        try:
            # 测试DNS解析
            socket.gethostbyname('smtp.qq.com')
            return {'status': 'success', 'message': '网络连接正常'}
        except Exception:
            return {'status': 'warning', 'message': '网络连接异常，可能影响邮件发送'}
    
    def check_process_status(self):
        """检查进程状态"""
        try:
            current_process = psutil.Process()
            cpu_percent = current_process.cpu_percent()
            memory_mb = current_process.memory_info().rss / (1024 * 1024)
            
            return {
                'status': 'success',
                'message': f'进程状态正常 (CPU: {cpu_percent:.1f}%, 内存: {memory_mb:.1f}MB)'
            }
        except Exception as e:
            return {'status': 'warning', 'message': f'无法检查进程状态: {str(e)}'}
    
    def check_system_resources(self):
        """检查系统资源"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            if cpu_percent > 90:
                return {'status': 'warning', 'message': f'CPU使用率过高: {cpu_percent:.1f}%'}
            
            return {'status': 'success', 'message': f'系统资源正常 (CPU: {cpu_percent:.1f}%)'}
        except Exception as e:
            return {'status': 'warning', 'message': f'无法检查系统资源: {str(e)}'}

    def auto_repair_system(self, check_results):
        """自动修复系统问题"""
        print("\n🔧 开始自动修复系统问题...")
        self.logger.info("开始自动修复系统问题")

        repair_results = {}

        # 修复依赖包问题
        if 'missing' in check_results.get('关键依赖包', {}):
            repair_results['依赖包修复'] = self.repair_dependencies(
                check_results['关键依赖包']['missing']
            )

        # 修复目录结构
        if 'missing' in check_results.get('目录结构', {}):
            repair_results['目录修复'] = self.repair_directories(
                check_results['目录结构']['missing']
            )

        # 修复数据库问题
        if check_results.get('数据库完整性', {}).get('status') in ['warning', 'error']:
            repair_results['数据库修复'] = self.repair_databases()

        # 修复配置文件
        if check_results.get('配置文件状态', {}).get('status') == 'warning':
            repair_results['配置文件修复'] = self.repair_config_files()

        # 清理临时文件
        repair_results['系统清理'] = self.cleanup_system()

        return repair_results

    def repair_dependencies(self, missing_packages):
        """修复依赖包问题"""
        print("  🔧 修复依赖包...")
        try:
            for package in missing_packages:
                print(f"    📦 安装 {package}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], capture_output=True, text=True, timeout=300)

                if result.returncode == 0:
                    print(f"    ✅ {package} 安装成功")
                else:
                    print(f"    ❌ {package} 安装失败: {result.stderr}")
                    return {'status': 'error', 'message': f'安装{package}失败'}

            return {'status': 'success', 'message': f'成功安装{len(missing_packages)}个依赖包'}
        except Exception as e:
            return {'status': 'error', 'message': f'依赖包修复失败: {str(e)}'}

    def repair_directories(self, missing_dirs):
        """修复目录结构"""
        print("  🔧 修复目录结构...")
        try:
            for dir_name in missing_dirs:
                os.makedirs(dir_name, exist_ok=True)
                print(f"    ✅ 创建目录: {dir_name}")

            return {'status': 'success', 'message': f'成功创建{len(missing_dirs)}个目录'}
        except Exception as e:
            return {'status': 'error', 'message': f'目录修复失败: {str(e)}'}

    def repair_databases(self):
        """修复数据库问题"""
        print("  🔧 修复数据库...")
        try:
            repaired_count = 0

            for db_path in self.critical_databases:
                if not os.path.exists(db_path):
                    self.create_database(db_path)
                    repaired_count += 1
                    print(f"    ✅ 创建数据库: {db_path}")
                else:
                    # 检查数据库是否可访问
                    try:
                        conn = sqlite3.connect(db_path, timeout=5)
                        conn.execute("SELECT 1")
                        conn.close()
                    except sqlite3.Error:
                        # 备份损坏的数据库
                        backup_path = f"{db_path}.corrupted_{int(time.time())}"
                        shutil.move(db_path, backup_path)
                        self.create_database(db_path)
                        repaired_count += 1
                        print(f"    ✅ 重建数据库: {db_path}")

            return {'status': 'success', 'message': f'修复了{repaired_count}个数据库'}
        except Exception as e:
            return {'status': 'error', 'message': f'数据库修复失败: {str(e)}'}

    def create_database(self, db_path):
        """创建数据库"""
        conn = sqlite3.connect(db_path)

        if 'email_history.db' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sent_emails (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_email TEXT,
                    recipient_email TEXT,
                    subject TEXT,
                    body TEXT,
                    sent_time TEXT,
                    status TEXT
                )
            ''')
        elif 'recipient_quality.db' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS recipient_quality (
                    email TEXT PRIMARY KEY,
                    quality_score REAL,
                    send_count INTEGER,
                    reply_count INTEGER,
                    last_reply_time TEXT,
                    status TEXT
                )
            ''')
        elif 'anti_spam.db' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS spam_detection (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_email TEXT,
                    detection_time TEXT,
                    risk_level TEXT,
                    details TEXT
                )
            ''')
        elif 'qq_anti_spam.db' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS qq_spam_status (
                    sender_email TEXT PRIMARY KEY,
                    daily_sent INTEGER,
                    consecutive_no_reply INTEGER,
                    emergency_active INTEGER,
                    last_update TEXT
                )
            ''')
        elif 'system_integration.db' in db_path:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sync_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_email TEXT,
                    sync_type TEXT,
                    sync_time TEXT,
                    success INTEGER,
                    details TEXT
                )
            ''')

        conn.commit()
        conn.close()

    def repair_config_files(self):
        """修复配置文件"""
        print("  🔧 修复配置文件...")
        try:
            os.makedirs('user_data', exist_ok=True)

            default_configs = {
                'user_data/user_settings.json': {
                    'sender_email': '',
                    'auto_queue_mode': True,
                    'send_mode': 'standard'
                },
                'user_data/email_content.json': {
                    'subject': '',
                    'body': '',
                    'recipient_emails': ''
                },
                'user_data/send_config.json': {
                    'send_mode': 'standard',
                    'add_personalization': True
                },
                'user_data/monitoring_settings.json': {
                    'check_interval': 5,
                    'monitor_duration': 2,
                    'auto_start': False
                }
            }

            created_count = 0
            for config_path, default_content in default_configs.items():
                if not os.path.exists(config_path):
                    with open(config_path, 'w', encoding='utf-8') as f:
                        json.dump(default_content, f, ensure_ascii=False, indent=2)
                    created_count += 1
                    print(f"    ✅ 创建配置文件: {config_path}")

            return {'status': 'success', 'message': f'创建了{created_count}个配置文件'}
        except Exception as e:
            return {'status': 'error', 'message': f'配置文件修复失败: {str(e)}'}

    def cleanup_system(self):
        """清理系统"""
        print("  🔧 清理系统...")
        try:
            cleaned_items = 0

            # 清理临时文件
            temp_patterns = ['*.tmp', '*.temp', '*.log.old', '*.bak']
            for pattern in temp_patterns:
                import glob
                for file_path in glob.glob(pattern):
                    try:
                        os.remove(file_path)
                        cleaned_items += 1
                    except:
                        pass

            # 清理过期日志
            logs_dir = 'logs'
            if os.path.exists(logs_dir):
                cutoff_time = time.time() - (7 * 24 * 3600)  # 7天前
                for file_name in os.listdir(logs_dir):
                    file_path = os.path.join(logs_dir, file_name)
                    if os.path.isfile(file_path) and os.path.getmtime(file_path) < cutoff_time:
                        try:
                            os.remove(file_path)
                            cleaned_items += 1
                        except:
                            pass

            return {'status': 'success', 'message': f'清理了{cleaned_items}个文件'}
        except Exception as e:
            return {'status': 'warning', 'message': f'系统清理部分失败: {str(e)}'}

    def save_system_state(self):
        """保存系统状态"""
        try:
            state = {
                'timestamp': datetime.now().isoformat(),
                'python_path': sys.executable,
                'working_directory': os.getcwd(),
                'environment_variables': dict(os.environ),
                'installed_packages': self.get_installed_packages(),
                'system_info': {
                    'platform': sys.platform,
                    'python_version': sys.version
                }
            }

            os.makedirs('backups', exist_ok=True)
            with open('backups/system_state.json', 'w', encoding='utf-8') as f:
                json.dump(state, f, ensure_ascii=False, indent=2)

            self.logger.info("系统状态已保存")
            return True
        except Exception as e:
            self.logger.error(f"保存系统状态失败: {str(e)}")
            return False

    def get_installed_packages(self):
        """获取已安装的包列表"""
        try:
            result = subprocess.run([sys.executable, '-m', 'pip', 'list'],
                                  capture_output=True, text=True)
            return result.stdout
        except:
            return "无法获取包列表"

    def create_recovery_service(self):
        """创建系统恢复服务"""
        try:
            # 创建Windows任务计划或Linux服务
            if sys.platform == 'win32':
                self.create_windows_task()
            else:
                self.create_linux_service()

            return True
        except Exception as e:
            self.logger.error(f"创建恢复服务失败: {str(e)}")
            return False

    def create_windows_task(self):
        """创建Windows任务计划"""
        script_path = os.path.abspath(__file__)
        python_path = sys.executable

        task_xml = f'''<?xml version="1.0" encoding="UTF-16"?>
<Task version="1.2">
  <Triggers>
    <BootTrigger>
      <Enabled>true</Enabled>
    </BootTrigger>
  </Triggers>
  <Actions>
    <Exec>
      <Command>{python_path}</Command>
      <Arguments>"{script_path}" --auto-recovery</Arguments>
      <WorkingDirectory>{os.getcwd()}</WorkingDirectory>
    </Exec>
  </Actions>
</Task>'''

        with open('temp/email_system_recovery.xml', 'w', encoding='utf-16') as f:
            f.write(task_xml)

        # 注册任务计划
        subprocess.run([
            'schtasks', '/create', '/tn', 'EmailSystemRecovery',
            '/xml', 'temp/email_system_recovery.xml', '/f'
        ], check=True)

    def monitor_system_health(self, duration_minutes=60):
        """监控系统健康状态"""
        print(f"🔍 开始监控系统健康状态 ({duration_minutes}分钟)...")

        start_time = time.time()
        end_time = start_time + (duration_minutes * 60)

        while time.time() < end_time:
            try:
                # 检查关键进程
                if not self.check_critical_processes():
                    self.logger.warning("检测到关键进程异常")

                # 检查资源使用
                resource_status = self.check_system_resources()
                if resource_status['status'] == 'warning':
                    self.logger.warning(f"系统资源警告: {resource_status['message']}")

                # 检查数据库连接
                if not self.check_database_connections():
                    self.logger.warning("数据库连接异常")

                time.sleep(60)  # 每分钟检查一次

            except KeyboardInterrupt:
                print("监控已停止")
                break
            except Exception as e:
                self.logger.error(f"监控过程中出现错误: {str(e)}")
                time.sleep(60)

    def check_critical_processes(self):
        """检查关键进程"""
        try:
            # 检查当前Python进程是否正常
            current_process = psutil.Process()
            return current_process.is_running()
        except:
            return False

    def check_database_connections(self):
        """检查数据库连接"""
        try:
            for db_path in self.critical_databases:
                if os.path.exists(db_path):
                    conn = sqlite3.connect(db_path, timeout=5)
                    conn.execute("SELECT 1")
                    conn.close()
            return True
        except:
            return False

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='2.0系统稳定性终极保障方案')
    parser.add_argument('--check', action='store_true', help='执行全面系统检查')
    parser.add_argument('--repair', action='store_true', help='自动修复系统问题')
    parser.add_argument('--monitor', type=int, default=0, help='监控系统健康状态(分钟)')
    parser.add_argument('--save-state', action='store_true', help='保存系统状态')
    parser.add_argument('--auto-recovery', action='store_true', help='自动恢复模式')
    parser.add_argument('--setup-service', action='store_true', help='设置恢复服务')

    args = parser.parse_args()

    guardian = SystemStabilityGuardian()

    print("🛡️ 2.0系统稳定性终极保障方案")
    print("=" * 50)

    if args.auto_recovery:
        print("🔄 自动恢复模式启动...")
        check_results = guardian.comprehensive_system_check()

        # 检查是否有需要修复的问题
        has_issues = any(
            result.get('status') in ['error', 'warning']
            for result in check_results.values()
        )

        if has_issues:
            print("发现问题，开始自动修复...")
            repair_results = guardian.auto_repair_system(check_results)

            # 再次检查
            print("修复完成，重新检查系统...")
            final_check = guardian.comprehensive_system_check()

            # 保存状态
            guardian.save_system_state()
        else:
            print("✅ 系统状态良好，无需修复")

    elif args.check or (not any(vars(args).values())):
        # 默认执行检查
        check_results = guardian.comprehensive_system_check()

        # 统计结果
        error_count = sum(1 for r in check_results.values() if r.get('status') == 'error')
        warning_count = sum(1 for r in check_results.values() if r.get('status') == 'warning')
        success_count = len(check_results) - error_count - warning_count

        print(f"\n📊 检查结果统计:")
        print(f"  ✅ 正常: {success_count}")
        print(f"  ⚠️ 警告: {warning_count}")
        print(f"  ❌ 错误: {error_count}")

        if error_count > 0 or warning_count > 0:
            print(f"\n💡 建议运行自动修复: python {__file__} --repair")

    if args.repair:
        check_results = guardian.comprehensive_system_check()
        repair_results = guardian.auto_repair_system(check_results)

        print(f"\n🔧 修复结果:")
        for repair_type, result in repair_results.items():
            status_icon = "✅" if result['status'] == 'success' else "❌"
            print(f"  {status_icon} {repair_type}: {result['message']}")

    if args.save_state:
        if guardian.save_system_state():
            print("✅ 系统状态已保存")
        else:
            print("❌ 保存系统状态失败")

    if args.setup_service:
        if guardian.create_recovery_service():
            print("✅ 恢复服务已设置")
        else:
            print("❌ 设置恢复服务失败")

    if args.monitor > 0:
        guardian.monitor_system_health(args.monitor)

if __name__ == "__main__":
    main()
