#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试界面修复效果
验证所有界面创建方法是否存在
"""

import tkinter as tk
from tkinter import messagebox
import sys
import traceback

def test_interface_methods():
    """测试界面方法"""
    print("🔧 开始测试界面修复效果...")
    print("=" * 50)
    
    try:
        # 导入修复后的GUI
        from gui_complete_v3 import EmailSenderGUI
        
        print("✅ GUI模块导入成功")
        
        # 创建测试窗口
        root = tk.Tk()
        app = EmailSenderGUI(root)
        
        print("✅ GUI界面创建成功")
        
        # 测试界面创建方法是否存在
        interface_methods = [
            ('_create_quality_manager_interface', '质量管理器界面'),
            ('_create_anti_spam_interface', '反垃圾邮件界面'),
            ('_create_emergency_interface', '应急管理界面'),
            ('_create_batch_manager_interface', '批次管理界面'),
            ('_create_import_interface', '导入功能界面'),
            ('_create_duplicate_detection_interface', '重复检测界面'),
            ('_create_monitor_interface', '监控管理界面'),
            ('_create_history_interface', '历史记录界面'),
            ('_create_search_interface', '智能搜索界面'),
        ]
        
        print("\n🔍 检查界面创建方法:")
        print("-" * 30)
        
        missing_methods = []
        working_methods = []
        
        for method_name, description in interface_methods:
            if hasattr(app, method_name):
                method = getattr(app, method_name)
                if callable(method):
                    working_methods.append((method_name, description))
                    print(f"✅ {description} ({method_name})")
                else:
                    missing_methods.append((method_name, description))
                    print(f"❌ {description} ({method_name}) - 不是可调用方法")
            else:
                missing_methods.append((method_name, description))
                print(f"❌ {description} ({method_name}) - 方法不存在")
        
        # 测试辅助功能方法
        helper_methods = [
            ('_analyze_quality', '质量分析'),
            ('_generate_quality_report', '生成质量报告'),
            ('_refresh_quality_data', '刷新质量数据'),
            ('_evaluate_spam_risk', '评估垃圾邮件风险'),
            ('_adjust_anti_spam_strategy', '调整反垃圾策略'),
            ('_show_anti_spam_report', '显示反垃圾报告'),
            ('_activate_emergency', '激活应急模式'),
            ('_reset_emergency', '重置应急系统'),
            ('_show_emergency_report', '显示应急报告'),
            ('_create_batches', '创建批次'),
            ('_recreate_batches', '重新创建批次'),
            ('_view_batches', '查看批次'),
            ('_select_import_file', '选择导入文件'),
            ('_start_import', '开始导入'),
            ('_import_to_main_system', '导入到主系统'),
            ('_start_duplicate_detection', '开始重复检测'),
            ('_show_duplicate_report', '显示重复检测报告'),
            ('_clean_duplicates', '清理重复内容'),
        ]
        
        print("\n🔍 检查辅助功能方法:")
        print("-" * 30)
        
        for method_name, description in helper_methods:
            if hasattr(app, method_name):
                method = getattr(app, method_name)
                if callable(method):
                    working_methods.append((method_name, description))
                    print(f"✅ {description} ({method_name})")
                else:
                    missing_methods.append((method_name, description))
                    print(f"❌ {description} ({method_name}) - 不是可调用方法")
            else:
                missing_methods.append((method_name, description))
                print(f"❌ {description} ({method_name}) - 方法不存在")
        
        print(f"\n📊 测试结果:")
        print(f"✅ 正常工作的方法: {len(working_methods)} 个")
        print(f"❌ 缺失或有问题的方法: {len(missing_methods)} 个")
        
        if missing_methods:
            print(f"\n⚠️ 需要修复的方法:")
            for method_name, description in missing_methods:
                print(f"  - {description} ({method_name})")
        
        # 计算修复率
        total_methods = len(interface_methods) + len(helper_methods)
        working_count = len(working_methods)
        fix_rate = (working_count / total_methods) * 100
        
        print(f"\n🎯 界面修复效果:")
        print(f"📊 方法修复率: {fix_rate:.1f}% ({working_count}/{total_methods})")
        
        if fix_rate >= 95:
            print(f"\n🎉 界面修复效果优秀！")
            print("✅ 几乎所有界面方法都已正常工作")
            print("💡 建议：可以正常使用所有高级功能")
        elif fix_rate >= 80:
            print(f"\n✅ 界面修复效果良好！")
            print("✅ 大部分界面方法已经修复")
            print("💡 建议：可以使用大部分功能")
        else:
            print(f"\n⚠️ 界面修复效果一般")
            print("❌ 仍有较多方法需要修复")
            print("💡 建议：继续修复缺失的方法")
        
        # 关闭测试窗口
        root.destroy()
        
        return fix_rate >= 80
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
        return False

def main():
    """主函数"""
    print("🚀 启动界面修复测试...")
    
    try:
        success = test_interface_methods()
        
        if success:
            print(f"\n🎉 界面修复测试完成！修复效果良好")
            print("💡 现在所有高级功能按钮都应该能正常打开界面")
            print("📝 建议运行: python gui_complete_v3.py 测试实际效果")
        else:
            print(f"\n⚠️ 界面测试发现问题，需要进一步修复")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        print(f"错误详情: {traceback.format_exc()}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
