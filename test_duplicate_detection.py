#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重复检测和智能检索功能
用于验证修复效果
"""

import os
import sys
import sqlite3
import datetime
from email_history_manager import EmailHistoryManager
from rag_search_engine import RAGSearchEngine

def setup_test_data():
    """设置测试数据"""
    print("🔧 设置测试数据...")
    
    # 删除现有数据库
    if os.path.exists("test_email_history.db"):
        os.remove("test_email_history.db")
    
    # 创建测试历史管理器
    history_manager = EmailHistoryManager("test_email_history.db")
    
    # 添加测试邮件记录
    test_emails = [
        {
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "subject": "重要通知",
            "body": "这是一封重要的通知邮件，请查收相关文件。",
            "success": True
        },
        {
            "sender": "<EMAIL>", 
            "recipient": "<EMAIL>",
            "subject": "重要通知",
            "body": "这是一封重要的通知邮件，请查收相关文件。",
            "success": True
        },
        {
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>", 
            "subject": "其他主题",
            "body": "这是一封完全不同的邮件内容。",
            "success": True
        },
        {
            "sender": "<EMAIL>",
            "recipient": "<EMAIL>",
            "subject": "另一个主题", 
            "body": "又是一封不同的邮件。",
            "success": True
        }
    ]
    
    for email in test_emails:
        record_id = history_manager.add_email_record(
            email["sender"],
            email["recipient"], 
            email["subject"],
            email["body"],
            email["success"]
        )
        print(f"✅ 添加测试邮件: {email['recipient']} - {record_id}")
    
    print(f"✅ 测试数据设置完成，共添加 {len(test_emails)} 封邮件")
    return history_manager

def test_smart_suggestions():
    """测试智能建议功能"""
    print("\n🤖 测试智能建议功能...")
    
    # 创建RAG搜索引擎
    rag_search = RAGSearchEngine("test_email_history.db")
    
    # 测试场景：发送相同内容的邮件
    test_subject = "重要通知"
    test_body = "这是一封重要的通知邮件，请查收相关文件。"
    test_sender = "<EMAIL>"
    
    print(f"📧 测试邮件内容:")
    print(f"   主题: {test_subject}")
    print(f"   正文: {test_body}")
    print(f"   发件人: {test_sender}")
    
    # 执行智能建议
    suggestions = rag_search.suggest_recipients(test_subject, test_body, test_sender)
    
    print(f"\n📊 智能建议结果:")
    print(f"   安全收件人数量: {len(suggestions['safe_recipients'])}")
    print(f"   重复收件人数量: {len(suggestions['duplicate_recipients'])}")
    print(f"   建议: {suggestions['recommendation']}")
    
    print(f"\n✅ 安全收件人列表:")
    for recipient in suggestions['safe_recipients']:
        print(f"   - {recipient}")
    
    print(f"\n⚠️ 重复收件人列表:")
    for recipient in suggestions['duplicate_recipients']:
        print(f"   - {recipient}")
    
    print(f"\n🔍 重复详情:")
    for detail in suggestions['duplicate_details']:
        print(f"   - {detail['recipient']}: 相似度 {detail['similarity']:.3f}")
    
    return suggestions

def test_duplicate_detection():
    """测试重复检测功能"""
    print("\n🔍 测试重复检测功能...")
    
    # 创建RAG搜索引擎
    rag_search = RAGSearchEngine("test_email_history.db")
    
    # 测试场景：检测收件人列表中的重复
    test_subject = "重要通知"
    test_body = "这是一封重要的通知邮件，请查收相关文件。"
    test_sender = "<EMAIL>"
    test_recipients = [
        "<EMAIL>",      # 已发送过相同内容
        "<EMAIL>",        # 已发送过相同内容  
        "<EMAIL>",        # 新收件人
        "<EMAIL>"      # 新收件人
    ]
    
    print(f"📧 测试收件人列表:")
    for recipient in test_recipients:
        print(f"   - {recipient}")
    
    # 执行重复检测
    detection_result = rag_search.advanced_duplicate_detection(
        test_subject, test_body, test_recipients, test_sender
    )
    
    print(f"\n📊 重复检测结果:")
    print(f"   总收件人: {detection_result['total_recipients']}")
    print(f"   安全收件人: {len(detection_result['safe_recipients'])}")
    print(f"   完全重复: {len(detection_result['exact_matches'])}")
    print(f"   相似重复: {len(detection_result['similar_matches'])}")
    print(f"   建议: {detection_result['recommendation']}")
    
    print(f"\n✅ 安全收件人:")
    for recipient in detection_result['safe_recipients']:
        print(f"   - {recipient}")
    
    print(f"\n⚠️ 完全重复:")
    for match in detection_result['exact_matches']:
        print(f"   - {match['recipient']}: 相似度 {match['similarity']:.3f}")
    
    print(f"\n🔍 相似重复:")
    for match in detection_result['similar_matches']:
        print(f"   - {match['recipient']}: 相似度 {match['similarity']:.3f}")
    
    return detection_result

def test_debug_analysis():
    """测试调试分析功能"""
    print("\n🔧 测试调试分析功能...")
    
    # 创建RAG搜索引擎
    rag_search = RAGSearchEngine("test_email_history.db")
    
    # 测试场景
    test_subject = "重要通知"
    test_body = "这是一封重要的通知邮件，请查收相关文件。"
    test_sender = "<EMAIL>"
    
    # 执行调试分析
    debug_result = rag_search.debug_similarity_analysis(test_subject, test_body, test_sender)
    
    print(f"📊 调试分析结果:")
    print(f"   历史收件人总数: {debug_result['total_historical_recipients']}")
    print(f"   相似邮件数量: {debug_result['total_similar_emails']}")
    print(f"   查询词汇: {', '.join(debug_result['query_terms'])}")
    
    print(f"\n🎯 不同阈值下的结果:")
    print(f"   阈值 0.6: {len(debug_result['duplicates_threshold_0_6'])} 重复, {len(debug_result['safe_recipients_0_6'])} 安全")
    print(f"   阈值 0.7: {len(debug_result['duplicates_threshold_0_7'])} 重复, {len(debug_result['safe_recipients_0_7'])} 安全")
    print(f"   阈值 0.8: {len(debug_result['duplicates_threshold_0_8'])} 重复, {len(debug_result['safe_recipients_0_8'])} 安全")
    
    return debug_result

def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    if os.path.exists("test_email_history.db"):
        os.remove("test_email_history.db")
        print("✅ 测试数据库已删除")

def main():
    """主测试函数"""
    print("🚀 开始测试重复检测和智能检索功能")
    print("="*60)
    
    try:
        # 1. 设置测试数据
        history_manager = setup_test_data()
        
        # 2. 测试智能建议
        suggestions = test_smart_suggestions()
        
        # 3. 测试重复检测
        detection_result = test_duplicate_detection()
        
        # 4. 测试调试分析
        debug_result = test_debug_analysis()
        
        # 5. 验证结果
        print("\n🎯 验证测试结果:")
        print("="*40)
        
        # 验证智能建议是否正确识别了重复收件人
        expected_duplicates = {"<EMAIL>", "<EMAIL>"}
        actual_duplicates = set(suggestions['duplicate_recipients'])
        
        if expected_duplicates.issubset(actual_duplicates):
            print("✅ 智能建议正确识别了重复收件人")
        else:
            print("❌ 智能建议未正确识别重复收件人")
            print(f"   期望: {expected_duplicates}")
            print(f"   实际: {actual_duplicates}")
        
        # 验证重复检测是否正确
        safe_recipients = set(detection_result['safe_recipients'])
        expected_safe = {"<EMAIL>", "<EMAIL>"}
        
        if expected_safe.issubset(safe_recipients):
            print("✅ 重复检测正确识别了安全收件人")
        else:
            print("❌ 重复检测未正确识别安全收件人")
            print(f"   期望: {expected_safe}")
            print(f"   实际: {safe_recipients}")
        
        print("\n🎉 测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        cleanup_test_data()

if __name__ == "__main__":
    main()
