# -*- coding: utf-8 -*-
"""
测试邮件发送助手的修复效果
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox

def test_personalization_feature():
    """测试个性化功能"""
    root = tk.Tk()
    root.title("个性化功能测试")
    root.geometry("500x400")
    
    # 主框架
    main_frame = ttk.Frame(root, padding="10")
    main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)
    main_frame.columnconfigure(1, weight=1)
    main_frame.rowconfigure(2, weight=1)
    
    # 标题
    title_label = ttk.Label(main_frame, text="个性化功能测试", 
                           font=('Arial', 14, 'bold'))
    title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
    
    # 个性化设置
    add_personalization = tk.BooleanVar(value=False)
    
    personalization_frame = ttk.LabelFrame(main_frame, text="个性化设置", padding="10")
    personalization_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
    
    ttk.Checkbutton(personalization_frame, 
                   text="添加邮件编号和时间戳",
                   variable=add_personalization).pack(anchor=tk.W)
    
    # 邮件正文输入
    ttk.Label(main_frame, text="邮件正文:").grid(row=2, column=0, sticky=(tk.W, tk.N), pady=5)
    
    body_text = scrolledtext.ScrolledText(main_frame, width=40, height=10,
                                         font=('Microsoft YaHei UI', 9),
                                         wrap=tk.WORD)
    body_text.grid(row=2, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
    
    # 预设测试内容
    test_content = """亲爱的朋友，

这是一封测试邮件 😊

包含各种内容：
• Emoji表情：🎉 🎊 ❤️ 👍 🌟
• 颜文字：(◕‿◕) ¯\\_(ツ)_/¯ (╯°□°）╯︵ ┻━┻
• 特殊符号：★ ☆ ♠ ♣ ♥ ♦
• 箭头：← → ↑ ↓ ⇒ ⇔

祝好！"""
    
    body_text.insert(tk.END, test_content)
    
    # 结果显示
    result_frame = ttk.LabelFrame(main_frame, text="处理结果", padding="10")
    result_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
    result_frame.columnconfigure(0, weight=1)
    result_frame.rowconfigure(0, weight=1)
    main_frame.rowconfigure(3, weight=1)
    
    result_text = scrolledtext.ScrolledText(result_frame, width=60, height=8,
                                           font=('Microsoft YaHei UI', 9),
                                           wrap=tk.WORD, state=tk.DISABLED)
    result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def _personalize_content(body, sequence):
        """模拟个性化内容处理"""
        if not body.strip():
            return body
            
        if not add_personalization.get():
            return body
            
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        personalized_body = body
        if body.strip():
            personalized_body += f"\n\n---\n邮件编号: #{sequence:03d} | 发送时间: {timestamp}"
            
        return personalized_body
    
    def process_content():
        """处理内容并显示结果"""
        original_content = body_text.get(1.0, tk.END).strip()
        processed_content = _personalize_content(original_content, 1)
        
        result_text.config(state=tk.NORMAL)
        result_text.delete(1.0, tk.END)
        
        if add_personalization.get():
            result_text.insert(tk.END, "✅ 已启用个性化设置\n\n")
        else:
            result_text.insert(tk.END, "❌ 未启用个性化设置\n\n")
            
        result_text.insert(tk.END, "处理后的邮件内容：\n")
        result_text.insert(tk.END, "-" * 50 + "\n")
        result_text.insert(tk.END, processed_content)
        result_text.insert(tk.END, "\n" + "-" * 50)
        
        result_text.config(state=tk.DISABLED)
    
    def reset_content():
        """重置内容"""
        body_text.delete(1.0, tk.END)
        body_text.insert(tk.END, test_content)
        result_text.config(state=tk.NORMAL)
        result_text.delete(1.0, tk.END)
        result_text.config(state=tk.DISABLED)
    
    # 按钮
    btn_frame = ttk.Frame(main_frame)
    btn_frame.grid(row=4, column=0, columnspan=2, pady=10)
    
    ttk.Button(btn_frame, text="处理内容", command=process_content).pack(side=tk.LEFT, padx=5)
    ttk.Button(btn_frame, text="重置内容", command=reset_content).pack(side=tk.LEFT, padx=5)
    ttk.Button(btn_frame, text="关闭", command=root.destroy).pack(side=tk.LEFT, padx=5)
    
    # 说明
    info_label = ttk.Label(main_frame, 
                          text="1. 在正文中输入包含Emoji的内容\n2. 选择是否启用个性化设置\n3. 点击'处理内容'查看结果",
                          font=('Arial', 9), foreground='gray', justify=tk.LEFT)
    info_label.grid(row=5, column=0, columnspan=2, pady=5)
    
    root.mainloop()

def main():
    """主函数"""
    print("启动个性化功能测试...")
    test_personalization_feature()

if __name__ == "__main__":
    main()
