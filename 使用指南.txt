自动化邮件发送助手 - 使用指南
=====================================

🚀 快速开始
-----------

方法1: VBS启动器（推荐，完美支持中文）
- 双击 "启动.vbs" 文件（中文菜单界面）
- 双击 "start.vbs" 文件（英文菜单界面）
- 双击 "快速启动.vbs" 文件（直接启动图形界面）

方法2: 批处理启动
- 双击 "启动.bat" 文件（启动VBS中文菜单）
- 双击 "start.bat" 文件（启动VBS英文菜单）

方法3: 直接运行Python脚本
- python gui_main.py     （图形界面版本，推荐）
- python main.py         （命令行单邮件）
- python main.py --batch （命令行批量邮件）

方法4: Python启动器
- python 启动器.py       （Python版本的中文菜单）

📋 功能说明
-----------

1. 图形界面版本（推荐）
   - 操作简单直观，支持中文界面
   - 两种发送模式：群发 / 单独发送
   - 实时操作日志显示
   - 支持拖拽添加附件
   - 详细的状态提示

2. 发送模式选择
   - 群发模式：所有收件人一起接收邮件
   - 单独发送：逐个发送给每个收件人
   - 自动控制发送频率，避免被识别为垃圾邮件

3. 日志功能
   - 实时显示操作过程
   - 详细的错误信息
   - 可保存日志到文件
   - 可查看系统日志文件

4. 命令行版本
   - 发送单封邮件
   - 批量发送不同内容邮件
   - 适合高级用户和自动化脚本

5. 测试功能
   - SMTP连接测试
   - 邮件发送测试
   - 环境检查与设置

🔧 配置信息
-----------

您的QQ邮箱SMTP配置已预设：
- SMTP服务器: smtp.qq.com
- 端口: 587 (TLS加密)
- 授权码: vwpboqxircdudgfa

使用时只需输入您的QQ邮箱地址即可。

📁 支持的附件格式
-----------------

文档类: .txt, .pdf, .doc, .docx, .xls, .xlsx, .ppt, .pptx
图片类: .jpg, .jpeg, .png, .gif
压缩包: .zip, .rar, .7z

单个附件最大25MB

⚠️ 注意事项
-----------

1. 确保网络连接正常
2. 首次使用建议先运行测试功能
3. 如收件人收不到邮件，请提醒检查垃圾邮件文件夹
4. 建议收件人将您的邮箱添加到通讯录

🆘 常见问题
-----------

Q: 启动脚本出现乱码？
A: 请使用 "启动.bat" 文件，或在PowerShell中运行 ".\start.bat"

Q: 邮件发送失败？
A: 检查网络连接，确认QQ邮箱地址正确，查看日志文件

Q: 图形界面无法启动？
A: 确保Python版本3.6+，运行环境检查脚本

Q: 附件无法添加？
A: 检查文件是否存在，文件大小是否超过25MB

📞 获取帮助
-----------

1. 查看 README.md 详细文档
2. 运行 python setup.py 进行环境检查
3. 查看 email_sender.log 日志文件
4. 参考 example_usage.py 示例代码

=====================================
祝您使用愉快！
